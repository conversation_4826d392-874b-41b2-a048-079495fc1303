#!/usr/bin/env python3
# coding: utf-8
__author__ = 'lishuanglin'
__version__ = '1.0.0'

import os
import time
import paramiko
import threading
import pandas as pd
import numpy as np
import tkinter as tk

from tkinter import ttk
from tkinter import scrolledtext
from tkinter import messagebox
from datetime import datetime

class GuiSlurm:
    def __init__(self, root):
        self.root = root
        self.root.title("Slurm GUI Manager")
        self.root.geometry("1000x500")  # Default size
        self.root.minsize(800, 400)  # Minimum size

        # SSH connection config
        self.hostname = "************"
        self.username = "lishuanglin"  # Hardcoded username
        self.password = "Meridian@YR"  # Hardcoded password
        self.ssh = None

        # Set default font for the entire application
        self.root.option_add('*Font', ('Consolas', 11))

        # Create style for widgets
        self.style = ttk.Style()
        self.style.configure("TLabelframe.Label", font=('Consolas', 11, 'bold'))
        self.style.configure("Treeview", font=('Consolas', 11))
        self.style.configure("Treeview.Heading", font=('Consolas', 11, 'bold'))
        self.style.configure("Refresh.TButton", font=('Consolas', 11, 'bold'))
        self.style.configure("TNotebook.Tab", font=('Consolas', 11, 'bold'))

        # Create a main frame to hold the sidebar and content
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create a frame for the sidebar (left side)
        sidebar_frame = ttk.Frame(main_frame, width=100)  # Reduced width
        sidebar_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 2), pady=0)
        sidebar_frame.pack_propagate(False)  # Prevent the frame from shrinking

        # Add a title to the sidebar
        sidebar_title = ttk.Label(sidebar_frame, text="OPTION", font=('Consolas', 11, 'bold'))
        sidebar_title.pack(pady=(0, 10))

        # Add a vertical separator between sidebar and content
        separator = ttk.Separator(main_frame, orient=tk.VERTICAL)
        separator.pack(side=tk.LEFT, fill=tk.Y, padx=2)

        # Create a frame for the content (right side)
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Create frames for each "tab" content
        self.tasks_tab = ttk.Frame(content_frame)
        self.jobs_tab = ttk.Frame(content_frame)
        self.nodes_tab = ttk.Frame(content_frame)
        self.history_tab = ttk.Frame(content_frame)
        self.operation_tab = ttk.Frame(content_frame)

        # Initially show the tasks tab
        self.current_tab = "tasks"
        self.tasks_tab.pack(fill=tk.BOTH, expand=True)

        # Create styles for active and inactive sidebar buttons
        self.style.configure("Sidebar.TButton", font=('Consolas', 11), padding=(5, 10))
        self.style.configure("SidebarActive.TButton", font=('Consolas', 11, 'bold'),
                            background="#4a6984", foreground="white", padding=(5, 10))

        # Create a separator above the buttons
        ttk.Separator(sidebar_frame, orient=tk.HORIZONTAL).pack(fill=tk.X, pady=5)

        # Create buttons for the sidebar
        self.tasks_btn = ttk.Button(sidebar_frame, text="Progress", style="SidebarActive.TButton",
                                   command=lambda: self.show_tab("tasks"))
        self.tasks_btn.pack(fill=tk.X, pady=2)

        # Add separator between buttons
        ttk.Separator(sidebar_frame, orient=tk.HORIZONTAL).pack(fill=tk.X, pady=1)

        self.jobs_btn = ttk.Button(sidebar_frame, text="Jobs", style="Sidebar.TButton",
                                  command=lambda: self.show_tab("jobs"))
        self.jobs_btn.pack(fill=tk.X, pady=2)

        # Add separator between buttons
        ttk.Separator(sidebar_frame, orient=tk.HORIZONTAL).pack(fill=tk.X, pady=1)

        self.nodes_btn = ttk.Button(sidebar_frame, text="Nodes", style="Sidebar.TButton",
                                   command=lambda: self.show_tab("nodes"))
        self.nodes_btn.pack(fill=tk.X, pady=2)

        # Add separator between buttons
        ttk.Separator(sidebar_frame, orient=tk.HORIZONTAL).pack(fill=tk.X, pady=1)

        self.history_btn = ttk.Button(sidebar_frame, text="History", style="Sidebar.TButton",
                                     command=lambda: self.show_tab("history"))
        self.history_btn.pack(fill=tk.X, pady=2)

        # Add separator between buttons
        ttk.Separator(sidebar_frame, orient=tk.HORIZONTAL).pack(fill=tk.X, pady=1)

        # Add Operation button
        self.operation_btn = ttk.Button(sidebar_frame, text="Operation", style="Sidebar.TButton",
                                      command=lambda: self.show_tab("operation"))
        self.operation_btn.pack(fill=tk.X, pady=2)

        # Add a final separator
        ttk.Separator(sidebar_frame, orient=tk.HORIZONTAL).pack(fill=tk.X, pady=5)

        # Store sidebar buttons in a dictionary for easy access
        self.sidebar_buttons = {
            "tasks": self.tasks_btn,
            "jobs": self.jobs_btn,
            "nodes": self.nodes_btn,
            "history": self.history_btn,
            "operation": self.operation_btn
        }

        # Setup each tab
        self.setup_tasks_tab()
        self.setup_jobs_tab()
        self.setup_nodes_tab()
        self.setup_history_tab()
        self.setup_operation_tab()

        # Auto refresh thread
        self.is_running = False
        self.refresh_thread = None

        # Connect to server
        self.connect_to_server()

    def connect_to_server(self):
        try:
            self.ssh = paramiko.SSHClient()
            self.ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            # Use username/password authentication
            self.ssh.connect(self.hostname, username=self.username, password=self.password)

            # Start auto refresh after successful connection
            self.is_running = True
            self.refresh_thread = threading.Thread(target=self.auto_refresh)
            self.refresh_thread.daemon = True
            self.refresh_thread.start()

        except Exception as e:
            messagebox.showerror("Error", f"Connection failed: {str(e)}")

    def setup_tasks_tab(self):
        # Tasks frame
        self.tasks_frame = ttk.LabelFrame(self.tasks_tab, text="Tasks Progress", padding="10")
        self.tasks_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.tasks_frame.columnconfigure(0, weight=1)
        self.tasks_frame.rowconfigure(0, weight=1)

        # Create tasks list
        task_columns = ("Job Name", "Job ID", "Total", "Completed", "Failed", "Running", "Progress", "ETA(min)")
        self.tasks_tree = ttk.Treeview(self.tasks_frame, columns=task_columns, show="headings", height=10)

        # Set task column headers and center alignment
        for col in task_columns:
            self.tasks_tree.heading(col, text=col, anchor=tk.CENTER)
            if col in ('Job Name'):
                width = 150
            elif col in ('Failed', 'Pending'):
                width = 60
            else:
                width = 90
            self.tasks_tree.column(col, width=width, anchor=tk.CENTER)  # Center the text

        # Add scrollbar for tasks
        tasks_scrollbar = ttk.Scrollbar(self.tasks_frame, orient=tk.VERTICAL, command=self.tasks_tree.yview)
        self.tasks_tree.configure(yscrollcommand=tasks_scrollbar.set)

        # Configure tag for alternating row colors and progress status
        self.tasks_tree.tag_configure('odd', background='#cccccc')
        self.tasks_tree.tag_configure('even', background='#cccccc')

        # Tags for job status
        self.tasks_tree.tag_configure('completed', background='#cccccc')  # Gray for completed jobs
        self.tasks_tree.tag_configure('running', background='#d4ffd4')  # Light green for running jobs

        self.tasks_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        tasks_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Create a frame for refresh button and timestamp
        refresh_frame = ttk.Frame(self.tasks_tab)
        refresh_frame.pack(fill=tk.X, padx=5, pady=5)

        # Refresh button
        self.refresh_button = ttk.Button(refresh_frame, text="Refresh", command=self.refresh_tasks, style="Refresh.TButton")
        self.refresh_button.pack(side=tk.LEFT, padx=10)

        # Last updated timestamp
        self.timestamp_label = ttk.Label(refresh_frame, text="Last updated: Never", font=('Consolas', 11))
        self.timestamp_label.pack(side=tk.LEFT, padx=10)

    def setup_jobs_tab(self):
        # Jobs control frame
        jobs_control_frame = ttk.Frame(self.jobs_tab)
        jobs_control_frame.pack(fill=tk.X, padx=5, pady=5)

        # User filter
        user_label = ttk.Label(jobs_control_frame, text="User:")
        user_label.pack(side=tk.LEFT, padx=5)
        self.user_entry = ttk.Entry(jobs_control_frame, width=15, font=('Consolas', 11))
        self.user_entry.pack(side=tk.LEFT, padx=5)

        # Node filter
        node_label = ttk.Label(jobs_control_frame, text="Node:")
        node_label.pack(side=tk.LEFT, padx=5)
        self.node_entry = ttk.Entry(jobs_control_frame, width=15, font=('Consolas', 11))
        self.node_entry.pack(side=tk.LEFT, padx=5)

        # Partition filter
        part_label = ttk.Label(jobs_control_frame, text="Partition:")
        part_label.pack(side=tk.LEFT, padx=5)
        self.part_entry = ttk.Entry(jobs_control_frame, width=15, font=('Consolas', 11))
        self.part_entry.pack(side=tk.LEFT, padx=5)

        # Update button
        update_button = ttk.Button(jobs_control_frame, text="Update", command=self.refresh_jobs, style="Refresh.TButton")
        update_button.pack(side=tk.RIGHT, padx=5)

        # Jobs text area
        self.jobs_text = scrolledtext.ScrolledText(self.jobs_tab, wrap=tk.WORD, font=('Consolas', 11), height=30)
        self.jobs_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Job status frame
        job_status_frame = ttk.Frame(self.jobs_tab)
        job_status_frame.pack(fill=tk.X, padx=5, pady=5)

        # Job ID entry
        job_id_label = ttk.Label(job_status_frame, text="Job ID:")
        job_id_label.pack(side=tk.LEFT, padx=5)
        self.job_entry = ttk.Entry(job_status_frame, width=15, font=('Consolas', 11))
        self.job_entry.pack(side=tk.LEFT, padx=5)

        # Show status button
        status_button = ttk.Button(job_status_frame, text="Show Status", command=self.show_job_status, style="Refresh.TButton")
        status_button.pack(side=tk.LEFT, padx=5)

        # Cancel job section
        cancel_label = ttk.Label(job_status_frame, text="Cancel ID:")
        cancel_label.pack(side=tk.LEFT, padx=5)
        self.job_id_cancel_entry = ttk.Entry(job_status_frame, width=15, font=('Consolas', 11))
        self.job_id_cancel_entry.pack(side=tk.LEFT, padx=5)

        # Cancel button
        cancel_button = ttk.Button(job_status_frame, text="Cancel", command=self.cancel_job, style="Refresh.TButton")
        cancel_button.pack(side=tk.LEFT, padx=5)

    def setup_nodes_tab(self):
        # Nodes control frame
        nodes_control_frame = ttk.Frame(self.nodes_tab)
        nodes_control_frame.pack(fill=tk.X, padx=5, pady=5)

        # Partition filter
        part_label = ttk.Label(nodes_control_frame, text="Partition:")
        part_label.pack(side=tk.LEFT, padx=5)
        self.part_entry2 = ttk.Entry(nodes_control_frame, width=15, font=('Consolas', 11))
        self.part_entry2.insert(0, "AMD_R")  # Default value
        self.part_entry2.pack(side=tk.LEFT, padx=5)

        # Detail checkbox
        self.detail_var = tk.BooleanVar(value=True)
        detail_label = ttk.Label(nodes_control_frame, text="Show details:")
        detail_label.pack(side=tk.LEFT, padx=5)
        detail_check = ttk.Checkbutton(nodes_control_frame, variable=self.detail_var)
        detail_check.pack(side=tk.LEFT, padx=5)

        # Update button
        update_nodes_button = ttk.Button(nodes_control_frame, text="Update", command=self.refresh_nodes, style="Refresh.TButton")
        update_nodes_button.pack(side=tk.RIGHT, padx=5)

        # Nodes text area
        self.nodes_text = scrolledtext.ScrolledText(self.nodes_tab, wrap=tk.WORD, font=('Consolas', 11), height=30)
        self.nodes_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def setup_history_tab(self):
        # History control frame
        history_control_frame = ttk.Frame(self.history_tab)
        history_control_frame.pack(fill=tk.X, padx=5, pady=5)

        # History button
        history_button = ttk.Button(history_control_frame, text="Show History", command=self.read_history_job, style="Refresh.TButton")
        history_button.pack(side=tk.LEFT, padx=5)

        # Failed button
        failed_button = ttk.Button(history_control_frame, text="Show Failed", command=self.read_failed_job, style="Refresh.TButton")
        failed_button.pack(side=tk.LEFT, padx=5)

        # History text area
        self.history_text = scrolledtext.ScrolledText(self.history_tab, wrap=tk.WORD, font=('Consolas', 11), height=30)
        self.history_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def setup_operation_tab(self):
        # Create a frame for the operation tab
        self.operation_frame = ttk.LabelFrame(self.operation_tab, text="Task Operations", padding="10")
        self.operation_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create a frame for task details
        task_details_frame = ttk.Frame(self.operation_frame)
        task_details_frame.pack(fill=tk.X, padx=5, pady=5)

        # Job ID entry for task details
        job_id_label = ttk.Label(task_details_frame, text="Job ID:")
        job_id_label.pack(side=tk.LEFT, padx=5)
        self.task_job_entry = ttk.Entry(task_details_frame, width=15, font=('Consolas', 11))
        self.task_job_entry.pack(side=tk.LEFT, padx=5)

        # Show details button
        details_button = ttk.Button(task_details_frame, text="Show Details",
                                  command=self.show_task_details, style="Refresh.TButton")
        details_button.pack(side=tk.LEFT, padx=5)

        # Output text area
        self.operation_text = scrolledtext.ScrolledText(self.operation_frame, wrap=tk.WORD,
                                                     font=('Consolas', 11), height=10)
        self.operation_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create a frame for job cancellation at the bottom
        cancel_frame = ttk.Frame(self.operation_frame)
        cancel_frame.pack(fill=tk.X, padx=5, pady=5)

        # Job ID entry for cancellation
        cancel_label = ttk.Label(cancel_frame, text="Cancel Job ID:")
        cancel_label.pack(side=tk.LEFT, padx=5)
        self.operation_cancel_entry = ttk.Entry(cancel_frame, width=15, font=('Consolas', 11))
        self.operation_cancel_entry.pack(side=tk.LEFT, padx=5)

        # Cancel button
        cancel_button = ttk.Button(cancel_frame, text="Cancel Job",
                                 command=self.cancel_operation_job, style="Refresh.TButton")
        cancel_button.pack(side=tk.LEFT, padx=5)

    def refresh_tasks(self):
        if not self.ssh:
            return None

        try:
            # Get user job information
            _, stdout, _ = self.ssh.exec_command('sacct -o JobID%25,JobName%25,Start,State%25 | grep -v -E ".batch|.extern"')
            jobs = stdout.readlines()[2:]  # Skip header 2 lines
            job_ids = sorted([line.split()[0] for line in jobs])
            unique_jobs = list(set([s.split('_')[0] for s in job_ids]))

            # Clear existing items in the tasks tree
            for item in self.tasks_tree.get_children():
                self.tasks_tree.delete(item)

            # Sort unique_jobs numerically
            unique_jobs = sorted(unique_jobs, key=lambda x: int(x) if x.isdigit() else float('inf'))

            for job in unique_jobs:
                job_group = [j for j in jobs if j.strip().startswith(job)]
                completed = [j for j in job_group if j.strip().startswith(job) and 'COMPLETED' in j]
                failed = [j for j in job_group if j.strip().startswith(job) and 'FAILED' in j]
                pending = [j for j in job_group if j.strip().startswith(job) and 'PENDING' in j]
                running = [j for j in job_group if j.strip().startswith(job) and 'RUNNING' in j]
                cancelled = [j for j in job_group if j.strip().startswith(job) and 'CANCELLED' in j]
                start_time = pd.to_datetime(job_group[0].split()[2])
                elapsed = (pd.to_datetime(datetime.now()) - start_time).total_seconds() / 60

                print(len(completed), len(running), len(failed), len(pending))
                if len(completed)>0:
                    jobname = completed[0].split()[1]
                elif len(running)>0:
                    jobname = running[0].split()[1]
                elif len(failed)>0:
                    jobname = failed[0].split()[1]
                elif len(cancelled)>0:
                    jobname = cancelled[0].split()[1]

                total = len(job_group)
                if len(pending) > 0:
                    total = int(pending[0].split()[0].split('-')[-1][:-1])+1-int(job_group[0].split()[0].split('_')[1])

                completed_count = len(completed)
                failed_count = len(failed)
                pending_count = len(pending)
                running_count = len(running)
                eta = round(elapsed * (total/max(1,completed_count+failed_count)-1.0), 2) # eta is the estimated time to completion
                if len(cancelled)==total: eta = 0.0

                # Calculate progress percentage (1 - running/total)
                progress_pct = 0
                if total > 0:
                    progress_pct = round((completed_count+failed_count) / total * 100, 2)

                # Determine row tag based on job status
                if completed_count == total and total > 0:
                    # Job is completed
                    row_tag = 'completed'
                elif running_count > 0:
                    # Job is running
                    row_tag = 'running'
                else:
                    # Use alternating colors for other states
                    row_tag = 'even' if len(self.tasks_tree.get_children()) % 2 == 0 else 'odd'

                # Format progress percentage with color indication
                progress_text = f"{progress_pct}%"

                # Insert with row tag
                self.tasks_tree.insert('', tk.END, values=(
                    jobname,
                    job,
                    total,
                    completed_count,
                    failed_count,
                    running_count,
                    progress_text,
                    eta
                ), tags=(row_tag,))

            # Update the timestamp
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.timestamp_label.config(text=f"Last updated: {current_time}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh tasks: {str(e)}")

    def refresh_jobs(self):
        if not self.ssh:
            return None

        try:
            user = self.user_entry.get().strip()
            node = self.node_entry.get().strip()
            part = self.part_entry.get().strip()

            fmt = "%.15i %.15P %.15j %.10u %.10M %.6D %R"
            command = 'squeue '
            if user:
                command += f'-u {user} '
            if node:
                command += f'--nodelist={node} '
            if part:
                command += f'-p {part} '

            command += f'-o "{fmt}" | head -n20'

            _, stdout, _ = self.ssh.exec_command(command)
            jobs_output = stdout.read().decode('utf-8')

            self.jobs_text.delete(1.0, tk.END)
            self.jobs_text.insert(tk.END, jobs_output)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh jobs: {str(e)}")

    def refresh_nodes(self):
        if not self.ssh:
            return None

        try:
            part = self.part_entry2.get().strip()
            detail = self.detail_var.get()

            command = 'sinfo '
            if part:
                command += f'-p {part} '

            if detail:
                command += '-lN'

            _, stdout, _ = self.ssh.exec_command(command)
            nodes_output = stdout.read().decode('utf-8')

            self.nodes_text.delete(1.0, tk.END)
            self.nodes_text.insert(tk.END, nodes_output)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh nodes: {str(e)}")

    def show_job_status(self):
        if not self.ssh:
            return None

        try:
            job_id = self.job_entry.get().strip()
            if job_id:
                _, stdout, _ = self.ssh.exec_command(f'scontrol show job {job_id}')
                job_output = stdout.read().decode('utf-8')

                self.jobs_text.delete(1.0, tk.END)
                self.jobs_text.insert(tk.END, job_output)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to show job status: {str(e)}")

    def read_history_job(self):
        if not self.ssh:
            return None

        try:
            command = 'sacct -o JobID%20,JobName%20,Partition%8,ReqCPUS%4,ReqMem%5,State%15,Elapsed | grep -v -E ".batch|.extern" | sort -k1 -r | head -n20'
            _, stdout, _ = self.ssh.exec_command(command)
            job_output = stdout.read().decode('utf-8')

            self.history_text.delete(1.0, tk.END)
            self.history_text.insert(tk.END, job_output)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to read job history: {str(e)}")

    def read_failed_job(self):
        if not self.ssh:
            return None

        try:
            command = 'sacct -o JobID%20,JobName%20,Partition%8,State%10,Elapsed | grep -E "FAILED|OUT_OF" | grep -v .batch | sort -k1 -r | head -n20'
            _, stdout, _ = self.ssh.exec_command(command)
            job_output = stdout.read().decode('utf-8')

            self.history_text.delete(1.0, tk.END)
            self.history_text.insert(tk.END, job_output)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to read failed jobs: {str(e)}")

    def cancel_job(self):
        if not self.ssh:
            return None

        try:
            job_id = self.job_id_cancel_entry.get().strip()
            if job_id:
                _, stdout, _ = self.ssh.exec_command(f'scancel {job_id}')
                result = stdout.read().decode('utf-8')

                self.jobs_text.delete(1.0, tk.END)
                self.jobs_text.insert(tk.END, f"Job {job_id} cancelled.\n{result}")

                # Refresh jobs list after cancellation
                self.refresh_jobs()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to cancel job: {str(e)}")

    def auto_refresh(self, interval=60):
        """Automatically refresh the information at the specified interval (in seconds)."""
        while self.is_running:
            self.refresh_tasks()
            time.sleep(interval)

    def show_tab(self, tab_name):
        """Switch between tabs by hiding all and showing the selected one."""
        # Don't do anything if the tab is already active
        if self.current_tab == tab_name:
            return

        # Hide all tabs
        self.tasks_tab.pack_forget()
        self.jobs_tab.pack_forget()
        self.nodes_tab.pack_forget()
        self.history_tab.pack_forget()
        self.operation_tab.pack_forget()

        # Reset all button styles
        for _, btn in self.sidebar_buttons.items():
            btn.configure(style="Sidebar.TButton")

        # Show the selected tab and update button style
        if tab_name == "tasks":
            self.tasks_tab.pack(fill=tk.BOTH, expand=True)
            self.sidebar_buttons["tasks"].configure(style="SidebarActive.TButton")
            self.current_tab = "tasks"
            # Refresh tasks when switching to this tab
            self.refresh_tasks()
        elif tab_name == "jobs":
            self.jobs_tab.pack(fill=tk.BOTH, expand=True)
            self.sidebar_buttons["jobs"].configure(style="SidebarActive.TButton")
            self.current_tab = "jobs"
            # Refresh jobs when switching to this tab
            self.refresh_jobs()
        elif tab_name == "nodes":
            self.nodes_tab.pack(fill=tk.BOTH, expand=True)
            self.sidebar_buttons["nodes"].configure(style="SidebarActive.TButton")
            self.current_tab = "nodes"
            # Refresh nodes when switching to this tab
            self.refresh_nodes()
        elif tab_name == "history":
            self.history_tab.pack(fill=tk.BOTH, expand=True)
            self.sidebar_buttons["history"].configure(style="SidebarActive.TButton")
            self.current_tab = "history"
            # Refresh history when switching to this tab
            self.read_history_job()
        elif tab_name == "operation":
            self.operation_tab.pack(fill=tk.BOTH, expand=True)
            self.sidebar_buttons["operation"].configure(style="SidebarActive.TButton")
            self.current_tab = "operation"

    def show_task_details(self):
        """Show detailed information about a specific task/job."""
        if not self.ssh:
            return None

        try:
            job_id = self.task_job_entry.get().strip()
            if not job_id:
                messagebox.showinfo("Info", "Please enter a Job ID")
                return

            # Clear the text area
            self.operation_text.delete(1.0, tk.END)

            # Get job details using scontrol
            _, stdout, _ = self.ssh.exec_command(f'scontrol show job {job_id}')
            job_details = stdout.read().decode('utf-8')

            if job_details.strip():
                self.operation_text.insert(tk.END, f"=== Job Details for {job_id} ===\n\n")
                self.operation_text.insert(tk.END, job_details)
                self.operation_text.insert(tk.END, "\n\n")
            else:
                self.operation_text.insert(tk.END, f"No details found for job {job_id}\n\n")

            # Get job steps information
            _, stdout, _ = self.ssh.exec_command(f'scontrol show step {job_id}')
            step_details = stdout.read().decode('utf-8')

            if step_details.strip():
                self.operation_text.insert(tk.END, f"=== Job Steps for {job_id} ===\n\n")
                self.operation_text.insert(tk.END, step_details)

            # Get job accounting information
            _, stdout, _ = self.ssh.exec_command(f'sacct -j {job_id} -o JobID,JobName,Partition,State,ExitCode,Elapsed,MaxRSS,NodeList')
            acct_details = stdout.read().decode('utf-8')

            if acct_details.strip():
                self.operation_text.insert(tk.END, f"\n\n=== Job Accounting for {job_id} ===\n\n")
                self.operation_text.insert(tk.END, acct_details)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to get task details: {str(e)}")

    def cancel_operation_job(self):
        """Cancel a job from the operation tab."""
        if not self.ssh:
            return None

        try:
            job_id = self.operation_cancel_entry.get().strip()
            if not job_id:
                messagebox.showinfo("Info", "Please enter a Job ID to cancel")
                return

            # Confirm cancellation
            confirm = messagebox.askyesno("Confirm", f"Are you sure you want to cancel job {job_id}?")
            if not confirm:
                return

            # Cancel the job
            _, stdout, _ = self.ssh.exec_command(f'scancel {job_id}')
            result = stdout.read().decode('utf-8')

            # Clear and update the text area
            self.operation_text.delete(1.0, tk.END)
            self.operation_text.insert(tk.END, f"Job {job_id} has been cancelled.\n{result}\n\n")

            # Get updated job status
            _, stdout, _ = self.ssh.exec_command(f'sacct -j {job_id} -o JobID,JobName,State')
            status = stdout.read().decode('utf-8')

            if status.strip():
                self.operation_text.insert(tk.END, "Current job status:\n")
                self.operation_text.insert(tk.END, status)

            # Refresh tasks view to reflect the cancellation
            self.refresh_tasks()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to cancel job: {str(e)}")

    def on_closing(self):
        self.is_running = False
        if self.ssh:
            self.ssh.close()
        self.root.destroy()

if __name__ == "__main__":
    root = tk.Tk()

    # Set default font for all widgets
    font_family = "Consolas"
    font_size = 11

    # Configure fonts for different widget types
    root.option_add("*Font", (font_family, font_size))
    root.option_add("*Label.Font", (font_family, font_size))
    root.option_add("*Button.Font", (font_family, font_size))
    root.option_add("*Entry.Font", (font_family, font_size))

    # Create and configure the application
    app = GuiSlurm(root)
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    root.mainloop()
