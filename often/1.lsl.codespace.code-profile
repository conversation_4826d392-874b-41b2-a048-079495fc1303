{"name": "1.codespace", "settings": "{\"settings\":\"{\\r\\n    \\\"window.commandCenter\\\": true,\\r\\n    \\\"workbench.colorTheme\\\": \\\"Default Light Modern\\\",\\r\\n    \\\"workbench.activityBar.orientation\\\": \\\"vertical\\\",\\r\\n    \\\"remote.SSH.remotePlatform\\\": {\\r\\n        \\\"10.218.224.7\\\": \\\"linux\\\"\\r\\n    },\\r\\n    \\\"git.openRepositoryInParentFolders\\\": \\\"never\\\",\\r\\n    \\\"workbench.iconTheme\\\": \\\"vscode-icons\\\"\\r\\n}\"}", "keybindings": "{\"keybindings\":\"// Place your key bindings in this file to override the defaults\\n[\\n    {\\n        \\\"key\\\": \\\"ctrl+i\\\",\\n        \\\"command\\\": \\\"composerMode.agent\\\"\\n    },\\n    {\\n        \\\"key\\\": \\\"alt+d\\\",\\n        \\\"command\\\": \\\"editor.action.deleteLines\\\",\\n        \\\"when\\\": \\\"textInputFocus && !editorReadonly\\\"\\n    },\\n    {\\n        \\\"key\\\": \\\"ctrl+shift+k\\\",\\n        \\\"command\\\": \\\"-editor.action.deleteLines\\\",\\n        \\\"when\\\": \\\"textInputFocus && !editorReadonly\\\"\\n    },\\n    {\\n        \\\"key\\\": \\\"ctrl+u ctrl+u\\\",\\n        \\\"command\\\": \\\"editor.foldAll\\\",\\n        \\\"when\\\": \\\"editorTextFocus && foldingEnabled\\\"\\n    },\\n    {\\n        \\\"key\\\": \\\"ctrl+m ctrl+0\\\",\\n        \\\"command\\\": \\\"-editor.foldAll\\\",\\n        \\\"when\\\": \\\"editorTextFocus && foldingEnabled\\\"\\n    },\\n    {\\n        \\\"key\\\": \\\"ctrl+shift+u ctrl+shift+u\\\",\\n        \\\"command\\\": \\\"editor.unfoldAll\\\",\\n        \\\"when\\\": \\\"editorTextFocus && foldingEnabled\\\"\\n    },\\n    {\\n        \\\"key\\\": \\\"ctrl+m ctrl+j\\\",\\n        \\\"command\\\": \\\"-editor.unfoldAll\\\",\\n        \\\"when\\\": \\\"editorTextFocus && foldingEnabled\\\"\\n    },\\n    {\\n        \\\"key\\\": \\\"ctrl+shift+l\\\",\\n        \\\"command\\\": \\\"-addCursorsAtSearchResults\\\",\\n        \\\"when\\\": \\\"fileMatchOrMatchFocus && searchViewletVisible\\\"\\n    },\\n    {\\n        \\\"key\\\": \\\"ctrl+shift+l\\\",\\n        \\\"command\\\": \\\"-aichat.insertselectionintochat\\\"\\n    },\\n    {\\n        \\\"key\\\": \\\"ctrl+shift+alt+l\\\",\\n        \\\"command\\\": \\\"-workbench.action.quickchat.toggle\\\",\\n        \\\"when\\\": \\\"chatIsEnabled\\\"\\n    }\\n]\",\"platform\":3}", "extensions": "[{\"identifier\":{\"id\":\"augment.vscode-augment\",\"uuid\":\"fc0e137d-e132-47ed-9455-c4636fa5b897\"},\"displayName\":\"Augment\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"charliermarsh.ruff\",\"uuid\":\"c2ca9b43-fa38-44fc-928e-5125970b9c00\"},\"displayName\":\"Ruff\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"kevinrose.vsc-python-indent\",\"uuid\":\"f3cbfb84-b1e1-40ff-b70f-************\"},\"displayName\":\"Python Indent\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-python.debugpy\",\"uuid\":\"4bd5d2c9-9d65-401a-b0b2-7498d9f17615\"},\"displayName\":\"Python Debugger\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-python.python\",\"uuid\":\"f1f59ae4-9318-4f3c-a9b5-81b2eaa5f8a5\"},\"displayName\":\"Python\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-python.vscode-pylance\",\"uuid\":\"364d2426-116a-433a-a5d8-a5098dc3afbd\"},\"displayName\":\"Pylance\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-toolsai.jupyter\",\"uuid\":\"6c2f1801-1e7f-45b2-9b5c-7782f1e076e8\"},\"displayName\":\"Jupyter\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-toolsai.jupyter-renderers\",\"uuid\":\"b15c72f8-d5fe-421a-a4f7-27ed9f6addbf\"},\"displayName\":\"Jupyter Notebook Renderers\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-toolsai.vscode-jupyter-cell-tags\",\"uuid\":\"ab4fb32a-befb-4102-adf9-1652d0cd6a5e\"},\"displayName\":\"Jupyter Cell Tags\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-toolsai.vscode-jupyter-slideshow\",\"uuid\":\"e153ca70-b543-4865-b4c5-b31d34185948\"},\"displayName\":\"Jupyter Slide Show\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode-remote.remote-ssh\",\"uuid\":\"607fd052-be03-4363-b657-2bd62b83d28a\"},\"displayName\":\"Remote - SSH\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode-remote.remote-ssh-edit\",\"uuid\":\"bfeaf631-bcff-4908-93ed-fda4ef9a0c5c\"},\"displayName\":\"Remote - SSH: Editing Configuration Files\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.cpptools\",\"uuid\":\"690b692e-e8a9-493f-b802-8089d50ac1b2\"},\"displayName\":\"C/C++\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.remote-explorer\",\"uuid\":\"11858313-52cc-4e57-b3e4-d7b65281e34b\"},\"displayName\":\"Remote Explorer\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"vscode-icons-team.vscode-icons\",\"uuid\":\"9ccc1dd7-7ec4-4a46-bd4f-7d7b8b9d322a\"},\"displayName\":\"vscode-icons\",\"applicationScoped\":false}]", "globalState": "{\"storage\":{\"workbench.explorer.views.state.hidden\":\"[{\\\"id\\\":\\\"backgroundComposerExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"notepad\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"outline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"timeline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.explorer.openEditorsView\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.explorer.emptyView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"npm\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.explorer.fileView\\\",\\\"isHidden\\\":false}]\",\"workbench.view.search.state.hidden\":\"[{\\\"id\\\":\\\"workbench.view.search\\\",\\\"isHidden\\\":false}]\",\"workbench.scm.views.state.hidden\":\"[{\\\"id\\\":\\\"workbench.scm.repositories\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.scm\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.scm.history\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.markers.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.markers.view\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.output.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.output\\\",\\\"isHidden\\\":false}]\",\"terminal.hidden\":\"[{\\\"id\\\":\\\"terminal\\\",\\\"isHidden\\\":false}]\",\"workbench.view.bugbot.hidden\":\"[{\\\"id\\\":\\\"workbench.views.bugbot\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.pinnedPanels\":\"[{\\\"id\\\":\\\"workbench.panel.markers\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":0},{\\\"id\\\":\\\"workbench.panel.output\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":1},{\\\"id\\\":\\\"workbench.panel.repl\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":2},{\\\"id\\\":\\\"terminal\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":3},{\\\"id\\\":\\\"workbench.panel.testResults\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":3},{\\\"id\\\":\\\"~remote.forwardedPortsContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":5},{\\\"id\\\":\\\"workbench.view.extension.jupyter-variables\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":7},{\\\"id\\\":\\\"workbench.view.extension.augment-panel\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":6},{\\\"id\\\":\\\"refactorPreview\\\",\\\"pinned\\\":true,\\\"visible\\\":false}]\",\"workbench.auxiliarybar.pinnedPanels\":\"[{\\\"id\\\":\\\"workbench.panel.aichat.ba76e9f1-60cb-4707-9add-177945b1573d\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":1},{\\\"id\\\":\\\"workbench.view.extension.augment-chat\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8}]\",\"workbench.panel.composerChatViewPane.ba76e9f1-60cb-4707-9add-177945b1573d.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.aichat.view.ab922b39-a401-4f5d-b995-ef4004bdc0b0\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.panel.aichat.view.c11d3f4f-33ad-4a4d-9540-b0fd9629df8a\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.panel.aichat.view.cc3dd361-5255-4793-b1a7-8ffcd51ada07\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.panel.aichat.view.fc45aa84-eaf9-4167-8af7-5099f96bb842\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.panel.aichat.view.d3799f1f-02c6-4943-80d7-a85b5a5dcbd9\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.panel.aichat.view.962a726c-2e35-4994-a217-3f1a2672d9a1\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.panel.aichat.view.632c1393-6385-40e6-9c98-c43bb9a65dcd\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.panel.aichat.view.3261e60b-3a90-45a7-b566-9112fdd42893\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.panel.aichat.view.685961d9-f484-4832-93a3-2be84003dc8e\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.panel.aichat.view.5ceaf0b0-0b40-4298-9cda-a680203b779c\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.panel.aichat.view.9071e5a8-a107-4f3d-8a8c-bd51d54be8eb\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.panel.aichat.view.74b60079-5247-4bbd-a751-41384b8ba5f3\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.panel.aichat.view.21c62cc1-b9dd-48e7-9d7c-0eed32d6368b\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.panel.aichat.view.5dd828bf-d9c1-4774-8543-4d32d6749da1\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.panel.aichat.view.0e4ce43b-d5da-4eff-9325-ef3d51c61e8c\\\",\\\"isHidden\\\":false}]\",\"src.vs.platform.reactivestorage.browser.reactiveStorageServiceImpl.persistentStorage.applicationUser\":\"{\\\"cursorCreds\\\":{\\\"cppBackendUrl\\\":\\\"https://api3.cursor.sh\\\",\\\"websiteUrl\\\":\\\"https://cursor.com\\\",\\\"backendUrl\\\":\\\"https://api2.cursor.sh\\\",\\\"authClientId\\\":\\\"KbZUR41cY7W6zRSdpSUJ7I7mLYBKOCmB\\\",\\\"authDomain\\\":\\\"prod.authentication.cursor.sh\\\",\\\"repoBackendUrl\\\":\\\"https://repo42.cursor.sh\\\",\\\"contextBankUrl\\\":\\\"https://repo42.cursor.sh\\\",\\\"telemBackendUrl\\\":\\\"https://api3.cursor.sh\\\",\\\"cmdkBackendUrl\\\":\\\"https://api3.cursor.sh\\\",\\\"geoCppBackendUrl\\\":\\\"https://us-only.gcpp.cursor.sh\\\",\\\"cppConfigBackendUrl\\\":\\\"https://api4.cursor.sh\\\",\\\"bcProxyUrl\\\":\\\"https://api2.cursor.sh\\\",\\\"credentialsDisplayName\\\":\\\"Prod\\\"},\\\"cppEnabled\\\":true,\\\"composerState\\\":{\\\"alwaysKeepComposerInBound\\\":true,\\\"location2\\\":\\\"pane\\\",\\\"nonBarLocation\\\":\\\"pane\\\",\\\"nonBarChatLocation\\\":\\\"pane\\\",\\\"chatLocation\\\":\\\"pane\\\",\\\"hasMigratedChatLocation\\\":true,\\\"hasMigratedComposerGlobalConfigsToModeSpecificConfigs\\\":true,\\\"hasMigratedToAgentIsCmdI\\\":true,\\\"defaultCapabilities\\\":[],\\\"barAnchor\\\":\\\"center\\\",\\\"autoApplyFilesOutsideContext\\\":true,\\\"shouldAutoContinueToolCall\\\":false,\\\"defaultMode2\\\":\\\"agent\\\",\\\"useYoloMode\\\":false,\\\"yoloPrompt\\\":\\\"\\\",\\\"yoloCommandAllowlist\\\":[],\\\"yoloCommandDenylist\\\":[],\\\"yoloMcpToolsDisabled\\\":false,\\\"preferDiffInChat\\\":true,\\\"useStaticContextBank\\\":false,\\\"defaultUseToolsInEdit\\\":false,\\\"unification2\\\":true,\\\"unification12_useReactiveUnificationModeInstead\\\":\\\"merged\\\",\\\"shouldReviewChanges\\\":\\\"enabled\\\",\\\"wasBarPreviouslyOpen\\\":false,\\\"doNotShowYoloModeWarningAgain\\\":false,\\\"selectedFakeStreamerId\\\":null,\\\"yoloDeleteFileDisabled\\\":false,\\\"yoloDotFilesDisabled\\\":true,\\\"yoloOutsideWorkspaceDisabled\\\":true,\\\"webViewerAllowNonLocalhost\\\":false,\\\"isWebSearchToolEnabled\\\":true,\\\"isWebSearchToolEnabled2\\\":false,\\\"isWebSearchToolEnabled3\\\":null,\\\"autoAcceptWebSearchTool\\\":false,\\\"backgroundComposerEnv\\\":\\\"dev\\\",\\\"areSearchToolsEnabledForAskMode\\\":true,\\\"includeProjectLayout\\\":false,\\\"modes4\\\":[{\\\"id\\\":\\\"agent\\\",\\\"name\\\":\\\"Agent\\\",\\\"actionId\\\":\\\"composerMode.agent\\\",\\\"icon\\\":\\\"infinity\\\",\\\"description\\\":\\\"Plan, search, make edits, run commands\\\",\\\"thinkingLevel\\\":\\\"none\\\",\\\"shouldAutoApplyIfNoEditTool\\\":true,\\\"autoFix\\\":true,\\\"autoRun\\\":false,\\\"enabledTools\\\":[],\\\"enabledMcpServers\\\":[]},{\\\"id\\\":\\\"chat\\\",\\\"name\\\":\\\"Ask\\\",\\\"actionId\\\":\\\"composerMode.chat\\\",\\\"icon\\\":\\\"chat\\\",\\\"description\\\":\\\"Ask Cursor questions about your codebase\\\",\\\"thinkingLevel\\\":\\\"none\\\",\\\"shouldAutoApplyIfNoEditTool\\\":false,\\\"autoFix\\\":true,\\\"autoRun\\\":false,\\\"enabledTools\\\":[],\\\"enabledMcpServers\\\":[]},{\\\"id\\\":\\\"edit\\\",\\\"name\\\":\\\"Manual\\\",\\\"actionId\\\":\\\"composerMode.edit\\\",\\\"icon\\\":\\\"targetTwo\\\",\\\"description\\\":\\\"Manually decide what gets added to the context (no tools)\\\",\\\"thinkingLevel\\\":\\\"none\\\",\\\"shouldAutoApplyIfNoEditTool\\\":true,\\\"autoFix\\\":true,\\\"autoRun\\\":false,\\\"enabledTools\\\":[],\\\"enabledMcpServers\\\":[]}],\\\"codeBlockDisplayPreference\\\":\\\"expanded\\\",\\\"thinkingLevel\\\":\\\"none\\\",\\\"composerMigrationVersion\\\":101},\\\"mcpServers\\\":[],\\\"availableDefaultModels2\\\":[{\\\"name\\\":\\\"default\\\",\\\"defaultOn\\\":true,\\\"supportsAgent\\\":false,\\\"degradationStatus\\\":0,\\\"supportsThinking\\\":false,\\\"supportsImages\\\":false,\\\"supportsAutoContext\\\":true,\\\"autoContextMaxTokens\\\":30000},{\\\"name\\\":\\\"claude-3.5-sonnet\\\",\\\"defaultOn\\\":true,\\\"isLongContextOnly\\\":false,\\\"supportsAgent\\\":true,\\\"degradationStatus\\\":0,\\\"supportsThinking\\\":false,\\\"supportsImages\\\":true,\\\"supportsAutoContext\\\":true,\\\"autoContextMaxTokens\\\":30000},{\\\"name\\\":\\\"claude-3.7-sonnet\\\",\\\"defaultOn\\\":true,\\\"supportsAgent\\\":true,\\\"degradationStatus\\\":0,\\\"tooltipData\\\":{\\\"primaryText\\\":\\\"Faster, but less intelligent than MAX.\\\",\\\"secondaryText\\\":\\\"Turn on 'Auto' for a balanced experience, or use 'MAX' for best performance\\\",\\\"secondaryWarningText\\\":false,\\\"icon\\\":\\\"\\\"},\\\"supportsThinking\\\":false,\\\"supportsImages\\\":true,\\\"supportsAutoContext\\\":true,\\\"autoContextMaxTokens\\\":40000},{\\\"name\\\":\\\"claude-3.7-sonnet-thinking\\\",\\\"defaultOn\\\":true,\\\"supportsAgent\\\":true,\\\"degradationStatus\\\":0,\\\"tooltipData\\\":{\\\"primaryText\\\":\\\"Less intelligent than MAX.\\\",\\\"secondaryText\\\":\\\"2x fast requests\\\",\\\"secondaryWarningText\\\":true,\\\"icon\\\":\\\"\\\"},\\\"supportsThinking\\\":true,\\\"supportsImages\\\":true,\\\"supportsAutoContext\\\":true,\\\"autoContextMaxTokens\\\":40000},{\\\"name\\\":\\\"claude-3.7-sonnet-max\\\",\\\"defaultOn\\\":true,\\\"supportsAgent\\\":true,\\\"degradationStatus\\\":0,\\\"tooltipData\\\":{\\\"primaryText\\\":\\\"Maximum intelligence and context\\\",\\\"secondaryText\\\":\\\"$0.05 per request and per tool use\\\",\\\"secondaryWarningText\\\":true,\\\"icon\\\":\\\"\\\"},\\\"supportsThinking\\\":false,\\\"supportsImages\\\":true,\\\"supportsAutoContext\\\":true,\\\"autoContextMaxTokens\\\":40000},{\\\"name\\\":\\\"claude-3.7-sonnet-thinking-max\\\",\\\"defaultOn\\\":true,\\\"supportsAgent\\\":true,\\\"degradationStatus\\\":0,\\\"price\\\":0.05,\\\"tooltipData\\\":{\\\"primaryText\\\":\\\"Maximum intelligence, context, and thinking\\\",\\\"secondaryText\\\":\\\"$0.05 per request and per tool use\\\",\\\"secondaryWarningText\\\":true,\\\"icon\\\":\\\"\\\"},\\\"supportsThinking\\\":true,\\\"supportsImages\\\":true,\\\"supportsAutoContext\\\":true,\\\"autoContextMaxTokens\\\":40000},{\\\"name\\\":\\\"claude-3.5-haiku\\\",\\\"defaultOn\\\":false,\\\"isLongContextOnly\\\":false,\\\"supportsAgent\\\":true,\\\"degradationStatus\\\":0,\\\"supportsThinking\\\":false,\\\"supportsImages\\\":true,\\\"supportsAutoContext\\\":true,\\\"autoContextMaxTokens\\\":30000},{\\\"name\\\":\\\"cursor-small\\\",\\\"defaultOn\\\":false,\\\"supportsAgent\\\":false,\\\"degradationStatus\\\":0,\\\"supportsThinking\\\":false,\\\"supportsImages\\\":false,\\\"supportsAutoContext\\\":true,\\\"autoContextMaxTokens\\\":30000},{\\\"name\\\":\\\"gemini-2.5-pro-exp-03-25\\\",\\\"defaultOn\\\":true,\\\"supportsAgent\\\":true,\\\"degradationStatus\\\":0,\\\"supportsThinking\\\":true,\\\"supportsImages\\\":true,\\\"supportsAutoContext\\\":true,\\\"autoContextMaxTokens\\\":80000},{\\\"name\\\":\\\"gemini-2.5-pro-max\\\",\\\"defaultOn\\\":true,\\\"supportsAgent\\\":true,\\\"degradationStatus\\\":0,\\\"price\\\":0.05,\\\"tooltipData\\\":{\\\"primaryText\\\":\\\"Maximum intelligence and context\\\",\\\"secondaryText\\\":\\\"$0.05 per request and per tool use\\\",\\\"secondaryWarningText\\\":true,\\\"icon\\\":\\\"\\\"},\\\"supportsThinking\\\":true,\\\"supportsImages\\\":true,\\\"supportsAutoContext\\\":true,\\\"autoContextMaxTokens\\\":80000},{\\\"name\\\":\\\"gemini-2.5-flash-preview-04-17\\\",\\\"defaultOn\\\":false,\\\"supportsAgent\\\":true,\\\"degradationStatus\\\":0,\\\"supportsThinking\\\":true,\\\"supportsImages\\\":true,\\\"supportsAutoContext\\\":true,\\\"autoContextMaxTokens\\\":51200},{\\\"name\\\":\\\"deepseek-r1\\\",\\\"defaultOn\\\":false,\\\"supportsAgent\\\":false,\\\"degradationStatus\\\":0,\\\"supportsThinking\\\":true,\\\"supportsImages\\\":false,\\\"supportsAutoContext\\\":true,\\\"autoContextMaxTokens\\\":30000},{\\\"name\\\":\\\"deepseek-v3.1\\\",\\\"defaultOn\\\":false,\\\"supportsAgent\\\":true,\\\"degradationStatus\\\":0,\\\"supportsThinking\\\":false,\\\"supportsImages\\\":false,\\\"supportsAutoContext\\\":true,\\\"autoContextMaxTokens\\\":30000},{\\\"name\\\":\\\"grok-3-beta\\\",\\\"defaultOn\\\":false,\\\"supportsAgent\\\":true,\\\"degradationStatus\\\":0,\\\"supportsThinking\\\":false,\\\"supportsImages\\\":false,\\\"supportsAutoContext\\\":true,\\\"autoContextMaxTokens\\\":30000},{\\\"name\\\":\\\"grok-3-mini-beta\\\",\\\"defaultOn\\\":false,\\\"supportsAgent\\\":true,\\\"degradationStatus\\\":0,\\\"supportsThinking\\\":false,\\\"supportsImages\\\":false,\\\"supportsAutoContext\\\":true,\\\"autoContextMaxTokens\\\":30000},{\\\"name\\\":\\\"gpt-4.1\\\",\\\"defaultOn\\\":true,\\\"supportsAgent\\\":true,\\\"degradationStatus\\\":0,\\\"supportsThinking\\\":false,\\\"supportsImages\\\":true,\\\"supportsAutoContext\\\":true,\\\"autoContextMaxTokens\\\":51200},{\\\"name\\\":\\\"gpt-4o\\\",\\\"defaultOn\\\":false,\\\"supportsAgent\\\":true,\\\"degradationStatus\\\":0,\\\"supportsThinking\\\":false,\\\"supportsImages\\\":true,\\\"supportsAutoContext\\\":true,\\\"autoContextMaxTokens\\\":30000},{\\\"name\\\":\\\"o4-mini\\\",\\\"defaultOn\\\":true,\\\"supportsAgent\\\":true,\\\"degradationStatus\\\":0,\\\"supportsThinking\\\":true,\\\"supportsImages\\\":true,\\\"supportsAutoContext\\\":true,\\\"autoContextMaxTokens\\\":51200},{\\\"name\\\":\\\"o3\\\",\\\"defaultOn\\\":true,\\\"supportsAgent\\\":true,\\\"degradationStatus\\\":0,\\\"tooltipData\\\":{\\\"primaryText\\\":\\\"\\\",\\\"secondaryText\\\":\\\"$0.30 per request\\\",\\\"secondaryWarningText\\\":true,\\\"icon\\\":\\\"\\\"},\\\"supportsThinking\\\":true,\\\"supportsImages\\\":true,\\\"supportsAutoContext\\\":true,\\\"autoContextMaxTokens\\\":51200}],\\\"notepadState\\\":{\\\"isNotepadEnabled\\\":true},\\\"bugbotState\\\":{\\\"preferredModelName\\\":\\\"o1-preview\\\"},\\\"backgroundComposerState\\\":{},\\\"dialogDontAskAgainPreferences\\\":{},\\\"aiFeaturesCopyPasteState\\\":{\\\"mentions\\\":[]},\\\"cursorIgnore\\\":{\\\"hierarchicalEnabled\\\":false},\\\"teamAdminSettings\\\":{\\\"cursorIgnore\\\":{\\\"hierarchicalEnabled\\\":false}},\\\"shouldShowViewZoneWhenPreviewBoxIsClipped6\\\":false,\\\"syncDevModeColorTheme\\\":true,\\\"cppModelsState\\\":{\\\"cppModels\\\":[\\\"fast\\\"],\\\"defaultCppModel\\\":\\\"main\\\",\\\"defaultModel\\\":\\\"fusion-01-27\\\"},\\\"isLinterEnabled\\\":false,\\\"aiSettings\\\":{\\\"openAIModel\\\":\\\"claude-3.5-sonnet\\\",\\\"regularChatModel\\\":\\\"claude-3.5-sonnet\\\",\\\"cmdKModel\\\":\\\"claude-3.5-sonnet\\\",\\\"terminalCmdKModel\\\":\\\"claude-3.5-sonnet\\\",\\\"composerModel\\\":\\\"default\\\",\\\"backgroundComposerModel\\\":\\\"claude-3.7-sonnet-thinking-max\\\",\\\"privateFTOpenAIModel\\\":null,\\\"longContextOpenAIModel\\\":\\\"claude-3-5-sonnet-200k\\\",\\\"composerPreviousNonDefaultModel\\\":\\\"claude-3.5-sonnet\\\",\\\"modelsWithNoDefaultSwitch\\\":[\\\"claude-3.7-sonnet-thinking-max\\\",\\\"claude-3.7-sonnet-max\\\",\\\"gemini-2.5-pro-max\\\",\\\"gemini-2.5-pro-exp-03-25-max\\\",\\\"gemini-2.5-pro-exp-03-25\\\",\\\"gemini-2.0-flash-thinking-exp\\\",\\\"gemini-2.0-pro-exp\\\",\\\"gemini-2.0-flash\\\",\\\"gpt-4o\\\",\\\"gpt-4.5-preview\\\",\\\"gpt-4o-mini\\\",\\\"o3-mini\\\",\\\"o1\\\",\\\"o3\\\",\\\"o1-preview\\\",\\\"o1-mini\\\",\\\"claude-3.5-haiku\\\",\\\"deepseek-v3\\\",\\\"deepseek-v3.1\\\",\\\"deepseek-r1\\\"],\\\"modelDefaultSwitchOnNewChat\\\":false,\\\"teamIds\\\":[],\\\"modelOverrideEnabled\\\":[\\\"gpt-4o\\\",\\\"claude-3.5-sonnet\\\"],\\\"modelOverrideDisabled\\\":[\\\"gpt-4\\\"],\\\"lastDefaultModelNudge\\\":\\\"1745829462838\\\"},\\\"authenticationSettings\\\":{\\\"githubLoggedIn\\\":false},\\\"docState\\\":{\\\"visibleDocs\\\":[],\\\"usableDocs\\\":[],\\\"previosulyUsedDocs\\\":[]},\\\"lastUpdateHiddenTimeInUnixSeconds\\\":0,\\\"lintRules\\\":\\\"\\\",\\\"bubbleTimesLeft\\\":1,\\\"showAgentActionDebugger\\\":false,\\\"cmdLineHookState\\\":{\\\"ignored\\\":true,\\\"timesShown\\\":1,\\\"remindLaterDate\\\":\\\"1746325400275\\\"},\\\"showLinterDebugger\\\":false,\\\"linterDebuggerState\\\":{\\\"specificRules\\\":true,\\\"compileErrors\\\":false,\\\"changeBehavior\\\":true,\\\"matchCode\\\":false,\\\"relevance\\\":true,\\\"userAwareness\\\":true},\\\"cacheChatPrompts\\\":true,\\\"cmdkDiffHistoryEnabled\\\":false,\\\"shouldOnlyImportOnAccept\\\":true,\\\"cppAutoImportDecorationStyle\\\":\\\"squiggle\\\",\\\"lintSettings\\\":{\\\"forceEnableDiscriminators\\\":[],\\\"forceDisableDiscriminators\\\":[],\\\"forceEnableGenerators\\\":[],\\\"forceDisableGenerators\\\":[],\\\"watcherThreshold\\\":0.2,\\\"watcherDebounceTimeSeconds\\\":30,\\\"runOnSaveInstead\\\":true,\\\"silenceIfOverlappingWithRegularLinter\\\":true},\\\"lastUpgradeToProNotificationTime\\\":0,\\\"haveNotSeen\\\":{\\\"chat\\\":true,\\\"submit\\\":true,\\\"context\\\":true},\\\"newUserData\\\":{\\\"toolUsageCount\\\":{\\\"plainChat\\\":0,\\\"contextChat\\\":0,\\\"intentChat\\\":0}},\\\"azureState\\\":{\\\"useAzure\\\":false},\\\"interpreterModeSettings\\\":{\\\"interpreterModeByDefault\\\":false},\\\"cppFireOnEveryCursorChange\\\":false,\\\"personalDocs\\\":[],\\\"cppInCmdF\\\":true,\\\"cppManualTriggerWithOpToken\\\":false,\\\"cppTriggerInComments\\\":true,\\\"cppShowWhitespaceOnlyChanges\\\":false,\\\"fastCppEnabled\\\":false,\\\"indexRepository\\\":true,\\\"haveNotImportedFromVSC\\\":false,\\\"shouldAutoParseCmdKLinks\\\":false,\\\"aiPreviewsEnabled\\\":true,\\\"autoCreateNewChatAfterTimeout\\\":true,\\\"aiPreviewSettings\\\":{\\\"enabledFeatures\\\":{\\\"summary\\\":true,\\\"relatedFiles\\\":true,\\\"relatedCommits\\\":true},\\\"summary\\\":{\\\"isExpanded\\\":true},\\\"relatedFiles\\\":{\\\"isExpanded\\\":true},\\\"relatedCommits\\\":{\\\"isExpanded\\\":false}},\\\"chatFadeInAnimationEnabled\\\":false,\\\"isFileSyncClientEnabled\\\":true,\\\"useFastApplyModel\\\":false,\\\"fastApplyModelType\\\":1,\\\"explicitlyEnableSemanticSearch\\\":false,\\\"aiCursorHelpEnabled\\\":true,\\\"showAllCmdKContexts\\\":false,\\\"markdownTestString\\\":\\\"\\\",\\\"cppInPeek\\\":true,\\\"fastSemanticSearchEnabled\\\":false,\\\"preferredEmbeddingModel\\\":0,\\\"cursorPredictionUIExperiments\\\":[],\\\"oneTimeSettings\\\":{\\\"shouldDisableGithubCopilot\\\":false,\\\"shouldMigrateFromGpt4ToGpt4o\\\":false,\\\"shouldMigrateFromGpt4oToClaudeSonnet\\\":false,\\\"didMigrateFromGpt4oToClaudeSonnet\\\":true,\\\"didMigrateBackFromClaudeSonnetToGpt4o\\\":false},\\\"indexingState\\\":{\\\"lastAskedToIndexTime\\\":0},\\\"internalAnalyticsDebugMode\\\":false,\\\"fullContextOptions\\\":{\\\"compress\\\":true,\\\"hasDismissed\\\":false},\\\"eligibleForSnippetLearning\\\":false,\\\"goneThroughCodeSnippetOnboarding\\\":false,\\\"goneThroughCodeSnippetChangeManagement\\\":false,\\\"membershipType\\\":\\\"free_trial\\\",\\\"havePerformedSettingsServiceMigration\\\":true,\\\"hasBedrockIamRole\\\":false,\\\"teamBlockRepos\\\":[],\\\"teamAllowRepos\\\":[],\\\"teamBlocklist\\\":[],\\\"shouldNotTryToGetThemToNoticeCpp\\\":true,\\\"checklistState\\\":{\\\"shouldSeeOnboardingChecklist\\\":true,\\\"doneAutoComplete\\\":true},\\\"SPECIAL_KEY_id\\\":\\\"7f91ae3b-25c1-444d-b74f-8a3ecfe7df24\\\",\\\"lastComposerOpenTime\\\":1746341754675,\\\"cppHasLoadedConfigFromCopilot\\\":true,\\\"hasDisabledErrorLensForAiLinter\\\":true,\\\"cursorPredictionState\\\":{\\\"modelConfigs\\\":[{\\\"name\\\":\\\"legacy\\\",\\\"radius\\\":80},{\\\"name\\\":\\\"default\\\",\\\"radius\\\":50},{\\\"name\\\":\\\"v2\\\",\\\"radius\\\":1000},{\\\"name\\\":\\\"v3\\\",\\\"radius\\\":1000}],\\\"defaultModel\\\":\\\"default\\\",\\\"heuristics\\\":[1]},\\\"onboardingDataPrivacyVersion\\\":\\\"legacy\\\",\\\"useOpenAIKey\\\":false,\\\"cppConfig\\\":{\\\"heuristics\\\":[4,5,6,2],\\\"excludeRecentlyViewedFilesPatterns\\\":[\\\".env\\\",\\\".production\\\",\\\".pem\\\",\\\".cursor-retrieval.\\\",\\\".cursor-always-local.\\\",\\\".svg\\\",\\\".lock\\\",\\\".jsonl\\\",\\\".csv\\\",\\\".tsv\\\",\\\"Copilot++\\\"],\\\"enableRvfTracking\\\":true,\\\"globalDebounceDurationMillis\\\":70,\\\"clientDebounceDurationMillis\\\":50,\\\"cppUrl\\\":\\\"https://api3.cursor.sh\\\",\\\"useWhitespaceDiffHistory\\\":true,\\\"enableFilesyncDebounceSkipping\\\":false,\\\"checkFilesyncHashPercent\\\":0.004999999888241291,\\\"geoCppBackendUrl\\\":\\\"https://us-only.gcpp.cursor.sh\\\",\\\"isFusedCursorPredictionModel\\\":true,\\\"includeUnchangedLines\\\":false,\\\"shouldFetchRvfText\\\":false,\\\"aboveRadius\\\":1,\\\"belowRadius\\\":2,\\\"isOn\\\":true,\\\"isGhostText\\\":true,\\\"shouldLetUserEnableCppEvenIfNotPro\\\":true,\\\"importPredictionConfig\\\":{\\\"isDisabledByBackend\\\":false,\\\"shouldTurnOnAutomatically\\\":true,\\\"pythonEnabled\\\":false},\\\"recentlyRejectedEditThresholds\\\":{\\\"hardRejectThreshold\\\":2,\\\"softRejectThreshold\\\":5},\\\"maxNumberOfClearedSuggestionsSinceLastAccept\\\":20,\\\"suggestionHintConfig\\\":{\\\"importantLspExtensions\\\":[\\\"vscode.typescript-language-features\\\",\\\"ms-python.python\\\",\\\"rust-lang.rust-analyzer\\\",\\\"golang.go\\\"],\\\"enabledForPathExtensions\\\":[\\\".ts\\\",\\\".tsx\\\",\\\".py\\\",\\\".js\\\",\\\".go\\\",\\\".rs\\\"]}},\\\"noStorageMode\\\":true,\\\"selectedPrivacyForOnboarding\\\":true,\\\"backendHasDisabledCppAutoImport\\\":false,\\\"cppAutoImportEnabled\\\":true,\\\"cppShown\\\":54}\",\"workbench.panel.alignment\":\"center\",\"workbench.view.debug.state.hidden\":\"[{\\\"id\\\":\\\"workbench.debug.welcome\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.variablesView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.watchExpressionsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.callStackView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.loadedScriptsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.breakPointsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jsBrowserBreakpoints\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jsExcludedCallers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jsDebugNetworkTree\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"CppSshTargetsView\\\",\\\"isHidden\\\":false}]\",\"~remote.forwardedPortsContainer.hidden\":\"[{\\\"id\\\":\\\"~remote.forwardedPorts\\\",\\\"isHidden\\\":false}]\",\"colorThemeData\":\"{\\\"id\\\":\\\"vs vscode-theme-defaults-themes-light_modern-json\\\",\\\"label\\\":\\\"Light Modern\\\",\\\"settingsId\\\":\\\"Default Light Modern\\\",\\\"themeTokenColors\\\":[{\\\"settings\\\":{\\\"foreground\\\":\\\"#000000ff\\\"},\\\"scope\\\":[\\\"meta.embedded\\\",\\\"source.groovy.embedded\\\",\\\"string meta.image.inline.markdown\\\",\\\"variable.legacy.builtin.python\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":\\\"emphasis\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\"},\\\"scope\\\":\\\"strong\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#000080\\\"},\\\"scope\\\":\\\"meta.diff.header\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#008000\\\"},\\\"scope\\\":\\\"comment\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0000ff\\\"},\\\"scope\\\":\\\"constant.language\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#098658\\\"},\\\"scope\\\":[\\\"constant.numeric\\\",\\\"variable.other.enummember\\\",\\\"keyword.operator.plus.exponent\\\",\\\"keyword.operator.minus.exponent\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#811f3f\\\"},\\\"scope\\\":\\\"constant.regexp\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#800000\\\"},\\\"scope\\\":\\\"entity.name.tag\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#800000\\\"},\\\"scope\\\":\\\"entity.name.selector\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#e50000\\\"},\\\"scope\\\":\\\"entity.other.attribute-name\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#800000\\\"},\\\"scope\\\":[\\\"entity.other.attribute-name.class.css\\\",\\\"source.css entity.other.attribute-name.class\\\",\\\"entity.other.attribute-name.id.css\\\",\\\"entity.other.attribute-name.parent-selector.css\\\",\\\"entity.other.attribute-name.parent.less\\\",\\\"source.css entity.other.attribute-name.pseudo-class\\\",\\\"entity.other.attribute-name.pseudo-element.css\\\",\\\"source.css.less entity.other.attribute-name.id\\\",\\\"entity.other.attribute-name.scss\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#cd3131\\\"},\\\"scope\\\":\\\"invalid\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"underline\\\"},\\\"scope\\\":\\\"markup.underline\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#000080\\\"},\\\"scope\\\":\\\"markup.bold\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#800000\\\"},\\\"scope\\\":\\\"markup.heading\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":\\\"markup.italic\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"strikethrough\\\"},\\\"scope\\\":\\\"markup.strikethrough\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#098658\\\"},\\\"scope\\\":\\\"markup.inserted\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#a31515\\\"},\\\"scope\\\":\\\"markup.deleted\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0451a5\\\"},\\\"scope\\\":\\\"markup.changed\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0451a5\\\"},\\\"scope\\\":[\\\"punctuation.definition.quote.begin.markdown\\\",\\\"punctuation.definition.list.begin.markdown\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#800000\\\"},\\\"scope\\\":\\\"markup.inline.raw\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#800000\\\"},\\\"scope\\\":\\\"punctuation.definition.tag\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0000ff\\\"},\\\"scope\\\":[\\\"meta.preprocessor\\\",\\\"entity.name.function.preprocessor\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#a31515\\\"},\\\"scope\\\":\\\"meta.preprocessor.string\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#098658\\\"},\\\"scope\\\":\\\"meta.preprocessor.numeric\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0451a5\\\"},\\\"scope\\\":\\\"meta.structure.dictionary.key.python\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0000ff\\\"},\\\"scope\\\":\\\"storage\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0000ff\\\"},\\\"scope\\\":\\\"storage.type\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0000ff\\\"},\\\"scope\\\":[\\\"storage.modifier\\\",\\\"keyword.operator.noexcept\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#a31515\\\"},\\\"scope\\\":[\\\"string\\\",\\\"meta.embedded.assembly\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0000ff\\\"},\\\"scope\\\":[\\\"string.comment.buffered.block.pug\\\",\\\"string.quoted.pug\\\",\\\"string.interpolated.pug\\\",\\\"string.unquoted.plain.in.yaml\\\",\\\"string.unquoted.plain.out.yaml\\\",\\\"string.unquoted.block.yaml\\\",\\\"string.quoted.single.yaml\\\",\\\"string.quoted.double.xml\\\",\\\"string.quoted.single.xml\\\",\\\"string.unquoted.cdata.xml\\\",\\\"string.quoted.double.html\\\",\\\"string.quoted.single.html\\\",\\\"string.unquoted.html\\\",\\\"string.quoted.single.handlebars\\\",\\\"string.quoted.double.handlebars\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#811f3f\\\"},\\\"scope\\\":\\\"string.regexp\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0000ff\\\"},\\\"scope\\\":[\\\"punctuation.definition.template-expression.begin\\\",\\\"punctuation.definition.template-expression.end\\\",\\\"punctuation.section.embedded\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#000000\\\"},\\\"scope\\\":[\\\"meta.template.expression\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0451a5\\\"},\\\"scope\\\":[\\\"support.constant.property-value\\\",\\\"support.constant.font-name\\\",\\\"support.constant.media-type\\\",\\\"support.constant.media\\\",\\\"constant.other.color.rgb-value\\\",\\\"constant.other.rgb-value\\\",\\\"support.constant.color\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#e50000\\\"},\\\"scope\\\":[\\\"support.type.vendored.property-name\\\",\\\"support.type.property-name\\\",\\\"source.css variable\\\",\\\"source.coffee.embedded\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0451a5\\\"},\\\"scope\\\":[\\\"support.type.property-name.json\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0000ff\\\"},\\\"scope\\\":\\\"keyword\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0000ff\\\"},\\\"scope\\\":\\\"keyword.control\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#000000\\\"},\\\"scope\\\":\\\"keyword.operator\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0000ff\\\"},\\\"scope\\\":[\\\"keyword.operator.new\\\",\\\"keyword.operator.expression\\\",\\\"keyword.operator.cast\\\",\\\"keyword.operator.sizeof\\\",\\\"keyword.operator.alignof\\\",\\\"keyword.operator.typeid\\\",\\\"keyword.operator.alignas\\\",\\\"keyword.operator.instanceof\\\",\\\"keyword.operator.logical.python\\\",\\\"keyword.operator.wordlike\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#098658\\\"},\\\"scope\\\":\\\"keyword.other.unit\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#800000\\\"},\\\"scope\\\":[\\\"punctuation.section.embedded.begin.php\\\",\\\"punctuation.section.embedded.end.php\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0451a5\\\"},\\\"scope\\\":\\\"support.function.git-rebase\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#098658\\\"},\\\"scope\\\":\\\"constant.sha.git-rebase\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#000000\\\"},\\\"scope\\\":[\\\"storage.modifier.import.java\\\",\\\"variable.language.wildcard.java\\\",\\\"storage.modifier.package.java\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0000ff\\\"},\\\"scope\\\":\\\"variable.language\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#795E26\\\"},\\\"scope\\\":[\\\"entity.name.function\\\",\\\"support.function\\\",\\\"support.constant.handlebars\\\",\\\"source.powershell variable.other.member\\\",\\\"entity.name.operator.custom-literal\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#267f99\\\"},\\\"scope\\\":[\\\"support.class\\\",\\\"support.type\\\",\\\"entity.name.type\\\",\\\"entity.name.namespace\\\",\\\"entity.other.attribute\\\",\\\"entity.name.scope-resolution\\\",\\\"entity.name.class\\\",\\\"storage.type.numeric.go\\\",\\\"storage.type.byte.go\\\",\\\"storage.type.boolean.go\\\",\\\"storage.type.string.go\\\",\\\"storage.type.uintptr.go\\\",\\\"storage.type.error.go\\\",\\\"storage.type.rune.go\\\",\\\"storage.type.cs\\\",\\\"storage.type.generic.cs\\\",\\\"storage.type.modifier.cs\\\",\\\"storage.type.variable.cs\\\",\\\"storage.type.annotation.java\\\",\\\"storage.type.generic.java\\\",\\\"storage.type.java\\\",\\\"storage.type.object.array.java\\\",\\\"storage.type.primitive.array.java\\\",\\\"storage.type.primitive.java\\\",\\\"storage.type.token.java\\\",\\\"storage.type.groovy\\\",\\\"storage.type.annotation.groovy\\\",\\\"storage.type.parameters.groovy\\\",\\\"storage.type.generic.groovy\\\",\\\"storage.type.object.array.groovy\\\",\\\"storage.type.primitive.array.groovy\\\",\\\"storage.type.primitive.groovy\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#267f99\\\"},\\\"scope\\\":[\\\"meta.type.cast.expr\\\",\\\"meta.type.new.expr\\\",\\\"support.constant.math\\\",\\\"support.constant.dom\\\",\\\"support.constant.json\\\",\\\"entity.other.inherited-class\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#AF00DB\\\"},\\\"scope\\\":[\\\"keyword.control\\\",\\\"source.cpp keyword.operator.new\\\",\\\"source.cpp keyword.operator.delete\\\",\\\"keyword.other.using\\\",\\\"keyword.other.directive.using\\\",\\\"keyword.other.operator\\\",\\\"entity.name.operator\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#001080\\\"},\\\"scope\\\":[\\\"variable\\\",\\\"meta.definition.variable.name\\\",\\\"support.variable\\\",\\\"entity.name.variable\\\",\\\"constant.other.placeholder\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0070C1\\\"},\\\"scope\\\":[\\\"variable.other.constant\\\",\\\"variable.other.enummember\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#001080\\\"},\\\"scope\\\":[\\\"meta.object-literal.key\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0451a5\\\"},\\\"scope\\\":[\\\"support.constant.property-value\\\",\\\"support.constant.font-name\\\",\\\"support.constant.media-type\\\",\\\"support.constant.media\\\",\\\"constant.other.color.rgb-value\\\",\\\"constant.other.rgb-value\\\",\\\"support.constant.color\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d16969\\\"},\\\"scope\\\":[\\\"punctuation.definition.group.regexp\\\",\\\"punctuation.definition.group.assertion.regexp\\\",\\\"punctuation.definition.character-class.regexp\\\",\\\"punctuation.character.set.begin.regexp\\\",\\\"punctuation.character.set.end.regexp\\\",\\\"keyword.operator.negation.regexp\\\",\\\"support.other.parenthesis.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#811f3f\\\"},\\\"scope\\\":[\\\"constant.character.character-class.regexp\\\",\\\"constant.other.character-class.set.regexp\\\",\\\"constant.other.character-class.regexp\\\",\\\"constant.character.set.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#000000\\\"},\\\"scope\\\":\\\"keyword.operator.quantifier.regexp\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EE0000\\\"},\\\"scope\\\":[\\\"keyword.operator.or.regexp\\\",\\\"keyword.control.anchor.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0000ff\\\"},\\\"scope\\\":[\\\"constant.character\\\",\\\"constant.other.option\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EE0000\\\"},\\\"scope\\\":\\\"constant.character.escape\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#000000\\\"},\\\"scope\\\":\\\"entity.name.label\\\"}],\\\"semanticTokenRules\\\":[{\\\"_selector\\\":\\\"newOperator\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#0000ff\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"stringLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#a31515\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"customLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#000000\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"numberLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#098658\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"newOperator\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#af00db\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"stringLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#a31515\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"customLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#795e26\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"numberLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#098658\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}}],\\\"extensionData\\\":{\\\"_extensionId\\\":\\\"vscode.theme-defaults\\\",\\\"_extensionIsBuiltin\\\":true,\\\"_extensionName\\\":\\\"theme-defaults\\\",\\\"_extensionPublisher\\\":\\\"vscode\\\"},\\\"themeSemanticHighlighting\\\":true,\\\"colorMap\\\":{\\\"checkbox.border\\\":\\\"#cecece\\\",\\\"editor.background\\\":\\\"#ffffff\\\",\\\"editor.foreground\\\":\\\"#3b3b3b\\\",\\\"editor.inactiveSelectionBackground\\\":\\\"#e5ebf1\\\",\\\"editorIndentGuide.background1\\\":\\\"#d3d3d3\\\",\\\"editorIndentGuide.activeBackground1\\\":\\\"#939393\\\",\\\"editor.selectionHighlightBackground\\\":\\\"#add6ff80\\\",\\\"editorSuggestWidget.background\\\":\\\"#f8f8f8\\\",\\\"activityBarBadge.background\\\":\\\"#005fb8\\\",\\\"sideBarTitle.foreground\\\":\\\"#3b3b3b\\\",\\\"list.hoverBackground\\\":\\\"#f2f2f2\\\",\\\"menu.border\\\":\\\"#cecece\\\",\\\"input.placeholderForeground\\\":\\\"#767676\\\",\\\"searchEditor.textInputBorder\\\":\\\"#cecece\\\",\\\"settings.textInputBorder\\\":\\\"#cecece\\\",\\\"settings.numberInputBorder\\\":\\\"#cecece\\\",\\\"statusBarItem.remoteForeground\\\":\\\"#ffffff\\\",\\\"statusBarItem.remoteBackground\\\":\\\"#005fb8\\\",\\\"ports.iconRunningProcessForeground\\\":\\\"#369432\\\",\\\"sideBarSectionHeader.background\\\":\\\"#f8f8f8\\\",\\\"sideBarSectionHeader.border\\\":\\\"#e5e5e5\\\",\\\"tab.selectedForeground\\\":\\\"#333333b3\\\",\\\"tab.selectedBackground\\\":\\\"#ffffffa5\\\",\\\"tab.lastPinnedBorder\\\":\\\"#d4d4d4\\\",\\\"notebook.cellBorderColor\\\":\\\"#e5e5e5\\\",\\\"notebook.selectedCellBackground\\\":\\\"#c8ddf150\\\",\\\"statusBarItem.errorBackground\\\":\\\"#c72e0f\\\",\\\"list.activeSelectionIconForeground\\\":\\\"#000000\\\",\\\"list.focusAndSelectionOutline\\\":\\\"#005fb8\\\",\\\"terminal.inactiveSelectionBackground\\\":\\\"#e5ebf1\\\",\\\"widget.border\\\":\\\"#e5e5e5\\\",\\\"actionBar.toggledBackground\\\":\\\"#dddddd\\\",\\\"diffEditor.unchangedRegionBackground\\\":\\\"#f8f8f8\\\",\\\"activityBar.activeBorder\\\":\\\"#005fb8\\\",\\\"activityBar.background\\\":\\\"#f8f8f8\\\",\\\"activityBar.border\\\":\\\"#e5e5e5\\\",\\\"activityBar.foreground\\\":\\\"#1f1f1f\\\",\\\"activityBar.inactiveForeground\\\":\\\"#616161\\\",\\\"activityBarBadge.foreground\\\":\\\"#ffffff\\\",\\\"badge.background\\\":\\\"#cccccc\\\",\\\"badge.foreground\\\":\\\"#3b3b3b\\\",\\\"button.background\\\":\\\"#005fb8\\\",\\\"button.border\\\":\\\"#0000001a\\\",\\\"button.foreground\\\":\\\"#ffffff\\\",\\\"button.hoverBackground\\\":\\\"#0258a8\\\",\\\"button.secondaryBackground\\\":\\\"#e5e5e5\\\",\\\"button.secondaryForeground\\\":\\\"#3b3b3b\\\",\\\"button.secondaryHoverBackground\\\":\\\"#cccccc\\\",\\\"chat.slashCommandBackground\\\":\\\"#d2ecff\\\",\\\"chat.slashCommandForeground\\\":\\\"#306ca2\\\",\\\"chat.editedFileForeground\\\":\\\"#895503\\\",\\\"checkbox.background\\\":\\\"#f8f8f8\\\",\\\"descriptionForeground\\\":\\\"#3b3b3b\\\",\\\"dropdown.background\\\":\\\"#ffffff\\\",\\\"dropdown.border\\\":\\\"#cecece\\\",\\\"dropdown.foreground\\\":\\\"#3b3b3b\\\",\\\"dropdown.listBackground\\\":\\\"#ffffff\\\",\\\"editorGroup.border\\\":\\\"#e5e5e5\\\",\\\"editorGroupHeader.tabsBackground\\\":\\\"#f8f8f8\\\",\\\"editorGroupHeader.tabsBorder\\\":\\\"#e5e5e5\\\",\\\"editorGutter.addedBackground\\\":\\\"#2ea043\\\",\\\"editorGutter.deletedBackground\\\":\\\"#f85149\\\",\\\"editorGutter.modifiedBackground\\\":\\\"#005fb8\\\",\\\"editorLineNumber.activeForeground\\\":\\\"#171184\\\",\\\"editorLineNumber.foreground\\\":\\\"#6e7681\\\",\\\"editorOverviewRuler.border\\\":\\\"#e5e5e5\\\",\\\"editorWidget.background\\\":\\\"#f8f8f8\\\",\\\"errorForeground\\\":\\\"#f85149\\\",\\\"focusBorder\\\":\\\"#005fb8\\\",\\\"foreground\\\":\\\"#3b3b3b\\\",\\\"icon.foreground\\\":\\\"#3b3b3b\\\",\\\"input.background\\\":\\\"#ffffff\\\",\\\"input.border\\\":\\\"#cecece\\\",\\\"input.foreground\\\":\\\"#3b3b3b\\\",\\\"inputOption.activeBackground\\\":\\\"#bed6ed\\\",\\\"inputOption.activeBorder\\\":\\\"#005fb8\\\",\\\"inputOption.activeForeground\\\":\\\"#000000\\\",\\\"keybindingLabel.foreground\\\":\\\"#3b3b3b\\\",\\\"list.activeSelectionBackground\\\":\\\"#e8e8e8\\\",\\\"list.activeSelectionForeground\\\":\\\"#000000\\\",\\\"menu.selectionBackground\\\":\\\"#005fb8\\\",\\\"menu.selectionForeground\\\":\\\"#ffffff\\\",\\\"notificationCenterHeader.background\\\":\\\"#ffffff\\\",\\\"notificationCenterHeader.foreground\\\":\\\"#3b3b3b\\\",\\\"notifications.background\\\":\\\"#ffffff\\\",\\\"notifications.border\\\":\\\"#e5e5e5\\\",\\\"notifications.foreground\\\":\\\"#3b3b3b\\\",\\\"panel.background\\\":\\\"#f8f8f8\\\",\\\"panel.border\\\":\\\"#e5e5e5\\\",\\\"panelInput.border\\\":\\\"#e5e5e5\\\",\\\"panelTitle.activeBorder\\\":\\\"#005fb8\\\",\\\"panelTitle.activeForeground\\\":\\\"#3b3b3b\\\",\\\"panelTitle.inactiveForeground\\\":\\\"#3b3b3b\\\",\\\"peekViewEditor.matchHighlightBackground\\\":\\\"#bb800966\\\",\\\"peekViewResult.background\\\":\\\"#ffffff\\\",\\\"peekViewResult.matchHighlightBackground\\\":\\\"#bb800966\\\",\\\"pickerGroup.border\\\":\\\"#e5e5e5\\\",\\\"pickerGroup.foreground\\\":\\\"#8b949e\\\",\\\"progressBar.background\\\":\\\"#005fb8\\\",\\\"quickInput.background\\\":\\\"#f8f8f8\\\",\\\"quickInput.foreground\\\":\\\"#3b3b3b\\\",\\\"settings.dropdownBackground\\\":\\\"#ffffff\\\",\\\"settings.dropdownBorder\\\":\\\"#cecece\\\",\\\"settings.headerForeground\\\":\\\"#1f1f1f\\\",\\\"settings.modifiedItemIndicator\\\":\\\"#bb800966\\\",\\\"sideBar.background\\\":\\\"#f8f8f8\\\",\\\"sideBar.border\\\":\\\"#e5e5e5\\\",\\\"sideBar.foreground\\\":\\\"#3b3b3b\\\",\\\"sideBarSectionHeader.foreground\\\":\\\"#3b3b3b\\\",\\\"statusBar.background\\\":\\\"#f8f8f8\\\",\\\"statusBar.foreground\\\":\\\"#3b3b3b\\\",\\\"statusBar.border\\\":\\\"#e5e5e5\\\",\\\"statusBar.debuggingBackground\\\":\\\"#fd716c\\\",\\\"statusBar.debuggingForeground\\\":\\\"#000000\\\",\\\"statusBar.focusBorder\\\":\\\"#005fb8\\\",\\\"statusBar.noFolderBackground\\\":\\\"#f8f8f8\\\",\\\"statusBarItem.focusBorder\\\":\\\"#005fb8\\\",\\\"statusBarItem.prominentBackground\\\":\\\"#6e768166\\\",\\\"tab.activeBackground\\\":\\\"#ffffff\\\",\\\"tab.activeBorder\\\":\\\"#f8f8f8\\\",\\\"tab.activeBorderTop\\\":\\\"#005fb8\\\",\\\"tab.activeForeground\\\":\\\"#3b3b3b\\\",\\\"tab.selectedBorderTop\\\":\\\"#68a3da\\\",\\\"tab.border\\\":\\\"#e5e5e5\\\",\\\"tab.hoverBackground\\\":\\\"#ffffff\\\",\\\"tab.inactiveBackground\\\":\\\"#f8f8f8\\\",\\\"tab.inactiveForeground\\\":\\\"#868686\\\",\\\"tab.unfocusedActiveBorder\\\":\\\"#f8f8f8\\\",\\\"tab.unfocusedActiveBorderTop\\\":\\\"#e5e5e5\\\",\\\"tab.unfocusedHoverBackground\\\":\\\"#f8f8f8\\\",\\\"terminalCursor.foreground\\\":\\\"#005fb8\\\",\\\"terminal.foreground\\\":\\\"#3b3b3b\\\",\\\"terminal.tab.activeBorder\\\":\\\"#005fb8\\\",\\\"textBlockQuote.background\\\":\\\"#f8f8f8\\\",\\\"textBlockQuote.border\\\":\\\"#e5e5e5\\\",\\\"textCodeBlock.background\\\":\\\"#f8f8f8\\\",\\\"textLink.activeForeground\\\":\\\"#005fb8\\\",\\\"textLink.foreground\\\":\\\"#005fb8\\\",\\\"textPreformat.foreground\\\":\\\"#3b3b3b\\\",\\\"textPreformat.background\\\":\\\"#0000001f\\\",\\\"textSeparator.foreground\\\":\\\"#21262d\\\",\\\"titleBar.activeBackground\\\":\\\"#f8f8f8\\\",\\\"titleBar.activeForeground\\\":\\\"#1e1e1e\\\",\\\"titleBar.border\\\":\\\"#e5e5e5\\\",\\\"titleBar.inactiveBackground\\\":\\\"#f8f8f8\\\",\\\"titleBar.inactiveForeground\\\":\\\"#8b949e\\\",\\\"welcomePage.tileBackground\\\":\\\"#f3f3f3\\\"},\\\"watch\\\":false}\",\"workbench.panel.repl.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.repl.view\\\",\\\"isHidden\\\":false}]\",\"themeUpdatedNotificationShown\":\"true\",\"cursorai/donotchange/privacyMode\":\"true\",\"commandPalette.mru.cache\":\"{\\\"usesLRU\\\":true,\\\"entries\\\":[{\\\"key\\\":\\\"workbench.action.openGlobalSettingsCursorAlias\\\",\\\"value\\\":1},{\\\"key\\\":\\\"workbench.action.openGlobalKeybindings\\\",\\\"value\\\":3},{\\\"key\\\":\\\"workbench.action.reloadWindow\\\",\\\"value\\\":4}]}\",\"commandPalette.mru.counter\":\"5\",\"workbench.activityBar.location\":\"default\",\"workbench.activity.pinnedViewlets2\":\"[{\\\"id\\\":\\\"workbench.view.explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":0},{\\\"id\\\":\\\"workbench.view.search\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":1},{\\\"id\\\":\\\"workbench.view.scm\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":2},{\\\"id\\\":\\\"workbench.view.bugbot\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":3},{\\\"id\\\":\\\"workbench.view.debug\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":3},{\\\"id\\\":\\\"workbench.view.remote\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":4},{\\\"id\\\":\\\"workbench.view.extensions\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":4},{\\\"id\\\":\\\"workbench.view.extension.test\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":6},{\\\"id\\\":\\\"workbench.view.extension.references-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":7},{\\\"id\\\":\\\"workbench.view.extension.jupyter\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9}]\",\"workbench.view.extensions.state.hidden\":\"[{\\\"id\\\":\\\"workbench.views.extensions.installed\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchOutdated\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.workspaceRecommendations\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.popular\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchRecentlyUpdated\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.otherRecommendations\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"extensions.recommendedList\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.enabled\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.extensions.disabled\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.extensions.marketplace\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchInstalled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchEnabled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchDisabled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchBuiltin\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchWorkspaceUnsupported\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinFeatureExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinThemeExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinProgrammingLanguageExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.untrustedUnsupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.untrustedPartiallySupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.virtualUnsupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.virtualPartiallySupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.deprecatedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.local.installed\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.remote.installed\\\",\\\"isHidden\\\":false}]\",\"workbench.view.remote.state.hidden\":\"[{\\\"id\\\":\\\"remoteTargets\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"~remote.helpPanel\\\",\\\"isHidden\\\":false}]\",\"workbench.welcomePage.walkthroughMetadata\":\"[[\\\"ms-python.python#pythonWelcome\\\",{\\\"firstSeen\\\":1746324990370,\\\"stepIDs\\\":[\\\"python.createPythonFolder\\\",\\\"python.createPythonFile\\\",\\\"python.installPythonWin8\\\",\\\"python.installPythonMac\\\",\\\"python.installPythonLinux\\\",\\\"python.createEnvironment\\\",\\\"python.runAndDebug\\\",\\\"python.learnMoreWithDS\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-python.python#pythonDataScienceWelcome\\\",{\\\"firstSeen\\\":1746324990370,\\\"stepIDs\\\":[\\\"python.installJupyterExt\\\",\\\"python.createNewNotebook\\\",\\\"python.openInteractiveWindow\\\",\\\"python.dataScienceLearnMore\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-vscode.cpptools#cppWelcome\\\",{\\\"firstSeen\\\":1746326462890,\\\"stepIDs\\\":[\\\"awaiting.activation.mac\\\",\\\"awaiting.activation.linux\\\",\\\"awaiting.activation.windows\\\",\\\"awaiting.activation.windows10\\\",\\\"awaiting.activation.windows11\\\",\\\"no.compilers.found.mac\\\",\\\"no.compilers.found.linux\\\",\\\"no.compilers.found.windows\\\",\\\"no.compilers.found.windows10\\\",\\\"no.compilers.found.windows11\\\",\\\"verify.compiler.mac\\\",\\\"verify.compiler.linux\\\",\\\"verify.compiler.windows\\\",\\\"verify.compiler.windows10\\\",\\\"verify.compiler.windows11\\\",\\\"create.cpp.file\\\",\\\"relaunch.developer.command.prompt.windows\\\",\\\"run.project.mac\\\",\\\"run.project.linux\\\",\\\"run.project.windows\\\",\\\"customize.debugging.linux\\\",\\\"customize.debugging.windows\\\",\\\"customize.debugging.mac\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-toolsai.jupyter#jupyterWelcome\\\",{\\\"firstSeen\\\":1746337731278,\\\"stepIDs\\\":[\\\"ipynb.newUntitledIpynb\\\",\\\"jupyter.selectKernel\\\",\\\"jupyter.exploreAndDebug\\\",\\\"jupyter.dataScienceLearnMore\\\"],\\\"manaullyOpened\\\":false}]]\",\"workbench.view.extension.augment-chat.state.hidden\":\"[{\\\"id\\\":\\\"augment-chat\\\",\\\"isHidden\\\":false}]\",\"views.customizations\":\"{\\\"viewContainerLocations\\\":{\\\"workbench.view.extension.augment-chat\\\":2},\\\"viewLocations\\\":{},\\\"viewContainerBadgeEnablementStates\\\":{}}\",\"memento/notebookGettingStarted2\":\"{\\\"hasOpenedNotebook\\\":true}\",\"fileBasedRecommendations/promptedRecommendations\":\"{\\\"python\\\":[\\\"ms-python.python\\\"]}\",\"workbench.view.extension.test.state.hidden\":\"[{\\\"id\\\":\\\"workbench.view.testing\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.view.testCoverage\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.jupyter-variables.state.hidden\":\"[{\\\"id\\\":\\\"jupyterViewVariables\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cell-tag\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.augment-panel.state.hidden\":\"[{\\\"id\\\":\\\"augment-next-edit\\\",\\\"isHidden\\\":false}]\"}}"}