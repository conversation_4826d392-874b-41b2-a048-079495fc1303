#!/usr/bin/env python3
# coding: utf-8
__author__ = 'lishuanglin'
__version__ = '1.0.0'

import os
import time
import json
import paramiko
import threading
import subprocess
import pandas as pd
import tkinter as tk
from datetime import datetime

from tkinter import ttk
from tkinter import scrolledtext
from tkinter import messagebox
from tkinter import filedialog

class GuiTask:
    def __init__(self, root):
        self.root = root
        self.root.title("GUI Task Manager")
        self.root.geometry("1100x500")  # Default size
        self.root.minsize(800, 400)  # Minimum size

        # Set default font for the entire application
        self.default_font = ("Consolas", 11)
        self.root.option_add('*Font', self.default_font)

        # SSH connection config for Slurm
        self.hostname = "************"
        self.username = "lishuanglin"  # Hardcoded username
        self.password = "Meridian@YR"  # Hardcoded password
        self.ssh = None

        # Create a style object for ttk widgets
        self.style = ttk.Style()
        self.style.configure("Treeview", font=self.default_font)
        self.style.configure("Treeview.Heading", font=self.default_font)
        self.style.configure("TButton", font=self.default_font)
        self.style.configure("TLabel", font=self.default_font)

        # Configure tag styles for Treeview
        self.style.map('Treeview',
            background=[('selected', '#0078D7')],
            foreground=[('selected', 'white')]
        )

        # Store script information for Script Manager
        self.scripts = []
        self.load_scripts()

        # Initialize auto-refresh interval
        self.auto_refresh_interval = 20  # seconds

        # Create the main layout
        self.create_layout()

        # Connect to server for Slurm functionality
        self.connect_to_server()

        # Display existing scripts and start auto-refresh
        self.refresh_status()
        self.start_auto_refresh()

    def create_layout(self):
        # Create a frame for the sidebar
        sidebar_frame = ttk.Frame(self.root, width=150)
        sidebar_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)

        # Create a frame for the content
        content_frame = ttk.Frame(self.root)
        content_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create frames for each "tab" content
        self.scripts_tab = ttk.Frame(content_frame)
        self.tasks_tab = ttk.Frame(content_frame)
        self.jobs_tab = ttk.Frame(content_frame)
        self.nodes_tab = ttk.Frame(content_frame)
        self.history_tab = ttk.Frame(content_frame)
        self.operation_tab = ttk.Frame(content_frame)

        # Initially show the scripts tab
        self.current_tab = "scripts"
        self.scripts_tab.pack(fill=tk.BOTH, expand=True)

        # Create styles for active and inactive sidebar buttons
        self.style.configure("Sidebar.TButton", font=self.default_font, padding=(5, 10))
        self.style.configure("SidebarActive.TButton", font=(self.default_font[0], self.default_font[1], 'bold'),
                            background="#4a6984", foreground="white", padding=(5, 10))

        # Create sidebar buttons
        self.scripts_btn = ttk.Button(sidebar_frame, text="Scripts", style="SidebarActive.TButton",
                                    command=lambda: self.show_tab("scripts"))
        self.scripts_btn.pack(fill=tk.X, pady=2)

        self.tasks_btn = ttk.Button(sidebar_frame, text="SLMTasks", style="Sidebar.TButton",
                                   command=lambda: self.show_tab("tasks"))
        self.tasks_btn.pack(fill=tk.X, pady=2)

        self.jobs_btn = ttk.Button(sidebar_frame, text="SLMJobs", style="Sidebar.TButton",
                                  command=lambda: self.show_tab("jobs"))
        self.jobs_btn.pack(fill=tk.X, pady=2)

        self.nodes_btn = ttk.Button(sidebar_frame, text="SLMNodes", style="Sidebar.TButton",
                                   command=lambda: self.show_tab("nodes"))
        self.nodes_btn.pack(fill=tk.X, pady=2)

        self.history_btn = ttk.Button(sidebar_frame, text="SLMHistory", style="Sidebar.TButton",
                                     command=lambda: self.show_tab("history"))
        self.history_btn.pack(fill=tk.X, pady=2)

        self.operation_btn = ttk.Button(sidebar_frame, text="SLMOperation", style="Sidebar.TButton",
                                       command=lambda: self.show_tab("operation"))
        self.operation_btn.pack(fill=tk.X, pady=2)

        # Setup each tab
        self.setup_scripts_tab()
        self.setup_tasks_tab()
        self.setup_jobs_tab()
        self.setup_nodes_tab()
        self.setup_history_tab()
        self.setup_operation_tab()

    def show_tab(self, tab_name):
        # Hide current tab
        if self.current_tab == "scripts":
            self.scripts_tab.pack_forget()
            self.scripts_btn.configure(style="Sidebar.TButton")
        elif self.current_tab == "tasks":
            self.tasks_tab.pack_forget()
            self.tasks_btn.configure(style="Sidebar.TButton")
        elif self.current_tab == "jobs":
            self.jobs_tab.pack_forget()
            self.jobs_btn.configure(style="Sidebar.TButton")
        elif self.current_tab == "nodes":
            self.nodes_tab.pack_forget()
            self.nodes_btn.configure(style="Sidebar.TButton")
        elif self.current_tab == "history":
            self.history_tab.pack_forget()
            self.history_btn.configure(style="Sidebar.TButton")
        elif self.current_tab == "operation":
            self.operation_tab.pack_forget()
            self.operation_btn.configure(style="Sidebar.TButton")

        # Show selected tab
        self.current_tab = tab_name
        if tab_name == "scripts":
            self.scripts_tab.pack(fill=tk.BOTH, expand=True)
            self.scripts_btn.configure(style="SidebarActive.TButton")
            self.refresh_status()  # Refresh script status when tab is shown
        elif tab_name == "tasks":
            self.tasks_tab.pack(fill=tk.BOTH, expand=True)
            self.tasks_btn.configure(style="SidebarActive.TButton")
            self.refresh_tasks()  # Refresh tasks when tab is shown
        elif tab_name == "jobs":
            self.jobs_tab.pack(fill=tk.BOTH, expand=True)
            self.jobs_btn.configure(style="SidebarActive.TButton")
            self.refresh_jobs()  # Refresh jobs when tab is shown
        elif tab_name == "nodes":
            self.nodes_tab.pack(fill=tk.BOTH, expand=True)
            self.nodes_btn.configure(style="SidebarActive.TButton")
            self.refresh_nodes()  # Refresh nodes when tab is shown
        elif tab_name == "history":
            self.history_tab.pack(fill=tk.BOTH, expand=True)
            self.history_btn.configure(style="SidebarActive.TButton")
            self.read_history_job()  # Refresh history when tab is shown
        elif tab_name == "operation":
            self.operation_tab.pack(fill=tk.BOTH, expand=True)
            self.operation_btn.configure(style="SidebarActive.TButton")

    # Script Manager Tab Setup and Functions
    def setup_scripts_tab(self):
        # Create toolbar
        toolbar = ttk.Frame(self.scripts_tab)
        toolbar.pack(fill=tk.X, padx=5, pady=5)

        # Add script button
        add_btn = ttk.Button(toolbar, text="Add Script", command=self.add_script)
        add_btn.pack(side=tk.LEFT, padx=5)

        # Refresh button
        refresh_btn = ttk.Button(toolbar, text="Refresh", command=self.refresh_status)
        refresh_btn.pack(side=tk.LEFT, padx=5)

        # Auto-refresh interval setting
        ttk.Label(toolbar, text="Auto-refresh interval (seconds):").pack(side=tk.LEFT, padx=5)
        interval_var = tk.StringVar(value=str(self.auto_refresh_interval))
        interval_entry = ttk.Entry(toolbar, textvariable=interval_var, width=5)
        interval_entry.pack(side=tk.LEFT, padx=5)

        def update_interval():
            try:
                new_interval = int(interval_var.get())
                if new_interval > 0:
                    self.auto_refresh_interval = new_interval
                else:
                    messagebox.showerror("Error", "Interval must be greater than 0")
            except ValueError:
                messagebox.showerror("Error", "Please enter a valid number")

        ttk.Button(toolbar, text="Update", command=update_interval).pack(side=tk.LEFT, padx=5)

        # Create script list
        self.script_tree = ttk.Treeview(self.scripts_tab, columns=("id", "type", "status", "name", "path", "log_file", "progress"), show="headings")
        self.script_tree.heading("id", text="ID")
        self.script_tree.heading("type", text="Session")
        self.script_tree.heading("status", text="Status")
        self.script_tree.heading("name", text="Script Name")
        self.script_tree.heading("path", text="Script Path")
        self.script_tree.heading("log_file", text="Log File")
        self.script_tree.heading("progress", text="Progress")

        # Set column widths and alignments
        self.script_tree.column("id", width=30, anchor="center")
        self.script_tree.column("type", width=80, anchor="center")
        self.script_tree.column("status", width=80, anchor="center")
        self.script_tree.column("name", width=200, anchor="w")
        self.script_tree.column("path", width=200, anchor="w")
        self.script_tree.column("log_file", width=200, anchor="w")
        self.script_tree.column("progress", width=100, anchor="center")

        # Define status tags
        self.script_tree.tag_configure('running', background='#90EE90')  # Light green background
        self.script_tree.tag_configure('not_running', background='#D3D3D3')  # Light gray background

        self.script_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Right-click menu
        self.script_context_menu = tk.Menu(self.root, tearoff=0, font=self.default_font)
        self.script_context_menu.add_command(label="Run", command=self.run_script)
        self.script_context_menu.add_command(label="Stop", command=self.stop_script)
        self.script_context_menu.add_command(label="View Log", command=self.view_log)
        self.script_context_menu.add_command(label="Edit", command=self.edit_script)
        self.script_context_menu.add_command(label="Delete", command=self.delete_script)

        self.script_tree.bind("<Button-3>", self.show_script_context_menu)

    # Slurm GUI Tabs Setup
    def setup_tasks_tab(self):
        # Create toolbar
        toolbar = ttk.Frame(self.tasks_tab)
        toolbar.pack(fill=tk.X, padx=5, pady=5)

        # Refresh button
        refresh_btn = ttk.Button(toolbar, text="Refresh", command=self.refresh_tasks)
        refresh_btn.pack(side=tk.LEFT, padx=5)

        # Create timestamp label
        self.timestamp_label = ttk.Label(toolbar, text="Last updated: ")
        self.timestamp_label.pack(side=tk.RIGHT, padx=5)

        # Create tasks treeview
        self.tasks_tree = ttk.Treeview(self.tasks_tab, columns=(
            "jobname", "job", "total", "completed", "failed", "running", "progress", "eta"
        ), show="headings")

        # Configure headings
        self.tasks_tree.heading("jobname", text="Job Name")
        self.tasks_tree.heading("job", text="Job ID")
        self.tasks_tree.heading("total", text="Total")
        self.tasks_tree.heading("completed", text="Completed")
        self.tasks_tree.heading("failed", text="Failed")
        self.tasks_tree.heading("running", text="Running")
        self.tasks_tree.heading("progress", text="Progress")
        self.tasks_tree.heading("eta", text="ETA")

        # Configure columns
        self.tasks_tree.column("jobname", width=150, anchor="w")
        self.tasks_tree.column("job", width=100, anchor="center")
        self.tasks_tree.column("total", width=60, anchor="center")
        self.tasks_tree.column("completed", width=80, anchor="center")
        self.tasks_tree.column("failed", width=60, anchor="center")
        self.tasks_tree.column("running", width=60, anchor="center")
        self.tasks_tree.column("progress", width=80, anchor="center")
        self.tasks_tree.column("eta", width=100, anchor="center")

        # Configure tags for different states
        self.tasks_tree.tag_configure('completed', background='#FFFFFF')  # Light green
        self.tasks_tree.tag_configure('running', background='#90EE90')    # Light green
        self.tasks_tree.tag_configure('even', background='#F0F0F0')       # Light gray
        self.tasks_tree.tag_configure('odd', background='#FFFFFF')        # White

        self.tasks_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def setup_jobs_tab(self):
        # Create toolbar
        toolbar = ttk.Frame(self.jobs_tab)
        toolbar.pack(fill=tk.X, padx=5, pady=5)

        # User filter
        ttk.Label(toolbar, text="User:").pack(side=tk.LEFT, padx=5)
        self.user_entry = ttk.Entry(toolbar, width=10)
        self.user_entry.insert(0, self.username)  # Default to current user
        self.user_entry.pack(side=tk.LEFT, padx=5)

        # Node filter
        ttk.Label(toolbar, text="Node:").pack(side=tk.LEFT, padx=5)
        self.node_entry = ttk.Entry(toolbar, width=10)
        self.node_entry.pack(side=tk.LEFT, padx=5)

        # Partition filter
        ttk.Label(toolbar, text="Partition:").pack(side=tk.LEFT, padx=5)
        self.part_entry = ttk.Entry(toolbar, width=10)
        self.part_entry.pack(side=tk.LEFT, padx=5)

        # Refresh button
        refresh_btn = ttk.Button(toolbar, text="Refresh", command=self.refresh_jobs)
        refresh_btn.pack(side=tk.LEFT, padx=5)

        # Create jobs text area
        self.jobs_text = scrolledtext.ScrolledText(self.jobs_tab, wrap=tk.WORD, font=("Consolas", 11))
        self.jobs_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def setup_nodes_tab(self):
        # Create toolbar
        toolbar = ttk.Frame(self.nodes_tab)
        toolbar.pack(fill=tk.X, padx=5, pady=5)

        # Partition filter
        ttk.Label(toolbar, text="Partition:").pack(side=tk.LEFT, padx=5)
        self.part_entry2 = ttk.Entry(toolbar, width=10)
        self.part_entry2.pack(side=tk.LEFT, padx=5)

        # Detail checkbox
        self.detail_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(toolbar, text="Show Details", variable=self.detail_var).pack(side=tk.LEFT, padx=5)

        # Refresh button
        refresh_btn = ttk.Button(toolbar, text="Refresh", command=self.refresh_nodes)
        refresh_btn.pack(side=tk.LEFT, padx=5)

        # Create nodes text area
        self.nodes_text = scrolledtext.ScrolledText(self.nodes_tab, wrap=tk.WORD, font=("Consolas", 11))
        self.nodes_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def setup_history_tab(self):
        # Create toolbar
        toolbar = ttk.Frame(self.history_tab)
        toolbar.pack(fill=tk.X, padx=5, pady=5)

        # History button
        history_btn = ttk.Button(toolbar, text="Recent Jobs", command=self.read_history_job)
        history_btn.pack(side=tk.LEFT, padx=5)

        # Failed jobs button
        failed_btn = ttk.Button(toolbar, text="Failed Jobs", command=self.read_failed_job)
        failed_btn.pack(side=tk.LEFT, padx=5)

        # Create history text area
        self.history_text = scrolledtext.ScrolledText(self.history_tab, wrap=tk.WORD, font=("Consolas", 11))
        self.history_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def setup_operation_tab(self):
        # Create toolbar
        toolbar = ttk.Frame(self.operation_tab)
        toolbar.pack(fill=tk.X, padx=5, pady=5)

        # Job ID entry for cancellation
        ttk.Label(toolbar, text="Job ID:").pack(side=tk.LEFT, padx=5)
        self.job_id_cancel_entry = ttk.Entry(toolbar, width=10)
        self.job_id_cancel_entry.pack(side=tk.LEFT, padx=5)

        # Cancel button
        cancel_btn = ttk.Button(toolbar, text="Cancel Job", command=self.cancel_job)
        cancel_btn.pack(side=tk.LEFT, padx=5)

        # Job ID entry for details
        ttk.Label(toolbar, text="Job ID:").pack(side=tk.LEFT, padx=20)
        self.job_id_details_entry = ttk.Entry(toolbar, width=10)
        self.job_id_details_entry.pack(side=tk.LEFT, padx=5)

        # Details button
        details_btn = ttk.Button(toolbar, text="Get Details", command=self.get_job_details)
        details_btn.pack(side=tk.LEFT, padx=5)

        # Create operation text area
        self.operation_text = scrolledtext.ScrolledText(self.operation_tab, wrap=tk.WORD, font=("Consolas", 11))
        self.operation_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    # Script Manager Functions
    def show_script_context_menu(self, event):
        item = self.script_tree.identify_row(event.y)
        if item:
            self.script_tree.selection_set(item)
            self.script_context_menu.post(event.x_root, event.y_root)

    def add_script(self):
        # Create add script dialog
        dialog = tk.Toplevel(self.root)
        dialog.title("Add Script")
        dialog.geometry("600x350")  # Larger dialog

        # Configure entry font
        entry_font = self.default_font

        # Script name
        ttk.Label(dialog, text="Name:").grid(row=0, column=0, padx=10, pady=10, sticky="e")
        name_entry = ttk.Entry(dialog, width=40, font=entry_font)
        name_entry.grid(row=0, column=1, padx=10, pady=10, columnspan=2, sticky="w")

        # Script path
        ttk.Label(dialog, text="Path:").grid(row=1, column=0, padx=10, pady=10, sticky="e")
        path_entry = ttk.Entry(dialog, width=40, font=entry_font)
        path_entry.grid(row=1, column=1, padx=10, pady=10, columnspan=2, sticky="w")
        ttk.Button(dialog, text="Browse", command=lambda: self.browse_file(path_entry, None)).grid(row=1, column=3, padx=10, pady=10)

        # Script type
        ttk.Label(dialog, text="Type:").grid(row=2, column=0, padx=10, pady=10, sticky="e")
        type_var = tk.StringVar(value="python")
        ttk.Radiobutton(dialog, text="Bash", variable=type_var, value="bash").grid(row=2, column=1, padx=10, pady=10, sticky="w")
        ttk.Radiobutton(dialog, text="Python", variable=type_var, value="python").grid(row=2, column=2, padx=10, pady=10, sticky="w")

        # Log file
        ttk.Label(dialog, text="Log File:").grid(row=3, column=0, padx=10, pady=10, sticky="e")
        log_entry = ttk.Entry(dialog, width=40, font=entry_font)
        log_entry.grid(row=3, column=1, padx=10, pady=10, columnspan=2, sticky="w")
        ttk.Button(dialog, text="Browse", command=lambda: self.browse_file(log_entry, [("Log files", "*.log"), ("All files", "*.*")])).grid(row=3, column=3, padx=10, pady=10)

        # Buttons frame
        button_frame = ttk.Frame(dialog)
        button_frame.grid(row=4, column=0, columnspan=4, pady=20)

        # Confirm button
        def confirm():
            name = name_entry.get()
            path = path_entry.get()
            script_type = type_var.get()
            log_file = log_entry.get()

            if not name or not path:
                messagebox.showerror("Error", "Please fill in all required information")
                return

            # Generate a unique session name
            session_name = f"{script_type}{len(self.scripts) + 1}"

            # Create new script entry
            script = {
                "name": name,
                "path": path,
                "type": script_type,
                "log_file": log_file,
                "session_name": session_name,
                "status": "Stopped",
                "id": len(self.scripts) + 1
            }

            self.scripts.append(script)
            self.save_scripts()
            self.refresh_status()
            dialog.destroy()

        # Cancel button
        def cancel():
            dialog.destroy()

        ttk.Button(button_frame, text="Confirm", command=confirm).pack(side=tk.LEFT, padx=20)
        ttk.Button(button_frame, text="Cancel", command=cancel).pack(side=tk.LEFT, padx=20)

    def edit_script(self):
        selected_item = self.script_tree.selection()
        if not selected_item:
            return

        try:
            item = selected_item[0]
            index = self.script_tree.index(item)
            if index < 0 or index >= len(self.scripts):
                messagebox.showerror("Error", "Invalid script selection")
                return

            script = self.scripts[index]

            # Create edit script dialog
            dialog = tk.Toplevel(self.root)
            dialog.title("Edit Script")
            dialog.geometry("600x350")  # Larger dialog

            # Configure entry font
            entry_font = self.default_font

            # Script name
            ttk.Label(dialog, text="Name:").grid(row=0, column=0, padx=10, pady=10, sticky="e")
            name_entry = ttk.Entry(dialog, width=40, font=entry_font)
            name_entry.insert(0, script["name"])
            name_entry.grid(row=0, column=1, padx=10, pady=10, columnspan=2, sticky="w")

            # Script path
            ttk.Label(dialog, text="Path:").grid(row=1, column=0, padx=10, pady=10, sticky="e")
            path_entry = ttk.Entry(dialog, width=40, font=entry_font)
            path_entry.insert(0, script["path"])
            path_entry.grid(row=1, column=1, padx=10, pady=10, columnspan=2, sticky="w")
            ttk.Button(dialog, text="Browse", command=lambda: self.browse_file(path_entry, None)).grid(row=1, column=3, padx=10, pady=10)

            # Script type
            ttk.Label(dialog, text="Type:").grid(row=2, column=0, padx=10, pady=10, sticky="e")
            type_var = tk.StringVar(value=script["type"])
            ttk.Radiobutton(dialog, text="Bash", variable=type_var, value="bash").grid(row=2, column=1, padx=10, pady=10, sticky="w")
            ttk.Radiobutton(dialog, text="Python", variable=type_var, value="python").grid(row=2, column=2, padx=10, pady=10, sticky="w")

            # Log file
            ttk.Label(dialog, text="Log File:").grid(row=3, column=0, padx=10, pady=10, sticky="e")
            log_entry = ttk.Entry(dialog, width=40, font=entry_font)
            log_entry.insert(0, script.get("log_file", ""))
            log_entry.grid(row=3, column=1, padx=10, pady=10, columnspan=2, sticky="w")
            ttk.Button(dialog, text="Browse", command=lambda: self.browse_file(log_entry, [("Log files", "*.log"), ("All files", "*.*")])).grid(row=3, column=3, padx=10, pady=10)

            # Buttons frame
            button_frame = ttk.Frame(dialog)
            button_frame.grid(row=4, column=0, columnspan=4, pady=20)

            # Confirm button
            def confirm():
                name = name_entry.get()
                path = path_entry.get()
                script_type = type_var.get()
                log_file = log_entry.get()

                if not name or not path:
                    messagebox.showerror("Error", "Please fill in all required information")
                    return

                script["name"] = name
                script["path"] = path
                script["type"] = script_type
                script["log_file"] = log_file

                self.save_scripts()
                self.refresh_status()
                dialog.destroy()

            # Cancel button
            def cancel():
                dialog.destroy()

            ttk.Button(button_frame, text="Confirm", command=confirm).pack(side=tk.LEFT, padx=20)
            ttk.Button(button_frame, text="Cancel", command=cancel).pack(side=tk.LEFT, padx=20)
        except Exception as e:
            messagebox.showerror("Error", f"Error processing script: {str(e)}")
            return

    def browse_file(self, entry, filetypes=None):
        # Handle the filetypes parameter correctly
        if filetypes is None:
            filename = filedialog.askopenfilename()
        else:
            filename = filedialog.askopenfilename(filetypes=filetypes)

        if filename:
            entry.delete(0, tk.END)
            entry.insert(0, filename)

    def run_script(self):
        selected_item = self.script_tree.selection()
        if not selected_item:
            return

        try:
            item = selected_item[0]
            # Get the index from the tree item
            index = self.script_tree.index(item)
            if index < 0 or index >= len(self.scripts):
                messagebox.showerror("Error", "Invalid script selection")
                return

            script = self.scripts[index]

            if script["status"] == "Running":
                messagebox.showinfo("Info", "Script is already running")
                return

            try:
                # Get log file path, default to empty string if not exists
                log_file = script.get("log_file", "")

                # If log file is set, ensure log directory exists
                if log_file:
                    log_dir = os.path.dirname(log_file)
                    if log_dir:  # If log file includes a directory path
                        try:
                            os.makedirs(log_dir, exist_ok=True)  # Create directory, ignore if exists
                        except Exception as e:
                            messagebox.showerror("Error", f"Failed to create log directory: {str(e)}")
                            return

                if script["type"] == "bash":
                    if log_file:
                        # Direct redirection to log file
                        cmd = f"tmux new-session -d -s {script['session_name']} 'bash {script['path']} 2>&1 | tee {log_file}'"
                    else:
                        cmd = f"tmux new-session -d -s {script['session_name']} 'bash {script['path']}'"
                else:
                    if log_file:
                        # Python script with -u parameter to ensure unbuffered output
                        cmd = f"tmux new-session -d -s {script['session_name']} 'python -u {script['path']} 2>&1 | tee {log_file}'"
                    else:
                        cmd = f"tmux new-session -d -s {script['session_name']} 'python {script['path']}'"

                subprocess.run(cmd, shell=True)
                script["status"] = "Running"
                script["start_time"] = datetime.now().strftime("%Y%m%d_%H%M%S")
                self.save_scripts()
                self.refresh_status()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to run script: {str(e)}")
        except Exception as e:
            messagebox.showerror("Error", f"Error processing script: {str(e)}")
            return

    def stop_script(self):
        selected_item = self.script_tree.selection()
        if not selected_item:
            return

        try:
            item = selected_item[0]
            index = self.script_tree.index(item)
            if index < 0 or index >= len(self.scripts):
                messagebox.showerror("Error", "Invalid script selection")
                return

            script = self.scripts[index]

            if script["status"] != "Running":
                messagebox.showinfo("Info", "Script is not running")
                return

            try:
                subprocess.run(f"tmux kill-session -t {script['session_name']}", shell=True)
                script["status"] = "Stopped"
                self.save_scripts()
                self.refresh_status()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to stop script: {str(e)}")
        except Exception as e:
            messagebox.showerror("Error", f"Error processing script: {str(e)}")
            return

    def delete_script(self):
        selected_item = self.script_tree.selection()
        if not selected_item:
            return

        try:
            item = selected_item[0]
            index = self.script_tree.index(item)
            if index < 0 or index >= len(self.scripts):
                messagebox.showerror("Error", "Invalid script selection")
                return

            if not messagebox.askyesno("Confirm", "Are you sure you want to delete the selected script?"):
                return

            script = self.scripts[index]

            if script["status"] == "Running":
                try:
                    subprocess.run(f"tmux kill-session -t {script['session_name']}", shell=True)
                except Exception:
                    pass

            del self.scripts[index]
            self.save_scripts()
            self.refresh_status()
        except Exception as e:
            messagebox.showerror("Error", f"Error processing script: {str(e)}")
            return

    def get_progress_from_log(self, log_file_path):
        """Reads the log file and extracts the latest progress."""
        if not log_file_path or not os.path.exists(log_file_path):
            return ""

        try:
            # Read the last few lines to find the latest progress
            with open(log_file_path, "r") as f:
                # Go to the end of the file
                f.seek(0, os.SEEK_END)
                file_size = f.tell()

                # Go back up to 1KB from the end (adjust as needed)
                seek_pos = max(file_size - 1024, 0)
                f.seek(seek_pos, os.SEEK_SET)

                # Read the lines from the seek position to the end
                lines = f.readlines()

            # Search for the latest line containing "(progress)"
            progress_line = None
            for line in reversed(lines):
                if "(progress)" in line:
                    progress_line = line
                    break

            if progress_line:
                # Extract the progress using regex
                import re
                match = re.search(r'\[(.*?)\]', progress_line)
                if match:
                    return match.group(1).strip()
            return ""

        except Exception:
            return "" # Return empty string on error

    def refresh_status(self):
        # Clear current tree
        for item in self.script_tree.get_children():
            self.script_tree.delete(item)

        # Step 1: Reload configuration from scripts.json
        try:
            with open("scripts.json", "r") as f:
                self.scripts = json.load(f)
        except FileNotFoundError:
            self.scripts = []

        # Ensure each script has an ID
        for i, script in enumerate(self.scripts):
            if "id" not in script:
                script["id"] = i + 1

        # First display all scripts based on configuration file
        for script in self.scripts:
            script_name = os.path.basename(script["path"])

            # Get the shortened path (last 30 characters)
            full_path = script["path"]
            if len(full_path) > 30:
                short_path = "..." + full_path[-30:]
            else:
                short_path = full_path

            # Get initial progress (will be updated for running scripts)
            initial_progress = ""

            item = self.script_tree.insert("", "end", values=(
                script["id"],
                script["session_name"],
                script["status"],
                script_name,
                short_path,
                script.get("log_file", ""),
                initial_progress # Add the new progress column
            ))

            # Set tag based on status
            if script["status"] == "Running":
                self.script_tree.item(item, tags=('running',))
            else:
                self.script_tree.item(item, tags=('not_running',))

        # Step 2: Update running status and progress of each script
        for i, script in enumerate(self.scripts):
            item = self.script_tree.get_children()[i] # Get the treeview item for this script
            if script["status"] == "Running":
                try:
                    result = subprocess.run(f"tmux has-session -t {script['session_name']}", shell=True)
                    if result.returncode != 0:
                        script["status"] = "Stopped"
                        # Update display status
                        values = list(self.script_tree.item(item)["values"])
                        values[2] = "Stopped"  # Update status column
                        values[6] = "" # Clear progress when stopped
                        self.script_tree.item(item, values=values, tags=('not_running',))
                    else:
                        # Script is still running, update progress
                        log_file = script.get("log_file", "")
                        progress = self.get_progress_from_log(log_file)
                        values = list(self.script_tree.item(item)["values"])
                        values[6] = progress # Update progress column
                        self.script_tree.item(item, values=values) # Update item values
                except Exception:
                    script["status"] = "Stopped"
                    # Update display status
                    values = list(self.script_tree.item(item)["values"])
                    values[2] = "Stopped"  # Update status column
                    values[6] = "" # Clear progress when stopped
                    self.script_tree.item(item, values=values, tags=('not_running',))

        # Save updated status
        self.save_scripts()

    def save_scripts(self):
        with open("scripts.json", "w") as f:
            json.dump(self.scripts, f, indent=4, sort_keys=False)

    def load_scripts(self):
        try:
            with open("scripts.json", "r") as f:
                self.scripts = json.load(f)
        except FileNotFoundError:
            self.scripts = []

    def start_auto_refresh(self):
        def auto_refresh_loop():
            while True:
                time.sleep(self.auto_refresh_interval)
                # Refresh the current tab based on which one is active
                if self.current_tab == "scripts":
                    self.root.after(0, self.refresh_status)
                elif self.current_tab == "tasks":
                    self.root.after(0, self.refresh_tasks)
                elif self.current_tab == "jobs":
                    self.root.after(0, self.refresh_jobs)
                elif self.current_tab == "nodes":
                    self.root.after(0, self.refresh_nodes)

        # Start auto-refresh in a separate thread
        self.auto_refresh_thread = threading.Thread(target=auto_refresh_loop, daemon=True)
        self.auto_refresh_thread.start()

    def view_log(self):
        """View the log file of the selected script"""
        selected_item = self.script_tree.selection()
        if not selected_item:
            return

        try:
            item = selected_item[0]
            index = self.script_tree.index(item)
            if index < 0 or index >= len(self.scripts):
                messagebox.showerror("Error", "Invalid script selection")
                return

            script = self.scripts[index]
            log_file = script.get("log_file", "")

            if not log_file:
                messagebox.showinfo("Info", "No log file specified for this script")
                return

            if not os.path.exists(log_file):
                messagebox.showinfo("Info", f"Log file does not exist: {log_file}")
                return

            # Create a new window to display the log
            log_window = tk.Toplevel(self.root)
            log_window.title(f"Log: {script['name']}")
            log_window.geometry("800x600")

            # Add a text area with scrollbars
            log_text = scrolledtext.ScrolledText(log_window, wrap=tk.WORD, font=("Consolas", 11))
            log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

            # Add toolbar with refresh button
            toolbar = ttk.Frame(log_window)
            toolbar.pack(fill=tk.X, padx=5, pady=5)

            # Function to refresh log content
            def refresh_log():
                try:
                    with open(log_file, "r") as f:
                        content = f.read()
                    log_text.delete(1.0, tk.END)
                    log_text.insert(tk.END, content)
                    # Auto-scroll to the end
                    log_text.see(tk.END)
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to read log file: {str(e)}")

            # Add refresh button
            refresh_btn = ttk.Button(toolbar, text="Refresh", command=refresh_log)
            refresh_btn.pack(side=tk.LEFT, padx=5)

            # Add auto-scroll checkbox
            auto_scroll_var = tk.BooleanVar(value=True)
            auto_scroll_check = ttk.Checkbutton(toolbar, text="Auto-scroll to end", variable=auto_scroll_var)
            auto_scroll_check.pack(side=tk.LEFT, padx=5)

            # Initial load of log content
            refresh_log()

            # Setup auto-refresh for log
            def auto_refresh_log():
                if auto_scroll_var.get():
                    refresh_log()
                log_window.after(5000, auto_refresh_log)  # Refresh every 5 seconds

            log_window.after(5000, auto_refresh_log)

        except Exception as e:
            messagebox.showerror("Error", f"Error viewing log: {str(e)}")
            return

    # Slurm GUI Functions
    def connect_to_server(self):
        try:
            self.ssh = paramiko.SSHClient()
            self.ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            # Use username/password authentication
            self.ssh.connect(self.hostname, username=self.username, password=self.password)
        except Exception as e:
            messagebox.showerror("Error", f"Connection failed: {str(e)}")

    def refresh_tasks(self):
        if not self.ssh:
            return None

        try:
            # Clear existing items in the tasks tree
            for item in self.tasks_tree.get_children():
                self.tasks_tree.delete(item)

            # Get user job information
            _, stdout, _ = self.ssh.exec_command('sacct -o JobID%25,JobName%25,Start,State%25 | grep -v -E ".batch|.extern"')
            jobs = stdout.readlines()[2:]  # Skip header 2 lines
            job_ids = sorted([line.split()[0] for line in jobs])
            unique_jobs = list(set([s.split('_')[0] for s in job_ids]))

            # Sort unique_jobs numerically
            unique_jobs = sorted(unique_jobs, key=lambda x: int(x) if x.isdigit() else float('inf'))

            for job in unique_jobs:
                job_group = [j for j in jobs if j.strip().startswith(job)]
                completed = [j for j in job_group if j.strip().startswith(job) and 'COMPLETED' in j]
                failed = [j for j in job_group if j.strip().startswith(job) and 'FAILED' in j]
                pending = [j for j in job_group if j.strip().startswith(job) and 'PENDING' in j]
                running = [j for j in job_group if j.strip().startswith(job) and 'RUNNING' in j]
                cancelled = [j for j in job_group if j.strip().startswith(job) and 'CANCELLED' in j]
                start_time = pd.to_datetime(job_group[0].split()[2])
                elapsed = (pd.to_datetime(datetime.now()) - start_time).total_seconds() / 60

                if len(completed)>0:
                    jobname = completed[0].split()[1]
                elif len(running)>0:
                    jobname = running[0].split()[1]
                elif len(failed)>0:
                    jobname = failed[0].split()[1]
                elif len(cancelled)>0:
                    jobname = cancelled[0].split()[1]

                total = len(job_group)
                if len(pending) > 0:
                    total = int(pending[0].split()[0].split('-')[-1][:-1])+1-int(job_group[0].split()[0].split('_')[1])

                completed_count = len(completed)
                failed_count = len(failed)
                pending_count = len(pending)
                running_count = len(running)
                eta = round(elapsed * (total/max(1,completed_count+failed_count)-1.0), 2) # eta is the estimated time to completion
                if len(cancelled)==total: eta = 0.0

                # Calculate progress percentage (1 - running/total)
                progress_pct = 0
                if total > 0:
                    progress_pct = round((completed_count+failed_count) / total * 100, 2)

                # Determine row tag based on job status
                if completed_count == total and total > 0:
                    # Job is completed
                    row_tag = 'completed'
                elif running_count > 0:
                    # Job is running
                    row_tag = 'running'
                else:
                    # Use alternating colors for other states
                    row_tag = 'even' if len(self.tasks_tree.get_children()) % 2 == 0 else 'odd'

                # Format progress percentage with color indication
                progress_text = f"{progress_pct}%"

                # Insert with row tag
                self.tasks_tree.insert('', tk.END, values=(
                    jobname,
                    job,
                    total,
                    completed_count,
                    failed_count,
                    running_count,
                    progress_text,
                    eta
                ), tags=(row_tag,))

            # Update the timestamp
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.timestamp_label.config(text=f"Last updated: {current_time}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh tasks: {str(e)}")

    def refresh_jobs(self):
        if not self.ssh:
            return None

        try:
            user = self.user_entry.get().strip()
            node = self.node_entry.get().strip()
            part = self.part_entry.get().strip()

            fmt = "%.15i %.15P %.15j %.10u %.10M %.6D %R"
            command = 'squeue '
            if user:
                command += f'-u {user} '
            if node:
                command += f'--nodelist={node} '
            if part:
                command += f'-p {part} '

            command += f'-o "{fmt}" | head -n20'

            _, stdout, _ = self.ssh.exec_command(command)
            jobs_output = stdout.read().decode('utf-8')

            self.jobs_text.delete(1.0, tk.END)
            self.jobs_text.insert(tk.END, jobs_output)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh jobs: {str(e)}")

    def refresh_nodes(self):
        if not self.ssh:
            return None

        try:
            part = self.part_entry2.get().strip()
            detail = self.detail_var.get()

            command = 'sinfo '
            if part:
                command += f'-p {part} '

            if detail:
                command += '-lN'

            _, stdout, _ = self.ssh.exec_command(command)
            nodes_output = stdout.read().decode('utf-8')

            self.nodes_text.delete(1.0, tk.END)
            self.nodes_text.insert(tk.END, nodes_output)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh nodes: {str(e)}")

    def read_history_job(self):
        if not self.ssh:
            return None

        try:
            command = 'sacct -o JobID%20,JobName%20,Partition%8,ReqCPUS%4,ReqMem%5,State%15,Elapsed | grep -v -E ".batch|.extern" | sort -k1 -r | head -n20'
            _, stdout, _ = self.ssh.exec_command(command)
            job_output = stdout.read().decode('utf-8')

            self.history_text.delete(1.0, tk.END)
            self.history_text.insert(tk.END, job_output)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to read job history: {str(e)}")

    def read_failed_job(self):
        if not self.ssh:
            return None

        try:
            command = 'sacct -o JobID%20,JobName%20,Partition%8,State%10,Elapsed | grep -E "FAILED|OUT_OF" | grep -v .batch | sort -k1 -r | head -n20'
            _, stdout, _ = self.ssh.exec_command(command)
            job_output = stdout.read().decode('utf-8')

            self.history_text.delete(1.0, tk.END)
            self.history_text.insert(tk.END, job_output)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to read failed jobs: {str(e)}")

    def cancel_job(self):
        if not self.ssh:
            return None

        try:
            job_id = self.job_id_cancel_entry.get().strip()
            if job_id:
                _, stdout, _ = self.ssh.exec_command(f'scancel {job_id}')
                result = stdout.read().decode('utf-8')

                self.operation_text.delete(1.0, tk.END)
                self.operation_text.insert(tk.END, f"Job {job_id} cancelled.\n{result}")

                # Refresh jobs list after cancellation
                self.refresh_jobs()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to cancel job: {str(e)}")

    def get_job_details(self):
        if not self.ssh:
            return None

        try:
            job_id = self.job_id_details_entry.get().strip()
            if not job_id:
                messagebox.showerror("Error", "Please enter a job ID")
                return

            self.operation_text.delete(1.0, tk.END)
            self.operation_text.insert(tk.END, f"=== Job Details for {job_id} ===\n\n")

            # Get job details
            _, stdout, _ = self.ssh.exec_command(f'scontrol show job {job_id}')
            job_details = stdout.read().decode('utf-8')

            self.operation_text.insert(tk.END, job_details)

            # Get job steps information
            _, stdout, _ = self.ssh.exec_command(f'scontrol show step {job_id}')
            step_details = stdout.read().decode('utf-8')

            if step_details.strip():
                self.operation_text.insert(tk.END, f"\n\n=== Job Steps for {job_id} ===\n\n")
                self.operation_text.insert(tk.END, step_details)

            # Get job accounting information
            _, stdout, _ = self.ssh.exec_command(f'sacct -j {job_id} -o JobID,JobName,Partition,State,ExitCode,Elapsed,MaxRSS,NodeList')
            acct_details = stdout.read().decode('utf-8')

            if acct_details.strip():
                self.operation_text.insert(tk.END, f"\n\n=== Job Accounting for {job_id} ===\n\n")
                self.operation_text.insert(tk.END, acct_details)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to get task details: {str(e)}")

    def on_closing(self):
        if self.ssh:
            self.ssh.close()
        self.root.destroy()

if __name__ == "__main__":
    root = tk.Tk()

    # Set default font for all widgets
    font_family = "Consolas"
    font_size = 11

    # Configure fonts for different widget types
    root.option_add("*Font", (font_family, font_size))
    root.option_add("*Label.Font", (font_family, font_size))
    root.option_add("*Button.Font", (font_family, font_size))
    root.option_add("*Entry.Font", (font_family, font_size))

    # Create and configure the application
    app = GuiTask(root)
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    root.mainloop()
