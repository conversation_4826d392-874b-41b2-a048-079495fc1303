[{"id": 1, "name": "ga.fit.x1_x16", "type": "python", "session_name": "py1", "path": "/mnt/sda/home/<USER>/crypto/scripts/ga/ga_fit_x1_x16.py", "status": "Stopped", "log_file": "log/ga_fit_x1_x16.log", "start_time": "20250513_170816"}, {"id": 2, "name": "calc_ret_v1", "type": "python", "session_name": "py2", "path": "/mnt/sda/home/<USER>/crypto/scripts/label/calc_ret_v1.py", "status": "Stopped", "log_file": "log/calc_ret_v1.log", "start_time": "20250508_203651"}, {"id": 3, "name": "gen_minbar", "type": "python", "session_name": "py3", "path": "/mnt/sda/home/<USER>/HFG/gen_minbar.py", "status": "Stopped", "log_file": "log/gen_minbar.log", "start_time": "20250513_171003"}, {"id": 4, "name": "bt.bar.ob4_v2", "type": "python", "session_name": "py4", "path": "/mnt/sda/home/<USER>/HFA/scripts/bt.bar.ob4_v2.py", "status": "Stopped", "log_file": "log/bar/3.bt.bar.ob4_v2.log", "start_time": "20250507_083218"}, {"id": 5, "name": "bt.tk.ob4_v1", "type": "python", "session_name": "py5", "path": "/mnt/sda/home/<USER>/HFA/scripts/bt.tk.ob4_v1.py", "status": "Stopped", "log_file": "log/tick/4.bt.tk.ob4_v1.log", "start_time": "20250509_104629"}, {"id": 6, "name": "bt.tk.ob3", "type": "python", "session_name": "py6", "path": "/mnt/sda/home/<USER>/HFA/scripts/bt.tk.ob3.py", "status": "Stopped", "log_file": "log/tick/5.bt.tk.ob3.log", "start_time": "20250506_192934"}, {"id": 7, "name": "bt.tk.ob3_v2", "type": "python", "session_name": "py7", "path": "/mnt/sda/home/<USER>/HFA/scripts/bt.tk.ob3_v2.py", "status": "Stopped", "log_file": "log/tick/6.bt.tk.ob3_v2.log", "start_time": "20250504_214454"}, {"id": 8, "name": "bt.tk.ob3_v3", "type": "python", "session_name": "py8", "path": "/mnt/sda/home/<USER>/HFA/scripts/bt.tk.ob3_v3.py", "status": "Stopped", "log_file": "log/tick/7.bt.tk.ob3_v3.log", "start_time": "20250504_214455"}, {"id": 9, "name": "bt.tk.ob3_v4", "type": "python", "session_name": "py9", "path": "/mnt/sda/home/<USER>/HFA/scripts/bt.tk.ob3_v4.py", "status": "Stopped", "log_file": "log/tick/8.bt.tk.ob3_v4.log", "start_time": "20250504_214457"}, {"id": 10, "name": "regen_trade_com", "type": "python", "session_name": "py10", "path": "/mnt/sda/home/<USER>/HFG/regen_trade_com.py", "status": "Stopped", "log_file": "log/regen_trade_com.log", "start_time": "20250512_171409"}]