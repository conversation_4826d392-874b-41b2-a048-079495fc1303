{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## feature"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>chfunctioncode</th>\n", "      <th>chorderkind</th>\n", "      <th>nactionday</th>\n", "      <th>nclientrecvtime</th>\n", "      <th>norder</th>\n", "      <th>nprice</th>\n", "      <th>nserverrecvid</th>\n", "      <th>nserverrecvtime</th>\n", "      <th>ntime</th>\n", "      <th>nvolume</th>\n", "      <th>nturnover</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-01-04 09:15:00.000</th>\n", "      <td>83.0</td>\n", "      <td>48.0</td>\n", "      <td>20240104.0</td>\n", "      <td>92510827.0</td>\n", "      <td>19.0</td>\n", "      <td>7.29</td>\n", "      <td>3491086.0</td>\n", "      <td>92510816.0</td>\n", "      <td>91500000.0</td>\n", "      <td>100.0</td>\n", "      <td>729.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-04 09:15:00.000</th>\n", "      <td>66.0</td>\n", "      <td>48.0</td>\n", "      <td>20240104.0</td>\n", "      <td>92510827.0</td>\n", "      <td>114.0</td>\n", "      <td>5.98</td>\n", "      <td>3491087.0</td>\n", "      <td>92510816.0</td>\n", "      <td>91500000.0</td>\n", "      <td>100.0</td>\n", "      <td>598.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-04 09:15:00.000</th>\n", "      <td>83.0</td>\n", "      <td>48.0</td>\n", "      <td>20240104.0</td>\n", "      <td>92510827.0</td>\n", "      <td>146.0</td>\n", "      <td>6.64</td>\n", "      <td>3491088.0</td>\n", "      <td>92510816.0</td>\n", "      <td>91500000.0</td>\n", "      <td>100.0</td>\n", "      <td>664.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-04 09:15:00.080</th>\n", "      <td>66.0</td>\n", "      <td>48.0</td>\n", "      <td>20240104.0</td>\n", "      <td>92510827.0</td>\n", "      <td>444.0</td>\n", "      <td>5.98</td>\n", "      <td>3491089.0</td>\n", "      <td>92510816.0</td>\n", "      <td>91500080.0</td>\n", "      <td>100.0</td>\n", "      <td>598.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-04 09:15:00.080</th>\n", "      <td>66.0</td>\n", "      <td>48.0</td>\n", "      <td>20240104.0</td>\n", "      <td>92510827.0</td>\n", "      <td>447.0</td>\n", "      <td>5.98</td>\n", "      <td>3491090.0</td>\n", "      <td>92510816.0</td>\n", "      <td>91500080.0</td>\n", "      <td>100.0</td>\n", "      <td>598.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                         chfunctioncode  chorderkind  nactionday  \\\n", "2024-01-04 09:15:00.000            83.0         48.0  20240104.0   \n", "2024-01-04 09:15:00.000            66.0         48.0  20240104.0   \n", "2024-01-04 09:15:00.000            83.0         48.0  20240104.0   \n", "2024-01-04 09:15:00.080            66.0         48.0  20240104.0   \n", "2024-01-04 09:15:00.080            66.0         48.0  20240104.0   \n", "\n", "                         nclientrecvtime  norder  nprice  nserverrecvid  \\\n", "2024-01-04 09:15:00.000       92510827.0    19.0    7.29      3491086.0   \n", "2024-01-04 09:15:00.000       92510827.0   114.0    5.98      3491087.0   \n", "2024-01-04 09:15:00.000       92510827.0   146.0    6.64      3491088.0   \n", "2024-01-04 09:15:00.080       92510827.0   444.0    5.98      3491089.0   \n", "2024-01-04 09:15:00.080       92510827.0   447.0    5.98      3491090.0   \n", "\n", "                         nserverrecvtime       ntime  nvolume  nturnover  \n", "2024-01-04 09:15:00.000       92510816.0  91500000.0    100.0      729.0  \n", "2024-01-04 09:15:00.000       92510816.0  91500000.0    100.0      598.0  \n", "2024-01-04 09:15:00.000       92510816.0  91500000.0    100.0      664.0  \n", "2024-01-04 09:15:00.080       92510816.0  91500080.0    100.0      598.0  \n", "2024-01-04 09:15:00.080       92510816.0  91500080.0    100.0      598.0  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from yrqtlib.utils import data\n", "from yrqtlib.utils import add_methods_to_pandas\n", "\n", "sym = '600000.SH'\n", "order = data.load_1sym_tick('cn_ashare', 'order', '2024-01-04', sym)\n", "trade = data.load_1sym_tick('cn_ashare', 'trade', '2024-01-04', sym)\n", "mkt = data.load_1sym_tick('cn_ashare', 'market', '2024-01-04', sym)\n", "order['nturnover'] = order['nvolume'] * order['nprice']\n", "order.head()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>chfunctioncode</th>\n", "      <th>chorderkind</th>\n", "      <th>nactionday</th>\n", "      <th>nclientrecvtime</th>\n", "      <th>norder</th>\n", "      <th>nprice</th>\n", "      <th>nserverrecvid</th>\n", "      <th>nserverrecvtime</th>\n", "      <th>ntime</th>\n", "      <th>nvolume</th>\n", "      <th>nturnover</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-01-04 09:15:00.000</th>\n", "      <td>66.0</td>\n", "      <td>48.0</td>\n", "      <td>20240104.0</td>\n", "      <td>92510827.0</td>\n", "      <td>114.0</td>\n", "      <td>5.98</td>\n", "      <td>3491087.0</td>\n", "      <td>92510816.0</td>\n", "      <td>91500000.0</td>\n", "      <td>100.0</td>\n", "      <td>598.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-04 09:15:00.080</th>\n", "      <td>66.0</td>\n", "      <td>48.0</td>\n", "      <td>20240104.0</td>\n", "      <td>92510827.0</td>\n", "      <td>444.0</td>\n", "      <td>5.98</td>\n", "      <td>3491089.0</td>\n", "      <td>92510816.0</td>\n", "      <td>91500080.0</td>\n", "      <td>100.0</td>\n", "      <td>598.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-04 09:15:00.080</th>\n", "      <td>66.0</td>\n", "      <td>48.0</td>\n", "      <td>20240104.0</td>\n", "      <td>92510827.0</td>\n", "      <td>447.0</td>\n", "      <td>5.98</td>\n", "      <td>3491090.0</td>\n", "      <td>92510816.0</td>\n", "      <td>91500080.0</td>\n", "      <td>100.0</td>\n", "      <td>598.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-04 09:15:00.090</th>\n", "      <td>66.0</td>\n", "      <td>48.0</td>\n", "      <td>20240104.0</td>\n", "      <td>92510827.0</td>\n", "      <td>575.0</td>\n", "      <td>5.98</td>\n", "      <td>3491091.0</td>\n", "      <td>92510816.0</td>\n", "      <td>91500090.0</td>\n", "      <td>100.0</td>\n", "      <td>598.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-04 09:15:00.090</th>\n", "      <td>66.0</td>\n", "      <td>68.0</td>\n", "      <td>20240104.0</td>\n", "      <td>92510827.0</td>\n", "      <td>114.0</td>\n", "      <td>5.98</td>\n", "      <td>3491093.0</td>\n", "      <td>92510816.0</td>\n", "      <td>91500090.0</td>\n", "      <td>100.0</td>\n", "      <td>598.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-04 14:59:50.410</th>\n", "      <td>83.0</td>\n", "      <td>48.0</td>\n", "      <td>20240104.0</td>\n", "      <td>150001463.0</td>\n", "      <td>10805073.0</td>\n", "      <td>6.00</td>\n", "      <td>337462179.0</td>\n", "      <td>150001442.0</td>\n", "      <td>145950410.0</td>\n", "      <td>2100.0</td>\n", "      <td>12600.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-04 14:59:53.420</th>\n", "      <td>83.0</td>\n", "      <td>48.0</td>\n", "      <td>20240104.0</td>\n", "      <td>150001463.0</td>\n", "      <td>10807487.0</td>\n", "      <td>5.98</td>\n", "      <td>337462185.0</td>\n", "      <td>150001442.0</td>\n", "      <td>145953420.0</td>\n", "      <td>2100.0</td>\n", "      <td>12558.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-04 14:59:55.020</th>\n", "      <td>83.0</td>\n", "      <td>48.0</td>\n", "      <td>20240104.0</td>\n", "      <td>150001463.0</td>\n", "      <td>10808749.0</td>\n", "      <td>6.49</td>\n", "      <td>337462193.0</td>\n", "      <td>150001442.0</td>\n", "      <td>145955020.0</td>\n", "      <td>27400.0</td>\n", "      <td>177826.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-04 14:59:56.040</th>\n", "      <td>83.0</td>\n", "      <td>48.0</td>\n", "      <td>20240104.0</td>\n", "      <td>150001463.0</td>\n", "      <td>10809557.0</td>\n", "      <td>6.49</td>\n", "      <td>337462205.0</td>\n", "      <td>150001442.0</td>\n", "      <td>145956040.0</td>\n", "      <td>600.0</td>\n", "      <td>3894.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-04 14:59:56.430</th>\n", "      <td>83.0</td>\n", "      <td>48.0</td>\n", "      <td>20240104.0</td>\n", "      <td>150001463.0</td>\n", "      <td>10810014.0</td>\n", "      <td>5.98</td>\n", "      <td>337462208.0</td>\n", "      <td>150001442.0</td>\n", "      <td>145956430.0</td>\n", "      <td>2100.0</td>\n", "      <td>12558.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5219 rows × 11 columns</p>\n", "</div>"], "text/plain": ["                         chfunctioncode  chorderkind  nactionday  \\\n", "2024-01-04 09:15:00.000            66.0         48.0  20240104.0   \n", "2024-01-04 09:15:00.080            66.0         48.0  20240104.0   \n", "2024-01-04 09:15:00.080            66.0         48.0  20240104.0   \n", "2024-01-04 09:15:00.090            66.0         48.0  20240104.0   \n", "2024-01-04 09:15:00.090            66.0         68.0  20240104.0   \n", "...                                 ...          ...         ...   \n", "2024-01-04 14:59:50.410            83.0         48.0  20240104.0   \n", "2024-01-04 14:59:53.420            83.0         48.0  20240104.0   \n", "2024-01-04 14:59:55.020            83.0         48.0  20240104.0   \n", "2024-01-04 14:59:56.040            83.0         48.0  20240104.0   \n", "2024-01-04 14:59:56.430            83.0         48.0  20240104.0   \n", "\n", "                         nclientrecvtime      norder  nprice  nserverrecvid  \\\n", "2024-01-04 09:15:00.000       92510827.0       114.0    5.98      3491087.0   \n", "2024-01-04 09:15:00.080       92510827.0       444.0    5.98      3491089.0   \n", "2024-01-04 09:15:00.080       92510827.0       447.0    5.98      3491090.0   \n", "2024-01-04 09:15:00.090       92510827.0       575.0    5.98      3491091.0   \n", "2024-01-04 09:15:00.090       92510827.0       114.0    5.98      3491093.0   \n", "...                                  ...         ...     ...            ...   \n", "2024-01-04 14:59:50.410      150001463.0  10805073.0    6.00    337462179.0   \n", "2024-01-04 14:59:53.420      150001463.0  10807487.0    5.98    337462185.0   \n", "2024-01-04 14:59:55.020      150001463.0  10808749.0    6.49    337462193.0   \n", "2024-01-04 14:59:56.040      150001463.0  10809557.0    6.49    337462205.0   \n", "2024-01-04 14:59:56.430      150001463.0  10810014.0    5.98    337462208.0   \n", "\n", "                         nserverrecvtime        ntime  nvolume  nturnover  \n", "2024-01-04 09:15:00.000       92510816.0   91500000.0    100.0      598.0  \n", "2024-01-04 09:15:00.080       92510816.0   91500080.0    100.0      598.0  \n", "2024-01-04 09:15:00.080       92510816.0   91500080.0    100.0      598.0  \n", "2024-01-04 09:15:00.090       92510816.0   91500090.0    100.0      598.0  \n", "2024-01-04 09:15:00.090       92510816.0   91500090.0    100.0      598.0  \n", "...                                  ...          ...      ...        ...  \n", "2024-01-04 14:59:50.410      150001442.0  145950410.0   2100.0    12600.0  \n", "2024-01-04 14:59:53.420      150001442.0  145953420.0   2100.0    12558.0  \n", "2024-01-04 14:59:55.020      150001442.0  145955020.0  27400.0   177826.0  \n", "2024-01-04 14:59:56.040      150001442.0  145956040.0    600.0     3894.0  \n", "2024-01-04 14:59:56.430      150001442.0  145956430.0   2100.0    12558.0  \n", "\n", "[5219 rows x 11 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["order.loc[order['nprice']<=6.5]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## backtest"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### minbar"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Writing ./scripts/bt.bar.ob4_v2.py\n"]}], "source": ["%%writefile './scripts/bt.bar.ob4_v2.py'\n", "# coding = utf-8\n", "import os\n", "import copy\n", "import pandas as pd\n", "import xarray as xr\n", "\n", "from shennong.stk import bar\n", "from shennong.utils import consts\n", "from shennong.utils import trading_days\n", "\n", "from yrqtlib.utils import tools, logger\n", "\n", "def xr2df(xrdata: xr.<PERSON>y):\n", "\t\"\"\"convert 3D xarray DataArray to 2D DataFrame with MultiIndex\n", "\tArgs:\n", "\t\txrdata: 3D xarray DataArray to convert\n", "\n", "\tReturns:\n", "\t\tpd.DataFrame: 2D DataFrame with MultiIndex\n", "\t\"\"\"\n", "\ta, b, c = xrdata.shape\n", "\tdata = xrdata.to_numpy()\n", "\tdims = xrdata.dims\n", "\tkeys1 = xrdata[dims[0]].to_numpy()\n", "\tkeys2 = xrdata[dims[1]].to_numpy()\n", "\tkeys3 = xrdata[dims[2]].to_numpy()\n", "\tindex = pd.MultiIndex.from_product([keys1, keys2], names=[dims[0], dims[1]])\n", "\tcolumns = pd.Index(keys3, name=dims[2])\n", "\treturn pd.DataFrame(data.reshape(a*b, c), index=index, columns=columns)\n", "\n", "\n", "def calc_1day_ic(date: str, load_config: dict):\n", "\tfrom yrqtlib.utils import add_methods_to_pandas\n", "\tfeature_config = copy.deepcopy(load_config)\n", "\tfeature_config['start_datetime'] = date\n", "\tfeature_config['end_datetime'] = date\n", "\ttry:\n", "\t\txrfeature = bar.load(**feature_config)\n", "\texcept Exception as e:\n", "\t\tprint(date, e)\n", "\t\treturn None\n", "\tdffeature = xr2df(xrfeature.transpose('DATETIME', 'SYMBOL', 'KEY'))\n", "\n", "\tlabel_config = copy.deepcopy(load_config)\n", "\tlabel_config['start_datetime'] = date\n", "\tlabel_config['end_datetime'] = date\n", "\tlabel_config['key_list'] = {'label_folei_HFA_v4': ['I240_exc']}\n", "\tlabel_config['load_root'] = '/mnt/sda/NAS/ShareFolder/folei/'\n", "\tlabel_config['freq'] = '3second'\n", "\tlabel_config['ret'] = consts.RET_PANDAS\n", "\tdflabel = bar.load(**label_config)\n", "\tdflabel = dflabel.groupby('SYMBOL').shift(1)\n", "\n", "\tcommomn_index = dflabel.index.intersection(dffeature.index)\n", "\tdflabel = dflabel.loc[commomn_index]\n", "\tdffeature = dffeature.loc[commomn_index]\n", "\txy = pd.concat([dffeature, dflabel], axis=1)\n", "\tic = xy.DIY.corr(k=-1).loc['r2']\n", "\tic.to_parquet(os.path.join(ic_saveroot, f'{date}.parquet'))\n", "\tlogger.Logger.info(f'processing {date} finised!')\n", "\treturn ic\n", "\n", "feature_config = {\n", "\t'region_product': 'cn_ashare',\n", "\t'freq': '1minute',\n", "\t'symbol_list': None,\n", "\t'load_root': '/mnt/sda/NAS/ShareFolder/lishuanglin/features/',\n", "\t'verbose': False,\n", "\t'key_list': 'lsl__fcts_ob4_v2',\n", "}\n", "\n", "ic_saveroot = '/mnt/sda/NAS/ShareFolder/lishuanglin/features/backtest/cn_ashare/ic/minbar/lsl__fcts_ob4_v2/'\n", "os.makedirs(ic_saveroot, exist_ok=True)\n", "\n", "tools.update_key_config(path=f'{feature_config[\"load_root\"]}{feature_config[\"region_product\"]}/')\n", "\n", "td_days = trading_days.load('2018-01-02', '2025-04-14', 'cn', 'ashare')[:]\n", "tasks = [(date, feature_config) for date in td_days]\n", "ics = tools.parallel(calc_1day_ic, tasks, njobs=40, backend='loky', progress_bar=True, desc='calc_1day_ic')\n", "ics = {date: ic for date, ic in zip(td_days, ics) if ic is not None}\n", "ics = pd.concat(ics.values(), axis=1, keys=ics.keys()).<PERSON>.droplevel(1)\n", "# ics.to_parquet('../backtest/ic_fcts_ob2.parquet')"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e30fd3c1526545c6a2875493e369fd97", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1765 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "import pandas as pd\n", "from tqdm.auto import tqdm\n", "import matplotlib.pyplot as plt\n", "\n", "ic_saveroot = '/mnt/sda/NAS/ShareFolder/lishuanglin/features/backtest/cn_ashare/ic/minbar/lsl__fcts_ob4_v2/'\n", "dates = sorted([s.split('.')[0] for s in os.listdir(ic_saveroot) if s>='2018-01-02'])\n", "\n", "ics_bar = {date: pd.read_parquet(os.path.join(ic_saveroot, f'{date}.parquet')) for date in (tqdm(dates))}\n", "ic_bar = pd.concat(ics_bar.values(), axis=1, keys=ics_bar.keys()).<PERSON>.droplevel(1)\n", "fig, ax = plt.subplots(figsize=(10, 5))\n", "ic_bar.mean().sort_values(ascending=False).plot(ax=ax, grid=True,rot=20)\n", "ax.set_title('mean ic over I240_exc')\n", "ax.set_xlabel('feature')\n", "ax.set_ylabel('ic')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["lsl__fcts_ob4_v2__b2alv1_tvpct          0.070434\n", "lsl__fcts_ob4_v2__ctv_pct_tylv31        0.028807\n", "lsl__fcts_ob4_v2__batv_tylv31           0.028376\n", "lsl__fcts_ob4_v2__banum_gtlv31_tv       0.026752\n", "lsl__fcts_ob4_v2__cnum_pct_tylv31_tv    0.023106\n", "lsl__fcts_ob4_v2__banum_tylv31_tv       0.017412\n", "lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv    0.016747\n", "lsl__fcts_ob4_v2__bnum_gtlv31_tv        0.013913\n", "lsl__fcts_ob4_v2__batv_gtlv31           0.013876\n", "lsl__fcts_ob4_v2__btv_gtlv31            0.011438\n", "lsl__fcts_ob4_v2__cnum_gtlv31_tv        0.011294\n", "lsl__fcts_ob4_v2__ctv_pct_gtlv31        0.007161\n", "lsl__fcts_ob4_v2__ctv_gtlv31            0.005470\n", "lsl__fcts_ob4_v2__btv_tylv31           -0.019181\n", "lsl__fcts_ob4_v2__bnum_tylv31_tv       -0.019484\n", "lsl__fcts_ob4_v2__cnum_tylv31_tv       -0.022944\n", "lsl__fcts_ob4_v2__ctv_tylv31           -0.023029\n", "lsl__fcts_ob4_v2__a2blv1_tvpct         -0.065112\n", "dtype: float64"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["ic_bar.mean().sort_values(ascending=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### tick"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# %%writefile './scripts/bt.tk.ob4_v1.py'\n", "import os\n", "import h5py\n", "import pandas as pd\n", "import numpy as np\n", "from tqdm.auto import tqdm\n", "from shennong.stk import stream\n", "from shennong.utils import trading_days, symbol\n", "from yrqtlib.utils import tools, logger\n", "\n", "def load_1sym_tick(date:str, key_group_name:str, sym:str, load_root:str):\n", "\twith h5py.File(f'{load_root}/{key_group_name}/{date}.h5', 'r') as f:\n", "\t\tif sym not in f: return None\n", "\t\trows = f[sym]['ROW'][:].astype('datetime64[ns]')\n", "\t\tif 'label' not in key_group_name:\n", "\t\t\trows = rows + pd.<PERSON><PERSON><PERSON>(hours=8)\n", "\t\tcols = f[sym]['COLUMN'][:].astype(np.str_)\n", "\t\tvals = f[sym]['DATA'][:]\n", "\t\tdf = pd.DataFrame(vals, index=rows, columns=cols)\n", "\t\treturn df.drop_duplicates()\n", "\n", "def calc_1sym(date:str, key_group_name:str, sym:str, feature_root:str, label_root:str):\n", "\tx = load_1sym_tick(date, key_group_name, sym, feature_root)\n", "\ty = load_1sym_tick(date, 'label_folei_second_v3', sym, label_root)\n", "\tif x is None or y is None: return None\n", "\tx.index = x.index.ceil('3s')\n", "\tcindex = x.index.intersection(y.index)\n", "\tx, y = x.loc[cindex], y.loc[cindex].iloc[:,:8]\n", "\tx = x.loc[~x.index.duplicated(keep='first')]\n", "\ty = y.loc[~y.index.duplicated(keep='first')]\n", "\treturn pd.concat([x, y], axis=1).corr().iloc[:-8,-8:]\n", "\n", "def calc_1day(date:str, key_group_name:str, feature_root:str, label_root:str):\n", "\tcsyms = symbol.load('cn', 'ashare')\n", "\ttasks = [(date, key_group_name, sym, feature_root, label_root) for sym in csyms]\n", "\tics = tools.parallel(calc_1sym, tasks, njobs=1, backend='threading', progress_bar=False)\n", "\tics = {sym: ic for sym, ic in zip(csyms, ics) if ic is not None}\n", "\treturn pd.concat(ics, names=['symbol','feature'])\n", "\n", "\n", "\n", "def run_task(date:str, key_group_name:str, feature_root:str, label_root:str):\n", "\tif os.path.exists(os.path.join(ic_saveroot, f'{date}.parquet')):\n", "\t\tlogger.Logger.info(f'{date} already exists!')\n", "\t\treturn None\n", "\tic = calc_1day(date, key_group_name, feature_root, label_root)\n", "\tic.to_parquet(os.path.join(ic_saveroot, f'{date}.parquet'))\n", "\tlogger.Logger.progress(date, td_days, 40, f'{td_days.index(date)+1}/{len(td_days)}')\n", "\n", "key_group_name = 'lsl__fcts_ob4_v1'\n", "td_days = trading_days.load('2018-01-02', '2025-04-14', 'cn', 'ashare')[:]\n", "label_root = '/mnt/sda/NAS/AllData/cn_ashare/tick/'\n", "feature_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/features/cn_ashare/tick/'\n", "# ic_saveroot = f'/mnt/sda/NAS/ShareFolder/lishuanglin/features/backtest/cn_ashare/ic/tick/{key_group_name}/'\n", "# os.makedirs(ic_saveroot, exist_ok=True)\n", "# tasks = [(date, key_group_name, feature_root, label_root) for date in td_days]\n", "# _ = tools.parallel(run_task, tasks, njobs=40, backend='loky', progress_bar=True)\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["date = '2024-01-02'\n", "sym = '600000.SH'\n", "x = load_1sym_tick(date, key_group_name, sym, feature_root)\n", "y = load_1sym_tick(date, 'label_folei_second_v3', sym, label_root)\n", "# x.index = x.index.ceil('3s')\n", "# cindex = x.index.intersection(y.index)\n", "# x, y = x.loc[cindex], y.loc[cindex].iloc[:,:8]\n", "# x = x.loc[~x.index.duplicated(keep='first')]\n", "# y = y.loc[~y.index.duplicated(keep='first')]\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>lsl__fcts_ob4_v1__bnum_tylv31_tv</th>\n", "      <th>lsl__fcts_ob4_v1__btv_tylv31</th>\n", "      <th>lsl__fcts_ob4_v1__cnum_tylv31_tv</th>\n", "      <th>lsl__fcts_ob4_v1__ctv_tylv31</th>\n", "      <th>lsl__fcts_ob4_v1__banum_tylv31_tv</th>\n", "      <th>lsl__fcts_ob4_v1__batv_tylv31</th>\n", "      <th>lsl__fcts_ob4_v1__cnum_pct_tylv31_tv</th>\n", "      <th>lsl__fcts_ob4_v1__ctv_pct_tylv31</th>\n", "      <th>lsl__fcts_ob4_v1__bnum_gtlv31_tv</th>\n", "      <th>lsl__fcts_ob4_v1__btv_gtlv31</th>\n", "      <th>lsl__fcts_ob4_v1__cnum_gtlv31_tv</th>\n", "      <th>lsl__fcts_ob4_v1__ctv_gtlv31</th>\n", "      <th>lsl__fcts_ob4_v1__banum_gtlv31_tv</th>\n", "      <th>lsl__fcts_ob4_v1__batv_gtlv31</th>\n", "      <th>lsl__fcts_ob4_v1__cnum_pct_gtlv31_tv</th>\n", "      <th>lsl__fcts_ob4_v1__ctv_pct_gtlv31</th>\n", "      <th>lsl__fcts_ob4_v1__b2alv1_tvpct</th>\n", "      <th>lsl__fcts_ob4_v1__a2blv1_tvpct</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-01-02 09:30:00.781</th>\n", "      <td>3.510009</td>\n", "      <td>7.502216</td>\n", "      <td>3.082786</td>\n", "      <td>6.248320</td>\n", "      <td>3.248464</td>\n", "      <td>-7.298506</td>\n", "      <td>0.110562</td>\n", "      <td>0.009199</td>\n", "      <td>0.626963</td>\n", "      <td>7.102091</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.626963</td>\n", "      <td>7.102091</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.424374e-01</td>\n", "      <td>1.561552e-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-02 09:30:03.807</th>\n", "      <td>2.225325</td>\n", "      <td>6.337946</td>\n", "      <td>3.082786</td>\n", "      <td>6.248320</td>\n", "      <td>2.164373</td>\n", "      <td>6.076284</td>\n", "      <td>1.103222</td>\n", "      <td>0.232036</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>6.691433e-03</td>\n", "      <td>2.821112e-03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-02 09:30:06.770</th>\n", "      <td>2.025345</td>\n", "      <td>6.299385</td>\n", "      <td>3.085648</td>\n", "      <td>6.277363</td>\n", "      <td>1.343317</td>\n", "      <td>-4.859847</td>\n", "      <td>1.106049</td>\n", "      <td>0.196004</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>5.746865e-03</td>\n", "      <td>5.482915e-03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-02 09:30:09.781</th>\n", "      <td>2.198674</td>\n", "      <td>6.234149</td>\n", "      <td>3.087782</td>\n", "      <td>6.281853</td>\n", "      <td>1.880889</td>\n", "      <td>5.658143</td>\n", "      <td>1.009179</td>\n", "      <td>0.263065</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>4.626784e-03</td>\n", "      <td>3.088555e-03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-02 09:30:12.756</th>\n", "      <td>2.049253</td>\n", "      <td>6.182534</td>\n", "      <td>3.092019</td>\n", "      <td>6.334275</td>\n", "      <td>1.869311</td>\n", "      <td>5.278804</td>\n", "      <td>1.212838</td>\n", "      <td>0.303217</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3.844012e-03</td>\n", "      <td>3.017187e-03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-02 14:56:54.761</th>\n", "      <td>0.909722</td>\n", "      <td>5.172661</td>\n", "      <td>4.205150</td>\n", "      <td>8.496474</td>\n", "      <td>-1.415614</td>\n", "      <td>-6.143001</td>\n", "      <td>2.862728</td>\n", "      <td>2.570221</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>4.734767e-07</td>\n", "      <td>5.174974e-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-02 14:56:57.807</th>\n", "      <td>1.380963</td>\n", "      <td>5.561483</td>\n", "      <td>4.206178</td>\n", "      <td>8.497318</td>\n", "      <td>-0.382776</td>\n", "      <td>-4.805378</td>\n", "      <td>2.791206</td>\n", "      <td>2.899337</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.158880e-06</td>\n", "      <td>1.439786e-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-02 14:57:00.757</th>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>4.206232</td>\n", "      <td>8.497446</td>\n", "      <td>-0.626963</td>\n", "      <td>-4.973109</td>\n", "      <td>3.729111</td>\n", "      <td>3.825358</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000e+00</td>\n", "      <td>3.160518e-07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-02 14:57:03.748</th>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>4.206232</td>\n", "      <td>8.497446</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>4.206232</td>\n", "      <td>8.497446</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-02 15:00:03.777</th>\n", "      <td>2.064490</td>\n", "      <td>6.530728</td>\n", "      <td>4.206232</td>\n", "      <td>8.497446</td>\n", "      <td>-1.845187</td>\n", "      <td>-5.815659</td>\n", "      <td>2.024427</td>\n", "      <td>1.926824</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.078626e-05</td>\n", "      <td>1.359899e-05</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4554 rows × 18 columns</p>\n", "</div>"], "text/plain": ["                         lsl__fcts_ob4_v1__bnum_tylv31_tv  \\\n", "2024-01-02 09:30:00.781                          3.510009   \n", "2024-01-02 09:30:03.807                          2.225325   \n", "2024-01-02 09:30:06.770                          2.025345   \n", "2024-01-02 09:30:09.781                          2.198674   \n", "2024-01-02 09:30:12.756                          2.049253   \n", "...                                                   ...   \n", "2024-01-02 14:56:54.761                          0.909722   \n", "2024-01-02 14:56:57.807                          1.380963   \n", "2024-01-02 14:57:00.757                          0.000000   \n", "2024-01-02 14:57:03.748                          0.000000   \n", "2024-01-02 15:00:03.777                          2.064490   \n", "\n", "                         lsl__fcts_ob4_v1__btv_tylv31  \\\n", "2024-01-02 09:30:00.781                      7.502216   \n", "2024-01-02 09:30:03.807                      6.337946   \n", "2024-01-02 09:30:06.770                      6.299385   \n", "2024-01-02 09:30:09.781                      6.234149   \n", "2024-01-02 09:30:12.756                      6.182534   \n", "...                                               ...   \n", "2024-01-02 14:56:54.761                      5.172661   \n", "2024-01-02 14:56:57.807                      5.561483   \n", "2024-01-02 14:57:00.757                      0.000000   \n", "2024-01-02 14:57:03.748                      0.000000   \n", "2024-01-02 15:00:03.777                      6.530728   \n", "\n", "                         lsl__fcts_ob4_v1__cnum_tylv31_tv  \\\n", "2024-01-02 09:30:00.781                          3.082786   \n", "2024-01-02 09:30:03.807                          3.082786   \n", "2024-01-02 09:30:06.770                          3.085648   \n", "2024-01-02 09:30:09.781                          3.087782   \n", "2024-01-02 09:30:12.756                          3.092019   \n", "...                                                   ...   \n", "2024-01-02 14:56:54.761                          4.205150   \n", "2024-01-02 14:56:57.807                          4.206178   \n", "2024-01-02 14:57:00.757                          4.206232   \n", "2024-01-02 14:57:03.748                          4.206232   \n", "2024-01-02 15:00:03.777                          4.206232   \n", "\n", "                         lsl__fcts_ob4_v1__ctv_tylv31  \\\n", "2024-01-02 09:30:00.781                      6.248320   \n", "2024-01-02 09:30:03.807                      6.248320   \n", "2024-01-02 09:30:06.770                      6.277363   \n", "2024-01-02 09:30:09.781                      6.281853   \n", "2024-01-02 09:30:12.756                      6.334275   \n", "...                                               ...   \n", "2024-01-02 14:56:54.761                      8.496474   \n", "2024-01-02 14:56:57.807                      8.497318   \n", "2024-01-02 14:57:00.757                      8.497446   \n", "2024-01-02 14:57:03.748                      8.497446   \n", "2024-01-02 15:00:03.777                      8.497446   \n", "\n", "                         lsl__fcts_ob4_v1__banum_tylv31_tv  \\\n", "2024-01-02 09:30:00.781                           3.248464   \n", "2024-01-02 09:30:03.807                           2.164373   \n", "2024-01-02 09:30:06.770                           1.343317   \n", "2024-01-02 09:30:09.781                           1.880889   \n", "2024-01-02 09:30:12.756                           1.869311   \n", "...                                                    ...   \n", "2024-01-02 14:56:54.761                          -1.415614   \n", "2024-01-02 14:56:57.807                          -0.382776   \n", "2024-01-02 14:57:00.757                          -0.626963   \n", "2024-01-02 14:57:03.748                           0.000000   \n", "2024-01-02 15:00:03.777                          -1.845187   \n", "\n", "                         lsl__fcts_ob4_v1__batv_tylv31  \\\n", "2024-01-02 09:30:00.781                      -7.298506   \n", "2024-01-02 09:30:03.807                       6.076284   \n", "2024-01-02 09:30:06.770                      -4.859847   \n", "2024-01-02 09:30:09.781                       5.658143   \n", "2024-01-02 09:30:12.756                       5.278804   \n", "...                                                ...   \n", "2024-01-02 14:56:54.761                      -6.143001   \n", "2024-01-02 14:56:57.807                      -4.805378   \n", "2024-01-02 14:57:00.757                      -4.973109   \n", "2024-01-02 14:57:03.748                       0.000000   \n", "2024-01-02 15:00:03.777                      -5.815659   \n", "\n", "                         lsl__fcts_ob4_v1__cnum_pct_tylv31_tv  \\\n", "2024-01-02 09:30:00.781                              0.110562   \n", "2024-01-02 09:30:03.807                              1.103222   \n", "2024-01-02 09:30:06.770                              1.106049   \n", "2024-01-02 09:30:09.781                              1.009179   \n", "2024-01-02 09:30:12.756                              1.212838   \n", "...                                                       ...   \n", "2024-01-02 14:56:54.761                              2.862728   \n", "2024-01-02 14:56:57.807                              2.791206   \n", "2024-01-02 14:57:00.757                              3.729111   \n", "2024-01-02 14:57:03.748                              4.206232   \n", "2024-01-02 15:00:03.777                              2.024427   \n", "\n", "                         lsl__fcts_ob4_v1__ctv_pct_tylv31  \\\n", "2024-01-02 09:30:00.781                          0.009199   \n", "2024-01-02 09:30:03.807                          0.232036   \n", "2024-01-02 09:30:06.770                          0.196004   \n", "2024-01-02 09:30:09.781                          0.263065   \n", "2024-01-02 09:30:12.756                          0.303217   \n", "...                                                   ...   \n", "2024-01-02 14:56:54.761                          2.570221   \n", "2024-01-02 14:56:57.807                          2.899337   \n", "2024-01-02 14:57:00.757                          3.825358   \n", "2024-01-02 14:57:03.748                          8.497446   \n", "2024-01-02 15:00:03.777                          1.926824   \n", "\n", "                         lsl__fcts_ob4_v1__bnum_gtlv31_tv  \\\n", "2024-01-02 09:30:00.781                          0.626963   \n", "2024-01-02 09:30:03.807                          0.000000   \n", "2024-01-02 09:30:06.770                          0.000000   \n", "2024-01-02 09:30:09.781                          0.000000   \n", "2024-01-02 09:30:12.756                          0.000000   \n", "...                                                   ...   \n", "2024-01-02 14:56:54.761                          0.000000   \n", "2024-01-02 14:56:57.807                          0.000000   \n", "2024-01-02 14:57:00.757                          0.000000   \n", "2024-01-02 14:57:03.748                          0.000000   \n", "2024-01-02 15:00:03.777                          0.000000   \n", "\n", "                         lsl__fcts_ob4_v1__btv_gtlv31  \\\n", "2024-01-02 09:30:00.781                      7.102091   \n", "2024-01-02 09:30:03.807                      0.000000   \n", "2024-01-02 09:30:06.770                      0.000000   \n", "2024-01-02 09:30:09.781                      0.000000   \n", "2024-01-02 09:30:12.756                      0.000000   \n", "...                                               ...   \n", "2024-01-02 14:56:54.761                      0.000000   \n", "2024-01-02 14:56:57.807                      0.000000   \n", "2024-01-02 14:57:00.757                      0.000000   \n", "2024-01-02 14:57:03.748                      0.000000   \n", "2024-01-02 15:00:03.777                      0.000000   \n", "\n", "                         lsl__fcts_ob4_v1__cnum_gtlv31_tv  \\\n", "2024-01-02 09:30:00.781                               0.0   \n", "2024-01-02 09:30:03.807                               0.0   \n", "2024-01-02 09:30:06.770                               0.0   \n", "2024-01-02 09:30:09.781                               0.0   \n", "2024-01-02 09:30:12.756                               0.0   \n", "...                                                   ...   \n", "2024-01-02 14:56:54.761                               0.0   \n", "2024-01-02 14:56:57.807                               0.0   \n", "2024-01-02 14:57:00.757                               0.0   \n", "2024-01-02 14:57:03.748                               0.0   \n", "2024-01-02 15:00:03.777                               0.0   \n", "\n", "                         lsl__fcts_ob4_v1__ctv_gtlv31  \\\n", "2024-01-02 09:30:00.781                           0.0   \n", "2024-01-02 09:30:03.807                           0.0   \n", "2024-01-02 09:30:06.770                           0.0   \n", "2024-01-02 09:30:09.781                           0.0   \n", "2024-01-02 09:30:12.756                           0.0   \n", "...                                               ...   \n", "2024-01-02 14:56:54.761                           0.0   \n", "2024-01-02 14:56:57.807                           0.0   \n", "2024-01-02 14:57:00.757                           0.0   \n", "2024-01-02 14:57:03.748                           0.0   \n", "2024-01-02 15:00:03.777                           0.0   \n", "\n", "                         lsl__fcts_ob4_v1__banum_gtlv31_tv  \\\n", "2024-01-02 09:30:00.781                           0.626963   \n", "2024-01-02 09:30:03.807                           0.000000   \n", "2024-01-02 09:30:06.770                           0.000000   \n", "2024-01-02 09:30:09.781                           0.000000   \n", "2024-01-02 09:30:12.756                           0.000000   \n", "...                                                    ...   \n", "2024-01-02 14:56:54.761                           0.000000   \n", "2024-01-02 14:56:57.807                           0.000000   \n", "2024-01-02 14:57:00.757                           0.000000   \n", "2024-01-02 14:57:03.748                           0.000000   \n", "2024-01-02 15:00:03.777                           0.000000   \n", "\n", "                         lsl__fcts_ob4_v1__batv_gtlv31  \\\n", "2024-01-02 09:30:00.781                       7.102091   \n", "2024-01-02 09:30:03.807                       0.000000   \n", "2024-01-02 09:30:06.770                       0.000000   \n", "2024-01-02 09:30:09.781                       0.000000   \n", "2024-01-02 09:30:12.756                       0.000000   \n", "...                                                ...   \n", "2024-01-02 14:56:54.761                       0.000000   \n", "2024-01-02 14:56:57.807                       0.000000   \n", "2024-01-02 14:57:00.757                       0.000000   \n", "2024-01-02 14:57:03.748                       0.000000   \n", "2024-01-02 15:00:03.777                       0.000000   \n", "\n", "                         lsl__fcts_ob4_v1__cnum_pct_gtlv31_tv  \\\n", "2024-01-02 09:30:00.781                                   0.0   \n", "2024-01-02 09:30:03.807                                   0.0   \n", "2024-01-02 09:30:06.770                                   0.0   \n", "2024-01-02 09:30:09.781                                   0.0   \n", "2024-01-02 09:30:12.756                                   0.0   \n", "...                                                       ...   \n", "2024-01-02 14:56:54.761                                   0.0   \n", "2024-01-02 14:56:57.807                                   0.0   \n", "2024-01-02 14:57:00.757                                   0.0   \n", "2024-01-02 14:57:03.748                                   0.0   \n", "2024-01-02 15:00:03.777                                   0.0   \n", "\n", "                         lsl__fcts_ob4_v1__ctv_pct_gtlv31  \\\n", "2024-01-02 09:30:00.781                               0.0   \n", "2024-01-02 09:30:03.807                               0.0   \n", "2024-01-02 09:30:06.770                               0.0   \n", "2024-01-02 09:30:09.781                               0.0   \n", "2024-01-02 09:30:12.756                               0.0   \n", "...                                                   ...   \n", "2024-01-02 14:56:54.761                               0.0   \n", "2024-01-02 14:56:57.807                               0.0   \n", "2024-01-02 14:57:00.757                               0.0   \n", "2024-01-02 14:57:03.748                               0.0   \n", "2024-01-02 15:00:03.777                               0.0   \n", "\n", "                         lsl__fcts_ob4_v1__b2alv1_tvpct  \\\n", "2024-01-02 09:30:00.781                    1.424374e-01   \n", "2024-01-02 09:30:03.807                    6.691433e-03   \n", "2024-01-02 09:30:06.770                    5.746865e-03   \n", "2024-01-02 09:30:09.781                    4.626784e-03   \n", "2024-01-02 09:30:12.756                    3.844012e-03   \n", "...                                                 ...   \n", "2024-01-02 14:56:54.761                    4.734767e-07   \n", "2024-01-02 14:56:57.807                    1.158880e-06   \n", "2024-01-02 14:57:00.757                    0.000000e+00   \n", "2024-01-02 14:57:03.748                    0.000000e+00   \n", "2024-01-02 15:00:03.777                    1.078626e-05   \n", "\n", "                         lsl__fcts_ob4_v1__a2blv1_tvpct  \n", "2024-01-02 09:30:00.781                    1.561552e-01  \n", "2024-01-02 09:30:03.807                    2.821112e-03  \n", "2024-01-02 09:30:06.770                    5.482915e-03  \n", "2024-01-02 09:30:09.781                    3.088555e-03  \n", "2024-01-02 09:30:12.756                    3.017187e-03  \n", "...                                                 ...  \n", "2024-01-02 14:56:54.761                    5.174974e-06  \n", "2024-01-02 14:56:57.807                    1.439786e-06  \n", "2024-01-02 14:57:00.757                    3.160518e-07  \n", "2024-01-02 14:57:03.748                    0.000000e+00  \n", "2024-01-02 15:00:03.777                    1.359899e-05  \n", "\n", "[4554 rows x 18 columns]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["import datetime\n", "x.drop_duplicates().loc[x.index.time>=datetime.time(9, 30, 0)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # timestamp-ntime\n", "# data_s['ntime'] = data_s['ntime'][~data_s['ntime'].index.duplicated()]\n", "# ntime2timestamp = pd.Series(data_s['ntime'].index, index=pd.to_datetime(data_s['ntime'].ntime)).sort_index()\n", "# ntime2timestamp = ntime2timestamp.loc[date+\" 09:30:00\":date+\" 14:50:00\"].sort_values()\n", "# timestamp2ntime = pd.to_datetime(data_s['ntime'].ntime)\n", "# timestamp2ntime = timestamp2ntime.reindex(ntime2timestamp.values)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "39526b8d96a1474f8d85cd65789df807", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1765 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>label_folei_ret_sec__30</th>\n", "      <th>label_folei_ret_sec__60</th>\n", "      <th>label_folei_ret_sec__120</th>\n", "      <th>label_folei_ret_sec__300</th>\n", "      <th>label_folei_ret_sec__600</th>\n", "      <th>label_folei_ret_sec__900</th>\n", "      <th>label_folei_ret_sec__1200</th>\n", "      <th>label_folei_ret_sec__1800</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th>feature</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2018-01-02</th>\n", "      <th>lsl__fcts_ob4_v1__bnum_tylv31_tv</th>\n", "      <td>0.120220</td>\n", "      <td>0.112900</td>\n", "      <td>0.093243</td>\n", "      <td>0.064191</td>\n", "      <td>0.039484</td>\n", "      <td>0.027978</td>\n", "      <td>0.027315</td>\n", "      <td>0.021225</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_ob4_v1__btv_tylv31</th>\n", "      <td>0.104561</td>\n", "      <td>0.098894</td>\n", "      <td>0.085360</td>\n", "      <td>0.061934</td>\n", "      <td>0.042649</td>\n", "      <td>0.033870</td>\n", "      <td>0.032319</td>\n", "      <td>0.028743</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_ob4_v1__cnum_tylv31_tv</th>\n", "      <td>0.017647</td>\n", "      <td>0.029103</td>\n", "      <td>0.040610</td>\n", "      <td>0.052283</td>\n", "      <td>0.086571</td>\n", "      <td>0.071464</td>\n", "      <td>0.035104</td>\n", "      <td>0.045498</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_ob4_v1__ctv_tylv31</th>\n", "      <td>0.015146</td>\n", "      <td>0.026467</td>\n", "      <td>0.039034</td>\n", "      <td>0.046877</td>\n", "      <td>0.080848</td>\n", "      <td>0.068717</td>\n", "      <td>0.032137</td>\n", "      <td>0.041589</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_ob4_v1__banum_tylv31_tv</th>\n", "      <td>0.172753</td>\n", "      <td>0.168185</td>\n", "      <td>0.147297</td>\n", "      <td>0.121295</td>\n", "      <td>0.111343</td>\n", "      <td>0.102576</td>\n", "      <td>0.099490</td>\n", "      <td>0.103982</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                              label_folei_ret_sec__30  \\\n", "date       feature                                                      \n", "2018-01-02 lsl__fcts_ob4_v1__bnum_tylv31_tv                  0.120220   \n", "           lsl__fcts_ob4_v1__btv_tylv31                      0.104561   \n", "           lsl__fcts_ob4_v1__cnum_tylv31_tv                  0.017647   \n", "           lsl__fcts_ob4_v1__ctv_tylv31                      0.015146   \n", "           lsl__fcts_ob4_v1__banum_tylv31_tv                 0.172753   \n", "\n", "                                              label_folei_ret_sec__60  \\\n", "date       feature                                                      \n", "2018-01-02 lsl__fcts_ob4_v1__bnum_tylv31_tv                  0.112900   \n", "           lsl__fcts_ob4_v1__btv_tylv31                      0.098894   \n", "           lsl__fcts_ob4_v1__cnum_tylv31_tv                  0.029103   \n", "           lsl__fcts_ob4_v1__ctv_tylv31                      0.026467   \n", "           lsl__fcts_ob4_v1__banum_tylv31_tv                 0.168185   \n", "\n", "                                              label_folei_ret_sec__120  \\\n", "date       feature                                                       \n", "2018-01-02 lsl__fcts_ob4_v1__bnum_tylv31_tv                   0.093243   \n", "           lsl__fcts_ob4_v1__btv_tylv31                       0.085360   \n", "           lsl__fcts_ob4_v1__cnum_tylv31_tv                   0.040610   \n", "           lsl__fcts_ob4_v1__ctv_tylv31                       0.039034   \n", "           lsl__fcts_ob4_v1__banum_tylv31_tv                  0.147297   \n", "\n", "                                              label_folei_ret_sec__300  \\\n", "date       feature                                                       \n", "2018-01-02 lsl__fcts_ob4_v1__bnum_tylv31_tv                   0.064191   \n", "           lsl__fcts_ob4_v1__btv_tylv31                       0.061934   \n", "           lsl__fcts_ob4_v1__cnum_tylv31_tv                   0.052283   \n", "           lsl__fcts_ob4_v1__ctv_tylv31                       0.046877   \n", "           lsl__fcts_ob4_v1__banum_tylv31_tv                  0.121295   \n", "\n", "                                              label_folei_ret_sec__600  \\\n", "date       feature                                                       \n", "2018-01-02 lsl__fcts_ob4_v1__bnum_tylv31_tv                   0.039484   \n", "           lsl__fcts_ob4_v1__btv_tylv31                       0.042649   \n", "           lsl__fcts_ob4_v1__cnum_tylv31_tv                   0.086571   \n", "           lsl__fcts_ob4_v1__ctv_tylv31                       0.080848   \n", "           lsl__fcts_ob4_v1__banum_tylv31_tv                  0.111343   \n", "\n", "                                              label_folei_ret_sec__900  \\\n", "date       feature                                                       \n", "2018-01-02 lsl__fcts_ob4_v1__bnum_tylv31_tv                   0.027978   \n", "           lsl__fcts_ob4_v1__btv_tylv31                       0.033870   \n", "           lsl__fcts_ob4_v1__cnum_tylv31_tv                   0.071464   \n", "           lsl__fcts_ob4_v1__ctv_tylv31                       0.068717   \n", "           lsl__fcts_ob4_v1__banum_tylv31_tv                  0.102576   \n", "\n", "                                              label_folei_ret_sec__1200  \\\n", "date       feature                                                        \n", "2018-01-02 lsl__fcts_ob4_v1__bnum_tylv31_tv                    0.027315   \n", "           lsl__fcts_ob4_v1__btv_tylv31                        0.032319   \n", "           lsl__fcts_ob4_v1__cnum_tylv31_tv                    0.035104   \n", "           lsl__fcts_ob4_v1__ctv_tylv31                        0.032137   \n", "           lsl__fcts_ob4_v1__banum_tylv31_tv                   0.099490   \n", "\n", "                                              label_folei_ret_sec__1800  \n", "date       feature                                                       \n", "2018-01-02 lsl__fcts_ob4_v1__bnum_tylv31_tv                    0.021225  \n", "           lsl__fcts_ob4_v1__btv_tylv31                        0.028743  \n", "           lsl__fcts_ob4_v1__cnum_tylv31_tv                    0.045498  \n", "           lsl__fcts_ob4_v1__ctv_tylv31                        0.041589  \n", "           lsl__fcts_ob4_v1__banum_tylv31_tv                   0.103982  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "import numpy as np\n", "import pandas as pd\n", "from tqdm.auto import tqdm\n", "\n", "ic_saveroot = '/mnt/sda/NAS/ShareFolder/lishuanglin/features/backtest/cn_ashare/ic/tick/lsl__fcts_ob4_v1/'\n", "dates = sorted([s.split('.')[0] for s in os.listdir(ic_saveroot) if s>='2018-01-02'])\n", "\n", "ics_tk = {date: pd.read_parquet(os.path.join(ic_saveroot, f'{date}.parquet')) for date in (tqdm(dates))}\n", "\n", "# ic_saveroot = '/mnt/sda/NAS/ShareFolder/lishuanglin/features/backtest/cn_ashare/ic/tick/lsl__fcts_ob2/'\n", "# pd.read_parquet(f'{ic_saveroot}/2018-02-12.parquet')\n", "\n", "ic_tk = pd.concat(ics_tk,names=['date'])\n", "ic_tk[np.isinf(ic_tk)] = np.nan\n", "ic_tk = ic_tk.groupby(['date', 'feature'],sort=False).mean()\n", "ic_tk.head()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x540 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from yrqtlib.utils import add_methods_to_pandas\n", "res_tk = ic_tk.groupby('feature',sort=False).mean()\n", "res_tk.DIY.clip_columns('label_folei_ret_').DIY.heatmap(low=-0.03,high=0.03)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "uvbase", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}