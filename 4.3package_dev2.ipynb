{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# backtest"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## loader"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/Loader.py'\n", "from shennong.utils import trading_days, symbol\n", "import polars as pl\n", "import pandas as pd, numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "import os, h5py\n", "from joblib import Parallel, delayed\n", "\n", "class Loader:\n", "    def __init__(self, **kwargs):\n", "        self.__dict__.update(kwargs)\n", "\n", "    def load_style_indus(self, barr_product: str, \n", "                         feature_index: pd.MultiIndex, \n", "                         barrainfo_path=None):\n", "        dates = sorted(feature_index.get_level_values('date').unique().tolist())\n", "        syms = sorted(feature_index.get_level_values('symbol').unique().tolist())\n", "        localids = [s.split('.')[0] for s in syms]\n", "        maps = {localid: sym for localid, sym in zip(localids, syms)}\n", "        start_date, end_date = dates[0], dates[-1]\n", "\n", "        from joblib import Parallel, delayed\n", "        import warnings\n", "        warnings.filterwarnings('ignore')\n", "\n", "        barrainfo_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/'\n", "        id_map_root = '/mnt/sda/NAS/ShareFolder/xielan/global/id_mapping/'\n", "\n", "        if barrainfo_path is None:\n", "            barrainfo_path = f'{barrainfo_root}{barr_product}/'\n", "\n", "        def load_1day(date):\n", "            df_info = pd.read_parquet(f'{barrainfo_path}{date}.parquet')\n", "            df_style_indus = df_info.loc[df_info['LocalID'].isin(localids), usecols+['LocalID']]\n", "            df_style_indus['LocalID'] = df_style_indus['LocalID'].apply(lambda x: maps[x])\n", "            df_style_indus['date'] = date\n", "            df_style_indus.rename(columns={'LocalID':'symbol'},inplace=True)\n", "            df_style_indus = df_style_indus.set_index(['date','symbol']).drop_duplicates(keep='first')\n", "            # print(f'loading df_style, {date}', end='\\r')\n", "            return df_style_indus.loc[df_style_indus.index.isin(feature_index)]\n", "\n", "        dates = sorted([s.split('.')[0] for s in os.listdir(barrainfo_path)])\n", "        dates = [d for d in dates if d >= start_date and d <= end_date]\n", "        df_info = pd.read_parquet(f'{barrainfo_path}{dates[0]}.parquet')\n", "        usecols = df_info.columns[df_info.columns.str.startswith(f'{barr_product}')].tolist() + ['indcell']\n", "\n", "        num_workers = max(len(dates) // 50, 1)\n", "        with Parallel(n_jobs=num_workers, backend='loky') as para:\n", "            df_info = para(delayed(load_1day)(date) for date in dates)\n", "        df_info = pd.concat(df_info, axis=0).dropna(how='all')\n", "        df_info = df_info[~df_info.index.duplicated(keep='first')]\n", "        return (df_info, df_info[['indcell']])\n", "\n", "\n", "    def load_label(self, barra_product: str,\n", "                   start_date: str,\n", "                   end_date: str,\n", "                   label_list=['H0','H1','H1_5'],\n", "                   usecols=['SpecificReturn','DlyReturn%']):\n", "        import warnings\n", "        from joblib import Parallel, delayed\n", "        import re\n", "        import itertools\n", "        warnings.filterwarnings('ignore')\n", "        barrainfo_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/'\n", "        usecols_info = ['LocalID','OpMIC']+usecols\n", "\n", "        dates = sorted([s.split('.')[0] for s in os.listdir(f'{barrainfo_root}{barra_product}/')])\n", "        cdates = [d for d in dates if d >= start_date and d <= end_date]\n", "        start, end = max(dates.index(cdates[0])-5, 0), dates.index(cdates[-1])+5\n", "        dates = dates[start:end]\n", "\n", "        def load_1day(date):\n", "            if not os.path.exists(f'{barrainfo_root}{barra_product}/{date}.parquet'):\n", "                return None\n", "            df_info = pd.read_parquet(f'{barrainfo_root}{barra_product}/{date}.parquet')[usecols_info]\n", "            df_info.dropna(subset=['OpMIC','LocalID'],inplace=True)\n", "            df_info['symbol'] = df_info['LocalID'] + '.' + df_info['OpMIC']\n", "            df_info['date'] = date\n", "            df_info = df_info[['date','symbol']+usecols]\n", "            # print(f'loading label {date}', end='\\r')\n", "            return df_info.set_index(['date','symbol']).drop_duplicates()\n", "\n", "        num_workers = max(len(dates) // 40, 1)\n", "        with Parallel(n_jobs=num_workers, backend='loky') as para:\n", "            labels = para(delayed(load_1day)(date) for date in dates)\n", "\n", "        df_label = pd.concat(labels) #.rename(columns={usecol:'H0'})\n", "        df_label = df_label[~df_label.index.duplicated(keep='first')]\n", "        label_list=[i for i in label_list if bool(re.match(r'^H\\d+(?:_\\d+)?$',i))] # label必须满足H+number或H+numbe_number格式\n", "\n", "        def label_func(df,label,usecol):# 对usecols进行label转换\n", "            if bool(re.match(r'H(\\d+)$', label)): # 如果label是H0则自动补齐为H0_0\n", "                match = re.match(r'H(\\d+)$', label)\n", "                new_label = f\"H{match.group(1)}_{match.group(1)}\"\n", "            else:new_label = label\n", "            match = re.match(r'^H(\\d+)_(\\d+)$', new_label) # 识别buy_lag,start_lag,得到window和lag\n", "            window = int(match.group(2))-int(match.group(1))+1\n", "            lag = -int(match.group(2))\n", "            result = df.groupby(level='symbol').apply(lambda x: x.rolling(window,min_periods=1).mean()\\\n", "                .shift(lag)).droplevel(-1).swaplevel('symbol','date')\n", "            result.name = f'{usecol}_{label}'\n", "            return result\n", "        \n", "        grids = list(itertools.product(label_list, usecols))\n", "        with Parallel(n_jobs=len(grids), backend='loky') as para:\n", "            result_label = para(delayed(label_func)(df_label[i[1]],i[0],i[1]) for i in grids)\n", "        result_label=pd.concat(result_label,axis=1).reindex(df_label.index)\n", "\n", "        return result_label.loc[start_date:end_date]\n", "\n", "\n", "    def load(self, load_path: str, \n", "             start_date: str, \n", "             end_date: str, \n", "             *, \n", "             file_format='.csv', \n", "             pred=False,\n", "             feature_list=None):\n", "        from joblib import Parallel, delayed\n", "        import warnings\n", "        warnings.filterwarnings('ignore')\n", "        \n", "        def load_1day(date):\n", "            if not os.path.exists(f'{load_path}/{date}{file_format}'): \n", "                return None\n", "            if file_format == '.csv':\n", "                feature_1day = pd.read_csv(f'{load_path}/{date}.csv')\n", "            elif file_format == '.parquet':\n", "                feature_1day = pd.read_parquet(f'{load_path}/{date}.parquet')\n", "            elif file_format == '.feather':\n", "                feature_1day = pd.read_feather(f'{load_path}/{date}.feather')\n", "            elif file_format == '.pkl':\n", "                feature_1day = pd.read_pickle(f'{load_path}/{date}.pkl')\n", "\n", "            feature_1day['date'] = date\n", "            print(f'loading feature {date}', end='\\r')\n", "            return feature_1day if feature_list is None else feature_1day[feature_list]\n", "\n", "        possible_symbol_cols = ['symbol','code','fullcode', 'ticker_exchange_factset']\n", "        if os.path.isdir(load_path):\n", "            if file_format != '.h5':\n", "                dates = sorted([s.split('.')[0] for s in os.listdir(load_path) if s.endswith(file_format)])\n", "                dates = [dt for dt in dates if dt >= start_date and dt <= end_date]\n", "                if len(dates) == 0: \n", "                    raise ValueError(f'no date in {load_path} between {start_date} and {end_date}')\n", "\n", "                num_workers = max(len(dates) // 200, 1)\n", "                with Parallel(n_jobs=num_workers, backend='loky') as para:\n", "                    results = para(delayed(load_1day)(date) for date in dates)\n", "                df_feature = pd.concat([s for s in results if s is not None])\n", "\n", "                if 'symbol' not in df_feature.index.names:\n", "                    assert len(set(possible_symbol_cols)\\\n", "                            .intersection(df_feature.columns.str.lower()))>0,\\\n", "                                f'[symbol | code | fullcode] ticker_exchange_factset | should be in column names'\n", "                    sym_col = [col for col in df_feature.columns if col.lower() in possible_symbol_cols][0]\n", "                    df_feature = df_feature.rename(columns={sym_col:'symbol'})\n", "                    df_feature = df_feature.set_index(['date','symbol']).sort_index()\n", "                else:\n", "                    df_feature = df_feature.reset_index().set_index(['date','symbol']).sort_index()\n", "            \n", "            elif file_format == '.h5':\n", "                from shennong.stk import bar\n", "                key_list = self.key_list if feature_list is None else {self.key_list: feature_list}\n", "                a = bar.load(region_product=self.region_product,\n", "                             freq=self.freq,\n", "                             load_root=self.feature_root,\n", "                             start_datetime=start_date,\n", "                             end_datetime=end_date,\n", "                             key_list=key_list,\n", "                             verbose=False).transpose('DATETIME', 'SYMB<PERSON>', 'KEY')\n", "                dates = a['DATETIME'].dt.date.astype(str).to_numpy()\n", "                syms = [s.replace('.SZ','.XSHE').replace('.SH','.XSHG') for \n", "                        s in a['SYMBOL'].to_numpy()]\n", "                keys = a['KEY'].to_numpy()\n", "                dt = a.values.reshape(len(dates)*len(syms),len(keys))\n", "                index = pd.MultiIndex.from_product([dates,syms],names=['date','symbol'])\n", "                df_feature = pd.DataFrame(dt,index=index,columns=keys).dropna(how='all').sort_index()\n", "\n", "        elif os.path.isfile(load_path):\n", "            df_feature = pd.read_csv(load_path)\n", "            if not pred:\n", "                assert 'symbol' in df_feature.columns, 'symbol should be in column names'\n", "                assert 'date' in df_feature.columns, 'date should be in column names'\n", "                df_feature = df_feature.set_index(['date','symbol']).sort_index()\n", "            elif pred:\n", "                df_feature = df_feature.set_index('date').stack()\n", "                return df_feature.to_frame(name='pred').rename_axis(['date','symbol'])\n", "        else:\n", "            raise ValueError('load_path should be folder or file path')\n", "\n", "        return df_feature.loc[start_date:end_date]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "pd.read_parquet('/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/EUE4/2024-08-29.parquet').columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## prep"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/backtest/Preprocessor.py'\n", "\n", "import warnings\n", "import pandas as pd\n", "import numpy as np\n", "import os, time, datetime, h5py\n", "\n", "def add_univer_mask(df: pd.DataFrame, cfg: dict):\n", "    warnings.filterwarnings('ignore')\n", "    barraids = df.index.get_level_values('BarraID')\n", "    date = df.name\n", "    df = df.droplevel(0)\n", "\n", "    factor_group_name = cfg.get('factor_group_name', None)\n", "    if factor_group_name is None:\n", "        raise ValueError('factor_group_name is None')\n", "\n", "    def load_daily_universe(date):\n", "        # universe_root = f'/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/universe/{factor_group_name}/'\n", "        universe_root = f'/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/universe/global/'\n", "        universe = pd.read_csv(f'{universe_root}{date}.csv')\n", "        return universe['BarraID'].tolist()\n", "\n", "    try:\n", "        universe = load_daily_universe(date)\n", "        df['universe_mask'] = barraids.isin(universe).astype(np.float64)\n", "    except:\n", "        df['universe_mask'] = 0.0\n", "    return df\n", "\n", "\n", "def calc_upper_lower(df: pd.DataFrame):\n", "    median_ = df.median()\n", "    mad = df.sub(median_, axis=1).abs().median()\n", "    upper, lower = median_ + 3 * mad, median_ - 3 * mad\n", "    return (upper, lower)\n", "\n", "\n", "def rm_outlier(df: pd.DataFrame):\n", "    upper, lower = calc_upper_lower(df)\n", "    # df = df.where(df <= upper, upper, axis=1)\\\n", "    #     .where(df >= lower, lower, axis=1)\n", "    df = df.mask(df >= upper, upper, axis=1).mask(df <= lower, lower, axis=1)\n", "    return df\n", "\n", "\n", "def neutralize_(df_factor, df_style):\n", "    from sklearn.linear_model import LinearRegression\n", "    df_factor = df_factor.copy()\n", "    df_factor = df_factor.loc[df_factor['universe_mask']>0].drop('universe_mask',axis=1)\n", "    df_style = df_style.loc[df_style.index.isin(df_factor.index)]\n", "\n", "    types = df_style.dtypes\n", "    cats = types[types=='object'].index.tolist()\n", "    if len(cats) > 0:\n", "        df_style = pd.get_dummies(df_style,columns=cats,drop_first=False).astype(np.float64)\n", "\n", "    rows = df_factor.index.intersection(df_style.index)\n", "    df_factor, df_style = df_factor.loc[rows], df_style.loc[rows]\n", "    style_columns = df_style.columns.tolist()\n", "\n", "    df_full = pd.concat([df_style, df_factor], axis=1)\n", "    df_full[np.isnan(df_full)] = 0.0\n", "    \n", "    def neut(df: pd.DataFrame, x_columns: list[str]):\n", "        x = df[x_columns]\n", "        y = df[[s for s in df.columns if s not in x_columns]]\n", "        lr = LinearRegression()\n", "        lr.fit(x, y)\n", "        return y.sub(lr.predict(x))\n", "    df_neutral = df_full.groupby(level='date').apply(lambda df: neut(df, style_columns)).droplevel(0)\n", "    df_neutral.columns = ['neutral_' + x for x in df_neutral.columns]\n", "    return df_neutral.groupby('date').apply(add_univer_mask)\n", "\n", "\n", "def neutralize(df_factor: pd.DataFrame, df_style: pd.DataFrame, option='indus_and_style'):\n", "    \"\"\"\n", "    df_factor: factor dataframe;\n", "    df_style: first column as indcell, others as style columns;\n", "    option: \n", "        1. indus: for industry only neutralization;\n", "        2. style: for style only neutralization;\n", "        3. indus_and_style: for industry and style neutralization at the same time\n", "        4. indus_and_style_by_country: option 3 for each country\n", "    \"\"\"\n", "    from sklearn.linear_model import LinearRegression\n", "    df_factor = df_factor.copy()\n", "    df_style = df_style.loc[df_style.index.isin(df_factor.index)]\n", "    rows = df_factor.index.intersection(df_style.index)\n", "    df_factor, df_style = df_factor.loc[rows], df_style.loc[rows]\n", "\n", "    types = df_style.dtypes\n", "    cats = types[types=='object'].index.tolist()\n", "\n", "    if option == 'indus' and len(cats)>0:               ## convert indecell to dummy industry columns\n", "        df_style = pd.get_dummies(df_style[['indcell']],columns=cats, drop_first=False).astype(np.float64)\n", "    elif option == 'style':\n", "        df_style = df_style.iloc[:, 1:]\n", "    elif option.startswith('indus_and_style') and len(cats)>0:  ## convert indecell to dummy industry columns\n", "        df_style = pd.get_dummies(df_style,columns=cats,drop_first=False).astype(np.float64)\n", "\n", "    df_full = pd.concat([df_style, df_factor], axis=1)\n", "    df_full[np.isnan(df_full)] = 0.0\n", "    \n", "    def neut(df: pd.DataFrame, x_columns: list[str]):\n", "        x = df[x_columns]\n", "        y = df[[s for s in df.columns if s not in x_columns]]\n", "        lr = LinearRegression()\n", "        lr.fit(x, y)\n", "        return y.sub(lr.predict(x))\n", "    \n", "    def neut_by_country(df: pd.DataFrame, x_columns: list[str]):\n", "        '''left for neutralization by count for option=4'''\n", "        pass\n", "\n", "    style_columns = df_style.columns.tolist()\n", "    df_neu = df_full.groupby(level='date').apply(lambda df: neut(df, style_columns)).droplevel(0)\n", "    df_neu.columns = ['neu_' + x for x in df_neu.columns]\n", "    return df_neu\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.read_csv('/mnt/sda/NAS/ShareFolder/xielan/global/universe/2023-01-03.csv')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## evaluator"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/home/<USER>/working/gitrepo/euronext_4xl/Evaluator.py'\n", "import pandas as pd\n", "import numpy as np\n", "\n", "class Evaluator:\n", "\tdef init(self, config):\n", "\t\tself.__dict__.update(config)\n", "\n", "\tdef calc_alpha(self, df_factor: pd.DataFrame, df_label: pd.DataFrame):\n", "\t\t\n", "\t\tdf_factor = df_factor.loc[df_factor['universe_mask']>0].drop('universe_mask',axis=1)\n", "\t\tdf_label = df_label.loc[df_label['universe_mask']>0].drop('universe_mask',axis=1)\n", "\t\tcindex = df_factor.index.intersection(df_label.index)\n", "\t\tcdates = cindex.get_level_values('date').unique()\n", "\n", "\t\tdf_alpha = []\n", "\t\tfor lname in df_label.columns:\n", "\t\t\tresults = {}\n", "\t\t\tfor fname in df_factor.columns[:]:\n", "\t\t\t\tresults[fname] = []\n", "\t\t\t\tfor date in cdates:\n", "\t\t\t\t\tf_ = df_factor.loc[date][fname].copy()\n", "\t\t\t\t\tl_ = df_label.loc[date][lname].copy()\n", "\t\t\t\t\tf_ -= f_.mean()\n", "\t\t\t\t\tf_ /= f_.abs().sum()\n", "\t\t\t\t\tl_.index.name = f_.index.name\n", "\t\t\t\t\talpha = (f_ * l_).sum()\n", "\t\t\t\t\tresults[fname].append(alpha)\n", "\t\t\t\tresults[fname] = pd.Series(results[fname],name=fname,index=cdates)\n", "\n", "\t\t\talpha = pd.concat(results, axis=1)\n", "\t\t\tmindex = pd.MultiIndex.from_product([cdates,df_factor.columns])\n", "\t\t\tdf_alpha.append(pd.Series(alpha.values.reshape(-1),index=mindex,name=lname))\n", "\t\treturn pd.concat(df_alpha, axis=1).rename_axis(['date', 'factor'])\n", "\t\n", "\tdef calc_ic(self, df_factor: pd.DataFrame, df_label: pd.DataFrame, corr_method='spearman'):\n", "\t\tdf_factor = df_factor.loc[df_factor['universe_mask']>0].drop('universe_mask',axis=1)\n", "\t\tdf_label = df_label.loc[df_label['universe_mask']>0].drop('universe_mask',axis=1)\n", "\t\tdf_full = pd.concat([df_factor, df_label], axis=1)\n", "\t\t\n", "\t\t# ic\n", "\t\tdf_ic = df_full.groupby(level='date').corr(method=corr_method)\n", "\t\treturn df_ic.loc[(slice(None), df_factor.columns), df_label.columns].rename_axis(['date', 'factor'])\n", "\t\n", "\tdef calc_indus_exposure(self, df_factor: pd.DataFrame, df_indus: pd.DataFrame):\n", "\n", "\t\tdf_new = df_factor.copy()\n", "\t\tcindex = df_new.index.intersection(df_indus.index)\n", "\t\tdf_new.loc[cindex, 'indus'] = df_indus.loc[cindex, 'indcell'].values\n", "\t\tdf_new = df_new.dropna(subset=['indus'])\n", "\t\tdf_new = df_new.loc[df_new['universe_mask']>0].drop('universe_mask',axis=1)\n", "\n", "\n", "\t\tdef each_industry(df):\n", "\t\t\tindus = df.name\n", "\t\t\ta = df.agg(['max', 'min'])\n", "\t\t\ta.index  = ['factor_max', 'factor_min']\n", "\t\t\tb = df.groupby(level='date').mean().agg(['max', 'min'])\n", "\t\t\tb.index = ['mean_max', 'mean_min']\n", "\t\t\treturn pd.concat([a, b])\n", "\n", "\t\treturn df_new.groupby('indus').apply(each_industry).rename_axis(['industry','eval'])\n", "\t\n", "\tdef calc_intra_corr(self, df_factor: pd.DataFrame, df_style: pd.DataFrame, corr_method='spearman'):\n", "\t\tdf_factor = df_factor.loc[df_factor['universe_mask']>0].drop('universe_mask',axis=1)\n", "\n", "\t\ttypes = df_style.dtypes\n", "\t\tcats = types[~(types=='object')].index.tolist()\n", "\t\tdf_style = df_style[cats]\n", "\n", "\t\trows = df_factor.index.intersection(df_style.index)\n", "\t\tdf_factor, df_style = df_factor.loc[rows], df_style.loc[rows]\n", "\t\tstyle_columns = df_style.columns.tolist()\n", "\n", "\t\tdf_full = pd.concat([df_style, df_factor], axis=1)\n", "\t\t\n", "\t\tdf_ic = df_full.groupby(level='date').corr(method=corr_method)\n", "\t\treturn df_ic.loc[(slice(None), df_factor.columns), df_style.columns].rename_axis(['date', 'factor'])\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## stablizer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/backtest/Stabilizer.py'\n", "import pandas as pd\n", "import numpy as np\n", "import polars as pl\n", "# from yrqtlib.utils import tools\n", "\n", "\n", "def npcorr4(a: np.n<PERSON><PERSON>, \n", "            b: np.<PERSON><PERSON><PERSON>, \n", "            amask: np.n<PERSON><PERSON>, \n", "            bmask: np.n<PERSON><PERSON>):\n", "    cnt = ((~amask)&(~bmask)).sum(axis=0)\n", "    meanx = a.sum(axis=0)/cnt \n", "    meany = b.sum(axis=0)/cnt \n", "    cov = (a * b).sum(axis=0)/cnt - meanx*meany \n", "    x_sqrd, y_sqrd = a**2, b**2\n", "    stdx = np.sqrt(x_sqrd.sum(axis=0)/cnt - meanx**2)\n", "    stdy = np.sqrt(y_sqrd.sum(axis=0)/cnt - meany**2)\n", "    return np.true_divide(cov,stdx*stdy)\n", "\n", "\n", "## correlation computation using numpy vectorization\n", "def corrTwoDf(f: pd.DataFrame, \n", "              l: pd.Data<PERSON>rame, \n", "              axis='row', \n", "              method='spearman'):\n", "    if axis == 'row': a, b = f.copy().T, l.copy().T\n", "    elif axis == 'column': a, b = f.copy(), l.copy()\n", "\n", "    rows, cols = a.index.intersection(b.index), a.columns.intersection(b.columns)\n", "    a, b = a.loc[rows, cols], b.loc[rows, cols]\n", "    index = a.columns\n", "    amask, bmask = np.isnan(a), np.isnan(b)\n", "    a[bmask], b[amask] = np.nan, np.nan\n", "    if method == 'spearman': \n", "        a = pl.Lazy<PERSON>rame(a.values).with_columns(pl.all().rank()).collect().to_numpy()\n", "        b = pl.Lazy<PERSON><PERSON>e(b.values).with_columns(pl.all().rank()).collect().to_numpy()\n", "    a[amask|bmask], b[amask|bmask] = 0.0, 0.0 # set to 0 for summation\n", "    return pd.Series(npcorr4(a, b, amask, bmask), index=index)\n", "\n", "\n", "class Stabilizer:\n", "    def init(self, config):\n", "        self.__dict__.update(config)\n", "    \n", "    def ACF_cs(self, df_factor: pd.DataFrame, nlags: int=20):\n", "        def acf(col):\n", "            tmp = df_factor[col].unstack(1)\n", "            corrs = [corrTwoDf(tmp, tmp.shift(i), axis='row').mean() for i in range(1,nlags+1)]\n", "            return pd.Series(corrs, index=range(1,nlags+1))\n", "\n", "        df_factor = df_factor.copy()\n", "        df_factor = df_factor.loc[df_factor['universe_mask']>0].drop('universe_mask',axis=1)\n", "        acfs = [acf(col) for col in df_factor.columns]\n", "        df_acf = pd.concat(acfs, axis=1,keys=df_factor.columns)\n", "        df_acf.index.name = 'nlags'\n", "        return df_acf\n", "    \n", "\n", "    def ACF_1day(self, df_factor: pd.DataFrame):\n", "        df_factor = df_factor.copy()\n", "        df_factor = df_factor.loc[df_factor['universe_mask']>0].drop('universe_mask',axis=1)\n", "        df_factor = df_factor.loc[~df_factor.index.duplicated()] ## filter out duplicated records\n", "        dates = sorted(df_factor.index.get_level_values('date').unique().tolist())\n", "\n", "        corrs = {}\n", "        for i, date in enumerate(dates):\n", "            if i == 0: continue\n", "            two_dates = dates[i-1:i+1]\n", "            smp = df_factor.loc[two_dates].swapaxes(0,1).stack('BarraID')\n", "            corrs[date] = smp.groupby(level=0).apply(lambda x: x.corr().iloc[0,1])\n", "            \n", "        return pd.concat(corrs,axis=1, names=['date']).T\n", "\n", "\n", "    def hist(self, df_factor: pd.DataFrame):\n", "        df_factor = df_factor.loc[df_factor['universe_mask']>0].drop('universe_mask',axis=1)\n", "        return df_factor.reset_index(drop=True)\n", "    \n", "\n", "    def coverage(self, df_factor: pd.DataFrame):\n", "        df_factor = df_factor.loc[df_factor['universe_mask']>0].drop('universe_mask',axis=1)\n", "        return df_factor.groupby(level='date').apply(lambda x: x.count()/len(x))\n", "\n", "\n", "    def valid_counts(self, df_factor: pd.DataFrame):\n", "        df_factor = df_factor.loc[df_factor['universe_mask']>0].drop('universe_mask',axis=1)\n", "        return df_factor.groupby(level='date').apply(lambda x: x.count())\n", "\n", "\n", "    def quantile(self, df_factor: pd.DataFrame, qtile_ticks: list):\n", "        def qtile(df: pd.DataFrame):\n", "            mean_, std_ = df.mean(), df.std()\n", "            tmp = pd.concat([mean_, mean_+std_, mean_-std_], axis = 1, \\\n", "                            keys=['mean','mean_plus_std','mean_minus_std']).T\n", "            qtile = df.quantile(qtile_ticks)\n", "            return pd.concat([tmp, qtile], axis = 0)\n", "        \n", "        # turnover\n", "        df_factor = df_factor.loc[df_factor['universe_mask']>0].drop('universe_mask',axis=1)\n", "        df_factor = df_factor.copy()\n", "        return df_factor.groupby('date').apply(qtile).rename_axis(['date', 'eval'])\n", "        \n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## reporter"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/backtest/reporter.py'\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "import numpy as np\n", "import warnings, os\n", "from matplotlib.backends.backend_pdf import PdfPages\n", "from multiprocessing import Lock\n", "lock = Lock()\n", "\n", "\n", "plt.rcParams['figure.figsize'] = (12,6)\n", "plt.rcParams['axes.grid'] = True\n", "\n", "def gen_csv(results, report_folder='./'): \n", "    import os\n", "    if not os.path.exists(report_folder):\n", "        os.makedirs(report_folder)\n", "    for mname, metric in results.items():\n", "        os.makedirs(os.path.join(report_folder, mname), exist_ok=True)\n", "        for fname, df in metric.items():\n", "            file_path = os.path.join(report_folder, mname, f'{fname}.csv')\n", "            df.to_csv(file_path)\n", "\n", "\n", "def box_plot(df: pd.DataFrame, figsize=(12,6)):\n", "    fig, ax = plt.subplots(figsize=figsize)\n", "    for i, col in enumerate(df.columns):\n", "        max2 = df[col].iloc[0]\n", "        min2 = df[col].iloc[1]\n", "        max1 = df[col].iloc[2]\n", "        min1 = df[col].iloc[3]\n", "        \n", "        ax.plot([i, i], [min2, max2], color='black', lw=1.5)  # Vertical line for whisker\n", "        ax.add_patch(plt.Rectangle((i - 0.2, min1), 0.4, max1 - min1, fill=True, \n", "                                   color='lightblue', edgecolor='blue', zorder=2))\n", "\n", "        ax.hlines(max2, i - 0.15, i + 0.15, color='black', lw=1.5)  # Bar at max2\n", "        ax.hlines(min2, i - 0.15, i + 0.15, color='black', lw=1.5)  # Bar at min2\n", "\n", "    ax.set_xticks(range(len(df.columns)))\n", "    ax.set_xticklabels(df.columns, rotation=30, ha='right')\n", "    ax.set_ylabel('Values')\n", "    plt.tight_layout()\n", "    return fig, ax\n", "\n", "\n", "def gen_pdf_by_metric(results, report_file='./report.pdf'): \n", "    from matplotlib.backends.backend_pdf import PdfPages\n", "    warnings.filterwarnings('ignore')\n", "\n", "    with PdfPages(report_file) as pdf:  # noqa: PLR1702\n", "        for mname, metric in results.items():\n", "            if mname in {'stab'}:\n", "                for fname, df in metric.items():\n", "                    if fname.startswith('df_acf'):\n", "                        for factor in df.columns:\n", "                            fig, ax = plt.subplots(figsize=(12, 6))\n", "                            df[factor].plot(ax=ax, marker='o', grid=True)\n", "                            ax.set_title(f'ACF-{factor}')\n", "                            pdf.savefig(fig,bbox_inches='tight')\n", "                            plt.close(fig)\n", "                    elif fname.startswith('df_hist'):\n", "                        for factor in df.columns:\n", "                            fig, ax = plt.subplots(figsize=(12, 6))\n", "                            df[factor].hist(ax=ax, bins=100)\n", "                            ax.set_title(f'Histogram-{factor}')\n", "                            pdf.savefig(fig,bbox_inches='tight')\n", "                            plt.close(fig)\n", "                    elif fname.startswith('df_quantile'):\n", "                        for factor in df.columns:\n", "                            fig, ax = plt.subplots(figsize=(12, 6))\n", "                            df[factor].unstack(1).plot(ax=ax, grid=True, label=True)\n", "                            ax.set_title(f'Quantile-{factor}')\n", "                            pdf.savefig(fig,bbox_inches='tight')\n", "                            plt.close(fig)\n", "                    elif fname.startswith('df_cov'):\n", "                        for factor in df.columns:\n", "                            fig, ax = plt.subplots(figsize=(12, 6))\n", "                            df[factor].plot(ax=ax, grid=True)\n", "                            ax.set_title(f'COVERAGE-{factor}')\n", "                            pdf.savefig(fig,bbox_inches='tight')\n", "                            plt.close(fig)\n", "            if mname in {'raw_eva', 'neu_eva'}:\n", "                for fname, df in metric.items():\n", "                    if fname in {'df_alpha', 'df_ic', 'df_corr'}:\n", "                        for factor in df.index.get_level_values(1).unique():\n", "                            if fname == 'df_alpha':\n", "                                title = f'ALPHA-{factor}'\n", "                            elif fname == 'df_ic':\n", "                                title = f'IC-{factor}'\n", "                            elif fname == 'df_corr':\n", "                                title = f'CORR-{factor}'\n", "                            fig, ax = plt.subplots(figsize=(12, 6))\n", "                            tmp = df.loc[(slice(None),factor),:].droplevel(1)\n", "                            if 'corr' not in fname:\n", "                                tmp.cumsum().plot(ax=ax, grid=True, label=True)\n", "                            else:\n", "                                tmp.plot(ax=ax, grid=True, label=True)\n", "                            ax.set_title(title)\n", "                            pdf.savefig(fig,bbox_inches='tight')\n", "                            plt.close(fig)\n", "                    if fname in {'df_exposure'}:\n", "                        for factor in df.columns:\n", "                            # fig, ax = plt.subplots(figsize=(12, 6))\n", "                            tmp = df[factor].unstack(1).T\n", "                            fig, ax = box_plot(tmp, figsize=(12,6))\n", "                            # tmp.boxplot(ax=ax, grid=True, rot=45)\n", "                            ax.set_title(f'EXPOSURE-{factor}')\n", "                            pdf.savefig(fig,bbox_inches='tight')\n", "                            plt.close(fig)\n", "\n", "\n", "def _plot_acf_2pdf(pdf, se, figsize=(12, 6), desc='ACF'):\n", "    fig, ax = plt.subplots(figsize=figsize)\n", "    se.plot(ax=ax, marker='o', grid=True)\n", "    ax.set_title(f'{desc}-{se.name}')\n", "    pdf.savefig(fig,bbox_inches='tight')\n", "    plt.close(fig)\n", "\n", "\n", "def _plot_hist_2pdf(pdf, se, figsize=(12, 6), desc=''):\n", "    fig, ax = plt.subplots(figsize=(12, 6))\n", "    se.hist(ax=ax, bins=100)\n", "    ax.set_title(f'Histogram-{se.name} {desc}')\n", "    pdf.savefig(fig,bbox_inches='tight')\n", "    plt.close(fig)\n", "\n", "\n", "def _plot_qtile_2pdf(pdf, se, figsize=(12, 6)):\n", "    fig, ax = plt.subplots(figsize=(12, 6))\n", "    se.unstack(1).plot(ax=ax, grid=True, label=True)\n", "    ax.set_title(f'Quantile-{se.name}')\n", "    pdf.savefig(fig,bbox_inches='tight')\n", "    plt.close(fig)\n", "\n", "\n", "def _plot_cov_2pdf(pdf, se, figsize=(12, 6), desc='COVERAGE'):\n", "    fig, ax = plt.subplots(figsize=(12, 6))\n", "    se.plot(ax=ax, grid=True)\n", "    ax.set_title(f'{desc}-{se.name}')\n", "    pdf.savefig(fig,bbox_inches='tight')\n", "    plt.close(fig)\n", "\n", "\n", "def _plot_ts_2pdf(pdf, se, fname, factor, figsize=(12, 6)):\n", "    fig, ax = plt.subplots(figsize=figsize)\n", "    if fname == 'df_corr':\n", "        se.plot(ax=ax, grid=True, label=True)\n", "    else:\n", "        se.cumsum().plot(ax=ax, grid=True, label=True)\n", "    if fname == 'df_alpha':\n", "        title = f'ALPHA-{factor}'\n", "        tmp = se.cumsum()\n", "        ann = (se.sum() * 250 / len(tmp)).to_numpy()\n", "        std = (se.std() * np.sqrt(250)).to_numpy()\n", "        shp = ann / std\n", "        title = f'{title}  \\\n", "            Ann: {ann[0]: .2%}, {ann[1]: .2%}\\n \\\n", "            Std: {std[0]: .2%}, {std[1]: .2%} \\\n", "            Sharpe: {shp[0]: .3f}, {shp[1]: .3f}\\n\\\n", "            '\n", "    elif fname == 'df_ic':\n", "        mean_ = se.mean().tolist()\n", "        std_ = se.std().tolist()\n", "        title = f'IC-{factor} \\\n", "            Mean: {mean_[0]: .3f}, {mean_[1]: .3f} \\\n", "            Std: {std_[0]: .3f}, {std_[1]: .3f}'\n", "    elif fname == 'df_corr':\n", "        title = f'CORR-{factor}'\n", "\n", "    ax.set_title(title)\n", "    pdf.savefig(fig,bbox_inches='tight')\n", "    plt.close(fig)\n", "\n", "\n", "def _plot_exposure_2pdf(pdf, se, figsize=(12, 6)):\n", "    tmp = se.unstack(1).T\n", "    fig, ax = box_plot(tmp, figsize=(12,6))\n", "    ax.set_title(f'EXPOSURE-{se.name}')\n", "    pdf.savefig(fig,bbox_inches='tight')\n", "    plt.close(fig)\n", "\n", "\n", "_plot_map = {\n", "    'df_acf': _plot_acf_2pdf,\n", "    'df_acf1': _plot_acf_2pdf,\n", "    'df_hist': _plot_hist_2pdf,\n", "    'df_hist2': _plot_hist_2pdf,\n", "    'df_quantile': _plot_qtile_2pdf,\n", "    'df_cov': _plot_cov_2pdf,\n", "    'df_non_nan': _plot_cov_2pdf,\n", "    'df_alpha': _plot_ts_2pdf,\n", "    'df_ic': _plot_ts_2pdf,\n", "    'df_corr': _plot_ts_2pdf,\n", "    'df_exposure': _plot_exposure_2pdf,\n", "    }\n", "\n", "\n", "def gen_pdf_by_factor(results, report_folder='./report/'): \n", "    from matplotlib.backends.backend_pdf import PdfPages\n", "    if not os.path.exists(report_folder):\n", "        os.makedirs(report_folder, exist_ok=True)\n", "\n", "    warnings.filterwarnings('ignore')\n", "    factors = results['stab']['df_acf'].columns.tolist()\n", "\n", "    for factor in factors:  # noqa: PLR1702\n", "        # with lock:\n", "        #     if not os.path.exists(f'{report_folder}by_factor/'):\n", "        #         os.makedirs(f'{report_folder}by_factor/')\n", "        with PdfPages(f'{report_folder}{factor}.pdf') as pdf:  # noqa: PLR1702\n", "            for mname, metric in results.items():\n", "                if mname in {'stab'}:\n", "                    for fname, df in metric.items():\n", "                        if fname == 'df_hist2':\n", "                            _plot_map[fname](pdf, df[factor], figsize=(12,6),desc='winsorized')\n", "                        elif fname == 'df_acf1':\n", "                            _plot_map[fname](pdf, df[factor], figsize=(12,6),desc='LAG_1DAY_ACF')\n", "                        elif fname == 'df_non_nan':\n", "                            _plot_map[fname](pdf, df[factor], figsize=(12,6),desc='VALID_COUNTS')\n", "                        else:\n", "                            _plot_map[fname](pdf, df[factor], figsize=(12, 6))\n", "                if mname in {'raw_eva', 'neu_eva'}:\n", "                    nfactor = f'neutral_{factor}' if mname == 'neu_eva' else factor\n", "                    for fname, df in metric.items():\n", "                        if fname in {'df_alpha', 'df_ic', 'df_corr'}:\n", "                            se = df.loc[(slice(None),nfactor),:].droplevel(1)\n", "                            _plot_map[fname](pdf, se, fname, nfactor, figsize=(12, 6))\n", "                        if fname in {'df_exposure'}:\n", "                            _plot_map[fname](pdf, df[nfactor], figsize=(12, 6))\n", "\n", "\n", "def calc_ann_alpha(df):\n", "    df = df.copy()\n", "    years = df.index.get_level_values('date').unique().str.split('-').str[0].unique().tolist()\n", "\n", "    ann_results = {'return':{}, 'sharpe':{}}\n", "    for year in years:\n", "        df_year = df.loc[df.index.get_level_values('date').str.startswith(year)]\n", "\n", "        ann_results['return'][year] = df_year.mean() * 250\n", "        ann_results['sharpe'][year] = df_year.mean() * 250 / (df_year.std()*np.sqrt(250))\n", "\n", "    ann_results['return']['all_years'] = df.mean() * 250\n", "    ann_results['sharpe']['all_years'] = df.mean() * 250 / (df.std()*np.sqrt(250))\n", "        \n", "    for key, value in ann_results.items():\n", "        ann_results[key] = pd.concat(value, axis=1, names=['year']).T\n", "    \n", "    return pd.concat(ann_results, axis=0, names=['annual'])\n", "\n", "\n", "def calc_ann_ic(df):\n", "    df = df.copy()\n", "    years = df.index.get_level_values('date').unique().str.split('-').str[0].unique().tolist()\n", "\n", "    ann_results = {'mean':{}, 'icir':{}}\n", "\n", "    for year in years:\n", "        df_year = df.loc[df.index.get_level_values('date').str.startswith(year)]\n", "        ann_results['mean'][year] = df_year.mean()\n", "        ann_results['icir'][year] = df_year.mean() / df_year.std()\n", "\n", "    ann_results['mean']['all_years'] = df.mean()\n", "    ann_results['icir']['all_years'] = df.mean() / df.std()\n", "\n", "    for key, value in ann_results.items():\n", "        ann_results[key] = pd.concat(value, axis=1, names=['year']).T\n", "        \n", "    return pd.concat(ann_results, axis=0, names=['annual'])\n", "\n", "\n", "def annual_report(result, report_folder='./'):\n", "    evas, reports = ['raw_eva'], {}\n", "    if 'neu_eva' in result.keys():\n", "        evas = ['raw_eva', 'neu_eva']\n", "    \n", "    for eva in evas:\n", "        reports[eva] = {}\n", "        for metric in ['df_alpha', 'df_ic']:\n", "            df = result[eva][metric]\n", "            if metric == 'df_ic':\n", "                reports[eva][metric] = df.groupby(level=1).apply(calc_ann_ic)\n", "            elif metric == 'df_alpha':\n", "                reports[eva][metric] = df.groupby(level=1).apply(calc_ann_alpha)\n", "            else:\n", "                pass\n", "        reports[eva] = pd.concat(reports[eva], axis=0, names=['metric'])\n", "    reports = pd.concat(reports, axis=0, names=['eva'])\n", "    reports.to_csv(f'{report_folder}annual_report.csv')\n", "    return reports\n", "\n", "\n", "def report_summary(annual_report, report_folder='./report/'):\n", "    ret = annual_report.xs(('all_years','return','df_alpha'), level=('year','annual','metric')).droplevel(0)\n", "    shp = annual_report.xs(('all_years','sharpe','df_alpha'), level=('year','annual','metric')).droplevel(0)\n", "    mean = annual_report.xs(('all_years','mean','df_ic'), level=('year','annual','metric')).droplevel(0)\n", "    icir = annual_report.xs(('all_years','icir','df_ic'), level=('year','annual','metric')).droplevel(0)\n", "    \n", "    ret = ret.sort_values(by='SpecificReturn_H1', key=abs)\n", "    shp,mean, icir = shp.loc[ret.index], mean.loc[ret.index], icir.loc[ret.index]    \n", "    \n", "    results = {'ret': ret, 'shp': shp, 'mean': mean, 'icir': icir}\n", "\n", "    with PdfPages(report_folder + 'summary.pdf') as pdf:\n", "        for key, df in results.items():\n", "            fig, ax = plt.subplots()\n", "            plt.gca().xaxis.set_label_position('top')\n", "            width_per_row = 0.25\n", "            df.plot.barh(figsize=(8,len(ret)*width_per_row),grid=True,ax=ax)\n", "            ax.yaxis.tick_right()\n", "            ax.xaxis.tick_top()\n", "            ax.set_title(key)\n", "            plt.legend(loc='center left')\n", "            pdf.savefig(fig,bbox_inches='tight')\n", "            plt.close(fig)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## workflow"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/backtest.py'\n", "# coding = utf-8\n", "import sys, argparse, logging\n", "import os, json, pickle, time\n", "\n", "os.environ['NUMEXPR_MAX_THREADS'] = '250'\n", "\n", "import pandas as pd\n", "import numpy as np\n", "from barra.Evaluator import Evaluator\n", "from barra.Stabilizer import Stabilizer\n", "from barra.Logger import Logger\n", "from barra.Loader import Loader\n", "import barra.Preprocessor as prep\n", "\n", "from barra import reporter\n", "from shennong.stk import bar\n", "from shennong.utils import key_group_manager as sn_km\n", "from joblib import Parallel, delayed\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument('--config', type=str, default='./config/')\n", "parser.add_argument('--timetag', type=str, default=time.strftime('%Y%m%d_%H%M'))\n", "args = parser.parse_args()\n", "\n", "\n", "def load_config(config_filepath: str):\n", "\tlogger.info(f'loading config from {config_filepath}')\n", "\twith open(config_filepath, 'r', encoding='utf-8') as f:\n", "\t\treturn json.load(f)\n", "\n", "\n", "def work_flow(df_factor, df_label, df_style, df_indus):\n", "\teva, stab = Evaluator(), Stabilizer()\n", "\txchg = df_factor.index.get_level_values('symbol')[-1].split('.')[-1]\n", "\n", "\tlogger.info(f'calculation for {xchg} starts.')\n", "\n", "\tdf_acf = stab.ACF_cs(df_factor, 20)\n", "\tdf_hist = stab.hist(df_factor)\n", "\tdf_cov = stab.coverage(df_factor)\n", "\tdf_quantile = stab.quantile(df_factor, [0.01, 0.15, 0.5, 0.85, 0.99])\n", "\tlogger.info('feature stability calculation done!')\n", "\n", "\tdf_factor = df_factor.groupby(level='date').apply(prep.rm_outlier).droplevel(0)\n", "\tdf_hist2 = stab.hist(df_factor)\n", "\n", "\tdf_factor = df_factor.groupby('date').apply(prep.add_univer_mask)\n", "\tlogger.info('feature outlier removed!')\n", "\n", "\tdf_alpha = eva.calc_alpha(df_factor, df_label)\n", "\tdf_ic = eva.calc_ic(df_factor, df_label, corr_method)\n", "\tdf_exposure = eva.calc_indus_exposure(df_factor, df_indus)\n", "\tdf_corr = eva.calc_intra_corr(df_factor, df_style, corr_method)\n", "\tlogger.info('raw feature evaluation done!')\n", "\n", "\tdf_neutral = prep.neutralize(df_factor, df_style)\n", "\tdf_neutral = df_neutral.groupby('date').apply(prep.add_univer_mask)\n", "\n", "\tdf_alpha_ = eva.calc_alpha(df_neutral, df_label)\n", "\tdf_ic_ = eva.calc_ic(df_neutral, df_label, corr_method)\n", "\tdf_exposure_ = eva.calc_indus_exposure(df_neutral, df_indus)\n", "\tdf_corr_ = eva.calc_intra_corr(df_neutral, df_style, corr_method)\n", "\tlogger.info('neutralized evaluation done!')\n", "\n", "\treturn {\n", "\t\t'stab': {'df_acf': df_acf, 'df_hist': df_hist, 'df_hist2': df_hist2, 'df_cov': df_cov, 'df_quantile': df_quantile},\n", "\t\t'raw_eva': {'df_alpha': df_alpha, 'df_ic': df_ic, 'df_exposure': df_exposure, 'df_corr': df_corr},\n", "\t\t'neu_eva': {'df_alpha': df_alpha_, 'df_ic': df_ic_, 'df_exposure': df_exposure_, 'df_corr': df_corr_},\n", "\t}\n", "\n", "\n", "def run_1group(feature, df_label, df_style, df_indus, group_name: str, group_xchgs: list, partition=0):\n", "\tif group_xchgs is None:\n", "\t\treturn None\n", "\tif len(group_xchgs) == 0:\n", "\t\treturn None\n", "\n", "\tgroup_xchgs = [s.upper() for s in group_xchgs]\n", "\n", "\tif group_name == 'all_market':\n", "\t\tf, l, style, indus = feature, df_label, df_style, df_indus\n", "\telse:\n", "\t\tf = feature.loc[feature.index.get_level_values('symbol').str.split('.').str[-1].isin(group_xchgs)]\n", "\t\tl = df_label.loc[df_label.index.get_level_values('symbol').str.split('.').str[-1].isin(group_xchgs)]\n", "\t\tstyle = df_style.loc[df_style.index.get_level_values('symbol').str.split('.').str[-1].isin(group_xchgs)]\n", "\t\tindus = df_indus.loc[df_indus.index.get_level_values('symbol').str.split('.').str[-1].isin(group_xchgs)]\n", "\tlogging.info(f'\\n{group_name}: {f.shape}, {l.shape}, {style.shape}, {indus.shape}')\n", "\tif f.shape[0] * l.shape[0] * style.shape[0] * indus.shape[0] == 0:\n", "\t\tlogger.info('one of feature, label, style, indus is empty, do nothing, exit!')\n", "\t\treturn None\n", "\tif (len(f) == 0) | (len(l) == 0):\n", "\t\treturn None\n", "\tresult = work_flow(f, l, style, indus)\n", "\n", "\treport_folder = f'./report/{save_tag}_{time_tag}/{group_name}_{partition}/'\n", "\treporter.gen_csv(result, report_folder=report_folder)\n", "\tlogger.info(f'csv report generated for ({group_name})')\n", "\treporter.gen_pdf_by_factor(result, report_folder=report_folder)\n", "\tlogger.info(f'pdf report generated for ({group_name})')\n", "\tann_report = reporter.annual_report(result, report_folder=report_folder)\n", "\treporter.report_summary(ann_report, report_folder=report_folder)\n", "\n", "\n", "def run_task(rank):\n", "\tfeature_list = feature_name_lists[int(rank)]\n", "\tlogger.info(f'loading features [num_feature {len(feature_list)}]')\n", "\tfor f in feature_list[:]: logger.info(f)\n", "\tfeature = loader.load(feature_root, start_date, end_date, \n", "\t\t\t\t\t   file_format=feature_file_format, pred=pred,feature_list=feature_list)\n", "\n", "\tif np.isinf(feature).sum().sum() > 0:\n", "\t\tlogger.info('feature contains inf or -inf, replace with NAN')\n", "\t\tfeature.replace([np.inf, -np.inf], np.nan, inplace=True)\n", "\n", "\tnum_opmic = len(feature.index[0][-1].split('.')[-1])\n", "\tif num_opmic == 3:\n", "\t\tnidx = [(s[0], s[1].replace('.', '.X')) for s in feature.index]\n", "\t\tfeature.index = pd.Index(nidx).rename(['date', 'symbol'])\n", "\n", "\tFEATURE_COUNTS = len(feature.columns)\n", "\tlogger.info(f'loading feature done, feature shape: {feature.shape}')\n", "\t# logger.info(f'feature names: {feature.columns.tolist()}')\n", "\n", "\tif 'universe_mask' not in feature.columns:\n", "\t\tfeature = feature.groupby('date').apply(prep.add_univer_mask)\n", "\tlogger.info(f'adding universe mask done, feature shape: {feature.shape}')\n", "\n", "\t## step 3: load style factors and indus\n", "\tdf_style, df_indus = loader.load_style_indus(barra_product, feature.index)\n", "\tlogger.info(f'loading style and indus factors done')\n", "\n", "\t## step 4: calculate label\n", "\tdf_label = (\n", "\t\tloader.load_label(barra_product, start_date, end_date, \n", "\t\t\t\t\tlabel_list=label_list, usecols=label_usecols) / 100.0\n", "\t)\n", "\tlogger.info(f'loading label done, label shape: {df_label.shape}')\n", "\n", "\t# all_market = []\n", "\t# for value in groups.values():\n", "\t# \tall_market.extend(value)\n", "\t# groups['all_market'] = all_market\n", "\n", "\tif 'universe_mask' not in df_label.columns:\n", "\t\tdf_label = df_label.groupby('date').apply(prep.add_univer_mask)\n", "\tlogger.info(f'adding universe mask done, df_label shape: {df_label.shape}')\n", "\n", "\tfor key, value in groups.items():\n", "\t\trun_1group(feature, df_label, df_style, df_indus, key, value, partition=rank)\n", "\n", "\t# num_workers = len(groups)\n", "\t# with Parallel(n_jobs=num_workers, backend='loky') as para:\n", "\t# \t_ = para(\n", "\t# \t\tdelayed(run_1group)(feature, df_label, df_style, df_indus, key, value) for key, value in groups.items()\n", "\t# \t)\n", "\n", "\n", "logger = Logger()\n", "config_filepath = args.config\n", "time_tag = args.timetag\n", "save_tag = config_filepath.split('/')[-1].split('.')[0]\n", "\n", "params = load_config(config_filepath)\n", "loader = Loader(**params)\n", "\n", "barra_product = params['barra_product']\n", "start_date, end_date = params['start_date'], params['end_date']\n", "feature_file_format = params['feature_file_format']\n", "groups = params['groups']\n", "label_list = params['label_list']\n", "label_usecols = params['label_usecols']\n", "pred = params['pred']\n", "corr_method = params['correlation_method']\n", "\n", "feature_root = params['feature_root']\n", "region_product = params['region_product']\n", "freq = params['freq']\n", "key_list = params['key_list']\n", "\n", "\n", "feature_names = sn_km.get_key_list(freq=freq,\n", "\t\t\t\t\t\t  key_group=key_list,\n", "\t\t\t\t\t\t  region_product=region_product,\n", "\t\t\t\t\t\t  load_root=feature_root)\n", "\n", "logger.info(f'feature_names: {feature_names}')\n", "feature_name_lists = []\n", "batch_feature_num = 20\n", "\n", "if len(feature_names) % batch_feature_num == 0:\n", "\tnum_array = len(feature_names) // batch_feature_num\n", "else:\n", "\tnum_array = len(feature_names) // batch_feature_num + 1 \n", "\n", "\n", "for i in range(0,len(feature_names),batch_feature_num):\n", "\tfeature_name_lists.append(feature_names[i:i+batch_feature_num])  # noqa: PERF401\n", "\n", "if num_array <= 1 and params['use_slurm']:\n", "\tlogger.info(f'num_feature ({len(feature_names)}) less than 20, no need to use slurm!')\n", "\tparams['use_slurm'] = False\n", "\n", "if __name__ == '__main__':\n", "\tif not params['use_slurm']:\n", "\t\twith Parallel(n_jobs=4, backend='multiprocessing') as para:\n", "\t\t\t_ = para(delayed(run_task)(i) for i in range(len(feature_name_lists[:])))\n", "\n", "\telif params['use_slurm']:\n", "\t\timport socket\n", "\t\tjob_id = os.environ[\"SLURM_ARRAY_JOB_ID\"]\n", "\t\trank = os.environ[\"SLURM_ARRAY_TASK_ID\"]\n", "\t\tsize = os.environ[\"SLURM_ARRAY_TASK_COUNT\"]\n", "\t\tname = socket.gethostname()\n", "\t\tprint(time_tag, __name__,job_id,rank,size,name,'###########\\n\\n')\n", "\t\trun_task(rank)\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## workflow2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### backtest2.py"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/backtest2.py'\n", "# coding = utf-8\n", "\n", "import os\n", "import time\n", "import socket\n", "import subprocess\n", "import numpy as np\n", "import pandas as pd\n", "import barra.utils.processor as prep\n", "\n", "from barra.data import loader\n", "from barra.utils import tools\n", "from barra.utils import calendar\n", "from barra.backtest import reporter\n", "from barra.backtest.Evaluator import Evaluator\n", "from barra.backtest.Stabilizer import Stabilizer\n", "\n", "\n", "def work_flow(df_factor, df_label, df_style, df_indus):\n", "    eva, stab = Evaluator(), Stabilizer()\n", "    tools.Logger.info(f\"calculation for {factor_name} starts.\", end=\"\\n\")\n", "\n", "    df_acf = stab.ACF_cs(df_factor, 20)\n", "    df_acf1 = stab.ACF_1day(df_factor)\n", "\n", "    df_hist = stab.hist(df_factor)\n", "    df_cov = stab.coverage(df_factor)\n", "    df_non_nan = stab.valid_counts(df_factor)\n", "\n", "    df_quantile = stab.quantile(df_factor, [0.01, 0.15, 0.5, 0.85, 0.99])\n", "    tools.Logger.info(\"feature stability calculation done!\", end=\"\\n\")\n", "\n", "    df_factor = df_factor.groupby(level=\"date\").apply(prep.winsorize).droplevel(0)\n", "    df_hist2 = stab.hist(df_factor)\n", "\n", "    df_factor = df_factor.groupby(\"date\").apply(lambda x: prep.add_univer_mask(x, cfg))\n", "    tools.Logger.info(\"feature winsorized\", end=\"\\n\")\n", "\n", "    df_alpha = eva.calc_alpha(df_factor, df_label)\n", "    df_ic = eva.calc_ic(df_factor, df_label, corr_method)\n", "    df_exposure = eva.calc_indus_exposure(df_factor, df_indus)\n", "    df_corr = eva.calc_intra_corr(df_factor, df_style, corr_method)\n", "    tools.Logger.info(\"raw feature evaluation done!\", end=\"\\n\")\n", "\n", "    if not cfg.get(\"backtest_neu\"):\n", "        return {\n", "            \"stab\": {\n", "                \"df_acf\": df_acf,\n", "                \"df_acf1\": df_acf1,\n", "                \"df_hist\": df_hist,\n", "                \"df_hist2\": df_hist2,\n", "                \"df_cov\": df_cov,\n", "                \"df_non_nan\": df_non_nan,\n", "                \"df_quantile\": df_quantile,\n", "            },\n", "            \"raw_eva\": {\n", "                \"df_alpha\": df_alpha,\n", "                \"df_ic\": df_ic,\n", "                \"df_exposure\": df_exposure,\n", "                \"df_corr\": df_corr,\n", "            },\n", "        }\n", "    else:\n", "        df_neutral = prep.neutralize(df_factor, df_style)\n", "        df_neutral = df_neutral.groupby(\"date\").apply(\n", "            lambda x: prep.add_univer_mask(x, cfg)\n", "        )\n", "\n", "        df_alpha_ = eva.calc_alpha(df_neutral, df_label)\n", "        df_ic_ = eva.calc_ic(df_neutral, df_label, corr_method)\n", "        df_exposure_ = eva.calc_indus_exposure(df_neutral, df_indus)\n", "        df_corr_ = eva.calc_intra_corr(df_neutral, df_style, corr_method)\n", "        tools.Logger.info(\"neutralized evaluation done!\", end=\"\\n\")\n", "\n", "        return {\n", "            \"stab\": {\n", "                \"df_acf\": df_acf,\n", "                \"df_hist\": df_hist,\n", "                \"df_hist2\": df_hist2,\n", "                \"df_cov\": df_cov,\n", "                \"df_quantile\": df_quantile,\n", "            },\n", "            \"raw_eva\": {\n", "                \"df_alpha\": df_alpha,\n", "                \"df_ic\": df_ic,\n", "                \"df_exposure\": df_exposure,\n", "                \"df_corr\": df_corr,\n", "            },\n", "            \"neu_eva\": {\n", "                \"df_alpha\": df_alpha_,\n", "                \"df_ic\": df_ic_,\n", "                \"df_exposure\": df_exposure_,\n", "                \"df_corr\": df_corr_,\n", "            },\n", "        }\n", "\n", "\n", "def run_1group(feature, df_label, df_style, df_indus):\n", "    f_, l_, style, indus = feature, df_label, df_style, df_indus\n", "    tools.Logger.info(\n", "        f\"{factor_name}: {f_.shape}, {l_.shape}, {style.shape}, {indus.shape}\", end=\"\\n\"\n", "    )\n", "    if f_.shape[0] * l_.shape[0] * style.shape[0] * indus.shape[0] == 0:\n", "        tools.Logger.info(\n", "            \"one of feature, label, style, indus is empty, do nothing, exit!\", end=\"\\n\"\n", "        )\n", "        return None\n", "    if (len(f_) == 0) | (len(l_) == 0):\n", "        return None\n", "    result = work_flow(f_, l_, style, indus)\n", "\n", "    report_folder = cfg.get(\"report_folder\")\n", "    reporter.gen_csv(result, report_folder=report_folder)\n", "    tools.Logger.info(f\"csv report generated for ({factor_group_name})\", end=\"\\n\")\n", "    reporter.gen_pdf_by_factor(result, report_folder=report_folder)\n", "    tools.Logger.info(f\"pdf report generated for ({factor_group_name})\", end=\"\\n\")\n", "    ann_report = reporter.annual_report(result, report_folder=report_folder)\n", "    # reporter.report_summary(ann_report, report_folder=report_folder)\n", "\n", "\n", "def run_task(cfg: dict, factor_list: list = []):\n", "    tools.Logger.info(f\"loading factor: {factor_name}\")\n", "    sDate, eDate = cfg.get(\"sDate\"), cfg.get(\"eDate\")\n", "    floader = loader.FactorLoader(\"./configs/user.path.json\")\n", "\n", "    factor_saveroot = cfg.get(\"factor_saveroot\")\n", "    feature = floader.load(\n", "        sDate=sDate,\n", "        eDate=eDate,\n", "        factor_name=cfg.get(\"factor_name\"),\n", "        factor_group_name=cfg.get(\"factor_group_name\"),\n", "        factor_type=cfg.get(\"factor_type\"),\n", "        backfill=cfg.get(\"backfill\"),\n", "        factor_root=f\"{factor_saveroot}\",\n", "    )\n", "    feature = feature.loc[~feature.index.duplicated()]  ## remove duplicated entries\n", "    feature = prep.check_universe_mask(feature, cfg)\n", "\n", "    ## if feature contains inf or -inf, replace with NAN\n", "    if np.isinf(feature).sum().sum() > 0:\n", "        tools.Logger.info(\"feature contains inf or -inf, replace with NAN\")\n", "        feature.replace([np.inf, -np.inf], np.nan, inplace=True)\n", "\n", "    ## load industry and style factors for neutralization and exposure calculation\n", "    dloader = loader.DataLoader()\n", "    df_indus_style = dloader.load_indus_style_factors(sDate, eDate, cfg)\n", "    df_indus_style = df_indus_style.loc[~df_indus_style.index.duplicated()]\n", "    tools.Logger.info(\"loading style and indus factors done\")\n", "\n", "    ## load label for alpha calculation\n", "    df_label, risk = loader.load_label(sDate, eDate, cfg)\n", "    df_label = df_label.loc[~df_label.index.duplicated()]\n", "    df_label = prep.check_universe_mask(df_label, cfg)\n", "    tools.Logger.info(f\"loading label done, label shape: {df_label.shape}\")\n", "\n", "    ## df_indus_style, first column is indcell factor as string, rest are indus factors as float\n", "    run_1group(\n", "        feature, df_label, df_indus_style.iloc[:, 1:], df_indus_style.iloc[:, [0]]\n", "    )\n", "\n", "\n", "if tools.is_notebook():\n", "    prefix = \"/mnt/sda/home/<USER>/working/gitrepo/barra_demo/\"\n", "    config_filepath = prefix + \"configs/backtest/xielan.puda.json\"\n", "    time_tag = time.strftime(\"%Y%m%d_%H%M%S\")\n", "    save_tag = \"xielan.puda\"\n", "    user_path = prefix + \"configs/user.path.json\"\n", "    country_path = prefix + \"configs/country.set.json\"\n", "else:\n", "    ## reading command line arguments, config file path and time tag\n", "    args = tools.parse_dynamic_args()\n", "    assert hasattr(args, \"config\"), \"config file path must be provided!\"\n", "    config_filepath = args.config\n", "    time_tag = (\n", "        args.timetag if hasattr(args, \"timetag\") else time.strftime(\"%Y%m%d_%H%M%S\")\n", "    )\n", "    save_tag = config_filepath.split(\"/\")[-1].split(\".json\")[0]\n", "    user_path = \"./configs/user.path.json\"\n", "    country_path = \"./configs/country.set.json\"\n", "\n", "\n", "## load backtest config file\n", "cfg = loader.load_config(config_filepath)\n", "factor_group_name = cfg.get(\"factor_group_name\")\n", "factor_name = cfg.get(\"factor_name\")\n", "corr_method = cfg.get(\"correlation_method\")\n", "\n", "## dynamically add factor_saveroot and report_folder to cfg for user convenience\n", "user_root = tools.parse_user_root(user_path)\n", "cfg[\"factor_saveroot\"] = f\"{user_root}factor/{cfg.get('barra_product')}/\"\n", "cfg[\"ctry_list\"] = loader.load_config(country_path)[cfg.get(\"country_set\")]\n", "cfg[\"report_folder\"] = (\n", "    f\"{cfg.get('factor_saveroot')}{factor_group_name}/{factor_name}/report_{time_tag}/\"\n", ")\n", "cfg['backfill'] = None\n", "ip = socket.gethostbyname(socket.gethostname())\n", "factor_names = loader.peek_factor_names(cfg)\n", "if len(factor_names) > 30: # need to generate slurm script\n", "    if 'SLURM_JOB_ID' in os.environ: \n", "        print(os.environ[\"SLURM_JOB_ID\"], os.environ[\"SLURM_ARRAY_TASK_ID\"], flush=True)\n", "    num_array = int(np.ceil(len(factor_names) / 30))\n", "    factor_names_list = np.array_split(factor_names, num_array)\n", "    task_map = {str(i + 1): factor_names_list[i] for i in range(num_array)}\n", "\n", "\n", "def main():\n", "    if cfg[\"use_slurm\"]: \n", "        ## generate slurm script if factor number is more than 30\n", "        if ('SLURM_JOB_ID' not in os.environ): # not in slurm cluster\n", "            if len(factor_names) > 30: # need to generate slurm script\n", "                assert ip == '************', \"slurm scripts should be generated on SLURM MANAGER (************)\"\n", "                pyargs = f'{os.path.abspath(__file__)} --config {config_filepath} --timetag {time_tag}'\n", "                job_name = 'bt2_' + cfg.get('factor_name')\n", "                script = tools.generate_slurm_commands(\"AMD_R\", 60, num_array, pyargs=pyargs, job_name=job_name)\n", "                print(script)\n", "                subprocess.run([\"sbatch\"],input=script.encode(\"utf-8\"),check=True)\n", "            else:\n", "                if ip == '************': # not in slurm cluster, but running on SLURM MANAGER\n", "                    tools.Logger.info('No tasks to be run on SLURM MANAGER, exit!',end='\\n')\n", "                    exit()\n", "                else: # not in slurm cluster, and not running on SLURM MANAGER\n", "                    run_task(cfg, factor_list=factor_names)\n", "        elif 'SLURM_JOB_ID' in os.environ: # already in slurm cluster, run separate task\n", "            run_task(cfg, factor_list=task_map[os.environ[\"SLURM_ARRAY_TASK_ID\"]])\n", "    else:  ## run locally\n", "        run_task(cfg, factor_list=factor_names)\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "pd.read_parquet('/mnt/sda/NAS/ShareFolder/pengpuda/factor_analysis/euronext/SP/features/EUTR/buysideInst/EU1/raw/2024-01-02.parquet')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["os.listdir('/mnt/sda/NAS/ShareFolder/pengpuda/factor_analysis/euronext/SP/features/EUTR/SP_SET_RAW/')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/home/<USER>/working/slurm/test.py'\n", "import os\n", "import socket\n", "import subprocess\n", "\n", "\n", "def main():\n", "    if \"SLURM_JOB_ID\" in os.environ:\n", "        run_core_computation()\n", "    else:\n", "        submit_via_stdin()\n", "\n", "\n", "def submit_via_stdin():\n", "    # 构建 Slurm 脚本内容\n", "\n", "    slurm_script = generate_slurm_commands(\"AMD_R\", ncpus=1, array=5)\n", "    # 通过管道直接提交（无需落盘）\n", "    subprocess.run(\n", "        [\"sbatch\"],\n", "        input=slurm_script.encode(\"utf-8\"),  # 必须编码为字节流\n", "        check=True,  # 检查命令执行结果\n", "    )\n", "\n", "\n", "def generate_slurm_commands(\n", "    partition: str, ncpus: int, array: int, mem_per_cpu: int = 4\n", "):\n", "    slurm_script = f\"\"\"#!/bin/bash\n", "#SBATCH --job-name=direct_submit\n", "#SBATCH --output=slurm_log/%A_%a.out\n", "#SBATCH --partition={partition}\n", "#SBATCH --nodes=1\n", "#SBATCH --ntasks-per-node=1\n", "#SBATCH --cpus-per-task={ncpus}\n", "#SBATCH --mem-per-cpu={mem_per_cpu}G\n", "#SBATCH --array=1-{array}\n", "\n", "source /mnt/sda/NAS/ShareFolder/lishuanglin/share/miniconda3/etc/profile.d/conda.sh\n", "conda activate barra_base\n", "\n", "python {os.path.abspath(__file__)} --config {config_filepath} --timetag {time_tag}\n", "\"\"\"\n", "    return slurm_script\n", "\n", "\n", "def run_core_computation():\n", "    print(\"核心计算执行中...\")\n", "    print(socket.gethostname())\n", "    print(socket.gethostbyname(socket.gethostname()))\n", "    for env in os.environ:\n", "        if env.startswith(\"SLURM\"):\n", "            print(env, os.environ[env])\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/fg.py'\n", "\n", "import os\n", "import sys\n", "import time\n", "import json\n", "import socket\n", "import argparse\n", "from shennong.utils import key_group_manager as sn_km\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\"--config\", type=str, default=\"./config/\")\n", "args = parser.parse_args()\n", "\n", "\n", "def load_config(config_filepath: str):\n", "    print(f\"loading config from {config_filepath}\")\n", "    with open(config_filepath, \"r\", encoding=\"utf-8\") as f:\n", "        return json.load(f)\n", "\n", "\n", "config_filepath = args.config\n", "save_tag = config_filepath.split(\"/\")[-1].split(\".\")[0]\n", "time_tag = time.strftime(\"%Y%m%d_%H%M%S\")\n", "\n", "params = load_config(config_filepath)\n", "freq = params[\"freq\"]\n", "key_list = params[\"key_list\"]\n", "region_product = params[\"region_product\"]\n", "feature_root = params[\"feature_root\"]\n", "\n", "feature_names = sn_km.get_key_list(\n", "    freq=freq, key_group=key_list, region_product=region_product, load_root=feature_root\n", ")\n", "\n", "feature_name_lists = []\n", "batch_feature_num = 20\n", "\n", "if len(feature_names) % batch_feature_num == 0:\n", "    num_array = len(feature_names) // batch_feature_num\n", "else:\n", "    num_array = len(feature_names) // batch_feature_num + 1\n", "\n", "use_slurm = params[\"use_slurm\"]\n", "\n", "if use_slurm:\n", "    ip = socket.gethostbyname(socket.gethostname())\n", "    assert ip=='************', \"slurm jobs should be allocated on SLURM MANAGER (************)\"\n", "    \n", "    if not os.path.exists(\"./tmp/\"):\n", "        os.makedirs(\"./tmp/\", exist_ok=True)\n", "\n", "    with open(f\"./tmp/{save_tag}_{time_tag}.sh\", \"w\") as f:\n", "        f.write(\"#!/bin/bash\\n\")\n", "        f.write(f\"#SBATCH -o ./tmp/slurm_log/{save_tag}_{time_tag}/%A_%a.out\\n\")\n", "        f.write(\"#SBATCH --partition=AMD_R\\n\")\n", "        f.write(\"#SBATCH --nodes=1\\n\")\n", "        f.write(\"#SBATCH --ntasks-per-node=1\\n\")\n", "        f.write(\"#SBATCH --cpus-per-task=60\\n\")\n", "        f.write(\"#SBATCH --mem=100G\\n\")\n", "        f.write(f\"#SBATCH --array=0-{num_array - 1}\\n\\n\")\n", "\n", "        f.write(\n", "            \"source /mnt/sda/NAS/ShareFolder/lishuanglin/share/miniconda3/etc/profile.d/conda.sh\\n\"\n", "        )\n", "        f.write(\"conda activate barra_base\\n\")\n", "        f.write(\n", "            'echo -e \"slurm_id: ${SLURM_ARRAY_TASK_ID} \\\\nconda_path: `which conda`\"\\n'\n", "        )\n", "        f.write('echo -e \"script_path: ./backtest.py\"\\n')\n", "        f.write(\n", "            f\"python backtest.py --config ./configs/{save_tag}.json --timetag {time_tag}\\n\"\n", "        )\n", "\n", "    print(\n", "        f\"slurm script genereated [./tmp/{save_tag}_{time_tag}.sh]: {os.path.exists(f'./tmp/{save_tag}_{time_tag}.sh')}\"\n", "    )\n", "    os.system(f\"sbatch ./tmp/{save_tag}_{time_tag}.sh\")\n", "\n", "elif not use_slurm:\n", "    os.system(f\"python ./backtest.py --config {config_filepath} --timetag {time_tag}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### fg2.py"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/fg2.py'\n", "# coding = utf-8\n", "import os\n", "import sys\n", "import time\n", "import json\n", "import argparse\n", "from barra.data import loader\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument('--config', type=str, default='./config/')\n", "args = parser.parse_args()\n", "\n", "\n", "config_filepath = args.config\n", "save_tag = config_filepath.split('/')[-1].split('.')[0]\n", "time_tag = time.strftime('%Y%m%d_%H%M%S')\n", "\n", "cfg = loader.load_config(config_filepath)\n", "os.system(f\"python ./backtest2.py --config {config_filepath} --timetag {time_tag}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/fg.py'\n", "\n", "import os\n", "import sys\n", "import time\n", "import json\n", "import socket\n", "import argparse\n", "from barra.data import loader\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\"--config\", type=str, default=\"./config/\")\n", "args = parser.parse_args()\n", "\n", "\n", "config_filepath = args.config\n", "save_tag = config_filepath.split(\"/\")[-1].split(\".\")[0]\n", "time_tag = time.strftime(\"%Y%m%d_%H%M%S\")\n", "\n", "params = loader.load_config(config_filepath)\n", "feature_root = params[\"feature_root\"]\n", "batch_feature_num = 20\n", "\n", "if len(feature_names) % batch_feature_num == 0:\n", "    num_array = len(feature_names) // batch_feature_num\n", "else:\n", "    num_array = len(feature_names) // batch_feature_num + 1\n", "\n", "use_slurm = params[\"use_slurm\"]\n", "\n", "if use_slurm:\n", "    ip = socket.gethostbyname(socket.gethostname())\n", "    assert ip=='************', \"slurm jobs should be allocated on SLURM MANAGER (************)\"\n", "    \n", "    if not os.path.exists(\"./tmp/\"):\n", "        os.makedirs(\"./tmp/\", exist_ok=True)\n", "\n", "    with open(f\"./tmp/{save_tag}_{time_tag}.sh\", \"w\") as f:\n", "        f.write(\"#!/bin/bash\\n\")\n", "        f.write(f\"#SBATCH -o ./tmp/slurm_log/{save_tag}_{time_tag}/%A_%a.out\\n\")\n", "        f.write(\"#SBATCH --partition=AMD_R\\n\")\n", "        f.write(\"#SBATCH --nodes=1\\n\")\n", "        f.write(\"#SBATCH --ntasks-per-node=1\\n\")\n", "        f.write(\"#SBATCH --cpus-per-task=60\\n\")\n", "        f.write(\"#SBATCH --mem=100G\\n\")\n", "        f.write(f\"#SBATCH --array=0-{num_array - 1}\\n\\n\")\n", "\n", "        f.write(\n", "            \"source /mnt/sda/NAS/ShareFolder/lishuanglin/share/miniconda3/etc/profile.d/conda.sh\\n\"\n", "        )\n", "        f.write(\"conda activate barra_base\\n\")\n", "        f.write(\n", "            'echo -e \"slurm_id: ${SLURM_ARRAY_TASK_ID} \\\\nconda_path: `which conda`\"\\n'\n", "        )\n", "        f.write('echo -e \"script_path: ./backtest.py\"\\n')\n", "        f.write(\n", "            f\"python backtest.py --config ./configs/{save_tag}.json --timetag {time_tag}\\n\"\n", "        )\n", "\n", "    print(\n", "        f\"slurm script genereated [./tmp/{save_tag}_{time_tag}.sh]: {os.path.exists(f'./tmp/{save_tag}_{time_tag}.sh')}\"\n", "    )\n", "    os.system(f\"sbatch ./tmp/{save_tag}_{time_tag}.sh\")\n", "\n", "elif not use_slurm:\n", "    os.system(f\"python ./backtest.py --config {config_filepath} --timetag {time_tag}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## DEBUG"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pd.read_parquet('/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/EUTR/2024-01-02.parquet')\n", "\n", "import os\n", "import pandas as pd\n", "from tqdm.notebook import tqdm\n", "\n", "# feature_root = '/mnt/sda/NAS/ShareFolder/xielan/tmpdata/otheralpha/xfc/'\n", "# saveroot = '/mnt/sda/NAS/ShareFolder/lishuanglin/test/factor/CNTR/xielan/xfc/raw/'\n", "\n", "feature_root = '/mnt/sda/NAS/ShareFolder/pengpuda/temp/to_xielan/feature/factset_cnashare_sample/'\n", "saveroot = '/mnt/sda/NAS/ShareFolder/lishuanglin/test/factor/CNTR/xielan/puda/raw/'\n", "\n", "universe_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/universe/cn_ashare/'\n", "\n", "def from_ashare(feature_root: str, date: str):\n", "    df = pd.read_parquet(f'{feature_root}{date}.parquet')\n", "    df.index = df.index.str.replace('.S', '.XS')\n", "    df.dropna(how='all', inplace=True)\n", "\n", "    universe = pd.read_csv(f'{universe_root}{date}.csv', dtype=str)\n", "    universe['index'] = universe['LocalID'] + '.' + universe['OpMIC']\n", "    mgd = df.reset_index().merge(universe[['BarraID','index']], on='index', how='left').set_index('BarraID')\n", "    return mgd.loc[~mgd.index.isna()].drop_duplicates().drop(columns=['index'])\n", "\n", "\n", "def convert_csv2parquet(feature_root: str, date: str):\n", "    df = pd.read_csv(f'{feature_root}{date}.csv')\n", "    df[['LocalID','OpMIC']] = df['symbol'].str.split('.', expand=True)\n", "    df['OpMIC'] = df['OpMIC'].map({'SZ':'XSHE', 'SH': 'XSHG'})\n", "\n", "    univ = pd.read_csv(f'{universe_root}{date}.csv', dtype=str)\n", "    mgd = df.merge(univ, on=['LocalID', 'OpMIC']).set_index('BarraID')\n", "    return mgd.loc[:, df.columns[1:-2]]\n", "\n", "\n", "dates = sorted([s.split('.')[0] for s in os.listdir(feature_root)])\n", "if not os.path.exists(saveroot): os.makedirs(saveroot, exist_ok=True)\n", "\n", "\n", "for date in dates:\n", "    if os.path.exists(f'{saveroot}{date}.parquet'): continue\n", "    try:\n", "        df = from_ashare(feature_root, date)\n", "    except Exception as err:\n", "        print(err)\n", "        continue\n", "    df.to_parquet(f'{saveroot}{date}.parquet_020000')\n", "    print(f'file saved for {date} / {dates[-1]}', end='\\r')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "pd.read_parquet('/mnt/sda/NAS/ShareFolder/lishuanglin/test/factor/CNTR/xielan/puda/raw/2024-01-02.parquet_020000')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from barra.data.loader import load_config, FactorLoader\n", "sDate, eDate = '2022-01-01', '2022-08-30'\n", "\n", "\n", "user_path_cfg = '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/configs/user.path.json'\n", "# cfg = load_config('/mnt/sda/home/<USER>/working/gitrepo/barra_demo/configs/fc/fc.wmmom.eu1.01.json')\n", "\n", "froot = '/mnt/sda/NAS/ShareFolder/lishuanglin/test/factor/CNTR/'\n", "fd = FactorLoader(user_path_cfg)\n", "df_factor = fd.load(sDate, eDate, 'xfc','xielan','raw',factor_root=froot)\n", "df_factor = df_factor.loc[~df_factor.index.duplicated()]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_factor"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# utils"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## tools.py"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/utils/tools.py'\n", "\n", "# coding = utf-8\n", "__all__ = [\n", "    \"parallel\",\n", "    \"parse_user_name\",\n", "    \"parse_user_root\",\n", "    \"parse_dynamic_args\",\n", "    \"is_notebook\",\n", "    \"generate_slurm_commands\",\n", "    \"Logger\",\n", "]\n", "\n", "import os\n", "import sys\n", "import getpass\n", "import datetime\n", "import argparse\n", "from tqdm.auto import tqdm\n", "from joblib import Parallel, delayed\n", "\n", "\n", "## parallel execution for multiple arguments\n", "def parallel(\n", "    func: callable,\n", "    list_args: list[tuple],\n", "    njobs: int = 10,\n", "    backend: str = \"multiprocessing\",\n", "    progress_bar: bool = False,\n", "    desc: str = \"\",\n", "):\n", "    \"\"\"Executes a function in parallel across multiple arguments.\n", "\n", "    Args:\n", "            func (callable): Function to execute in parallel\n", "            list_args (list[tuple]): List of argument tuples to pass to func, e.g. [('a',), ('b',)]\n", "            njobs (int, optional): Number of parallel jobs. Defaults to 10.\n", "            backend (str, optional): Joblib parallel backend. Defaults to 'multiprocessing'.\n", "            progress_bar (bool, optional): Whether to show progress bar. Defaults to False.\n", "            desc (str, optional): Description for progress bar. Defaults to ''.\n", "\n", "    Returns:\n", "            list: Results from parallel execution of func across list_args\n", "    \"\"\"\n", "    list_args = tqdm(list_args, desc=desc, disable=not progress_bar)\n", "    with Parallel(n_jobs=njobs, backend=backend) as para:\n", "        return para(delayed(func)(*arg) for arg in list_args)\n", "\n", "\n", "## parse user name and root\n", "def parse_user_name():\n", "    \"\"\"parse user name from current system\"\"\"\n", "    return getpass.getuser()\n", "\n", "\n", "def parse_user_root(user_path: str = \"./configs/user.path.json\"):\n", "    \"\"\"parse user root from user_path.json, which is like:\n", "    {\n", "            'lishuanglin': '/mnt/sda/NAS/ShareFolder/lishuanglin/test/',\n", "    }\n", "    \"\"\"\n", "    from ..data import loader\n", "\n", "    user_path_configs = loader.load_config(user_path)\n", "    user_name = parse_user_name()\n", "    if user_name not in user_path_configs.keys():\n", "        raise Exception(\n", "            f\"no save path found for user {user_name} in {user_path_configs}\"\n", "        )\n", "    else:\n", "        return user_path_configs.get(user_name, None)\n", "\n", "\n", "## parse dynamic command line arguments\n", "def parse_dynamic_args():\n", "    \"\"\"\n", "    Parses command-line arguments dynamically without pre-defining them.\n", "\n", "    Handles arguments in the format --key value or -k value.\n", "    Handles boolean flags like --flag or -f (assigned True).\n", "\n", "    Typically used in python scripts whose args are passed from command line (e.g. in bash).\n", "\n", "    Returns:\n", "            argparse.Namespace: An object containing parsed arguments as attributes.\n", "                                                    Keys are derived from argument names (leading hyphens removed).\n", "                                                    Values are strings, or True for boolean flags.\n", "    \"\"\"\n", "    args_dict = {}\n", "    argv = sys.argv[1:]  ## the first arg is the script name\n", "    i = 0\n", "    while i < len(argv):\n", "        arg = argv[i]\n", "        if arg.startswith(\"-\"):\n", "            key = arg.lstrip(\"-\")\n", "\n", "            if i + 1 < len(argv) and not argv[i + 1].startswith(\"-\"):\n", "                value = argv[i + 1]\n", "                args_dict[key] = value\n", "                i += 2\n", "            else:\n", "                args_dict[key] = True\n", "                i += 1\n", "        else:\n", "            print(f\"Warning: Ignoring non-keyed argument '{arg}'\")\n", "            i += 1\n", "\n", "    return argparse.Namespace(**args_dict)\n", "\n", "\n", "def is_notebook():\n", "    \"\"\"check if the script is running in a notebook\"\"\"\n", "    return \"ipykernel\" in sys.modules\n", "\n", "\n", "def generate_slurm_commands(\n", "    partition: str, ncpus: int, array: int, mem_per_cpu: int = 4, pyargs: str = \"\", job_name: str = \"\"\n", "):\n", "    \"\"\"generate slurm script for parallel execution with slurm cluster\n", "    Args:\n", "            partition (str): slurm partition\n", "            ncpus (int): number of cpus per task\n", "            array (int): number of tasks to run in parallel\n", "            mem_per_cpu (int, optional): memory per cpu. Defaults to 4.\n", "\n", "    Returns:\n", "            str: slurm script\n", "    \"\"\"\n", "    slurm_script = f\"\"\"#!/bin/bash\n", "#SBATCH --output=slurm_log/%A_%a.out\n", "#SBATCH --partition={partition}\n", "#SBATCH --job-name={job_name if job_name != '' else 'sbatch.sh'}\n", "#SBATCH --nodes=1\n", "#SBATCH --ntasks-per-node=1\n", "#SBATCH --cpus-per-task={ncpus}\n", "#SBATCH --mem-per-cpu={mem_per_cpu}G\n", "#SBATCH --array=1-{array}\n", "\n", "source /mnt/sda/NAS/ShareFolder/lishuanglin/share/miniconda3/etc/profile.d/conda.sh\n", "conda activate barra_base\n", "\n", "python {pyargs}\"\"\"\n", "    return slurm_script\n", "\n", "\n", "class Logger:\n", "    def __init__(self) -> None:\n", "        pass\n", "\n", "    @staticmethod\n", "    def info(message: str, end: str = \"\\r\"):\n", "        \"\"\"log info message, automatically add timestamp\"\"\"\n", "        now = datetime.datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "        print(f\"[{now}] {message}\", flush=True, end=end)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.read_parquet('/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/CNTR/2024-01-02.parquet',columns=['SpecRisk%','BarraID','LocalID','OpMIC'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## processor.py"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/utils/processor.py'\n", "# coding = utf-8\n", "\n", "import warnings\n", "import numpy as np\n", "import pandas as pd\n", "from .tools import Logger\n", "from sklearn.linear_model import LinearRegression\n", "\n", "\n", "def add_univer_mask(df: pd.DataFrame, cfg: dict):\n", "    \"\"\"add universe mask for filtering\n", "    Args:\n", "            df (pd.DataFrame): Input dataframe with BarraID index,\n", "                            the name of df should be date, typically used as pd.groupby.apply(add_univer_mask)\n", "            cfg (dict): Configuration dictionary containing factor_group_name\n", "\n", "    Returns:\n", "            pd.DataFrame: DataFrame with added universe_mask column\n", "    \"\"\"\n", "    warnings.filterwarnings(\"ignore\")\n", "    barraids = df.index.get_level_values(\"BarraID\")\n", "    date = df.name\n", "    df = df.droplevel(0)\n", "\n", "    factor_group_name = cfg.get(\"factor_group_name\", None)\n", "    if factor_group_name is None:\n", "        raise ValueError(\"factor_group_name is None\")\n", "\n", "    def load_daily_universe(date):\n", "        univ_root = \"/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/universe/\"\n", "        universe_name = cfg.get(\"universe\", \"global\")\n", "        universe_root = f\"{univ_root}{universe_name}/\"\n", "        universe = pd.read_csv(f\"{universe_root}{date}.csv\")\n", "        return universe[\"BarraID\"].tolist()\n", "\n", "    try:\n", "        universe = load_daily_universe(date)\n", "        df[\"universe_mask\"] = barraids.isin(universe).astype(np.float64)\n", "    except:  # noqa: E722\n", "        df[\"universe_mask\"] = 0.0\n", "    return df\n", "\n", "\n", "def add_rootid_mask(df: pd.DataFrame, cfg: dict):\n", "    \"\"\"add universe mask for filtering\n", "    Args:\n", "            df (pd.DataFrame): Input dataframe with BarraID index,\n", "                            the name of df should be date, typically used as pd.groupby.apply(add_univer_mask)\n", "            cfg (dict): Configuration dictionary containing factor_group_name\n", "\n", "    Returns:\n", "            pd.DataFrame: DataFrame with added rootid_mask column\n", "    \"\"\"\n", "    warnings.filterwarnings(\"ignore\")\n", "    barraids = df.index.get_level_values(\"BarraID\")\n", "    date = df.name\n", "    df = df.droplevel(0)\n", "\n", "    factor_group_name = cfg.get(\"factor_group_name\", None)\n", "    if factor_group_name is None:\n", "        raise ValueError(\"factor_group_name is None\")\n", "\n", "    def filter_barraids(date):\n", "        univ_root = \"/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/universe/\"\n", "        universe_name = cfg.get(\"universe\", \"global\")\n", "        universe_root = f\"{univ_root}{universe_name}/\"\n", "        universe = pd.read_csv(f\"{universe_root}{date}.csv\")\n", "        return universe.loc[\n", "            universe[\"BarraID\"] == universe[\"RootID\"], \"BarraID\"\n", "        ].tolist()\n", "\n", "    try:\n", "        filtered_barraids = filter_barraids(date)\n", "        df[\"rootid_mask\"] = barraids.isin(filtered_barraids).astype(np.float64)\n", "    except:  # noqa: E722\n", "        df[\"rootid_mask\"] = 0.0\n", "    return df\n", "\n", "\n", "def check_universe_mask(df: pd.DataFrame, cfg: dict):\n", "    \"\"\"check if universe mask is already added, if not, add it\n", "    Args:\n", "            df (pd.DataFrame): Input dataframe with MultiIndex (date, BarraID)\n", "            cfg (dict): Configuration dictionary containing factor_group_name\n", "    Returns:\n", "            pd.DataFrame: DataFrame with added universe_mask column\n", "    \"\"\"\n", "\n", "    if \"universe_mask\" not in df.columns:\n", "        df = df.groupby(\"date\").apply(lambda x: add_univer_mask(x, cfg))\n", "        Logger.info(\"adding universe mask done\")\n", "    return df\n", "\n", "def check_rootid_mask(df: pd.DataFrame, cfg: dict):\n", "    \"\"\"check if rootid mask is already added, if not, add it\n", "    Args:\n", "            df (pd.DataFrame): Input dataframe with MultiIndex (date, BarraID)\n", "            cfg (dict): Configuration dictionary containing factor_group_name\n", "    Returns:\n", "            pd.DataFrame: DataFrame with added rootid_mask column\n", "    \"\"\"\n", "\n", "    if \"rootid_mask\" not in df.columns:\n", "        df = df.groupby(\"date\").apply(lambda x: add_rootid_mask(x, cfg))\n", "        Logger.info(\"adding rootid mask done\")\n", "    return df\n", "\n", "def as_weight(df: pd.DataFrame):\n", "    \"\"\"convert raw factor as weight (score), typically used in groupby.apply\n", "    Args:\n", "            df (pd.DataFrame): Input dataframe\n", "\n", "    Returns:\n", "            pd.DataFrame: Normalized weights that sum to 1\n", "    \"\"\"\n", "    return df.div(df.abs().sum())\n", "\n", "\n", "def demean(df: pd.DataFrame):\n", "    \"\"\"demean each column of df, typically used in groupby.apply\n", "    Args:\n", "            df (pd.DataFrame): Input dataframe\n", "\n", "    Returns:\n", "            pd.DataFrame: Demeaned dataframe\n", "    \"\"\"\n", "    return df.sub(df.mean())\n", "\n", "\n", "def zscore(df: pd.DataFrame):\n", "    \"\"\"standardize each column of df to a normal distribution, typically used in groupby.apply\n", "    Args:\n", "            df (pd.DataFrame): Input dataframe\n", "\n", "    Returns:\n", "            pd.DataFrame: Standardized dataframe with mean 0 and std 1\n", "    \"\"\"\n", "    return df.sub(df.mean()).div(df.std())\n", "\n", "\n", "def winsorize_with_mad(df: pd.DataFrame, sigma: int = 4):\n", "    \"\"\"winsorize each column of df using median absolute deviation to recap outliers, typically used in groupby.apply\n", "    Args:\n", "            df (pd.DataFrame): Input dataframe\n", "            sigma (int): Number of MAD for winsorization bounds. Defaults to 4.\n", "\n", "    Returns:\n", "            pd.DataFrame: Winsorized dataframe using median absolute deviation\n", "    \"\"\"\n", "    median_ = df.median()\n", "    mad = df.sub(median_).abs().median()\n", "    upper = median_ + sigma * mad\n", "    lower = median_ - sigma * mad\n", "    return df.mask(df >= upper, upper, axis=1).mask(df <= lower, lower, axis=1)\n", "\n", "\n", "def winsorize_with_std(df: pd.DataFrame, sigma: int = 4):\n", "    \"\"\"winsorize each column of df using standard deviation to recap outliers, typically used in groupby.apply\n", "    Args:\n", "            df (pd.DataFrame): Input dataframe\n", "            sigma (int): Number of std for winsorization bounds. Defaults to 4.\n", "\n", "    Returns:\n", "            pd.DataFrame: Winsorized dataframe using standard deviation\n", "    \"\"\"\n", "    mean_, std_ = df.mean(), df.std()\n", "    upper = mean_ + sigma * std_\n", "    lower = mean_ - sigma * std_\n", "    return df.mask(df >= upper, upper, axis=1).mask(df <= lower, lower, axis=1)\n", "\n", "\n", "def winsorize_by_pct(df: pd.DataFrame, percentile: float = 0.01):\n", "    \"\"\"winsorize each column of df using percentiles to recap outliers, typically used in groupby.apply\n", "    Args:\n", "            df (pd.DataFrame): Input dataframe\n", "            percentile (float): Percentile for winsorization bounds. Defaults to 0.01 could work fine in most cases.\n", "\n", "    Returns:\n", "            pd.DataFrame: Winsorized dataframe using percentiles\n", "    \"\"\"\n", "    df_ = df.copy()\n", "    upper = df_.quantile(1 - percentile)\n", "    lower = df_.quantile(percentile)\n", "    return df_.mask(df_ >= upper, upper, axis=1).mask(df_ <= lower, lower, axis=1)\n", "\n", "\n", "def winsorize(\n", "    df: pd.<PERSON><PERSON><PERSON><PERSON>,\n", "    method: str = \"median_mad\",\n", "    sigma: int = 4,\n", "    percentile: float = 0.01,\n", "):\n", "    \"\"\"winsorize each column of df using specified method and bounds to recap outliers, typically used in groupby.apply\n", "    Args:\n", "            df (pd.DataFrame): Input dataframe\n", "            method (str): Winsorization method ('median_mad', 'mean_std', or 'percentile'). Defaults to 'median_mad'.\n", "            sigma (int): Number of std/mad for bounds. Defaults to 4.\n", "            percentile (float): Percentile for bounds. Defaults to 0.01.\n", "\n", "    Returns:\n", "            pd.DataFrame: Winsorized dataframe using specified method\n", "    \"\"\"\n", "    if \"universe_mask\" in df.columns:\n", "        df_ = df.drop(columns=[\"universe_mask\"])\n", "    else:\n", "        df_ = df.copy()\n", "\n", "    if method == \"median_mad\":\n", "        df_ = winsorize_with_mad(df_, sigma)\n", "    elif method == \"mean_std\":\n", "        df_ = winsorize_with_std(df_, sigma)\n", "    elif method == \"percentile\":\n", "        df_ = winsorize_by_pct(df_, percentile)\n", "\n", "    if \"universe_mask\" in df.columns:\n", "        return pd.concat([df_, df[[\"universe_mask\"]]], axis=1)\n", "    else:\n", "        return df_\n", "\n", "\n", "def neutralize(df_factor: pd.DataFrame, df_style, option=\"indus_and_style\"):\n", "    \"\"\"neutralize factor by industry and style\n", "    Args:\n", "            df_factor (pd.DataFrame): Factor dataframe\n", "            df_style (pd.DataFrame): Style dataframe with first column as indcell\n", "            option (str): Neutralization type ('indus', 'style', 'indus_and_style', or 'indus_and_style_by_country'). Defaults to 'indus_and_style'.\n", "\n", "    Returns:\n", "            pd.DataFrame: Neutralized factor dataframe\n", "    \"\"\"\n", "    df_factor = df_factor.copy()\n", "    df_style = df_style.loc[df_style.index.isin(df_factor.index)]\n", "    rows = df_factor.index.intersection(df_style.index)\n", "    df_factor, df_style = df_factor.loc[rows], df_style.loc[rows]\n", "\n", "    types = df_style.dtypes\n", "    cats = types[types == \"object\"].index.tolist()\n", "\n", "    if (\n", "        option == \"indus\" and len(cats) > 0\n", "    ):  ## convert indecell to dummy industry columns\n", "        df_style = pd.get_dummies(\n", "            df_style[[\"indcell\"]], columns=cats, drop_first=False\n", "        ).astype(np.float64)\n", "    elif option == \"style\":\n", "        df_style = df_style.iloc[:, 1:]\n", "    elif (\n", "        option.startswith(\"indus_and_style\") and len(cats) > 0\n", "    ):  ## convert indecell to dummy industry columns\n", "        df_style = pd.get_dummies(df_style, columns=cats, drop_first=False).astype(\n", "            np.float64\n", "        )\n", "\n", "    df_full = pd.concat([df_style, df_factor], axis=1)\n", "    df_full[np.isnan(df_full)] = 0.0\n", "\n", "    def neut(df: pd.DataFrame, x_columns: list[str]):\n", "        x = df[x_columns]\n", "        y = df[[s for s in df.columns if s not in x_columns]]\n", "        lr = LinearRegression()\n", "        lr.fit(x, y)\n", "        return y.sub(lr.predict(x))\n", "\n", "    def neut_by_country(df: pd.DataFrame, x_columns: list[str]):\n", "        \"\"\"left for neutralization by county for option=4\"\"\"\n", "        pass\n", "\n", "    style_columns = df_style.columns.tolist()\n", "    df_neu = (\n", "        df_full.groupby(level=\"date\")\n", "        .apply(lambda df: neut(df, style_columns))\n", "        .droplevel(0)\n", "    )\n", "    df_neu.columns = [\"neu_\" + x for x in df_neu.columns]\n", "    return df_neu\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## calendar.py"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/utils/calendar.py'\n", "\n", "# coding = utf-8\n", "import datetime\n", "import pandas as pd\n", "from shennong.utils import trading_days\n", "\n", "\n", "def get_trading_days_by_country(region_product='cn_ashare'):\n", "\t\"\"\"\n", "\tArgs:\n", "\t    region_product (str): Region/product code to get trading days for. Defaults to 'cn_ashare'.\n", "\n", "\tReturns:\n", "\t    list: List of trading days for the specified region/product\n", "\t\"\"\"\n", "\treturn trading_days.load(region_product=region_product, verbose=False)\n", "\n", "\n", "def get_trading_days_by_region(region_product='euro_next'):\n", "\t\"\"\"\n", "\tArgs:\n", "\t    region_product (str): Region code to get trading days for. Defaults to 'euro_next'.\n", "\n", "\tReturns:\n", "\t    list: List of trading days for the specified region, filtered to business days\n", "\t\"\"\"\n", "\tregion_map = {\n", "\t\t'euro_next': ['pt_lis', 'nl_ams', 'be_bru', 'ie_dub', 'no_osl', 'it_mil', 'fr_par'],\n", "\t\t'EU1': [\n", "\t\t\t'at_xbo',\n", "\t\t\t'be_bru',\n", "\t\t\t'ch_swx',\n", "\t\t\t'cz_pra',\n", "\t\t\t'de_fra',\n", "\t\t\t'de_ber',\n", "\t\t\t'de_etr',\n", "\t\t\t'dk_cse',\n", "\t\t\t'es_bmex',\n", "\t\t\t'fi_hel',\n", "\t\t\t'fr_par',\n", "\t\t\t'gb_lon',\n", "\t\t\t'gr_asex',\n", "\t\t\t'hu_bud',\n", "\t\t\t'ie_dub',\n", "\t\t\t'it_mil',\n", "\t\t\t'nl_ams',\n", "\t\t\t'no_osl',\n", "\t\t\t'pl_war',\n", "\t\t\t'pt_lis',\n", "\t\t\t'se_sto',\n", "\t\t],\n", "\t}\n", "\tif region_product in {'euronext', 'euro_next'}:\n", "\t\tregion_product = 'euro_next'\n", "\telif region_product in {'EU1', 'eu1'}:\n", "\t\tregion_product = 'EU1'\n", "\tregion_products = region_map.get(region_product, [])\n", "\tassert len(region_products) > 0, f'wrong cfg for {region_product}'\n", "\n", "\ttd_days = []\n", "\tfor rp in region_products:\n", "\t\ttd_days.append(get_trading_days_by_country(rp))\n", "\n", "\t# Get union of trading days across all region products\n", "\tif len(td_days) > 0:\n", "\t\ttd_days = list(set.union(*[set(days) for days in td_days]))\n", "\ttd_days = sorted(set(td_days))\n", "\tbusiness_days = pd.bdate_range('2001-01-01', '2050-12-31').astype(str).tolist()\n", "\treturn sorted(set(td_days).intersection(set(business_days)))\n", "\n", "\n", "def get_trading_days(region_product: str):\n", "\t\"\"\"load all trading days since 2012 for a given region/product\n", "\tArgs:\n", "\t    region_product (str): Region/product str to get trading days for\n", "\n", "\tReturns:\n", "\t    list: List of trading days for the specified region/product\n", "\t\"\"\"\n", "\tif region_product == 'global':\n", "\t\ttoday = str(datetime.date.today())\n", "\t\treturn pd.bdate_range('2012-01-01', today).astype(str).tolist()\n", "\telif region_product in ['euro_next', 'euronext', 'EU1', 'eu1']:\n", "\t\treturn get_trading_days_by_region(region_product)\n", "\telse:\n", "\t\treturn get_trading_days_by_country(region_product)\n", "\n", "\n", "def next_trading_days(region_product: str, date: str, lookforward: int = 1):\n", "\t\"\"\"get next few trading days after a given date\n", "\tArgs:\n", "\t    region_product (str): Region/product code to get trading days for\n", "\t    date (str): Reference date\n", "\t    lookforward (int): Number of trading days to look forward. Defaults to 1.\n", "\n", "\tReturns:\n", "\t    list: List of next trading day(s) after the reference date\n", "\t\"\"\"\n", "\ttd_days = get_trading_days(region_product)\n", "\tfor i, dt in enumerate(td_days):\n", "\t\tif date == td_days[0]:\n", "\t\t\treturn td_days[1]\n", "\t\tif date == td_days[-1]:\n", "\t\t\tprint(f'{date} is the latest trading day!')\n", "\t\t\treturn None\n", "\n", "\t\tif td_days[i - 1] <= date < dt:\n", "\t\t\treturn td_days[i : i + lookforward]\n", "\n", "\n", "def previous_trading_days(region_product: str, date: str, lookback: int = 1):\n", "\t\"\"\"get previous few trading days before a given date\n", "\tArgs:\n", "\t    region_product (str): Region/product code to get trading days for\n", "\t    date (str): Reference date\n", "\t    lookback (int): Number of trading days to look back. Defaults to 1.\n", "\n", "\tReturns:\n", "\t    list: List of previous trading day(s) before the reference date\n", "\t\"\"\"\n", "\ttd_days = get_trading_days(region_product)\n", "\tfor i, dt in enumerate(td_days):\n", "\t\tif date == td_days[0]:\n", "\t\t\tprint(f'{date} is the first trading day!')\n", "\t\t\treturn None\n", "\t\tif date == td_days[-1]:\n", "\t\t\treturn td_days[-2]\n", "\n", "\t\tif td_days[i - 1] < date <= dt:\n", "\t\t\treturn td_days[i - lookback + 1 : i + 1]\n", "\n", "\n", "def trading_days_between(region_product: str, sDate: str, eDate: str):\n", "\t\"\"\"get all trading days between two dates inclusive\n", "\tArgs:\n", "\t    region_product (str): Region/product code to get trading days for\n", "\t    sDate (str): Start date\n", "\t    eDate (str): End date\n", "\n", "\tReturns:\n", "\t    list: List of trading days between start and end dates inclusive\n", "\t\"\"\"\n", "\tassert sDate <= eDate, f'{sDate} should be earlier than {eDate}!'\n", "\ttd_days = get_trading_days(region_product)\n", "\tdates_between = []\n", "\tfor dt in td_days:\n", "\t\tif sDate <= dt <= eDate:\n", "\t\t\tdates_between.append(dt)\n", "\tif len(dates_between) == 0:\n", "\t\tprint(f'no effective trading days between {sDate} and {eDate}! please check input!')\n", "\treturn dates_between"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## converter.py"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/utils/converter.py'\n", "\n", "def to_date_with_slash(date: str):\n", "    \"\"\"convert date from YYYYMMDD to YYYY-MM-DD\n", "    Args:\n", "        date (str): Date string in YYYYMMDD format\n", "\n", "    Returns:\n", "        str: Date string in YYYY-MM-DD format\n", "    \"\"\"\n", "    assert len(date) == 8, f'wrong date format {date}, should be in YYYYMMDD'\n", "    return f'{date[0:4]}-{date[4:6]}-{date[6:8]}'\n", "\n", "def to_date_without_slash(date: str):\n", "    \"\"\"convert date from YYYY-MM-DD to YYYYMMDD\n", "    Args:\n", "        date (str): Date string in YYYY-MM-DD format\n", "\n", "    Returns:\n", "        str: Date string in YYYYMMDD format\n", "    \"\"\"\n", "    assert '-' in date, f'wrong date format {date}, should be in YYYY-MM-DD'\n", "    return date.replace('-','')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# loader.py"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/data/loader.py'\n", "# coding = utf-8\n", "import os\n", "import re\n", "import json\n", "import getpass\n", "import warnings\n", "import itertools\n", "import numpy as np\n", "import polars as pl\n", "import pandas as pd\n", "from tqdm.auto import tqdm\n", "\n", "from ..utils import tools\n", "from ..utils import calendar\n", "\n", "os.environ[\"NUMEXPR_MAX_THREADS\"] = \"200\"\n", "\n", "\n", "def load_config(cfg_filepath: str, verbose: bool = False):\n", "    \"\"\"read config from json file\n", "    Args:\n", "        cfg_filepath (str): Path to the config file to load\n", "        verbose (bool, optional): Whether to print loading message. Defaults to False.\n", "\n", "    Returns:\n", "        dict: Loaded config dictionary from JSON file\n", "    \"\"\"\n", "    if verbose:\n", "        tools.Logger.info(f\"loading config from {cfg_filepath}\", end=\"\\n\")\n", "    with open(cfg_filepath, \"r\", encoding=\"utf-8\") as f:\n", "        return json.load(f)\n", "\n", "\n", "class DataLoader:\n", "    def __init__(\n", "        self,\n", "    ):\n", "        pass\n", "\n", "    def load_1day_info(\n", "        self, fund_dir: str, date: str, cfg: dict = {}, columns: list[str] = []\n", "    ):\n", "        \"\"\"load 1 day cached info from parquet file\n", "        Args:\n", "            fund_dir (str): Directory path containing parquet files\n", "            date (str): Date string to load data for\n", "            cfg (dict, optional): Configuration dictionary. Defaults to {}.\n", "            columns (list[str], optional): List of columns to load. Defaults to [].\n", "\n", "        Returns:\n", "            pd.DataFrame: DataFrame containing the loaded data, filtered based on config,\n", "                         with BarraID as index. Returns None if file doesn't exist.\n", "        \"\"\"\n", "        if not os.path.exists(f\"{fund_dir}{date}.parquet\"):\n", "            return None\n", "        if len(columns) == 0:\n", "            a = pd.read_parquet(f\"{fund_dir}{date}.parquet\")  ## read all columns\n", "        else:\n", "            a = pd.read_parquet(\n", "                f\"{fund_dir}{date}.parquet\", columns=columns\n", "            )  ## read specified columns\n", "\n", "        a.set_index(\"BarraID\", inplace=True)\n", "        if (len(cfg) == 0) | (cfg.get(\"country_set\") == \"\"):\n", "            return a.loc[~a.index.isna()]  ## filterout invalid records\n", "\n", "        ## filter with country_set\n", "        if cfg.get(\"country_set\") != \"\":\n", "            ctry_list = cfg.get(\"ctry_list\", None)  ## load in fc.py before calculation\n", "            if ctry_list is None:\n", "                raise ValueError(\"country list is not prepared!\")\n", "\n", "            ## filter out non-trading or unwanted countries\n", "            excluded = [s[1:] for s in ctry_list if s.startswith(\"-\")]\n", "            cond1 = a[\"Country_ISO3\"].isin(ctry_list) & (\n", "                ~a[\"Country_ISO3\"].isin(excluded)\n", "            )\n", "            cond2 = ~(a[\"Country_of_Exposure\"].isin(excluded))\n", "            return a.loc[cond1 & cond2]\n", "\n", "    def load_fundamental(\n", "        self, sDate: str, eDate: str, cfg: dict, load_root: str = \"DEFAULT\"\n", "    ):\n", "        \"\"\"load multiple days fundamental barra info from parquet files\n", "        Args:\n", "            sDate (str): Start date for loading data\n", "            eDate (str): End date for loading data\n", "            cfg (dict): Configuration dictionary containing:\n", "                - preload_days: Number of days to preload\n", "                - barra_product: Barra product name (e.g., EUTR, GEM3, CNTR)\n", "                - use_info_columns: Columns used to calculate factors\n", "                - load_ind_style: Whether to load industry style factors\n", "            load_root (str, optional): Root directory for loading data. Defaults to \"DEFAULT\".\n", "\n", "        Returns:\n", "            pd.DataFrame: DataFrame containing fundamental data with MultiIndex (date, BarraID),\n", "                         with duplicates removed\n", "        \"\"\"\n", "        preload_days = cfg.get(\"preload_days\")  ## number of days to preload\n", "        barra_product = cfg.get(\"barra_product\")  ## e.g., EUTR, GEM3, CNTR\n", "        info_columns = cfg.get(\"use_info_columns\")  ## columns used to calculate factors\n", "        if load_root == \"DEFAULT\":\n", "            load_root = (\n", "                \"/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/\"\n", "            )\n", "        fund_dir = f\"{load_root}{barra_product}/\"\n", "\n", "        all_dates = sorted([s.split(\".\")[0] for s in os.listdir(fund_dir)])\n", "        dates = [s for s in all_dates if s >= sDate and s < eDate]\n", "        start, end = all_dates.index(dates[0]), all_dates.index(dates[-1])\n", "        dates = all_dates[max(0, start - preload_days) : end]\n", "\n", "        if cfg.get(\"load_ind_style\"):\n", "            sample = self.load_1day_info(fund_dir, dates[0], cfg)\n", "            style_cols = sample.columns[\n", "                sample.columns.str.startswith(barra_product)\n", "            ].tolist()\n", "            info_columns = list(set(info_columns).union(set(style_cols)))\n", "            info_columns = [\"indcell\", \"regioncell\"] + info_columns\n", "\n", "        ## parallelly loading\n", "        njobs = max(1, len(dates) // 30)\n", "        tools.Logger.info(\n", "            f\"loading fundamental data from {sDate} to {eDate} ...\",\n", "            end=\"\\n\",\n", "        )\n", "        tasks = [(fund_dir, date, cfg, info_columns) for date in dates]\n", "        datas = tools.parallel(self.load_1day_info, tasks, njobs, \"loky\")\n", "        datas = {date: value for date, value in zip(dates, datas) if value is not None}\n", "        datas = pd.concat(datas, names=[\"date\"])\n", "        return datas.loc[~datas.index.duplicated()]\n", "\n", "    def load_indus_style_factors(\n", "        self, sDate: str, eDate: str, cfg: dict, load_root: str = \"DEFAULT\"\n", "    ):\n", "        \"\"\"load multiple days industry and style factors from parquet files\n", "        Args:\n", "            sDate (str): Start date for loading data\n", "            eDate (str): End date for loading data\n", "            cfg (dict): Configuration dictionary containing:\n", "                - preload_days: Number of days to preload\n", "                - barra_product: Barra product name (e.g., EUTR, GEM3, CNTR)\n", "            load_root (str, optional): Root directory for loading data. Defaults to \"DEFAULT\".\n", "\n", "        Returns:\n", "            pd.DataFrame: DataFrame containing indus and style factors with MultiIndex (date, BarraID),\n", "                         with duplicates removed\n", "        \"\"\"\n", "        preload_days = cfg.get(\"preload_days\")  ## number of days to preload\n", "        barra_product = cfg.get(\"barra_product\")  ## e.g., EUTR, GEM3, CNTR\n", "        if load_root == \"DEFAULT\":\n", "            load_root = (\n", "                \"/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/\"\n", "            )\n", "        fund_dir = f\"{load_root}{barra_product}/\"\n", "\n", "        all_dates = sorted([s.split(\".\")[0] for s in os.listdir(fund_dir)])\n", "        dates = [s for s in all_dates if s >= sDate and s < eDate]\n", "        start, end = all_dates.index(dates[0]), all_dates.index(dates[-1])\n", "        dates = all_dates[max(0, start - preload_days) : end]\n", "\n", "        ## read a sample to get indus_style_columns\n", "        sample = self.load_1day_info(fund_dir, dates[0], cfg)\n", "        style_cols = sample.columns[\n", "            sample.columns.str.startswith(barra_product)\n", "        ].tolist()\n", "\n", "        ## add Country_ISO3 and Country_of_Exposure for filtering\n", "        info_columns = [\n", "            \"BarraID\",\n", "            \"Country_ISO3\",\n", "            \"Country_of_Exposure\",\n", "            \"indcell\",\n", "        ] + style_cols\n", "\n", "        ## parallelly loading\n", "        njobs = max(1, len(dates) // 30)\n", "        tools.Logger.info(\n", "            f\"loading indus and style factors from {sDate} to {eDate} ...\",\n", "            end=\"\\n\",\n", "        )\n", "        tasks = [(fund_dir, date, cfg, info_columns) for date in dates]\n", "        datas = tools.parallel(\n", "            self.load_1day_info,\n", "            tasks,\n", "            njobs,\n", "            \"loky\",\n", "            True,\n", "            \"loading_indus_style_factors\",\n", "        )\n", "        datas = {date: value for date, value in zip(dates, datas) if value is not None}\n", "        if len(datas) == 0:\n", "            return None\n", "        return pd.concat(datas, names=[\"date\"]).drop(\n", "            columns=[\"Country_ISO3\", \"Country_of_Exposure\"]\n", "        )\n", "\n", "    def load_universe(\n", "        self, sDate: str, eDate: str, cfg: dict, load_root: str = \"DEFAULT\"\n", "    ):\n", "        \"\"\"load universe from csv files\n", "        Args:\n", "            sDate (str): Start date for loading data\n", "            eDate (str): End date for loading data\n", "            cfg (dict): Configuration dictionary containing:\n", "                - universe_set: Universe set name (e.g., global, EU1, euro_next, cn, etc.)\n", "            load_root (str, optional): Root directory for loading data. Defaults to \"DEFAULT\".\n", "\n", "        Returns:\n", "            pd.DataFrame: DataFrame containing universe data with MultiIndex (date, BarraID),\n", "                         with duplicates removed\n", "        \"\"\"\n", "        tools.Logger.info(f\"loading universe for {sDate} to {eDate} ...\", end=\"\\n\")\n", "        if load_root == \"DEFAULT\":\n", "            univ_root = \"/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/universe/\"\n", "\n", "        universe_set = \"global\"\n", "        if cfg.get(\"universe_set\") != \"\":\n", "            universe_set = cfg.get(\"universe_set\")\n", "\n", "        univ_root = f\"{univ_root}{universe_set}/\"\n", "\n", "        all_dates = sorted([s.split(\".\")[0] for s in os.listdir(univ_root)])\n", "        dates = [s for s in all_dates if sDate <= s <= eDate]\n", "\n", "        univ_dict = {\n", "            date: pd.read_csv(f\"{univ_root}{date}.csv\").set_index(\"BarraID\")\n", "            for date in dates\n", "        }\n", "        return pd.concat(univ_dict, names=[\"date\"])\n", "\n", "\n", "class FactorLoader:\n", "    def __init__(self, user_path: str = \"./configs/user.path.json\"):\n", "        self.user_path = user_path\n", "        self.user_name = getpass.getuser()\n", "\n", "    def _load(self, filepath: str):\n", "        \"\"\"load factor from parquet file\n", "        Args:\n", "            filepath (str): Path to the parquet file to load\n", "\n", "        Returns:\n", "            pd.DataFrame: DataFrame loaded from parquet file, filtered by factor_list if specified.\n", "                         Returns None if file does not exist.\n", "        \"\"\"\n", "        ## check file exists\n", "        if not os.path.exists(filepath):\n", "            return None\n", "        if len(self.factor_list) > 0:\n", "            return pd.read_parquet(filepath, columns=self.factor_list)\n", "        else:\n", "            return pd.read_parquet(filepath)\n", "\n", "    def load_(\n", "        self,\n", "        sDate: str,\n", "        eDate: str,\n", "        factor_name: str,\n", "        factor_group_name: str,\n", "        factor_type: str = \"raw\",\n", "        factor_root: str = \"DEFAULT\",\n", "        backfill=\"020000\",\n", "    ):\n", "        \"\"\"load multiple days factor from parquet files\n", "        Args:\n", "            sDate (str): Start date for loading data\n", "            eDate (str): End date for loading data\n", "            factor_name (str): Name of the factor to load\n", "            factor_group_name (str): Name of the factor group\n", "            factor_type (str, optional): Type of factor data to load. Defaults to \"raw\".\n", "            factor_root (str, optional): Root directory for loading data. Defaults to \"DEFAULT\".\n", "            backfill (str, optional): Backfill timestamp filter. Defaults to \"020000\".\n", "\n", "        Returns:\n", "            pd.DataFrame: DataFrame containing factor data with MultiIndex (date),\n", "                         or None if no data is found\n", "        \"\"\"\n", "        if factor_root == \"DEFAULT\":\n", "            self.user_path_cfg = load_config(self.user_path)\n", "            factor_root = self.user_path_cfg.get(self.user_name, None)\n", "            if factor_root is None:\n", "                raise ValueError(\n", "                    f\"no factor_root found for user {self.user_name} in {self.user_path_cfg}\"\n", "                )\n", "\n", "        factor_root = f\"{factor_root}{factor_group_name}/{factor_name}/{factor_type}/\"\n", "\n", "        all_files = os.listdir(factor_root)  ## list all files\n", "        if backfill is None:\n", "            files = sorted(set(all_files))\n", "        else:\n", "            ## filter by backfill\n", "            files = sorted([s for s in all_files if s.endswith(backfill)])\n", "        files = [s for s in files if sDate <= s.split(\".\")[0] <= eDate]\n", "\n", "        tasks = [(f\"{factor_root}{file}\",) for file in files]\n", "        features = tools.parallel(self._load, tasks, 1, \"loky\", True, \"loading_factor\")\n", "\n", "        features = {\n", "            s.split(\".\")[0]: feature\n", "            for s, feature in zip(files, features)\n", "            if feature is not None\n", "        }\n", "        if len(features) == 0:\n", "            return None\n", "        else:\n", "            return pd.concat(features, names=[\"date\"])\n", "\n", "    def load(\n", "        self,\n", "        sDate: str,\n", "        eDate: str,\n", "        factor_name: str,\n", "        factor_group_name: str,\n", "        factor_type: str = \"raw\",\n", "        factor_root: str = \"DEFAULT\",\n", "        backfill=\"020000\",\n", "        factor_list: list[str] = [],\n", "    ):\n", "        self.factor_list = factor_list\n", "\n", "        factor = []\n", "        for item in [\"raw\", \"neu\"]:\n", "            if item in factor_type:\n", "                f = self.load_(\n", "                    sDate,\n", "                    eDate,\n", "                    factor_name,\n", "                    factor_group_name,\n", "                    item,\n", "                    factor_root,\n", "                    backfill,\n", "                )\n", "                factor.append(f)\n", "        factor = [s for s in factor if s is not None]\n", "        if len(factor) == 0:\n", "            return None\n", "        else:\n", "            return pd.concat(factor, axis=1)\n", "\n", "\n", "def peek_factor_names(cfg: dict):\n", "    \"\"\"peek factor names for a given config\n", "    Args:\n", "        cfg: dict, config\n", "    Returns:\n", "        list, factor names\n", "    \"\"\"\n", "    sDate, eDate = cfg.get(\"sDate\"), cfg.get(\"eDate\")\n", "    floader = FactorLoader(\"./configs/user.path.json\")\n", "\n", "    factor_saveroot = cfg.get(\"factor_saveroot\")\n", "    td_days = calendar.trading_days_between(cfg.get(\"calendar\"), sDate, eDate)\n", "    load_cfg = {\n", "        \"sDate\": td_days[0],\n", "        \"eDate\": td_days[0],\n", "        \"factor_name\": cfg.get(\"factor_name\"),\n", "        \"factor_group_name\": cfg.get(\"factor_group_name\"),\n", "        \"factor_type\": cfg.get(\"factor_type\"),\n", "        \"backfill\": cfg.get(\"backfill\"),\n", "        \"factor_root\": f\"{factor_saveroot}\",\n", "    }\n", "    feature = floader.load(**load_cfg)\n", "    return feature.columns.tolist()\n", "\n", "\n", "def load_raw_label(\n", "    sDate: str,\n", "    eDate: str,\n", "    cfg: dict,\n", "    barraids: list = [],\n", "):\n", "    \"\"\"Load raw label data from parquet files.\n", "\n", "    Args:\n", "        sDate (str): Start date in YYYY-MM-DD format\n", "        eDate (str): End date in YYYY-MM-DD format\n", "        cfg (dict): Configuration dictionary containing:\n", "            - label_usecols: List of label columns to load\n", "            - label_options: List of label options\n", "            - barra_product: Barra product name\n", "        barraids (list, optional): List of specific BarraIDs to load. Defaults to empty list.\n", "\n", "    Returns:\n", "        pd.DataFrame: DataFrame containing raw label data with MultiIndex (date, BarraID)\n", "    \"\"\"\n", "    # warnings.filterwarnings(\"ignore\", category=UserWarning)\n", "    label_usecols = cfg.get(\"label_usecols\")\n", "    label_options = cfg.get(\"label_options\")\n", "    barra_product = cfg.get(\"barra_product\")\n", "\n", "    barrainfo_root = (\n", "        \"/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/\"\n", "    )\n", "    usecols_info = [\"BarraID\"] + label_usecols\n", "\n", "    preload_days = max([int(s.split(\"_\")[-1]) for s in label_options if \"_\" in s]) + 5\n", "    dates = sorted(\n", "        [s.split(\".\")[0] for s in os.listdir(f\"{barrainfo_root}{barra_product}/\")]\n", "    )\n", "    cdates = [d for d in dates if sDate <= d <= eDate]\n", "    start, end = (\n", "        max(dates.index(cdates[0]) - preload_days, 0),\n", "        dates.index(cdates[-1]) + preload_days,\n", "    )\n", "    dates = dates[start:end]\n", "    td_days = calendar.trading_days_between(cfg.get(\"calendar\"), dates[0], dates[-1])\n", "    dates = [\n", "        s for s in dates if s in td_days\n", "    ]  ## cover more than 5 + largest_horizon days\n", "\n", "    def load_1day(date):\n", "        warnings.filterwarnings(\"ignore\", category=UserWarning)\n", "        filepath = f\"{barrainfo_root}{barra_product}/{date}.parquet\"\n", "        if not os.path.exists(filepath):\n", "            return None\n", "        df_info = pd.read_parquet(filepath, columns=usecols_info)\n", "        if len(barraids) > 0:  ## filter by barraids\n", "            df_info = df_info[df_info[\"BarraID\"].isin(barraids)]\n", "        df_info.dropna(subset=[\"BarraID\"], inplace=True)\n", "        df_info[\"date\"] = date\n", "        label = df_info.set_index([\"date\", \"BarraID\"])\n", "        return label.loc[~label.index.duplicated(keep=\"first\")]\n", "\n", "    num_workers = max(len(dates) // 40, 1)\n", "    tasks = [(date,) for date in dates]\n", "    labels = tools.parallel(\n", "        load_1day, tasks, num_workers, \"loky\", True, \"loading_label\"\n", "    )\n", "    return pd.concat(labels)\n", "\n", "\n", "## expand label using pandas, runing slow\n", "def expand_label_withpd(df_label: pd.DataFrame, label_options: list[str]):\n", "    \"\"\"Expand label data using pandas (slower implementation). Runtime efficiency is relatively low.\n", "\n", "    Args:\n", "        df_label (pd.DataFrame): Input label DataFrame with MultiIndex (date, BarraID)\n", "        label_options (list[str]): List of label options in format H+number or H+number_number\n", "\n", "    Returns:\n", "        pd.DataFrame: Expanded label data with rolling means and shifts applied\n", "    \"\"\"\n", "\n", "    def label_func(df, label, usecol):  # 对usecols进行label转换\n", "        if bool(re.match(r\"H(\\d+)$\", label)):  # 如果label是H0则自动补齐为H0_0\n", "            match = re.match(r\"H(\\d+)$\", label)\n", "            new_label = f\"H{match.group(1)}_{match.group(1)}\"\n", "        else:\n", "            new_label = label\n", "        match = re.match(\n", "            r\"^H(\\d+)_(\\d+)$\", new_label\n", "        )  # 识别 buy_lag, start_lag,得到window和lag\n", "        window = int(match.group(2))  # group: (H1_5,1,5)\n", "\n", "        ## no oveerlapping with today close, tomorrow close to close after n days\n", "        lag = -int(match.group(2)) - int(match.group(1))\n", "\n", "        if usecol == \"SpecificReturn\":\n", "            ret_name = \"SpecRet\"\n", "        elif use<PERSON>l == \"DlyReturn%\":\n", "            ret_name = \"TotalRet\"\n", "        result = (\n", "            df.groupby(level=\"BarraID\")\n", "            .rolling(window, min_periods=1)\n", "            .mean()\n", "            .shift(lag)\n", "            .droplevel(0)\n", "        )\n", "        result.name = f\"{ret_name}_{label}\"\n", "        return result\n", "\n", "    ## label option should be in format of H+number or H+number_number\n", "    label_list = [i for i in label_options if bool(re.match(r\"^H\\d+(?:_\\d+)?$\", i))]\n", "    ret_cols = df_label.columns[df_label.columns.str.contains(\"return|Return\")].tolist()\n", "    grids = list(itertools.product(label_list, ret_cols))\n", "    tasks = [(df_label[i[1]], i[0], i[1]) for i in grids]\n", "    expanded = tools.parallel(label_func, tasks, 1, \"loky\")\n", "    return pd.concat(expanded, axis=1).reindex(df_label.index)\n", "\n", "\n", "def expand_label(raw_label: pd.Series, label_options: list[str]):\n", "    \"\"\"Expand label data using polars and numpy (faster implementation).\n", "\n", "    Args:\n", "        raw_label (pd.Series): Input label Series with MultiIndex (date, BarraID)\n", "        label_options (list[str]): List of label options in format H+number_number\n", "\n", "    Returns:\n", "        pd.DataFrame: Expanded label data with rolling means and shifts applied\n", "    \"\"\"\n", "    tmp = raw_label.unstack(\"BarraID\")\n", "\n", "    def expand_pq(df_label: pl.DataFrame, label_option: str = \"H1_1\"):\n", "        label_option = label_option.split(\"_\")\n", "        p, q = int(label_option[0][1:]), int(label_option[-1])\n", "        tmp = df_label.with_columns(pl.exclude(\"date\").rolling_mean(q, min_periods=1))\n", "        tmp = tmp.to_pandas().shift(-p - q)\n", "        return tmp.iloc[:, 1:]\n", "\n", "    b = pl.from_pandas(tmp.reset_index()).fill_nan(None)\n", "    tasks = [(b, lopt) for lopt in label_options]\n", "    results = tools.parallel(expand_pq, tasks, 1, \"threading\", True, \"expanding_label\")\n", "\n", "    res = np.stack(results)\n", "    res = res.transpose(1, 2, 0).reshape(-1, len(label_options))\n", "\n", "    dates = b[\"date\"].to_list()\n", "    syms = b.columns[1:]\n", "    mindex = pd.MultiIndex.from_product([dates, syms], names=[\"date\", \"BarraID\"])\n", "\n", "    if raw_label.name == \"SpecificReturn\":\n", "        ret_name = \"SpecRet\"\n", "    elif raw_label.name == \"DlyReturn%\":\n", "        ret_name = \"TotalRet\"\n", "    columns = [f\"{ret_name}_H1_{q}\" for q in range(1, len(label_options) + 1)]\n", "    res = pd.DataFrame(res, index=mindex, columns=columns)\n", "    return res.loc[raw_label.index]\n", "\n", "\n", "def load_label(sDate: str, eDate: str, cfg: dict, barraids: list = []):\n", "    \"\"\"load label data from parquet files\n", "    Args:\n", "        sDate (str): Start date for loading data\n", "        eDate (str): End date for loading data\n", "        cfg (dict): Configuration dictionary\n", "        barraids (list): partial BarraIDs to load if provided to save time, empty for all\n", "\n", "    Returns:\n", "        pd.DataFrame: DataFrame containing label data with MultiIndex (date, BarraID),\n", "                     with duplicates removed\n", "    \"\"\"\n", "    df_label = load_raw_label(sDate, eDate, cfg, barraids) * 0.01\n", "    label_options = cfg.get(\"label_options\")\n", "    # label = expand_label(df_label, label_options).loc[sDate:eDate]\n", "    label = expand_label(df_label.iloc[:, 0], label_options).loc[sDate:eDate]\n", "    risk = df_label.loc[label.index, df_label.columns.str.contains(\"risk|Risk\")]\n", "    risk = risk.loc[label.index]\n", "    return label, risk\n", "\n", "\n", "def load_asset_exposure(sDate: str, eDate: str, cfg: dict, barraids: list = []):\n", "    \"\"\"Load asset factor exposure data from parquet files for a date range.\n", "\n", "    Args:\n", "        sDate (str): Start date in YYYY-MM-DD format\n", "        eDate (str): End date in YYYY-MM-DD format\n", "        cfg (dict): Configuration dictionary containing:\n", "            - barra_product: Barra product name (e.g., EUTR, GEM3, CNTR)\n", "        barraids (list): partial BarraIDs to load if provided to save time, empty for all\n", "\n", "    Returns:\n", "        pd.DataFrame: Multi-index DataFrame with dates, BarraID as indices.\n", "                     Contains factor exposures for each asset over the date range.\n", "                     Returns None if no data found.\n", "\n", "    \"\"\"\n", "    exp_root = \"/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/factor_exp/\"\n", "    exposure_root = f\"{exp_root}{cfg.get('barra_product')}/\"\n", "    dates = calendar.trading_days_between(cfg.get(\"calendar\"), sDate, eDate)\n", "    # dates = pd.date_range(start=sDate, end=eDate).astype(str).tolist()\n", "\n", "    def load_1day(date: str):\n", "        try:\n", "            fexp = pd.read_parquet(f\"{exposure_root}{date}.parquet\")\n", "            if len(barraids) > 0:\n", "                fexp = fexp[fexp[\"BarraID\"].isin(barraids)]\n", "            fexp = fexp.set_index([\"BarraID\", \"Factor\"]).unstack(\"Factor\")\n", "            return fexp.fillna(0.0).droplevel(0, axis=1)\n", "        except Exception as err:\n", "            return None\n", "\n", "    tasks = [(date,) for date in dates]\n", "    fexps = tools.parallel(\n", "        load_1day, tasks, 20, \"loky\", True, \"loading_factor_exposure\"\n", "    )\n", "    fexps = {date: fexp for date, fexp in zip(dates, fexps) if fexp is not None}\n", "    return pd.concat(fexps, names=[\"date\"]).fillna(0.0)\n", "\n", "\n", "def load_factor_covariance(sDate: str, eDate: str, cfg: dict):\n", "    \"\"\"Load factor covariance data from parquet files for a date range.\n", "\n", "    Args:\n", "        sDate (str): Start date in YYYY-MM-DD format\n", "        eDate (str): End date in YYYY-MM-DD format\n", "        cfg (dict): Configuration dictionary containing:\n", "            - barra_product: Barra product name (e.g., EUTR, GEM3, CNTR)\n", "\n", "    Returns:\n", "        pd.DataFrame: Multi-index DataFrame with dates as the index.\n", "                     Contains factor covariance matrices for each date.\n", "                     Returns None if no data found.\n", "    \"\"\"\n", "    cov_root = \"/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/factor_cov/\"\n", "    covariance_root = f\"{cov_root}{cfg.get('barra_product')}/\"\n", "    dates = calendar.trading_days_between(cfg.get(\"calendar\"), sDate, eDate)\n", "    cov_mats = {}\n", "    for date in tqdm(dates, desc=\"loading_factor_covariance\"):\n", "        try:\n", "            fexp = pd.read_parquet(f\"{covariance_root}{date}.parquet\")\n", "            cov_mats[date] = fexp\n", "        except FileNotFoundError as err:\n", "            tools.Logger.info(f\"{err}\", end=\"\\r\")\n", "            continue\n", "\n", "    if len(cov_mats) == 0:\n", "        tools.Logger.info(\n", "            f\"No covariance data found for the given date range {sDate} to {eDate}!\",\n", "            end=\"\\n\",\n", "        )\n", "        return None\n", "    else:\n", "        return pd.concat(cov_mats, axis=0, names=[\"date\"])\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# feature calculator"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### FeatureCalculator.py"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/alpha_fc/FeatureCalculator.py'\n", "\n", "# coding = utf-8\n", "import json, os\n", "import datetime\n", "import pandas as pd\n", "import numpy as np\n", "import xarray as xr\n", "from shennong.stk import bar\n", "from ..utils.logging import Logger\n", "from ..utils import processor as prep\n", "from ..data.fc_dataloader import DataLoader\n", "\t\t\n", "\n", "class SimpleFeatureCalclator:\n", "\tdef __init__(self, cfg: dict):\n", "\t\tself.__dict__.update(cfg)\n", "\t\tself.cfg = cfg\n", "\n", "\n", "\tdef set_params(self, cfg: dict):\n", "\t\tself.__dict__.update(cfg)\n", "\n", "\n", "\tdef _save(self, df: pd.DataFrame, save_root: str, key_group_name: str):\n", "\t\tall_dates = df.index.get_level_values('date').unique()\n", "\t\tfor date in all_dates:\n", "\t\t\tdf_ = df.loc[date]\n", "\t\t\tcoords = (('DATETIME',[pd.to_datetime(f'{date} 09:30:00')]), ('SYMBOL',df_.index), ('KEY',df_.columns))\n", "\t\t\txr_ = xr.<PERSON>y(np.array([df_.values]), coords=coords)\n", "\n", "\t\t\txr_ = xr_.transpose('SYMB<PERSON>', 'DATETIME','KEY')\n", "\t\t\tbar.save(entity=xr_,region_product=self.region_product, freq='1day',\n", "\t\t\t\t\tkey_group_name=key_group_name, save_root=save_root, filter=None, verbose=False, update_h5_key_group_structure=True)\n", "\t\t\tLogger.info(f'feature saved for {date}!', end='\\r')\n", "\t\n", "\t\n", "\tdef _save2(self, df: pd.DataFrame, save_root: str, key_group_name: str):\n", "\t\tfactor_names = df.columns.tolist()\n", "\n", "\t\tfor col in factor_names:\n", "\t\t\tfactor = df[col]\n", "\n", "\t\t\tif col.startswith('neu_'):\n", "\t\t\t\tsavedir = f'{save_root}{key_group_name}/{col.split(\"neu_\")[-1]}/neu/'\n", "\t\t\telse:\n", "\t\t\t\tsavedir = f'{save_root}{key_group_name}/{col}/raw/'\n", "\t\t\tif not os.path.exists(savedir): os.makedirs(savedir, exist_ok=True)\n", "\n", "\t\t\tall_dates = factor.index.get_level_values('date').unique()\n", "\t\t\tfor date in all_dates:\n", "\t\t\t\tdf_ = factor.loc[date]\n", "\t\t\t\tdf_.to_frame().to_parquet(f'{savedir}{date}.parquet_{self.backfill}')\n", "\n", "\n", "\tdef calc(self,):\n", "\t\tLogger.info(f'callback to be applied: {self.callback.__name__}', end='\\n')\n", "\t\tdf = self.callback(self.sDate, self.eDate, self.cfg)\n", "\t\t\n", "\t\tif self.cfg.get('gen_neu_factor'):\n", "\t\t\tloader = DataLoader()\n", "\t\t\tdf_style = loader.load_indus_style_factors(self.sDate, self.eDate, self.cfg)\n", "\t\t\tdf_ = df.groupby('date').apply(prep.demean).droplevel(0) ## cs demean\n", "\t\t\tdf_ = df.groupby('date').apply(prep.winsorize).droplevel(0) ## cs remove outlier\n", "\t\t\tneu_factor = prep.neutralize(df_, df_style) ## regression\n", "\t\t\tneu_factor = neu_factor.groupby('date').apply(prep.winsorize).droplevel(0) ## cs remove outlier\n", "\t\t\tneu_factor = neu_factor.groupby('date').apply(prep.zscore).droplevel(0) ## cs zscore\n", "\t\t\tneu_factor.columns = [f'neu_{self.cfg.get(\"factor_name\")}']\n", "\t\t\tdf = pd.concat([df, neu_factor],axis=1).dropna()\n", "\t\t\n", "\t\tdf = df.groupby('date').apply(prep.as_weight).droplevel(0) ## output as weights (abs sum to be 1.0)\n", "\t\tdf = df.loc[~df.index.duplicated()] ## remove duplicated records\n", "\t\tsavedir = f'{self.save_root}{self.barra_product}/'\n", "\t\tself._save2(df, savedir, self.factor_group_name)\n", "\t\tLogger.info(f'saving {self.callback.__name__} finished!', end='\\n')\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from barra.data.loader import load_config, load_label\n", "\n", "ctry_path = '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/configs/country.set.json'\n", "cfg = load_config('/mnt/sda/home/<USER>/working/gitrepo/barra_demo/configs/fc/anlystsn.eu1.01.json')\n", "cfg['ctry_list'] = load_config(ctry_path)['EU1']\n", "\n", "# cfg = load_config('/mnt/sda/home/<USER>/working/gitrepo/barra_demo/configs/backtest/bt_demo.json')\n", "\n", "# fund_dir=  '/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/EUTR/'\n", "# a = loader.load_universe('2016-01-01','2016-02-01', cfg)\n", "# a = loader.load_fundamental('2024-01-01', '2024-02-01', cfg)\n", "\n", "# from barra.data import loader\n", "# user_path = '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/configs/user.path.json'\n", "# fd = loader.FactorLoader(user_path)\n", "# f = fd.load('2024-01-01','2024-06-30', 'WKMOM.EU1.01', 'EU1')\n", "\n", "# load_label('2024-01-01','2024-02-01', cfg)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from barra.data import loader\n", "dloader = loader.DataLoader()\n", "\n", "info_data = dloader.load_fundamental('2024-01-01', '2024-06-30', cfg)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.read_parquet('/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/EUE4/2024-01-01.parquet').columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### fc.py"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/fc.py'\n", "# coding = utf-8\n", "import os\n", "import datetime\n", "import argparse\n", "import getpass\n", "import importlib.util as impt\n", "\n", "from zoneinfo import ZoneInfo\n", "from barra.data.loader import load_config\n", "from barra.alpha_fc import FeatureCalculator as fc\n", "\n", "\n", "## parser definition\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument('--config', type=str, default='./config/')\n", "parser.add_argument('--sDate', type=str, default='2024-01-01')\n", "parser.add_argument('--eDate', type=str, default='2024-01-01')\n", "parser.add_argument('--backfill', type=str, default=None)\n", "args = parser.parse_args()\n", "\n", "## load config\n", "cfg = load_config(args.config)\n", "\n", "## parse user-defined factor save_root\n", "user_save_path_configs = load_config('./configs/user.path.json')\n", "user_name = getpass.getuser()\n", "if user_name not in user_save_path_configs.keys():\n", "\traise Exception(f'no save path found for user {user_name} in {user_save_path_configs}')\n", "else:\n", "\tcfg['save_root'] = f'{user_save_path_configs.get(user_name, None)}factor/'\n", "\n", "## parse sDate and eDate\n", "if args.sDate > args.eDate:\n", "\traise ValueError('sDate must not be later than eDate')\n", "else:\n", "\tcfg['sDate'] = args.sDate\n", "\tcfg['eDate'] = args.eDate\n", "\n", "## override backfill if specified in terminal\n", "if args.backfill is not None:\n", "\tcfg['backfill'] = args.backfill\n", "else:\n", "\t## set backfill as nowtime if sDate == eDate, otherwise keep default backfill\n", "\tif args.sDate == args.eDate:\n", "\t\ttz = cfg.get('time_zone', None)\n", "\t\tassert tz is not None, 'time_zone not found in config'\n", "\t\tnowtime = datetime.datetime.now(tz=ZoneInfo(tz)).strftime('%H%M%S')\n", "\t\tcfg['backfill'] = nowtime \n", "\n", "## read exchange_list\n", "cfg['ctry_list'] = load_config('./configs/country.set.json')[cfg.get('country_set')]\n", "\n", "## initialize feature calculator\n", "sfc = fc.SimpleFeatureCalclator(cfg)\n", "\n", "## load callback from script\n", "callback = cfg['callback']\n", "spec = impt.spec_from_file_location(callback, cfg['script_path'])\n", "module = impt.module_from_spec(spec)\n", "spec.loader.exec_module(module)\n", "\n", "if hasattr(module, callback):\n", "\tsfc.callback = getattr(module, callback)\n", "\n", "sfc.calc()\n", "\n", "print(args.config)\n", "bt = cfg.get('backtest', None)\n", "if bt:\n", "\tcfg_filename = args.config.split('/')[-1].split('.json')[0]\n", "\tos.system(f\"bash backtest2.sh {cfg_filename}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### user_script"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/fc_scripts/EU1.py'\n", "\n", "import pandas as pd\n", "import numpy as np\n", "\n", "from barra.data.loader import DataLoader\n", "loader = DataLoader()\n", "\n", "\n", "def rolling_ema(x, com, span, halflife, alpha, adjust):\n", "\treturn x.ewm(com=com, span=span, halflife=halflife, alpha=alpha, adjust=adjust).mean().iloc[-1]\n", "\n", "\n", "def custom_ema(x, halflife, adjust):\n", "\tprewt = 0.5 ** ((1 / halflife) * (x.shape[0] - np.arange(1, x.shape[0] + 1)))\n", "\tif adjust: prewt /= np.sum(prewt)  # adjust to unity if adjust\n", "\treturn np.nansum(x * prewt, axis=0) ## calculate last weighted sum\n", "\n", "\n", "## corresponding factor_name of wkmom.json\n", "def fc_wkmom_score(sDate: str, eDate: str, cfg: dict):\n", "\t## load fundamental info data\n", "\tinfo_data = loader.load_fundamental(sDate, eDate, cfg)\n", "\n", "\t## factor name should be defined in cfg as factor_name\n", "\tfactor_name = cfg.get('factor_name', None)\n", "\tassert factor_name is not None, 'factor_name should be defined in cfg'\n", "\n", "\traw_factor = info_data['SpecificReturn'].groupby(level='BarraID').rolling(5).mean().droplevel(0)\n", "\traw_factor = raw_factor.to_frame(name=factor_name)  # series to dataframe\n", "\n", "\t## filter out duplicated entries\n", "\traw_factor = raw_factor.loc[~raw_factor.index.duplicated()]\n", "\n", "\t## filter out stocks not in universe\n", "\tif cfg.get('universe_set') != '':\n", "\t\tuniv_df = loader.load_universe(sDate, eDate, cfg)\n", "\t\traw_factor = raw_factor.loc[raw_factor.index.intersection(univ_df.index)]\n", "\n", "\t## raw factor fed back to feature calculator, followed by neutralization and as_weight\n", "\treturn raw_factor.sort_index()\n", "\n", "\n", "## corresponding factor_name of wkmom.json\n", "def fc_mnmom_score(sDate: str, eDate: str, cfg: dict):\n", "\t## load fundamental info data\n", "\tinfo_data = loader.load_fundamental(sDate, eDate, cfg)\n", "\n", "\t## factor name should be defined in cfg as factor_name\n", "\tfactor_name = cfg.get('factor_name', None)\n", "\tassert factor_name is not None, 'factor_name should be defined in cfg'\n", "\n", "\traw_factor = info_data['SpecificReturn'].groupby(level='BarraID').rolling(5).mean().droplevel(0)\n", "\traw_factor = raw_factor.to_frame(name=factor_name)  # series to dataframe\n", "\n", "\t## filter out duplicated entries\n", "\traw_factor = raw_factor.loc[~raw_factor.index.duplicated()]\n", "\n", "\t## filter out stocks not in universe\n", "\tif cfg.get('universe_set') != '':\n", "\t\tuniv_df = loader.load_universe(sDate, eDate, cfg)\n", "\t\traw_factor = raw_factor.loc[raw_factor.index.intersection(univ_df.index)]\n", "\n", "\t## raw factor fed back to feature calculator, followed by neutralization and as_weight\n", "\treturn raw_factor.sort_index()\n", "\n", "\n", "\n", "\n", "## expotential moving average of ANLYSTSN factor difference\n", "def fc_anlystsn_ema_score(sDate: str, eDate: str, cfg: dict):\n", "\t## load fundamental info data\n", "\tinfo_data = loader.load_fundamental(sDate, eDate, cfg)\n", "\n", "\t## factor name should be defined in cfg as factor_name\n", "\tfactor_name = cfg.get('factor_name', None)\n", "\tassert factor_name is not None, 'factor_name should be defined in cfg'\n", "\tbp = cfg.get('barra_product', None)\n", "\tif bp in {'EUTR'}:\n", "\t\tprefix = f'{bp}D_'\n", "\telif bp in {'EUE4'}:\n", "\t\tprefix = f'{bp}BASS_'\n", "\ta = info_data[f'{prefix}ANLYSTSN'].groupby(level='BarraID').diff()\n", "\n", "\t## parallel calculation with pandarallel\n", "\tfrom pandarallel import pandarallel\n", "\tpandarallel.initialize(nb_workers=10)\n", "\traw_factor = a.to_frame().groupby('BarraID')\\\n", "\t\t.parallel_apply(lambda x: x.rolling(260)\\\n", "\t\t\t\t  .apply(lambda y: custom_ema(y, halflife=130, adjust=True), raw=False)).droplevel(0)\n", "\traw_factor.columns = [factor_name]\n", "\n", "\t## filter out duplicated entries\n", "\traw_factor = raw_factor.loc[~raw_factor.index.duplicated()]\n", "\n", "\t## filter out stocks not in universe\n", "\tif cfg.get('universe_set') != '':\n", "\t\tuniv_df = loader.load_universe(sDate, eDate, cfg)\n", "\t\traw_factor = raw_factor.loc[raw_factor.index.intersection(univ_df.index)]\n", "\n", "\t## raw factor fed back to feature calculator, followed by neutralization and as_weight\n", "\treturn raw_factor.sort_index()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# batch_fc.sh"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/batch_fc.sh'\n", "#!/bin/bash\n", "\n", "## cfg file names to be applied, spaced with space\n", "cfgs=(\"fc.wkmom.eu1.01\")\n", "sDate=\"2016-01-01\"\n", "eDate=\"2016-06-30\"\n", "backfill=\"020000\"\n", "\n", "for i in \"${cfgs[@]}\";\n", "do\n", "    echo \"running ${i}\"\n", "    bash fc.sh ${i} ${sDate} ${eDate} ${backfill} &\n", "done\n", "wait\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# fitting"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## barra.fitting.py"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Overwriting /mnt/sda/home/<USER>/working/gitrepo/barra/fitting/fitting.py\n"]}], "source": ["%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/fitting/fitting.py'\n", "# coding = utf-8\n", "__all__ = ['LinearRegressor', 'calc_time_weight', 'calc_cs_time_weight']\n", "\n", "import os\n", "import numpy as np\n", "import pandas as pd\n", "from scipy.optimize import minimize, Bounds\n", "\n", "from ..utils import tools\n", "\n", "\n", "def calc_time_weight(length: int, halflife: float) -> np.array:\n", "\t\"\"\"Calculate time weights using exponential moving average with a given halflife.\n", "\n", "\tArgs:\n", "\t    length (int): Number of periods.\n", "\t    halflife (float): Halflife parameter for the EMA.\n", "\n", "\tReturns:\n", "\t    pd.Series: Array of time weights, most recent first.\n", "\t\"\"\"\n", "\t# Decay factor per period\n", "\talpha = 1 - np.exp(np.log(0.5) / halflife)\n", "\tweights = np.array([(1 - alpha) ** i for i in range(length - 1, -1, -1)])\n", "\tweights /= weights.sum()\n", "\treturn weights\n", "\n", "\n", "def calc_cs_time_weight(df: pd.DataFrame, halflife: float) -> pd.DataFrame:\n", "\t\"\"\"\n", "\tGenerate a cross-sectional time weight for a multi-index DataFrame (date, BarraID).\n", "\tThe weight for all BarraIDs of the same date is the same, based on the calculate_time_weight function.\n", "\tThe output is a multi-index DataFrame with one column named 'TimeWeight'.\n", "\n", "\tArgs:\n", "\t    df (pd.DataFrame): Multi-index DataFrame with (date, BarraID) as index.\n", "\t    halflife (float): Halflife parameter for the EMA.\n", "\n", "\tReturns:\n", "\t    pd.DataFrame: Multi-index DataFrame with one column 'TimeWeight'.\n", "\t\"\"\"\n", "\tdates = df.index.get_level_values('date').unique().sort_values()\n", "\tweights = calc_time_weight(len(dates), halflife)\n", "\tdate_weight_map = dict(zip(dates, weights))\n", "\ttime_weight = df.index.get_level_values('date').map(date_weight_map)\n", "\treturn pd.DataFrame({'TimeWeight': time_weight}, index=df.index)\n", "\n", "\n", "class LinearRegressor:\n", "\tdef __init__(self, **cfg: dict):\n", "\t\t\"\"\"Initialize the LinearRegressor with configuration parameters.\n", "\n", "\t\tArgs:\n", "\t\t\t**cfg (dict): Configuration parameters to set as instance attributes\n", "\n", "\t\t\"\"\"\n", "\t\tself.__dict__.update(**cfg)\n", "\t\tself.fitted = False\n", "\n", "\tdef gradient(self, beta: np.ndarray, *args):\n", "\t\t\"\"\"Calculate gradient of loss function with respect to beta parameters.\n", "\n", "\t\tArgs:\n", "\t\t\tbeta (np.ndarray): Current beta parameter values\n", "\t\t\t*args: Additional arguments (unused)\n", "\n", "\t\tReturns:\n", "\t\t\tnp.n<PERSON><PERSON>: Gradient vector\n", "\t\t\"\"\"\n", "\t\ty_pred = self.x.dot(beta)\n", "\t\terror = y_pred - self.y\n", "\t\treturn 2 * self.x.T.dot(error)\n", "\n", "\tdef loss(self, beta: np.n<PERSON><PERSON>):\n", "\t\t\"\"\"Calculate loss function value for given beta parameters.\n", "\n", "\t\tArgs:\n", "\t\t\tbeta (np.ndarray): Beta parameter values\n", "\n", "\t\tReturns:\n", "\t\t\tfloat: Loss function value\n", "\t\t\"\"\"\n", "\t\tx, y = self.x, self.y\n", "\n", "\t\t# model_method = self.reg_settings.get('method')\n", "\t\tsw = (1.0 / self.sw**2).squeeze()\n", "\t\ttw = self.tw.squeeze()\n", "\t\t# if model_method == 'linear':\n", "\t\t# \tx = self.x / np.abs(self.sw)\n", "\t\t# \ty = self.y / np.abs(self.sw)\n", "\t\t# \tsw = np.ones_like(self.sw)\n", "\t\t# elif model_method == 'ridge':\n", "\t\t# \tx, y = self.x, self.y\n", "\t\t# \tsw = (1.0 / self.sw**2).squeeze()\n", "\n", "\t\ty_pred = x.dot(beta)\n", "\t\terror = (y - y_pred) ** 2\n", "\t\tl2 = self.reg_settings.get('lambda') * np.sum(np.square(beta))\n", "\t\tif self.reg_settings.get('time_weight', False):\n", "\t\t\treturn np.sum(error * sw * tw) - l2\n", "\t\telse:\n", "\t\t\treturn np.sum(error * sw) - l2\n", "\n", "\tdef fit_(self, x: np.ndarray, y: np.ndarray):\n", "\t\t\"\"\"Internal fitting method for single target variable.\n", "\n", "\t\tArgs:\n", "\t\t\tx (np.n<PERSON>ray): Feature matrix\n", "\t\t\ty (np.ndarray): Target vector\n", "\n", "\t\tReturns:\n", "\t\t\tnp.ndarray: Fitted beta parameters\n", "\t\t\"\"\"\n", "\t\tself.x = np.hstack([x, np.ones((x.shape[0], 1))])\n", "\t\tself.y = y\n", "\t\tbeta_init = np.random.randn(self.x.shape[1])\n", "\t\tdayLB = self.reg_settings.get('dayLB')\n", "\t\tdayUB = self.reg_settings.get('dayUB')\n", "\t\tbounds = Bounds(dayLB, dayUB)\n", "\t\tresult = minimize(self.loss, beta_init, method='L-BFGS-B', bounds=bounds, jac=self.gradient)\n", "\t\t# result = minimize(self.loss, beta_init, method='L-BFGS-B', bounds=bounds)\n", "\t\treturn result.x\n", "\n", "\tdef fit(\n", "\t\tself,\n", "\t\tdf_factor: pd.DataFrame,\n", "\t\tdf_label: pd.DataFrame,\n", "\t\tdf_risk: pd.DataFrame = None,\n", "\t):\n", "\t\t\"\"\"Fit the model to training data.\n", "\n", "\t\tArgs:\n", "\t\t\tdf_factor (pd.DataFrame): Feature dataframe\n", "\t\t\tdf_label (pd.DataFrame): Target dataframe\n", "\t\t\tdf_risk (pd.DataFrame, optional): Risk weights dataframe. Defaults to None.\n", "\t\t\"\"\"\n", "\t\tfactor_names = df_factor.columns.tolist()\n", "\t\tself.label_names = df_label.columns.tolist()\n", "\t\trows = df_factor.index.intersection(df_label.index)\n", "\t\tx, y = df_factor.loc[rows], df_label.loc[rows].fillna(0.0)\n", "\t\tx = x.loc[~x.isna().all(axis=1)].fillna(0.0)\n", "\t\ty = y.loc[x.index]\n", "\n", "\t\tif df_risk is not None:\n", "\t\t\tself.sw = df_risk.loc[x.index].values  ## get sample_weight\n", "\t\telse:\n", "\t\t\tself.sw = np.ones_like(x.index)\n", "\n", "\t\tself.tw = calc_cs_time_weight(x, self.default_settings.get('halflife'))\n", "\t\t## convert dataframe to numpy for fitting\n", "\t\tx_, y_ = x.values, y.values\n", "\n", "\t\tbetas = []\n", "\t\tfor i in range(y_.shape[1]):\n", "\t\t\tbeta = self.fit_(x_, y_[:, i])\n", "\t\t\tbetas.append(beta)\n", "\t\t\ttools.Logger.info(f'fitted: {i + 1}/{y_.shape[-1]}', end='\\r')\n", "\n", "\t\trows, cols = self.label_names, factor_names + ['intercept']\n", "\t\tself.beta = pd.DataFrame(np.array(betas), index=rows, columns=cols).T\n", "\n", "\t\tself.fitted = True\n", "\t\tself.x, self.y = x, y\n", "\n", "\tdef predict(self, df_factor: pd.DataFrame):\n", "\t\t\"\"\"Make predictions using fitted model.\n", "\n", "\t\tArgs:\n", "\t\t\tdf_factor (pd.DataFrame): Feature dataframe for prediction\n", "\n", "\t\tReturns:\n", "\t\t\tpd.DataFrame: Predicted values\n", "\t\t\"\"\"\n", "\t\tindex = df_factor.index\n", "\t\tx = df_factor.copy()\n", "\t\t# x['intercept'] = 1.0\n", "\n", "\t\tinfered = x.dot(self.beta.iloc[:-1, :])\n", "\t\tself.alpha = pd.DataFrame(infered, index=index, columns=self.label_names)\n", "\t\treturn self.alpha\n", "\n", "\tdef save_results(self, user_root: str, date: str, debug: bool = False, verbose: bool = False):\n", "\t\t\"\"\"Save fitted model results to files.\n", "\n", "\t\tArgs:\n", "\t\t\tuser_root (str): Root directory for saving files\n", "\t\t\tdate (str): Date string for file naming\n", "\t\t\tdebug (bool, optional): Whether to save debug info. Defaults to False.\n", "\t\t\tverbose (bool, optional): Whether to print save messages. Defaults to False.\n", "\t\t\"\"\"\n", "\t\talphadir = f'{user_root}fitting/{self.barra_product}/{self.alpha_group_name}/{self.alpha_name}/alpha/'\n", "\t\tbetadir = f'{user_root}fitting/{self.barra_product}/{self.alpha_group_name}/{self.alpha_name}/beta/'\n", "\t\txydir = f'{user_root}fitting/{self.barra_product}/{self.alpha_group_name}/{self.alpha_name}/xy/'\n", "\t\txstddir = f'{user_root}fitting/{self.barra_product}/{self.alpha_group_name}/{self.alpha_name}/xstd/'\n", "\n", "\t\tif not os.path.exists(alphadir):\n", "\t\t\tos.makedirs(alphadir, exist_ok=True)\n", "\t\tself.alpha.to_parquet(f'{alphadir}{date}.parquet')\n", "\n", "\t\tif not os.path.exists(betadir):\n", "\t\t\tos.makedirs(betadir, exist_ok=True)\n", "\t\tbeta_index = pd.MultiIndex.from_product([[date], self.beta.index])\n", "\t\tbeta_index.names = ['date', 'factor']\n", "\t\tself.beta.set_index(beta_index).to_parquet(f'{betadir}{date}.parquet')\n", "\n", "\t\tif not os.path.exists(xstddir):\n", "\t\t\tos.makedirs(xstddir, exist_ok=True)\n", "\t\tself.xstd = self.x.std(axis=0).to_frame(name='x_std')\n", "\t\txstd_index = pd.MultiIndex.from_product([[date], self.xstd.index])\n", "\t\txstd_index.names = ['date', 'factor']\n", "\t\tself.xstd.set_index(xstd_index).to_parquet(f'{xstddir}{date}.parquet')\n", "\n", "\t\tif debug:\n", "\t\t\tif not os.path.exists(xydir):\n", "\t\t\t\tos.makedirs(xydir, exist_ok=True)\n", "\t\t\txy = pd.concat([self.x, self.y, self.sw], axis=1)\n", "\t\t\txy.to_parquet(f'{xydir}{date}.parquet')\n", "\n", "\t\tif verbose:\n", "\t\t\ttools.Logger.info(f'alpha saved for [{date}] at [{alphadir}]', end='\\n')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## demo.fitting.py"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[2025-05-30 13:41:46] loading factor for 2016-01-20 - 2024-06-30 ...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b64d395754b142bcaec123e57c2954c2", "version_major": 2, "version_minor": 0}, "text/plain": ["loading_factor:   0%|          | 0/2203 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"ename": "AssertionError", "evalue": "slurm scripts should be generated on SLURM MANAGER", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAssertionError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/mnt/sda/home/<USER>/working/4.3package_dev2.ipynb Cell 82\u001b[0m line \u001b[0;36m2\n\u001b[1;32m    <a href='vscode-notebook-cell://ssh-remote%2B10.218.224.7/mnt/sda/home/<USER>/working/4.3package_dev2.ipynb#Y144sdnNjb2RlLXJlbW90ZQ%3D%3D?line=249'>250</a>\u001b[0m \t\t_ \u001b[39m=\u001b[39m run_task(dates, \u001b[39m50\u001b[39m)\n\u001b[1;32m    <a href='vscode-notebook-cell://ssh-remote%2B10.218.224.7/mnt/sda/home/<USER>/working/4.3package_dev2.ipynb#Y144sdnNjb2RlLXJlbW90ZQ%3D%3D?line=252'>253</a>\u001b[0m \u001b[39mif\u001b[39;00m \u001b[39m__name__\u001b[39m \u001b[39m==\u001b[39m \u001b[39m\"\u001b[39m\u001b[39m__main__\u001b[39m\u001b[39m\"\u001b[39m:\n\u001b[0;32m--> <a href='vscode-notebook-cell://ssh-remote%2B10.218.224.7/mnt/sda/home/<USER>/working/4.3package_dev2.ipynb#Y144sdnNjb2RlLXJlbW90ZQ%3D%3D?line=253'>254</a>\u001b[0m \tmain()\n\u001b[1;32m    <a href='vscode-notebook-cell://ssh-remote%2B10.218.224.7/mnt/sda/home/<USER>/working/4.3package_dev2.ipynb#Y144sdnNjb2RlLXJlbW90ZQ%3D%3D?line=254'>255</a>\u001b[0m \tt1 \u001b[39m=\u001b[39m time\u001b[39m.\u001b[39mtime()\n\u001b[1;32m    <a href='vscode-notebook-cell://ssh-remote%2B10.218.224.7/mnt/sda/home/<USER>/working/4.3package_dev2.ipynb#Y144sdnNjb2RlLXJlbW90ZQ%3D%3D?line=255'>256</a>\u001b[0m \t\u001b[39mprint\u001b[39m(\u001b[39mf\u001b[39m\u001b[39m'\u001b[39m\u001b[39mtime elasped: \u001b[39m\u001b[39m{\u001b[39;00m(t1\u001b[39m \u001b[39m\u001b[39m-\u001b[39m\u001b[39m \u001b[39mt0)\u001b[39m:\u001b[39;00m\u001b[39m.2f\u001b[39m\u001b[39m}\u001b[39;00m\u001b[39m'\u001b[39m)\n", "\u001b[1;32m/mnt/sda/home/<USER>/working/4.3package_dev2.ipynb Cell 82\u001b[0m line \u001b[0;36m2\n\u001b[1;32m    <a href='vscode-notebook-cell://ssh-remote%2B10.218.224.7/mnt/sda/home/<USER>/working/4.3package_dev2.ipynb#Y144sdnNjb2RlLXJlbW90ZQ%3D%3D?line=237'>238</a>\u001b[0m \u001b[39mif\u001b[39;00m num_factor \u001b[39m>\u001b[39m\u001b[39m=\u001b[39m \u001b[39m50\u001b[39m:\n\u001b[1;32m    <a href='vscode-notebook-cell://ssh-remote%2B10.218.224.7/mnt/sda/home/<USER>/working/4.3package_dev2.ipynb#Y144sdnNjb2RlLXJlbW90ZQ%3D%3D?line=238'>239</a>\u001b[0m \t\u001b[39mif\u001b[39;00m \u001b[39m'\u001b[39m\u001b[39mSLURM_JOB_ID\u001b[39m\u001b[39m'\u001b[39m \u001b[39mnot\u001b[39;00m \u001b[39min\u001b[39;00m os\u001b[39m.\u001b[39menviron:  \u001b[39m## not in a slurm cluster\u001b[39;00m\n\u001b[0;32m--> <a href='vscode-notebook-cell://ssh-remote%2B10.218.224.7/mnt/sda/home/<USER>/working/4.3package_dev2.ipynb#Y144sdnNjb2RlLXJlbW90ZQ%3D%3D?line=239'>240</a>\u001b[0m \t\t\u001b[39massert\u001b[39;00m ip \u001b[39m==\u001b[39m \u001b[39m'\u001b[39m\u001b[39m************\u001b[39m\u001b[39m'\u001b[39m, \u001b[39m'\u001b[39m\u001b[39mslurm scripts should be generated on SLURM MANAGER\u001b[39m\u001b[39m'\u001b[39m\n\u001b[1;32m    <a href='vscode-notebook-cell://ssh-remote%2B10.218.224.7/mnt/sda/home/<USER>/working/4.3package_dev2.ipynb#Y144sdnNjb2RlLXJlbW90ZQ%3D%3D?line=240'>241</a>\u001b[0m \t\tpyargs \u001b[39m=\u001b[39m \u001b[39mf\u001b[39m\u001b[39m'\u001b[39m\u001b[39m{\u001b[39;00mos\u001b[39m.\u001b[39mpath\u001b[39m.\u001b[39mabspath(\u001b[39m__file__\u001b[39m)\u001b[39m}\u001b[39;00m\u001b[39m'\u001b[39m\n\u001b[1;32m    <a href='vscode-notebook-cell://ssh-remote%2B10.218.224.7/mnt/sda/home/<USER>/working/4.3package_dev2.ipynb#Y144sdnNjb2RlLXJlbW90ZQ%3D%3D?line=241'>242</a>\u001b[0m \t\tjob_name \u001b[39m=\u001b[39m \u001b[39m'\u001b[39m\u001b[39mfit_\u001b[39m\u001b[39m'\u001b[39m \u001b[39m+\u001b[39m fit_cfg\u001b[39m.\u001b[39mget(\u001b[39m'\u001b[39m\u001b[39malpha_name\u001b[39m\u001b[39m'\u001b[39m)\n", "\u001b[0;31mAssertionError\u001b[0m: slurm scripts should be generated on SLURM MANAGER"]}], "source": ["# %%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/fit.py'\n", "\n", "# coding = utf-8\n", "import os\n", "import sys\n", "import time\n", "import socket\n", "import datetime\n", "import subprocess\n", "\n", "os.environ['NUMEXPR_MAX_THREADS'] = '200'\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from barra.utils import tools\n", "from barra.utils import calendar\n", "from barra.utils import processor as prep\n", "from barra.data import loader\n", "from barra.fitting import fitting\n", "from barra.fitting.alpha_reporter import AlphaReporter\n", "ip = socket.gethostbyname((socket.gethostname()))\n", "\n", "\n", "def parse_factor_settings(input_settings: dict):\n", "\treturn {k: v for k, v in input_settings.items() if k != 'Intercept'}\n", "\n", "\n", "def parse_intercept_settings(input_settings: dict):\n", "\treturn input_settings.get('Intercept')\n", "\n", "\n", "t0 = time.time()\n", "prefix = './'\n", "prefix = '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/'\n", "user_path_json = f'{prefix}configs/user.path.json'\n", "user_root = tools.parse_user_root(user_path_json)\n", "floader = loader.FactorLoader(user_path_json)\n", "\n", "# fit_cfg_file = f'{prefix}configs/fitting/eu1.01.json'\n", "# fit_cfg_file = f'{prefix}configs/fitting/buyside.json'\n", "# fit_cfg_file = f'{prefix}configs/fitting/mom.json'\n", "fit_cfg_file = f'{prefix}configs/fitting/mom2.json'\n", "# fit_cfg_file = f'{prefix}configs/fitting/analyst.json'\n", "# fit_cfg_file = f'{prefix}configs/fitting/spraw.json'\n", "# fit_cfg_file = f'{prefix}configs/fitting/spraw2_tw4.json'\n", "fit_cfg = loader.load_config(fit_cfg_file)\n", "\n", "input_settings = fit_cfg.get('input', None)\n", "factor_settings = parse_factor_settings(input_settings)\n", "reg_settings = fit_cfg.get('reg_settings', None)\n", "default = fit_cfg.get('default_settings', None)\n", "\n", "\n", "barra_product = fit_cfg.get('barra_product')\n", "fgroup_name = fit_cfg.get('factor_group_name', None)\n", "algroup_name = fit_cfg.get('alpha_group_name', None)\n", "alname = fit_cfg.get('alpha_name', None)\n", "\n", "half_life = default.get('halflife', None)\n", "lookback = half_life * default.get('number_ff_HL', None)\n", "horizons = range(1, int(default.get('predict_horizon', None)) + 1)\n", "label_options = [f'H1_{i}' for i in horizons]\n", "\n", "return_map = {\n", "\t'idio': ['SpecificReturn', 'SpecRisk%'],\n", "\t'total': ['DlyReturn%', 'TotalRisk%'],\n", "}\n", "\n", "\n", "def prepare_factor(fit_cfg: dict, sample: bool = False):\n", "\tsDate, eDate = fit_cfg.get('sDate'), fit_cfg.get('eDate')\n", "\ttd_days = calendar.previous_trading_days(fit_cfg.get('calendar'), sDate, lookback=lookback + 5)\n", "\tsDate = td_days[0]\n", "\n", "\t# sp.set1\n", "\t# factor_list = [\n", "\t# \t\"5YRel_PTMargin\",\n", "\t# \t\"OCFRatio\",\n", "\t# \t\"IndRel_FCFEV\",\n", "\t# \t\"5YRel_FCFP\",\n", "\t# \t\"FCFEV\",\n", "\t# \t\"6MChgTgtPrcGapEMA\",\n", "\t# \t\"EP\",\n", "\t# \t\"RelPrStr_12M\",\n", "\t# \t\"<PERSON><PERSON><PERSON>\",\n", "\t# \t\"HL1M\",\n", "\t# ]\n", "\n", "\t# buyesideInst\n", "\t# factor_list = [\n", "\t# \t'VWAF_1_Day_Change_s5',\n", "\t# \t'InventoryIncrease_ratio_m5',\n", "\t# \t'Transactions_pervolume',\n", "\t# \t'Short_Loan_Quantity_as_of_Shares_Outstanding',\n", "\t# \t'short_ratio_m5',\n", "\t# \t'DIPS',\n", "\t# \t'DIMV',\n", "\t# \t'BO_On_Loan_Value_Rank_1_Market_Share_m5',\n", "\t# \t'DNS',\n", "\t# \t'DPS',\n", "\t# \t# 'DCBS',\n", "\t# \t'Short_Utilisation_m5',\n", "\t# \t'Broker_Demand_ratio_m5',\n", "\t# \t'Active_Short_Utilisation_m5',\n", "\t# \t'BOInventoryValue_on_mv_m5dividem130',\n", "\t# \t'SL_Tenure_m5',\n", "\t# \t'BOInventoryValue_on_mv_m5',\n", "\t# \t'Inactive_Agents_m5dm130',\n", "\t# ]\n", "\n", "\t## for illustration, need to adapt to real use cases\n", "\ttools.Logger.info(f'loading factor for {sDate} - {eDate} ...', end='\\n')\n", "\t# momentum & splib\n", "\tfactor_list = []\n", "\tif sample:\n", "\t\teDate = sDate\n", "\treturn floader.load(\n", "\t\tsDate,\n", "\t\teDate,\n", "\t\t'EU1',\n", "\t\t'SP_SET_RAW', # 'SP_SET_RAW', 'mom\n", "\t\t'raw',\n", "\t\tfactor_root=factor_root,\n", "\t\tbackfill=None,\n", "\t\tfactor_list=factor_list,\n", "\t)\n", "\n", "\n", "def prepare_label(fit_cfg: dict):\n", "\tsDate, eDate = fit_cfg.get('sDate'), fit_cfg.get('eDate')\n", "\ttd_days = calendar.previous_trading_days(fit_cfg.get('calendar'), sDate, lookback=lookback + 5)\n", "\tsDate = td_days[0]\n", "\n", "\tlabel_cfg = {\n", "\t\t'label_usecols': return_map.get(reg_settings.get('return_and_risk_type')),\n", "\t\t'label_options': label_options,\n", "\t\t'barra_product': barra_product,\n", "\t\t'calendar': fit_cfg.get('calendar'),\n", "\t}\n", "\n", "\ttools.Logger.info(f'loading label and risk from {sDate} to {eDate} ...', end='\\n')\n", "\tdf_label, risk = loader.load_label(sDate, eDate, label_cfg)\n", "\n", "\treturn df_label, risk\n", "\n", "\n", "def fit_1day(date: str, df_factor: pd.DataFrame, df_label: pd.DataFrame, df_risk: pd.DataFrame):\n", "\tlr = fitting.LinearRegressor(**fit_cfg)\n", "\t# lr = LinearRegressor(**fit_cfg)\n", "\tuser_root = tools.parse_user_root(user_path_json)\n", "\talpha_dir = f'{user_root}fitting/{barra_product}/{algroup_name}/{alname}/alpha/'\n", "\t# if os.path.exists(f'{alpha_dir}{date}.parquet'):\n", "\t# \treturn None\n", "\n", "\t# train with data before yesterday and yesterday's factor, label and risk\n", "\ttrain_dates = calendar.previous_trading_days(fit_cfg.get('calendar'), date, lookback=lookback + 2)[:-2]\n", "\tfeature = df_factor.loc[train_dates[0] : train_dates[-1]].copy()\n", "\tlabel = df_label.loc[train_dates[0] : train_dates[-1]].copy()\n", "\trisk = df_risk.loc[train_dates[0] : train_dates[-1]].copy()\n", "\n", "\t# print(f'fitting for {date}!', end='\\n')\n", "\tlr.fit(feature, label, risk)\n", "\n", "\t# use yesterday's factor to predict today's alpha\n", "\tyesterday = calendar.previous_trading_days(fit_cfg.get('calendar'), date, lookback=2)[-2]\n", "\tif yesterday not in cdates:\n", "\t\treturn None\n", "\tnext_factor = df_factor.loc[yesterday].copy()\n", "\tif (next_factor is None) | (len(next_factor) == 0):\n", "\t\treturn None\n", "\n", "\tlr.predict(next_factor.fillna(0.0))\n", "\tlr.alpha['date'] = date\n", "\tlr.alpha = lr.alpha.set_index('date', append=True).swaplevel(0, 1)\n", "\tlr.alpha.index.names = ['date', 'BarraID']\n", "\ttools.Logger.info(f'fitting finished for {date}!', end='\\r')\n", "\n", "\tlr.save_results(user_root, date)\n", "\n", "\tif date in dates[-1:]:  ## only report at the last day\n", "\t\tfiles = os.listdir(alpha_dir)\n", "\t\tif len(files) > 0:\n", "\t\t\talpha_reporter = AlphaReporter(alpha_dir)\n", "\t\t\talpha_reporter.report()\n", "\n", "\n", "# outlier winsorization\n", "def yprep(df: pd.DataFrame):\n", "\tdf = prep.winsorize(df, 'percentile', percentile=0.05)\n", "\treturn df\n", "\n", "\n", "def xprep(df: pd.DataFrame):  # one group\n", "\tdf = prep.winsorize(df, 'percentile', percentile=0.05)\n", "\tdf = prep.winsorize(df, 'median_mad', sigma=4)\n", "\tdf = prep.zscore(df)\n", "\treturn df\n", "\n", "\n", "factor_root = '/mnt/sda/NAS/ShareFolder/pengpuda/factor_analysis/euronext/SP/features/EUTR/'\n", "df_factor = prepare_factor(fit_cfg, sample=False)\n", "# drop_names = ['OEA', '5YRel_OEA', 'ROA', '5YRel_ROA']\n", "# if len(set(drop_names).intersection(set(df_factor.columns)))>0:\n", "# \tdf_factor = df_factor.drop(drop_names, axis=1)\n", "num_factor = df_factor.shape[1]\n", "\n", "num_array = 40\n", "dates = calendar.trading_days_between(fit_cfg['calendar'], fit_cfg['sDate'], fit_cfg['eDate'])\n", "cdates = df_factor.index.get_level_values('date').unique().tolist()\n", "dates = [date for date in dates if date in cdates]\n", "dates_list = np.array_split(dates, num_array)\n", "task_map = {str(i + 1): dates_list[i] for i in range(num_array)}\n", "\n", "if (num_factor >= 50) & ('SLURM_JOB_ID' in os.environ): # not in slurm cluster\n", "\tdf_label, df_risk = prepare_label(fit_cfg)\n", "\n", "\t# Get common index across all 3 dataframes\n", "\tcommon_idx = df_factor.index.intersection(df_label.index).intersection(df_risk.index)\n", "\tdf_factor = df_factor.loc[common_idx]\n", "\tdf_label = df_label.loc[common_idx] * 100  # in unit percentage\n", "\tdf_risk = df_risk.loc[common_idx] * 100  # in unit percentage\n", "\n", "\n", "\tdf_label = df_label.groupby('date').apply(yprep).droplevel(0)\n", "\tdf_risk = df_risk.groupby('BarraID').shift(1)\n", "\tdf_risk = df_risk.groupby('date').apply(yprep).droplevel(0)\n", "\tdf_factor = df_factor.groupby(level='date').apply(xprep).droplevel(0)\n", "\n", "\n", "\n", "def run_task(dates, njobs=5):\n", "\ttasks = [(date, df_factor, df_label, df_risk) for date in dates[:]]\n", "\t_ = tools.parallel(fit_1day, tasks, njobs=njobs, backend='loky', progress_bar=True, desc='fitting')\n", "\n", "\n", "def main():\n", "\tif num_factor >= 50:\n", "\t\tif 'SLURM_JOB_ID' not in os.environ:  ## not in a slurm cluster\n", "\t\t\tassert ip == '************', 'slurm scripts should be generated on SLURM MANAGER'\n", "\t\t\tpyargs = f'{os.path.abspath(__file__)}'\n", "\t\t\tjob_name = 'fit_' + fit_cfg.get('alpha_name')\n", "\t\t\tscript = tools.generate_slurm_commands('AMD_R', 50, num_array, 10, pyargs=pyargs, job_name=job_name)\n", "\t\t\tprint(script)\n", "\t\t\tsubprocess.run(['sbatch'], input=script.encode('utf-8'), check=True)\n", "\t\telif 'SLURM_JOB_ID' in os.environ:  # already in slurm cluster\n", "\t\t\trun_task(dates=task_map[os.environ['SLURM_ARRAY_TASK_ID']], njobs=45)\n", "\telse:\n", "\t\tassert ip != '************', 'standalone mode should not be run on SLURM MANAGER'\n", "\t\t_ = run_task(dates, 50)\n", "\n", "\n", "if __name__ == \"__main__\":\n", "\tmain()\n", "\tt1 = time.time()\n", "\tprint(f'time elasped: {(t1 - t0):.2f}')"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["drop_names = ['OEA', '5YRel_OEA', 'ROA', '5YRel_ROA']\n", "df_factor[drop_names].hist(bins=100);"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["df_factor_ = df_factor.groupby(level='date').apply(xprep).droplevel(0)\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["df_factor_[drop_names].hist(bins=100);"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[2025-05-30 16:48:11] report pdf file generated at /mnt/sda/NAS/ShareFolder/lishuanglin/test/fitting/GEM3/momentum/EU1.momentum2/\n"]}], "source": ["from barra.fitting.alpha_reporter import AlphaReporter\n", "alpha_dir = '/mnt/sda/NAS/ShareFolder/lishuanglin/test/fitting/GEM3/momentum/EU1.momentum2/alpha/'\n", "alpha_reporter = AlphaReporter(alpha_dir)\n", "alpha_reporter.report()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## alpha report"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/fitting/alpha_reporter.py'\n", "import os\n", "import datetime\n", "import pandas as pd\n", "from barra.utils import tools\n", "import matplotlib.pyplot as plt\n", "from matplotlib.backends.backend_pdf import PdfPages\n", "from multiprocessing import Lock\n", "\n", "plt.rcParams['figure.figsize'] = (12, 6)\n", "plt.rcParams['axes.grid'] = True\n", "plt.rcParams['legend.loc'] = 'lower left'\n", "\n", "\n", "\n", "class AlphaReporter:\n", "\tdef __init__(self, alpha_dir: str):\n", "\t\tself.alpha_dir = alpha_dir\n", "\t\tself.beta_dir = self.alpha_dir.replace('alpha', 'beta')\n", "\t\tself.xstd_dir = self.alpha_dir.replace('alpha', 'xstd')\n", "\n", "\tdef read(self, file_dir: str, date: str):\n", "\t\tfilepath = f'{file_dir}{date}.parquet'\n", "\t\tif not os.path.exists(filepath):\n", "\t\t\treturn None\n", "\t\treturn pd.read_parquet(filepath)\n", "\n", "\tdef read_files(self, file_dir: str):\n", "\t\tdates = sorted([s.split('.')[0] for s in os.listdir(file_dir)])\n", "\t\tdatas = [self.read(file_dir, date) for date in dates]\n", "\t\tdatas = [value for value in datas if value is not None]\n", "\t\tif len(datas) == 0: return None\n", "\t\treturn pd.concat(datas)\n", "\n", "\tdef report_beta(self, pdf, df: pd.DataFrame, data_type: str):\n", "\t\tb = df.iloc[:, 0].unstack('factor')\n", "\t\tfnames = b.columns.tolist()\n", "\t\tfnames_group = [fnames[i : i + 5] for i in range(0, len(fnames), 5)]\n", "\t\tfor fgroup in fnames_group:\n", "\t\t\tfig, ax = plt.subplots()\n", "\t\t\tb.loc[:, fgroup].plot(alpha=0.8, ax=ax, title=f'{data_type}')\n", "\t\t\tplt.legend()\n", "\t\t\tpdf.savefig(fig, bbox_inches='tight')\n", "\t\t\tplt.close()\n", "\n", "\tdef report_alpha(self, pdf, alpha: pd.DataFrame):\n", "\t\ta = alpha.iloc[:, 0].unstack(0)\n", "\t\tb = a.agg(['mean', 'max', 'min', 'std']).T\n", "\t\tb[['qtile0.1', 'qtile0.9']] = a.quantile([0.1, 0.9]).T\n", "\t\tfig, ax = plt.subplots()\n", "\t\tb[['mean', 'max', 'min', 'qtile0.1', 'qtile0.9']].plot(ax=ax)\n", "\t\tplt.title('alpha -statistics')\n", "\t\tpdf.savefig(fig, bbox_inches='tight')\n", "\t\tplt.close()\n", "\n", "\t\tfig, ax = plt.subplots()\n", "\t\tb[['std']].plot(ax=ax)\n", "\t\tplt.title('alpha -std')\n", "\t\tpdf.savefig(fig, bbox_inches='tight')\n", "\t\tplt.close()\n", "\n", "\t\tfig, ax = plt.subplots()\n", "\t\talpha.groupby('date').count().plot(title='valid_count', ax=ax)\n", "\t\tpdf.savefig(fig, bbox_inches='tight')\n", "\t\tplt.close()\n", "\n", "\t\tfig, ax = plt.subplots()\n", "\t\t(~(alpha==0.0)).groupby('date').sum().plot(title='valid_score_count', ax=ax)\n", "\t\tpdf.savefig(fig, bbox_inches='tight')\n", "\t\tplt.close()\n", "\n", "\n", "\n", "\tdef report_(self, alpha, beta, xstd):\n", "\t\treport_folder = f'{self.alpha_dir.replace(\"alpha/\", \"\")}'\n", "\t\ttime_tag = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')\n", "  \n", "\t\twith Lock():\n", "\t\t\twith PdfPages(f'{report_folder}stats_report_{time_tag}.pdf') as pdf:\n", "\t\t\t\tself.report_alpha(pdf, alpha)\n", "\t\t\t\tself.report_beta(pdf, beta, 'beta')\n", "\t\t\t\tself.report_beta(pdf, xstd, 'xstd')\n", "\n", "\tdef report(\n", "\t\tself,\n", "\t):\n", "\t\tself.alpha = self.read_files(self.alpha_dir)\n", "\t\tself.beta = self.read_files(self.beta_dir)\n", "\t\tself.xstd = self.read_files(self.xstd_dir)\n", "\t\tself.report_(self.alpha, self.beta, self.xstd)\n", "\t\ttools.Logger.info(f'report pdf file generated at {self.alpha_dir.replace(\"alpha/\", \"\")}', end='\\n')\n", "\n", "\n", "\n", "if __name__ == \"__main__\":\n", "\tfitting_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/test/fitting/'\n", "\tbarra_product = 'EUTR'\n", "\talpha_group_name = 'EU1.SP'\n", "\talpha_name = 'EU1.SP1.01'\n", "\n", "\talpha_dir = f'{fitting_root}{barra_product}/{alpha_group_name}/{alpha_name}/alpha/'\n", "\n", "\tareporter = AlphaReporter(alpha_dir)\n", "\tareporter.report()\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# portfolio"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## theo_opt.py"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/portfolio/theo_opt.py'\n", "# coding = utf-8\n", "\n", "import numpy as np\n", "import pandas as pd\n", "from ..utils import tools\n", "\n", "\n", "def calc_asset_cov_mat(\n", "    exp_mat: pd.DataFrame, fcov_mat: pd.DataFrame, srisk_vector: pd.DataFrame\n", ") -> pd.DataFrame:\n", "    \"\"\"\n", "    calculate asset covariance matrix\n", "\n", "    Args:\n", "        exp_mat (pd.DataFrame): exposure matrix (Barrid, risk factors)\n", "        fcov_mat (pd.DataFrame): factor covariance matrix (risk factors, risk factors), symmetric\n", "        srisk_vector (pd.DataFrame): idio risk vector (Barrid, )\n", "\n", "    Returns:\n", "        pd.DataFrame: asset covariance matrix (<PERSON><PERSON>, Barrid)\n", "        v = exp_matrix @ fcov_matrix @ exp_matrix.T + diag(srisk_vector^2)\n", "    \"\"\"\n", "    barraids = exp_mat.index.tolist()\n", "    exp_mat = exp_mat[fcov_mat.columns].to_numpy()  ## align risk factor names\n", "    srisk = srisk_vector.loc[barraids].to_numpy().squeeze()  ## align barra id\n", "    v1 = np.linalg.multi_dot([exp_mat, fcov_mat, np.transpose(exp_mat)])\n", "    v2 = np.diag(np.power(srisk, 2))\n", "    return pd.DataFrame(v1 + v2, index=barraids, columns=barraids)\n", "\n", "\n", "def inverse(matrix: pd.DataFrame, TOL: float = 1e-8) -> pd.DataFrame:\n", "    \"\"\"\n", "    inverse of a symmetric 2D matrix\n", "\n", "    Args:\n", "        matrix (pd.DataFrame): 2D symmetric matrix\n", "        TOL (float, optional): rcond. Defaults to 1e-8.\n", "\n", "    Returns:\n", "        pd.DataFrame: inverse matrix of matrix\n", "    \"\"\"\n", "    assert matrix.shape[0] == matrix.shape[1], \"Matrix must be square\"\n", "    assert np.allclose(matrix, matrix.T), \"Matrix must be symmetric\"\n", "\n", "    indexs = matrix.index.tolist()\n", "    matrix = matrix.to_numpy()\n", "    invertable = np.linalg.det(matrix)\n", "\n", "    if invertable:\n", "        inv_matrix = np.linalg.inv(matrix)\n", "    else:\n", "        inv_matrix = np.linalg.pinv(matrix, rcond=TOL, hermitian=False)\n", "\n", "    return pd.DataFrame(inv_matrix, index=indexs, columns=indexs)\n", "\n", "\n", "def calc_asset_cov_mat_inv(\n", "    exp_mat: pd.DataFrame,\n", "    fcov_mat: pd.DataFrame,\n", "    srisk_vector: pd.DataFrame,\n", "    is_fcov_inverted: bool = False,\n", "    TOL: float = 1e-8,\n", ") -> pd.DataFrame:\n", "    \"\"\"has same result as invert_matrix(calc_asset_cov_mat()), but since calc_asset_cov_mat() returns\n", "    a large matrix and it's slow to calculate the inverse matrix, so use an alternative\n", "    method to get the same result as invert_matrix(calc_asset_cov_mat())\n", "\n", "    Args:\n", "        exp_mat (pd.DataFrame): (Barrid, risk factors)\n", "        fcov_mat (pd.DataFrame): (risk factors, risk factors)\n", "        srisk_vector (pd.DataFrame): (<PERSON><PERSON>, )\n", "        is_fcov_inverted (bool, optional): is fcov already inverted before passing in.\n", "\n", "    Returns:\n", "        pd.DataFrame: inverse of asset covariance matrix (<PERSON><PERSON>, <PERSON><PERSON>)\n", "    \"\"\"\n", "\n", "    \"\"\"\n", "    1. srisk_dinv = diag(srisk_vector^-2)  (<PERSON><PERSON>, <PERSON><PERSON>)\n", "    \"\"\"\n", "    barraids = exp_mat.index.tolist()\n", "    exp_mat = exp_mat[fcov_mat.columns].to_numpy()  ## align risk factor names\n", "    srisk = srisk_vector.loc[barraids].to_numpy().squeeze()  ## align barra id\n", "\n", "    with np.errstate(divide=\"ignore\"):\n", "        srisk_dinv = np.diag(\n", "            np.where(\n", "                np.abs(srisk) > TOL,\n", "                np.power(srisk, -2),\n", "                0,\n", "            )\n", "        )\n", "\n", "    \"\"\"\n", "    2. fcov = inversed factor covariance matrix  (risk factor, risk factor)\n", "    fcov2 = exp.T * dinv * exp + fcov  (risk factor, risk factor)\n", "    fcov3 = ginv(fcov2, tol=1e-8)  (risk factor, risk factor)\n", "    \"\"\"\n", "    # if fcov_mat is inverted, just use it as fcov\n", "    # if fcov_mat is not inverted, then invert it\n", "    fcov = fcov_mat if is_fcov_inverted else inverse(matrix=fcov_mat, TOL=TOL)\n", "    fcov2 = np.linalg.multi_dot([np.transpose(exp_mat), srisk_dinv, exp_mat]) + fcov\n", "    fcov3 = np.linalg.pinv(fcov2, rcond=TOL, hermitian=False)\n", "\n", "    \"\"\"\n", "    3. dx = srisk_dinv * exp  (Barrid, risk factors)\n", "    \"\"\"\n", "    dx = srisk_dinv.dot(exp_mat)\n", "\n", "    \"\"\"\n", "    4. v = srisk_dinv - (dx * fcov3 * dx.T)  (<PERSON><PERSON>, <PERSON>id)\n", "    \"\"\"\n", "    v = srisk_dinv - np.linalg.multi_dot([dx, fcov3, dx.T])\n", "\n", "    return pd.DataFrame(v, index=barraids, columns=barraids)\n", "\n", "\n", "def calc_theo_opt_wt(\n", "    exp_mat: pd.DataFrame,\n", "    fcov_mat: pd.DataFrame,\n", "    srisk_vector: pd.DataFrame,\n", "    score: pd.<PERSON><PERSON><PERSON><PERSON>,\n", "    is_fcov_inverted: bool,\n", "    method: str = \"complete\",\n", ") -> pd.DataFrame:\n", "    \"\"\"\n", "    calculate theoretical optimal weight\n", "\n", "    Args:\n", "        exp_mat (pd.DataFrame): (Barrid, risk factors)\n", "        fcov_mat (pd.DataFrame): (risk factors, risk factors)\n", "        srisk_vector (pd.DataFrame): (Barr<PERSON>, ), specRisk or totalRisk\n", "        score (pd.DataFrame): (Barrid, alps)\n", "        is_fcov_inversed (bool, optional): is fcov already inversed.\n", "\n", "    Returns:\n", "        pd.DataFrame: theoretical optimal portfolio weight (<PERSON><PERSON>, )\n", "    \"\"\"\n", "\n", "    if method == \"woodbury\":\n", "        asset_cov_inv = calc_asset_cov_mat_inv(\n", "            exp_mat=exp_mat,\n", "            fcov_mat=fcov_mat,\n", "            srisk_vector=srisk_vector,\n", "            is_fcov_inverted=is_fcov_inverted,\n", "        )\n", "    elif method == \"complete\":\n", "        asset_cov_inv = inverse(calc_asset_cov_mat(exp_mat, fcov_mat, srisk_vector))\n", "\n", "    assert np.allclose(asset_cov_inv, asset_cov_inv.T, rtol=0.01), (\n", "        \"asset_cov_inv is not symmetric\"\n", "    )\n", "\n", "    ## calculated optimized weight\n", "    h = asset_cov_inv.dot(score)\n", "    return h.div(np.sum(np.abs(h), axis=0))\n", "\n", "\n", "def calc_theo_opt_alpha_1day(\n", "    date: str,\n", "    exposure: pd.DataFrame,\n", "    covariance: pd.Data<PERSON><PERSON>e,\n", "    df_risk: pd.DataFrame,\n", "    df_alpha: pd.DataFrame,\n", "):\n", "    \"\"\"calculate theoretical optimal asset weights for a single day\n", "    Args:\n", "        date (str): Target date for calculation in 'YYYY-MM-DD' format\n", "        exposure (pd.DataFrame): Factor exposure matrix for assets (assets, factor)\n", "        covariance (pd.DataFrame): Factor covariance matrix, should be symmetric\n", "        df_risk (pd.DataFrame): Specific risk data for assets (assets, risk_name)\n", "        df_alpha (pd.DataFrame): Alpha signals/scores for assets (assets, alpha_name)\n", "\n", "    Returns:\n", "        pd.DataFrame: Optimal theoretical portfolio weights calculated by calc_theo_opt_wt function\n", "    \"\"\"\n", "    score_ = df_alpha.loc[date, :]\n", "    fcov_ = covariance.loc[date, :]\n", "\n", "    srisk_ = df_risk.loc[date, :].loc[score_.index]\n", "    exp_mat_ = exposure.loc[date, :].loc[score_.index,:]\n", "    exp_mat = pd.DataFrame(\n", "        np.ones((exp_mat_.shape[0], fcov_.shape[1])),\n", "        # np.zeros((exp_mat_.shape[0], fcov_.shape[1])),\n", "        index=exp_mat_.index,\n", "        columns=fcov_.columns,\n", "    )\n", "    exp_mat.loc[exp_mat_.index, exp_mat_.columns] = exp_mat_\n", "    return calc_theo_opt_wt(exp_mat, fcov_, srisk_, score_, False, \"woodbury\")\n", "    # return calc_theo_opt_wt(exp_mat, fcov_, srisk_, score_, False, \"complete\")\n", "\n", "\n", "def calc_theo_opt_alpha(\n", "    df_alpha: pd.DataFrame,\n", "    exposure: pd.DataFrame,\n", "    covariance: pd.Data<PERSON><PERSON>e,\n", "    df_risk: pd.DataFrame,\n", "):\n", "    \"\"\"Calculate optimal theoretical alpha weights for multiple dates in parallel.\n", "\n", "    Args:\n", "        df_alpha (pd.DataFrame): Alpha signals/scores with MultiIndex ((date, BarraID),)\n", "        exposure (pd.DataFrame): Factor exposure matrix with MultiIndex ((date, BarraID), factor)\n", "        covariance (pd.DataFrame): Factor covariance matrix with MultiIndex ((date, factor), factor)\n", "        df_risk (pd.DataFrame): Specific risk data with MultiIndex (date, BarraID)\n", "\n", "    Returns:\n", "        pd.DataFrame: Concatenated optimal weights for all dates with MultiIndex (date, BarraID)\n", "    \"\"\"\n", "    df_alpha = df_alpha.copy()\n", "    dates = df_alpha.index.get_level_values(\"date\").unique()\n", "    tasks = [(date, exposure, covariance, df_risk, df_alpha) for date in dates]\n", "    results = tools.parallel(\n", "        calc_theo_opt_alpha_1day, tasks, 5, \"threading\", True, \"theo_opt\"\n", "    )\n", "    results = {date: result for date, result in zip(dates, results)}\n", "    return pd.concat(results, names=[\"date\", \"BarraID\"])\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## portfolio_evaluator.py"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/portfolio/portfolio_evaluator.py'\n", "# coding = utf-8\n", "import os\n", "import numpy as np\n", "import pandas as pd\n", "from tqdm.auto import tqdm\n", "from ..utils import processor as prep\n", "\n", "\n", "def load_alpha(alpha_root: str, sDate: str, eDate: str) -> pd.DataFrame:\n", "    \"\"\"\n", "    Load and concatenate alpha data from parquet files in the specified directory.\n", "\n", "    Args:\n", "        alpha_root (str): Directory path containing alpha parquet files named as 'date.parquet'\n", "\n", "    Returns:\n", "        pandas.DataFrame: DataFrame with MultiIndex ['date', 'BarraID']\n", "\n", "    \"\"\"\n", "    dates = sorted([s.split(\".\")[0] for s in os.listdir(alpha_root)])\n", "    dates = [date for date in dates if date >= sDate and date <= eDate]\n", "    alphas = {}\n", "    for date in tqdm(dates, desc=\"loading_alpha\"):\n", "        alphas[date] = pd.read_parquet(f\"{alpha_root}{date}.parquet\")\n", "    alphas = pd.concat(alphas, names=[\"date\"])\n", "    if (\n", "        len(alphas.index.names) == 3\n", "    ):  ## (date, date, BarraID) index should remove the first date level\n", "        alphas = alphas.droplevel(0)\n", "        alphas.index.names = [\"date\", \"BarraID\"]\n", "    return alphas\n", "\n", "\n", "def prepare_xy(df_alpha, df_label):\n", "    \"\"\"\n", "    Prepare alpha and label data for backtesting by aligning indexes and preprocessing.\n", "\n", "    Args:\n", "        df_alpha (pd.DataFrame): Alpha data with MultiIndex ['date', 'BarraID']\n", "        df_label (pd.DataFrame): Label data with MultiIndex ['date', 'BarraID']\n", "\n", "    Returns:\n", "        tuple:\n", "            - xy (pd.DataFrame): Joined and preprocessed alpha and label data\n", "            - df_alpha (pd.DataFrame): Preprocessed alpha data\n", "            - df_label (pd.DataFrame): Preprocessed label data\n", "\n", "    \"\"\"\n", "    common_index = df_alpha.index.intersection(df_label.index)\n", "    df_alpha = df_alpha.loc[common_index]\n", "    df_label = df_label.loc[common_index]\n", "\n", "    df_alpha = df_alpha.groupby(\"date\").apply(lambda x: prep.as_weight(x)).droplevel(0)\n", "    df_label = (\n", "        df_label.groupby(level=\"date\")\n", "        .apply(lambda x: prep.winsorize(x, \"percentile\"))\n", "        .droplevel(0)\n", "    )\n", "\n", "    if len(df_alpha.columns.intersection(df_label.columns)) > 0:\n", "        assert \"alpha name overlap with label names\"\n", "    xy = df_alpha.join(df_label, how=\"left\").dropna()\n", "    return xy, df_alpha, df_label\n", "\n", "\n", "def calc_ret(xy: pd.DataFrame, num_alpha: int = 1):\n", "    \"\"\"\n", "    Calculate returns for each alpha-label combination.\n", "\n", "    Args:\n", "        xy (pd.DataFrame): DataFrame containing alpha and label data\n", "        num_alpha (int): Number of alpha columns in the DataFrame\n", "\n", "    Returns:\n", "        pd.DataFrame: Returns for each alpha-label combination, indexed by date\n", "\n", "    \"\"\"\n", "    alpha_names = xy.columns[:num_alpha]\n", "    label_names = xy.columns[num_alpha:]\n", "\n", "    rets = []\n", "    for label in label_names:\n", "        x, y = xy[alpha_names], xy[label]\n", "        ret = x.mul(y.to_numpy(), axis=0).sum(axis=0)\n", "        ret /= x.abs().sum(axis=0)\n", "        rets.append(ret)\n", "    return pd.concat(rets, axis=1, keys=label_names)\n", "\n", "\n", "def backtest(xy: pd.DataFrame, num_alpha: int = 1, corr_method: str = \"pearson\"):\n", "    \"\"\"\n", "    Perform backtesting analysis by calculating information coefficients (IC) and returns.\n", "\n", "    Args:\n", "        xy (pd.DataFrame): DataFrame containing alpha and label data, with date index\n", "        num_alpha (int): Number of alpha columns in the DataFrame\n", "\n", "    Returns:\n", "        tuple: A tuple containing:\n", "            - ret (pd.DataFrame): Returns for each alpha combination, indexed by date and alpha\n", "            - ic (pd.DataFrame): IC between alphas and labels, indexed by date and alpha\n", "\n", "    \"\"\"\n", "    ic = xy.groupby(\"date\").apply(\n", "        lambda x: x.corr(method=corr_method).iloc[:num_alpha, num_alpha:]\n", "    )\n", "    ic.index.names = [\"date\", \"alpha\"]\n", "    ic = ic.swaplevel(0, 1)\n", "    ret = xy.groupby(\"date\").apply(calc_ret)\n", "    ret.index.names = [\"date\", \"alpha\"]\n", "    ret = ret.swaplevel(0, 1)\n", "    return ret, ic\n", "\n", "\n", "def summary_turnover(df_alpha: pd.DataFrame):\n", "    \"\"\"calculate turnover for each date\n", "    Args:\n", "        df_alpha (pd.DataFrame): Alpha dataframe with dates and symbols as multi-index\n", "\n", "    Returns:\n", "        pd.Series: Turnover values for each date with alpha and date as index\n", "    \"\"\"\n", "    # Reshape to have dates as columns\n", "    weights = df_alpha.unstack(level=0)\n", "\n", "    # Shift columns by 1 to get yesterday's weights\n", "    weights_yesterday = weights.shift(1, axis=1).fillna(0.0)\n", "    turnovers = (weights - weights_yesterday).abs().sum()\n", "    turnovers.index.names = [\"alpha\", \"date\"]\n", "    return turnovers\n", "\n", "\n", "def calc_AvgDailyTvr(df_alpha: pd.DataFrame):\n", "    \"\"\"Calculate average daily turnover by year.\n", "\n", "    Args:\n", "        df_alpha (pd.DataFrame): Alpha dataframe with dates and symbols as multi-index\n", "\n", "    Returns:\n", "        pd.DataFrame: Average daily turnover for each year and overall, with years as index\n", "            and 'dayTvr' as column\n", "    \"\"\"\n", "    tvr = summary_turnover(df_alpha).droplevel(0)\n", "    tvr = tvr.to_frame(\"dayTvr\")\n", "    tvr[\"year\"] = pd.to_datetime(tvr.index).year\n", "    tvr = tvr.groupby(\"year\").mean()\n", "    tvr.loc[\"all\"] = tvr.mean(axis=0)\n", "    return tvr\n", "\n", "\n", "def calc_breath(df_alpha: pd.DataFrame):\n", "    \"\"\"calculate breath for each date, breath is the number of alpha for each date\n", "    Args:\n", "        df_alpha (pd.DataFrame): Alpha dataframe with dates and symbols as multi-index\n", "\n", "    Returns:\n", "        pd.DataFrame: Breath for each date and year\n", "    \"\"\"\n", "    breath = df_alpha.groupby(\"date\").count()\n", "    breath[\"year\"] = pd.to_datetime(breath.index).year\n", "    breath = breath.groupby(\"year\").mean().astype(int)\n", "    breath.loc[\"all\"] = df_alpha.groupby(\"date\").count().mean().astype(int)\n", "    return breath\n", "\n", "\n", "def calc_days(df_alpha: pd.DataFrame):\n", "    \"\"\"calculate days for each year\n", "    Args:\n", "        df_alpha (pd.DataFrame): Alpha dataframe with dates and symbols as multi-index\n", "\n", "    Returns:\n", "        pd.DataFrame: Days for each year\n", "    \"\"\"\n", "    df_alpha = df_alpha.copy()\n", "    df_alpha = df_alpha.groupby(\"date\").count()\n", "    df_alpha[\"year\"] = pd.to_datetime(df_alpha.index.get_level_values(\"date\")).year\n", "    days = df_alpha.groupby(\"year\").count()\n", "    days.loc[\"all\"] = df_alpha.count()\n", "    return days\n", "\n", "\n", "def summary_max_drawdown(ret: pd.DataFrame):\n", "    \"\"\"calculate max drawdown\n", "    Args:\n", "        ret (pd.DataFrame): Returns dataframe with dates as index and return horizons as columns\n", "\n", "    Returns:\n", "        pd.Series: Maximum drawdown values for 1-day and 5-day horizons\n", "    \"\"\"\n", "    ret = ret.loc[:, ret.columns.str.contains(\"_1$|_5$\")].copy()\n", "    cum_rets = (1 + ret).cumprod()\n", "    running_max = cum_rets.expanding().max()\n", "    drawdowns = cum_rets / running_max - 1\n", "    res = drawdowns.min()\n", "    res.index = [\"maxDD_H1\", \"maxDD_H5\"]\n", "    return res\n", "\n", "\n", "def summary_ann_and_sharpe(ret: pd.DataFrame):\n", "    \"\"\"calculate annualized return and sharpe ratio for H1 and H5\n", "    Args:\n", "        ret (pd.DataFrame): Returns dataframe with dates as index and return horizons as columns\n", "\n", "    Returns:\n", "        pd.Series: Annualized returns and Sharpe ratios for 1-day and 5-day horizons\n", "    \"\"\"\n", "    ret = ret.loc[:, ret.columns.str.contains(\"_1$|_5$\")].copy()\n", "    ann = ret.sum() * 252 / len(ret)\n", "    shp = ann.div(ret.std().to_numpy()) / np.sqrt(252)\n", "    res = pd.concat([ann, shp], axis=0)\n", "    res.index = [\"RetH1\", \"RetH5\", \"SharpeH1\", \"SharpeH5\"]\n", "    res = res.loc[[\"SharpeH1\", \"RetH1\", \"SharpeH5\", \"RetH5\"]]\n", "    res[\"SharpeH5\"] = res[\"SharpeH5\"] / np.sqrt(5)\n", "    return res\n", "\n", "\n", "def summary_ic(ic: pd.DataFrame):\n", "    \"\"\"calculate mean IC and ICIR for H1 and H5\n", "    Args:\n", "        ic (pd.DataFrame): Information coefficient dataframe with dates as index and horizons as columns\n", "\n", "    Returns:\n", "        pd.Series: Mean IC and ICIR values for 1-day and 5-day horizons\n", "    \"\"\"\n", "    ic = ic.loc[:, ic.columns.str.contains(\"_1$|_5$\")].copy()\n", "    res = pd.concat([ic.mean(), ic.std()])\n", "    res.index = [\"IC_H1\", \"IC_H5\", \"ICIR_H1\", \"ICIR_H5\"]\n", "    return res.loc[[\"IC_H1\", \"ICIR_H1\", \"IC_H5\", \"ICIR_H5\"]]\n", "\n", "\n", "def summary(ret: pd.DataFrame, ic: pd.DataFrame, df_alpha: pd.DataFrame):\n", "    \"\"\"summary returns, ic, max drawdown for each alpha and year\n", "    Args:\n", "        ret (pd.DataFrame): Returns dataframe with alpha and dates as multi-index\n", "        ic (pd.DataFrame): Information coefficient dataframe with alpha and dates as multi-index\n", "        df_alpha (pd.DataFrame): Alpha dataframe with dates and symbols as multi-index\n", "    Returns:\n", "        pd.DataFrame: Summary statistics including returns, Sharpe ratios, IC, ICIR and max drawdowns\n", "                     grouped by alpha and year\n", "    \"\"\"\n", "    alpha_names = ic.index.get_level_values(\"alpha\").unique().tolist()\n", "\n", "    results = {}\n", "    for alpha_name in alpha_names:\n", "        ret = ret.xs(alpha_name, level=\"alpha\").copy()\n", "        ic = ic.xs(alpha_name, level=\"alpha\").copy()\n", "\n", "        ## add year tag\n", "        ic.loc[:, \"year\"] = pd.to_datetime(ic.index).to_period(\"Y\")\n", "        ret.loc[:, \"year\"] = pd.to_datetime(ret.index).to_period(\"Y\")\n", "\n", "        ## calc ann and sharpe\n", "        res0 = summary_ann_and_sharpe(ret.drop(columns=[\"year\"])).to_frame(\"all\").T\n", "        res1 = ret.groupby(\"year\").apply(summary_ann_and_sharpe, include_groups=False)\n", "        ret_ = pd.concat([res1, res0], axis=0)\n", "\n", "        ## calc ic\n", "        ic0 = summary_ic(ic.drop(columns=[\"year\"])).to_frame(\"all\").T\n", "        ic1 = ic.groupby(\"year\").apply(summary_ic, include_groups=False)\n", "        ic_ = pd.concat([ic0, ic1], axis=0)\n", "\n", "        ## calc max drawdown\n", "        dd0 = summary_max_drawdown(ret.drop(columns=[\"year\"])).to_frame(\"all\").T\n", "        dd1 = ret.groupby(\"year\").apply(summary_max_drawdown, include_groups=False)\n", "        dd_ = pd.concat([dd1, dd0], axis=0)\n", "\n", "        results[alpha_name] = pd.concat([ret_, ic_, dd_], axis=1)\n", "\n", "    results = pd.concat(results, axis=0, names=[\"alpha\", \"year\"])\n", "\n", "    breath = calc_breath(df_alpha)\n", "    dtv = calc_AvgDailyTvr(df_alpha)\n", "    days = calc_days(df_alpha)\n", "    results[\"dayTvr\"] = dtv[\"dayTvr\"].values\n", "    results[\"breath\"] = breath.iloc[:, 0].values\n", "    results[\"days\"] = days.iloc[:, 0].values\n", "    return results\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from barra.portfolio import portfolio_evaluator as pe\n", "\n", "alpha_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/test/fitting/EUTR/EU1.SP/EU1.SP1.01/alpha/'\n", "sDate, eDate = '2018-01-01', '2024-06-30'\n", "df_alpha = pe.load_alpha(alpha_root, sDate, eDate)\n", "df_alpha.columns = ['alpha_test']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import barra.utils.processor as prep\n", "\n", "hn_alpha = pd.read_pickle(\"/mnt/sda/NAS/ShareFolder/huaneng/dirt/europe_a/T5_Netural_Standard.pkl\")\n", "hn_alpha.columns.name = 'symbol'\n", "\n", "hn_alpha.dropna(axis=1,how='all').shape\n", "\n", "def reset_barraid_as_symbol(df_alpha:pd.DataFrame):\n", "    alpha = df_alpha.stack('symbol').to_frame('alpha').reset_index('symbol')\n", "    alpha['BarraID'] = np.nan\n", "    dates = df_alpha.index.get_level_values('date').unique()\n", "    mapping_root = '/mnt/sda/NAS/Global/mapping/bmll_barra_map/'\n", "    for date in tqdm(dates,desc='load_mapping'):  \n", "        dt = pd.read_csv(f'{mapping_root}{date}.csv',usecols=['Ticker_MIC','Barrid'])\n", "        mapped = dict(zip(dt['Ticker_MIC'],dt['Barrid']))\n", "        alpha.loc[date,'BarraID'] = alpha.loc[date,'symbol'].map(mapped)\n", "    return alpha.loc[alpha['Barra<PERSON>'].notna(),['Barra<PERSON>','alpha']].set_index('BarraID',append=True)\n", "    \n", "\n", "hnalpha = reset_barraid_as_symbol(hn_alpha)\n", "        \n", "cfg = {'barra_product': 'EUTR', 'calendar': 'EU1', 'factor_group_name': 'EU1'}\n", "hnalpha = hnalpha.groupby('date').apply(lambda x: prep.add_univer_mask(x, cfg))\n", "hnalpha = hnalpha.loc[hnalpha['universe_mask']==1.0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from barra.data import loader\n", "return_map = {\n", "    'idio': ['SpecificReturn', 'SpecRisk%'],\n", "    'total': ['DlyReturn%', 'TotalRisk%'],\n", "}\n", "\n", "label_cfg = {\n", "    'label_usecols': return_map.get('idio'), \n", "    'label_options': [f'H1_{i}' for i in range(1,21)], \n", "    'barra_product': 'EUTR',\n", "    'calendar': 'EU1',\n", "    }\n", "\n", "sDate, eDate = df_alpha.index.get_level_values('date').unique()[[0,-1]].tolist()\n", "df_label, df_risk = loader.load_label(sDate, eDate, label_cfg)"]}, {"cell_type": "code", "execution_count": 130, "metadata": {}, "outputs": [], "source": ["from barra.utils import processor as prep\n", "import numpy as np\n", "\n", "\n", "xy, df_alpha, df_label = pe.prepare_xy(hnalpha, df_label)\n", "dtv = pe.summary_turnover(df_alpha)\n", "ret, ic = pe.backtest(xy, num_alpha=1, corr_method='spearman')\n", "df_summary = pe.summary(ret, ic)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## portfolio_reporter.py"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/portfolio/pdf_reporter.py'\n", "# coding = utf-8\n", "import os\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from matplotlib.backends.backend_pdf import PdfPages\n", "\n", "\n", "def plot_cum_ret(ret: pd.DataFrame, pdf: PdfPages):\n", "    \"\"\" plot cumulative return for each alpha\n", "    Args:\n", "        ret: DataFrame containing returns data with alpha and date multi-index\n", "        pdf: PdfPages object to save plots to\n", "\n", "    Returns:\n", "        None\n", "    \"\"\"\n", "    ret = ret.loc[:, ret.columns.str.contains(\"_1$|_5$|_10$\")].copy()\n", "    for alpha_name in ret.index.get_level_values(\"alpha\").unique():\n", "        ret_ = ret.xs(alpha_name, level=\"alpha\").copy()\n", "        cum_ret = ret_.add(1.0).cumprod().sub(1.0)\n", "\n", "        fig, ax = plt.subplots()\n", "        cum_ret.plot(grid=True, rot=30, figsize=(10, 6), alpha=0.8, ax=ax)\n", "        plt.title(f\"Portfolio Performance - {alpha_name}\")\n", "        plt.legend(loc=\"upper left\")\n", "        plt.tight_layout()\n", "        pdf.savefig(fig, bbox_inches=\"tight\")\n", "        plt.close()\n", "\n", "\n", "def plot_sharpe(ret: pd.DataFrame, pdf: PdfPages):\n", "    \"\"\" plot sharpe ratio for each alpha for each horizon\n", "    Args:\n", "        ret: DataFrame containing returns data with alpha and date multi-index\n", "        pdf: PdfPages object to save plots to\n", "\n", "    Returns:\n", "        None\n", "    \"\"\"\n", "    ret = ret.copy()\n", "    for alpha_name in ret.index.get_level_values(\"alpha\").unique():\n", "        ret_ = ret.xs(alpha_name, level=\"alpha\").copy()\n", "        ann = ret_.sum() * 252 / len(ret_)\n", "        shp = ann.div(ret_.std().to_numpy()) / np.sqrt(252)\n", "        scale = [np.sqrt(int(col.split('_')[-1])) for col in shp.index]\n", "        shp = shp.div(scale, axis=0)\n", "\n", "        fig, ax = plt.subplots()\n", "        shp.plot(kind=\"bar\", figsize=(10, 6), grid=True, alpha=0.9, ax=ax)\n", "        plt.title(f\"<PERSON> - {alpha_name}\")\n", "        plt.xticks(range(len(shp)), [str(s) for s in range(1, len(shp) + 1)])\n", "        plt.xlabel(\"Horizon\")\n", "        plt.ylabel(\"Sharpe\")\n", "        plt.tight_layout()\n", "        pdf.savefig(fig, bbox_inches=\"tight\")\n", "        plt.close()\n", "\n", "\n", "def plot_risk(ret: pd.DataFrame):\n", "    \"\"\" left to be implemented\n", "    Args:\n", "        ret: DataFrame containing returns data with alpha and date multi-index\n", "\n", "    Returns:\n", "        None\n", "    \"\"\"\n", "    pass\n", "\n", "\n", "def plot_turnover(dtv: pd.Series, pdf: PdfPages):\n", "    \"\"\" plot daily turnover for each alpha\n", "    Args:\n", "        dtv: Series containing turnover data with alpha and date multi-index\n", "        pdf: PdfPages object to save plots to\n", "\n", "    Returns:\n", "        None\n", "    \"\"\"\n", "    dtv = dtv.copy()\n", "    for alpha_name in dtv.index.get_level_values(\"alpha\").unique():\n", "        dtv_ = dtv.xs(alpha_name, level=\"alpha\").copy()\n", "\n", "        fig, ax = plt.subplots()\n", "        dtv_.plot(figsize=(10, 6), grid=True, alpha=0.9, ax=ax)\n", "        plt.title(f\"Daily Turnover - {alpha_name}\")\n", "        plt.tight_layout()\n", "        pdf.savefig(fig, bbox_inches=\"tight\")\n", "        plt.close()\n", "\n", "\n", "def plot_monthly_ret(ret: pd.DataFrame, pdf: PdfPages):\n", "    \"\"\" plot monthly return for each alpha for H1 and H5\n", "    Args:\n", "        ret: DataFrame containing returns data with alpha and date multi-index\n", "        pdf: PdfPages object to save plots to\n", "\n", "    Returns:\n", "        None\n", "    \"\"\"\n", "    ret = ret.loc[:, ret.columns.str.contains(\"_1$|_5$\")].copy()\n", "    for alpha_name in ret.index.get_level_values(\"alpha\").unique():\n", "        ret_ = ret.xs(alpha_name, level=\"alpha\").copy()\n", "        ret_[\"month\"] = pd.to_datetime(ret_.index.get_level_values(\"date\")).to_period(\n", "            \"M\"\n", "        )\n", "        mret = ret_.groupby([\"month\"]).apply(lambda x: x.add(1.0).prod().sub(1.0))\n", "        mret.index = mret.index.astype(str)\n", "        fig, ax = plt.subplots()\n", "        mret.plot(\n", "            marker=\"o\", linestyle=\"--\", figsize=(10, 6), grid=True, alpha=0.8, ax=ax\n", "        )\n", "        plt.title(f\"Monthly Return - {alpha_name}\")\n", "        plt.tight_layout()\n", "        plt.legend(loc=\"upper left\")\n", "        pdf.savefig(fig, bbox_inches=\"tight\")\n", "        plt.close()\n", "\n", "\n", "def report(ret: pd.DataFrame, dtv: pd.Series, report_folder: str = \".\", savetag: str=''):\n", "    \"\"\" generate report for backtest results and save to file\n", "    Args:\n", "        ret: DataFrame containing returns data with alpha and date multi-index\n", "        dtv: Series containing turnover data with alpha and date multi-index\n", "        report_folder: String path to folder where report should be saved\n", "        savetag: String tag to append to report filename\n", "\n", "    Returns:\n", "        None\n", "    \"\"\"\n", "    if not os.path.exists(report_folder):\n", "        os.makedirs(report_folder, exist_ok=True)\n", "    with PdfPages(f\"{report_folder}/portfolio_{savetag}.pdf\") as pdf:\n", "        plot_cum_ret(ret, pdf)\n", "        plot_sharpe(ret, pdf)\n", "        plot_turnover(dtv, pdf)\n", "        plot_monthly_ret(ret, pdf)\n", "    \n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from barra.data import loader\n", "from barra.portfolio import portfolio_reporter as pr\n", "\n", "pr.report(\n", "    ret,\n", "    dtv,\n", "    report_folder=\"/mnt/sda/NAS/ShareFolder/lishuanglin/test/fitting/EUTR/\",\n", "    savetag=\"hnalpha\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## html_reporter.py"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/portfolio/html_reporter.py'\n", "# coding = utf-8\n", "\n", "import json\n", "import jinja2\n", "import numpy as np\n", "import pandas as pd\n", "from datetime import datetime\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "\n", "__author__ = \"lishuanglin\"\n", "__version__ = \"0.1.1\"\n", "__all__ = ['report']\n", "\n", "\n", "def add_line(\n", "    fig, x, y, row, col, line_name, mode=\"lines\", dash=\"solid\", showlegend=True\n", "):\n", "    \"\"\"Add a line trace to a plotly figure\n", "    \n", "    Args:\n", "        fig: plotly.graph_objects.Figure, the figure to add the line to\n", "        x: array-like, x-axis values\n", "        y: array-like, y-axis values  \n", "        row: int, subplot row number\n", "        col: int, subplot column number\n", "        line_name: str, name of the line for legend\n", "        mode: str, plotting mode ('lines', 'markers', 'lines+markers')\n", "        dash: str, line dash style ('solid', 'dash', 'dot', etc)\n", "        showlegend: bool, whether to show this line in legend\n", "        \n", "    Returns:\n", "        None, modifies figure in place\n", "    \"\"\"\n", "    fig.add_trace(\n", "        <PERSON><PERSON>(\n", "            x=x,\n", "            y=y,\n", "            name=line_name,\n", "            mode=mode,\n", "            line=dict(dash=dash),\n", "            showlegend=showlegend,\n", "        ),\n", "        row=row,\n", "        col=col,\n", "    )\n", "\n", "\n", "def update_yaxes(fig, row, col, title):\n", "    \"\"\"Update y-axis properties of a plotly subplot\n", "    \n", "    Args:\n", "        fig: plotly.graph_objects.Figure, the figure to update\n", "        row: int, subplot row number\n", "        col: int, subplot column number\n", "        title: str, y-axis title\n", "        \n", "    Returns:\n", "        None, modifies figure in place\n", "    \"\"\"\n", "    fig.update_yaxes(\n", "        title_text=title,\n", "        row=row,\n", "        col=col,\n", "        gridcolor=\"lightgrey\",\n", "        range=[None, None],\n", "        autorange=True,\n", "        automargin=True,\n", "    )\n", "\n", "\n", "def create_performance_plots(\n", "    ret_df: pd.DataFrame, dtv_df: pd.DataFrame, alpha_name: str\n", ") -> str:\n", "    \"\"\"Create four performance plots for an alpha\n", "    Args:\n", "            ret_df: pd.DataFrame, the dataframe containing returns\n", "            dtv_df: pd.DataFrame, the dataframe containing turnovers\n", "            alpha_name: str, the name of the alpha\n", "\n", "    Returns:\n", "            str, the HTML string of the performance plots\n", "    \"\"\"\n", "    # Convert date column and set index\n", "    ret_df[\"date\"] = pd.to_datetime(ret_df[\"date\"])\n", "    dtv_df[\"date\"] = pd.to_datetime(dtv_df[\"date\"])\n", "\n", "    # Filter data for the specific alpha\n", "    alpha_ret = ret_df[ret_df[\"alpha\"] == alpha_name].copy()\n", "    alpha_dtv = dtv_df[dtv_df[\"alpha\"] == alpha_name].copy()\n", "\n", "    # Get all return columns (contains Ret_)\n", "    ret_cols = [col for col in alpha_ret.columns if \"Ret_\" in col]\n", "\n", "    # Convert data to numeric\n", "    for col in ret_cols:\n", "        alpha_ret[col] = pd.to_numeric(alpha_ret[col], errors=\"coerce\")\n", "    alpha_dtv[\"dtv\"] = pd.to_numeric(alpha_dtv[\"dtv\"], errors=\"coerce\")\n", "\n", "    # Create figure with subplots\n", "    fig = make_subplots(\n", "        rows=2,\n", "        cols=2,\n", "        subplot_titles=(\n", "            \"Portfolio Performance\",\n", "            \"Monthly Return\",\n", "            \"Sharpe Ratio\",\n", "            \"Daily Turnover\",\n", "        ),\n", "        vertical_spacing=0.1,\n", "        horizontal_spacing=0.08,\n", "    )\n", "\n", "    # Update figure size and layout\n", "    fig.update_layout(\n", "        width=1200,\n", "        height=800,\n", "        showlegend=True,\n", "        margin=dict(t=40, b=40, l=40, r=40),\n", "        template=\"plotly_white\",\n", "        plot_bgcolor=\"white\",\n", "        modebar=dict(orientation=\"h\", bgcolor=\"rgba(255, 255, 255, 0.8)\"),\n", "        legend=dict(\n", "            orientation=\"h\",\n", "            yanchor=\"bottom\",\n", "            y=1.04,\n", "            xanchor=\"left\",\n", "            x=0.0,\n", "            bgcolor=\"rgba(255, 255, 255, 0.8)\",\n", "            traceorder=\"normal\",\n", "            itemsizing=\"constant\",\n", "            itemwidth=30,\n", "            itemclick=\"toggle\",\n", "            itemdoubleclick=\"toggleothers\",\n", "        ),\n", "    )\n", "\n", "    # 1. Portfolio Performance (Cumulative Return, only plots H1, H5 and H10)\n", "    h1_h5_cols = [col for col in ret_cols if col.endswith((\"_1\", \"_5\", \"_10\"))]\n", "    for col in h1_h5_cols:\n", "        cum_ret = alpha_ret[col].cumsum()\n", "        add_line(fig, alpha_ret[\"date\"], cum_ret, 1, 1, col, dash=\"solid\")\n", "    fig.update_xaxes(title_text=\"Date\", row=1, col=1, gridcolor=\"lightgrey\")\n", "    update_yaxes(fig, row=1, col=1, title=\"Cumulative Return\")\n", "\n", "    # 2. Monthly Return\n", "    # Set date as index for resampling\n", "    alpha_ret.set_index(\"date\", inplace=True)\n", "\n", "    # Calculate monthly returns for H1 and H5\n", "    monthly_rets = {}\n", "    to_plot_cols = [s for s in ret_cols if s.endswith((\"_1\", \"_5\"))]\n", "    for col in to_plot_cols:\n", "        if col in ret_cols:\n", "            # Calculate monthly return\n", "            monthly_data = pd.DataFrame(index=alpha_ret.index)\n", "            monthly_data[col] = alpha_ret[col]\n", "            monthly_ret = monthly_data.resample(\"ME\")[col].sum()\n", "            monthly_rets[col] = monthly_ret.dropna()\n", "\n", "    # Plot monthly returns\n", "    for col, monthly_ret in monthly_rets.items():\n", "        x, y = monthly_ret.index, monthly_ret\n", "        add_line(fig, x, y, 1, 2, f\"{col} Monthly\", mode=\"lines+markers\", dash=\"dash\")\n", "    fig.update_xaxes(title_text=\"Horizon\", row=1, col=2, gridcolor=\"lightgrey\")\n", "    update_yaxes(fig, row=1, col=2, title=\"Monthly Return\")\n", "\n", "    # 3. <PERSON>\n", "    ret_data = alpha_ret[ret_cols].apply(pd.to_numeric, errors=\"coerce\")\n", "    ann_ret = ret_data.mean() * 252\n", "    sharpe = ann_ret / (ret_data.std() * np.sqrt(252))\n", "    scale = [np.sqrt(int(col.split(\"_\")[-1])) for col in ret_cols]\n", "    sharpe = sharpe / scale\n", "\n", "    x_labels = [str(i) for i in range(1, len(ret_cols) + 1)]\n", "    fig.add_trace(go.Bar(x=x_labels, y=sharpe, showlegend=False), row=2, col=1)\n", "    fig.update_xaxes(title_text=\"Date\", row=2, col=1, gridcolor=\"lightgrey\")\n", "    update_yaxes(fig, row=2, col=1, title=\"Sharpe Ratio\")\n", "\n", "    # 4. Daily Turnover\n", "    date, dtv = alpha_dtv[\"date\"], alpha_dtv[\"dtv\"]\n", "    add_line(fig, date, dtv, row=2, col=2, line_name=\"Turnover\", showlegend=False)\n", "    fig.update_xaxes(title_text=\"Date\", row=2, col=2, gridcolor=\"lightgrey\")\n", "    update_yaxes(fig, row=2, col=2, title=\"Turnover\")\n", "\n", "    fig.update_xaxes(showgrid=True)\n", "    fig.update_yaxes(showgrid=True)\n", "    return fig.to_html(full_html=False, include_plotlyjs=\"cdn\")\n", "\n", "\n", "def format_percentage(value: float, decimals: int = 2) -> str:\n", "    \"\"\"Format value as percentage with specified decimal places\n", "    Args:\n", "            value: float, the value to be formatted\n", "            decimals: int, the number of decimal places\n", "\n", "    Returns:\n", "            str, the formatted string of the percentage value\n", "    \"\"\"\n", "    return f\"{value * 100:.{decimals}f} %\"\n", "\n", "\n", "def format_float(value: float, decimals: int = 3) -> str:\n", "    \"\"\"Format float value with 3 decimal places\n", "    Args:\n", "            value: float, the value to be formatted\n", "            decimals: int, the number of decimal places\n", "\n", "    Returns:\n", "            str, the formatted string of the float value\n", "    \"\"\"\n", "    return f\"{value:.{decimals}f}\"\n", "\n", "\n", "def config_to_table(config: dict) -> str:\n", "    \"\"\"Convert config dictionary to HTML table format\n", "    Args:\n", "            config: dict, the config dictionary for portfolio construction\n", "\n", "    Returns:\n", "            str, the HTML string of the config table\n", "    \"\"\"\n", "    keys = list(config.keys())[:-2]\n", "    rows = [keys[i : i + 6] for i in range(0, len(keys), 6)]\n", "\n", "    table_html = '<table class=\"config-table\">'\n", "    for row in rows:\n", "        table_html += \"<tr>\"\n", "        for key in row:\n", "            value = config.get(key, \"\")\n", "            table_html += f\"<td><strong>{key}</strong><br>{value}</td>\"\n", "        table_html += \"</tr>\"\n", "    table_html += \"</table>\"\n", "    return table_html\n", "\n", "\n", "def style_table(df: pd.DataFrame, h1_columns: list) -> str:\n", "    \"\"\"Apply styles to the table\n", "    Args:\n", "            df: 2-dimensional pd.DataFrame, the dataframe to be styled\n", "            h1_columns: list, the columns to be bolded\n", "\n", "    Returns:\n", "            str, the HTML string of the styled table\n", "    \"\"\"\n", "    # Create a copy of the DataFrame\n", "    styled_df = df.copy()\n", "\n", "    # Create the HTML table header with dataframe columns\n", "    html = '<table border=\"1\" class=\"dataframe\">\\n<thead>\\n<tr style=\"text-align: right;\">\\n'\n", "    for col in styled_df.columns:\n", "        html += f\"<th>{col}</th>\"\n", "    html += \"</tr>\\n</thead>\\n<tbody>\\n\"\n", "\n", "    # Add rows\n", "    for idx, row in styled_df.iterrows():\n", "        is_all_row = row[\"year\"] == \"all\"\n", "        html += \"<tr\" + (' class=\"all-row\"' if is_all_row else \"\") + \">\"\n", "        for col in styled_df.columns:\n", "            # Add bold style for H1 columns or all row\n", "            is_h1_col = col in h1_columns\n", "            cell_style = (\n", "                ' style=\"font-weight: bold;\"' if (is_h1_col or is_all_row) else \"\"\n", "            )\n", "            html += f\"<td{cell_style}>{row[col]}</td>\"\n", "        html += \"</tr>\\n\"\n", "\n", "    html += \"</tbody>\\n</table>\"\n", "    return html\n", "\n", "\n", "def generate_html_report(\n", "    config: dict,\n", "    df_summary_raw: pd.DataFrame,\n", "    df_summary_theo: pd.DataFrame,\n", "    df_ret_raw: pd.DataFrame,\n", "    df_dtv_raw: pd.DataFrame,\n", "    df_ret_theo: pd.DataFrame,\n", "    df_dtv_theo: pd.DataFrame,\n", "    output_file: str,\n", "):\n", "    \"\"\"Generate HTML report\n", "    Args:\n", "            config: dict, the config dictionary for portfolio construction\n", "            df_summary_raw: pd.DataFrame, summary of performance of raw alpha\n", "            df_summary_theo: pd.DataFrame, summary of performance of theoretical optimization\n", "            df_ret_raw: pd.DataFrame, daily return of raw alpha\n", "            df_dtv_raw: pd.DataFrame, daily turnover of raw alpha\n", "            df_ret_theo: pd.DataFrame, daily return of theoretical optimization\n", "            df_dtv_theo: pd.DataFrame, daily turnover of theoretical optimization\n", "            output_file: str, path to save the html file, e.g. './gitrepo/report_test.html'\n", "\n", "    Returns:\n", "            None\n", "    \"\"\"\n", "    # Create HTML template\n", "    template = \"\"\"\n", "    <!DOCTYPE html>\n", "    <html>\n", "    <head>\n", "        <meta charset=\"UTF-8\">\n", "        <title>Portfolio Performance Report</title>\n", "        <style>\n", "            body { \n", "                font-family: <PERSON><PERSON>, sans-serif; \n", "                margin: 0;\n", "                display: flex;\n", "            }\n", "            .sidebar {\n", "                width: 150px;\n", "                background-color: #f8f9fa;\n", "                padding: 10px 20px;\n", "                height: 100vh;\n", "                position: fixed;\n", "                overflow-y: auto;\n", "                transition: transform 0.3s ease;\n", "            }\n", "            .sidebar.collapsed {\n", "                transform: translateX(-130px);\n", "            }\n", "            .sidebar-toggle {\n", "                position: absolute;\n", "                right: 10px;\n", "                top: 10px;\n", "                cursor: pointer;\n", "                font-size: 20px;\n", "                z-index: 1000;\n", "            }\n", "            .content {\n", "                margin-left: 160px;\n", "                padding: 10px 40px;\n", "                width: calc(100% - 280px);\n", "                transition: margin-left 0.3s ease;\n", "            }\n", "            .content.expanded {\n", "                margin-left: 70px;\n", "                width: calc(100% - 150px);\n", "            }\n", "            h1, h2, h3 { color: #333; }\n", "            table { \n", "                border-collapse: collapse; \n", "                width: 100%; \n", "                margin: 20px 0; \n", "            }\n", "            .config-table {\n", "                width: 100%;\n", "                margin: 20px 0;\n", "            }\n", "            .config-table td {\n", "                padding: 10px;\n", "                border: 1px solid #ddd;\n", "                width: 20%;\n", "            }\n", "            th, td { \n", "                border: 1px solid #ddd; \n", "                padding: 8px; \n", "                text-align: left; \n", "            }\n", "            th { background-color: #f2f2f2; }\n", "            tr:nth-child(even) { background-color: #f9f9f9; }\n", "            .nav-item {\n", "                margin: 5px 0;\n", "                cursor: pointer;\n", "                padding-left: 10px;\n", "            }\n", "            .nav-item:hover {\n", "                color: #007bff;\n", "            }\n", "            .nav-group {\n", "                margin: 15px 0;\n", "            }\n", "            .nav-subitem {\n", "                margin: 5px 0;\n", "                padding-left: 20px;\n", "                cursor: pointer;\n", "                font-size: 0.9em;\n", "            }\n", "            .nav-subitem:hover {\n", "                color: #007bff;\n", "            }\n", "            .section {\n", "                margin-bottom: 40px;\n", "            }\n", "            .h1-column {\n", "                font-weight: bold;\n", "            }\n", "            .all-row {\n", "                font-weight: bold;\n", "            }\n", "            .plot-container {\n", "                margin: 20px 0;\n", "                padding: 10px;\n", "                background-color: white;\n", "                border-radius: 5px;\n", "                box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n", "            }\n", "        </style>\n", "        <script>\n", "            function toggleSidebar() {\n", "                const sidebar = document.querySelector('.sidebar');\n", "                const content = document.querySelector('.content');\n", "                sidebar.classList.toggle('collapsed');\n", "                content.classList.toggle('expanded');\n", "            }\n", "            \n", "            function scrollToSection(sectionId) {\n", "                document.getElementById(sectionId).scrollIntoView({\n", "                    behavior: 'smooth'\n", "                });\n", "            }\n", "            \n", "            window.onload = function() {\n", "                if (document.querySelector('.section')) {\n", "                    document.querySelector('.section').scrollIntoView();\n", "                }\n", "            }\n", "        </script>\n", "    </head>\n", "    <body>\n", "        <div class=\"sidebar\">\n", "            <div class=\"sidebar-toggle\" onclick=\"toggleSidebar()\">☰</div>\n", "            <h2>Navigation</h2>\n", "            {% for alpha in alpha_names %}\n", "            <div class=\"nav-group\">\n", "                <div class=\"nav-item\"><strong>{{ alpha }}</strong></div>\n", "                <div class=\"nav-subitem\" onclick=\"scrollToSection('{{ alpha }}_config')\">Config</div>\n", "                <div class=\"nav-subitem\" onclick=\"scrollToSection('{{ alpha }}_raw')\">Raw Alpha</div>\n", "                <div class=\"nav-subitem\" onclick=\"scrollToSection('{{ alpha }}_raw_plots')\">Raw Plots</div>\n", "                <div class=\"nav-subitem\" onclick=\"scrollToSection('{{ alpha }}_theo')\"><PERSON>t</div>\n", "                <div class=\"nav-subitem\" onclick=\"scrollToSection('{{ alpha }}_theo_plots')\"><PERSON> Plots</div>\n", "            </div>\n", "            {% endfor %}\n", "        </div>\n", "        \n", "        <div class=\"content\">\n", "            <h1>Portfolio Performance Report</h1>\n", "            <p>Generated at: {{ timestamp }}</p>\n", "            \n", "            {% for alpha in alpha_names %}\n", "            <div class=\"section\">\n", "                <h2>{{ alpha }}</h2>\n", "                \n", "                <div id=\"{{ alpha }}_config\">\n", "                    <h3>Config</h3>\n", "                    {{ config_tables[alpha] }}\n", "                </div>\n", "                \n", "                <div id=\"{{ alpha }}_raw\">\n", "                    <h3>Raw Alpha Performance</h3>\n", "                    {{ raw_alpha_tables[alpha] }}\n", "                </div>\n", "                \n", "                <div id=\"{{ alpha }}_raw_plots\">\n", "                    <h3>Raw Alpha Plots</h3>\n", "                    <div class=\"plot-container\">\n", "                        {{ raw_plots[alpha] }}\n", "                    </div>\n", "                </div>\n", "                \n", "                <div id=\"{{ alpha }}_theo\">\n", "                    <h3>Theoretical Optimization Performance</h3>\n", "                    {{ theo_opt_tables[alpha] }}\n", "                </div>\n", "                \n", "                <div id=\"{{ alpha }}_theo_plots\">\n", "                    <h3>Theoretical Optimization Plots</h3>\n", "                    <div class=\"plot-container\">\n", "                        {{ theo_plots[alpha] }}\n", "                    </div>\n", "                </div>\n", "            </div>\n", "            {% endfor %}\n", "        </div>\n", "    </body>\n", "    </html>\n", "    \"\"\"\n", "\n", "    # Prepare data\n", "    if \"alpha\" in df_summary_raw.index.names:\n", "        alpha_names = sorted(df_summary_raw.index.get_level_values(\"alpha\").unique())\n", "    else:\n", "        alpha_names = sorted(df_summary_raw[\"alpha\"].unique())\n", "\n", "    raw_alpha_tables = {}\n", "    theo_opt_tables = {}\n", "    config_tables = {}\n", "    raw_plots = {}\n", "    theo_plots = {}\n", "\n", "    # Define H1 columns\n", "    h1_columns = [\"SharpeH1\", \"RetH1\", \"IC_H1\", \"ICIR_H1\", \"maxDD_H1\"]\n", "\n", "    # Load configs and format tables for each alpha\n", "    for alpha in alpha_names:\n", "        # Load config and convert to table format\n", "        # config = load_config(alpha.lower())\n", "        config_tables[alpha] = config_to_table(config)\n", "\n", "        # Create performance plots\n", "        raw_plots[alpha] = create_performance_plots(df_ret_raw, df_dtv_raw, alpha)\n", "        theo_plots[alpha] = create_performance_plots(\n", "            df_ret_theo, df_dtv_theo, alpha\n", "        )  # Use ret2 and dtv2 for theo plots\n", "\n", "        # Filter and format table data\n", "        raw_alpha_data = (\n", "            df_summary_raw[df_summary_raw[\"alpha\"] == alpha]\n", "            .copy()\n", "            .drop(columns=[\"alpha\"])\n", "        )\n", "        theo_opt_data = (\n", "            df_summary_theo[df_summary_theo[\"alpha\"] == alpha]\n", "            .copy()\n", "            .drop(columns=[\"alpha\"])\n", "        )\n", "\n", "        # Format percentage columns with 2 decimal places\n", "        percentage_columns = [\"RetH1\", \"RetH5\", \"maxDD_H1\", \"maxDD_H5\"]\n", "        for col in percentage_columns:\n", "            raw_alpha_data[col] = raw_alpha_data[col].apply(\n", "                lambda x: format_percentage(x, 2)\n", "            )\n", "            theo_opt_data[col] = theo_opt_data[col].apply(\n", "                lambda x: format_percentage(x, 2)\n", "            )\n", "\n", "        # Format float columns\n", "        float_columns = [\n", "            \"SharpeH1\",\n", "            \"SharpeH5\",\n", "            \"IC_H1\",\n", "            \"ICIR_H1\",\n", "            \"IC_H5\",\n", "            \"ICIR_H5\",\n", "            \"dayTvr\",\n", "        ]\n", "        for col in float_columns:\n", "            raw_alpha_data[col] = raw_alpha_data[col].apply(format_float)\n", "            theo_opt_data[col] = theo_opt_data[col].apply(format_float)\n", "\n", "        # Generate HTML tables with custom styles\n", "        raw_alpha_tables[alpha] = style_table(raw_alpha_data, h1_columns)\n", "        theo_opt_tables[alpha] = style_table(theo_opt_data, h1_columns)\n", "\n", "    # Render template\n", "    template = jinja2.Template(template)\n", "    html_content = template.render(\n", "        timestamp=datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "        alpha_names=alpha_names,\n", "        raw_alpha_tables=raw_alpha_tables,\n", "        theo_opt_tables=theo_opt_tables,\n", "        config_tables=config_tables,\n", "        raw_plots=raw_plots,\n", "        theo_plots=theo_plots,\n", "    )\n", "\n", "    # Save HTML file\n", "    with open(output_file, \"w\", encoding=\"utf-8\") as f:\n", "        f.write(html_content)\n", "\n", "\n", "def report(\n", "    config: dict,\n", "    df_summary_raw: pd.DataFrame,\n", "    df_summary_theo: pd.DataFrame,\n", "    df_ret_raw: pd.DataFrame,\n", "    df_dtv_raw: pd.DataFrame,\n", "    df_ret_theo: pd.DataFrame,\n", "    df_dtv_theo: pd.DataFrame,\n", "    report_file: str,\n", "):\n", "    \"\"\"Generate HTML report for portfolio performance\n", "    Args:\n", "            config: dict, the config dictionary for portfolio construction\n", "            df_summary_raw: pd.DataFrame, summary of performance of raw alpha\n", "            df_summary_theo: pd.DataFrame, summary of performance of theoretical optimization\n", "            df_ret_raw: pd.DataFrame, daily return of raw alpha\n", "            df_dtv_raw: pd.DataFrame, daily turnover of raw alpha\n", "            df_ret_theo: pd.DataFrame, daily return of theoretical optimization\n", "            df_dtv_theo: pd.DataFrame, daily turnover of theoretical optimization\n", "            report_file: str, path to save the html file, e.g. './gitrepo/report_test.html'\n", "\n", "    Returns:\n", "            None\n", "    \"\"\"\n", "    # Generate report\n", "    generate_html_report(\n", "        config,\n", "        df_summary_raw,\n", "        df_summary_theo,\n", "        df_ret_raw,\n", "        df_dtv_raw,\n", "        df_ret_theo,\n", "        df_dtv_theo,\n", "        report_file,\n", "    )\n", "    print(f\"Report generated at: {report_file}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## port.demo.py"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c6bd4a90197e4204bc5fec751fd1b4ba", "version_major": 2, "version_minor": 0}, "text/plain": ["loading_alpha:   0%|          | 0/1695 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[2025-05-30 09:05:52] adding rootid mask donene\r"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ec9fff5dd80f4d69873afb8b046e29b2", "version_major": 2, "version_minor": 0}, "text/plain": ["loading_label:   0%|          | 0/1744 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7d55815346c541c2a09f4e14afa2465f", "version_major": 2, "version_minor": 0}, "text/plain": ["expanding_label:   0%|          | 0/20 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4d56749154ac478cbd6d5b55dbc16a56", "version_major": 2, "version_minor": 0}, "text/plain": ["loading_factor_exposure:   0%|          | 0/1695 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "62f16e47853b4f51892dba934c7bac96", "version_major": 2, "version_minor": 0}, "text/plain": ["loading_factor_covariance:   0%|          | 0/1695 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7796fe99bc2941afa127e029dbe36c37", "version_major": 2, "version_minor": 0}, "text/plain": ["theo_opt:   0%|          | 0/1695 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Report generated at: ./gitrepo/barra_demo/report/report.spraw2_tw4.html\n"]}], "source": ["# %%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/port.demo.py'\n", "# coding = utf-8\n", "\n", "import os\n", "import time\n", "import numpy as np\n", "import pandas as pd\n", "from tqdm.auto import tqdm\n", "\n", "from barra.data import loader\n", "from barra.utils import tools\n", "from barra.utils import processor as prep\n", "from barra.portfolio import theo_opt\n", "from barra.portfolio import portfolio_evaluator as pe\n", "\n", "# from barra.portfolio import portfolio_reporter as pr\n", "from barra.portfolio import html_reporter\n", "\n", "return_type_map = {\"idio\": \"SpecificReturn\", \"total\": \"DlyReturn%\"}\n", "risk_type_map = {\"idio\": \"SpecRisk%\", \"total\": \"TotalRisk%\"}\n", "\n", "\n", "prefix = \"./\"\n", "prefix = \"/mnt/sda/home/<USER>/working/gitrepo/barra_demo/\"\n", "user_path = f\"{prefix}configs/user.path.json\"\n", "user_root = tools.parse_user_root(user_path)\n", "\n", "if tools.is_notebook():\n", "    # cfg = loader.load_config(f\"{prefix}configs/portfolio/puda.ftiming.json\")\n", "    cfg = loader.load_config(f\"{prefix}configs/portfolio/spraw2_tw4.json\")\n", "    timetag = time.strftime(\"%Y%m%d_%H%M%S\")\n", "else:\n", "    args = tools.parse_dynamic_args()\n", "    assert args.config is not None, \"portfolio config file is required!\"\n", "    cfg = loader.load_config(args.config)  ## load portfolio config\n", "    timetag = (\n", "        args.timetag if hasattr(args, \"timetag\") else time.strftime(\"%Y%m%d_%H%M%S\")\n", "    )\n", "\n", "\n", "## loading alpha  / factor data\n", "barra_product = cfg.get(\"barra_product\")\n", "alpha_group_name = cfg.get(\"alpha_group_name\")\n", "alpha_name = cfg.get(\"alpha_name\")\n", "alpha_root = (\n", "    f\"{user_root}/fitting/{barra_product}/{alpha_group_name}/{alpha_name}/alpha/\"\n", ")\n", "sDate, eDate = cfg.get(\"sDate\"), cfg.get(\"eDate\")\n", "df_alpha = pe.load_alpha(alpha_root, sDate, eDate)\n", "df_alpha.columns = [cfg.get(\"alpha_name\")]\n", "\n", "## check alpha universe and rootid\n", "df_alpha = prep.check_universe_mask(df_alpha, cfg).fillna(0.0)\n", "df_alpha = df_alpha.loc[df_alpha[\"universe_mask\"] == 1.0].drop(\n", "    columns=[\"universe_mask\"]\n", ")\n", "df_alpha = prep.check_rootid_mask(df_alpha, cfg)\n", "df_alpha = df_alpha.loc[df_alpha[\"rootid_mask\"] == 1.0].drop(columns=[\"rootid_mask\"])\n", "barraids = df_alpha.index.get_level_values(\"BarraID\").unique().tolist()\n", "\n", "## loading label / risk data\n", "cfg[\"label_usecols\"] = [\n", "    return_type_map[cfg.get(\"return_type\")],\n", "    risk_type_map[cfg.get(\"risk_type\")],\n", "]\n", "cfg[\"label_options\"] = [f\"H1_{i}\" for i in range(1, cfg.get(\"label_horizon\") + 1)]\n", "df_label, df_risk = loader.load_label(sDate, eDate, cfg, barraids)\n", "\n", "\n", "## recap the label and risk\n", "def yprep(df: pd.DataFrame):\n", "    df = prep.winsorize(df, \"percentile\", percentile=0.01)\n", "    return df\n", "\n", "\n", "df_label = df_label.groupby(\"date\").apply(yprep).droplevel(0).fillna(0.0)\n", "df_risk = df_risk.groupby(\"date\").apply(yprep).droplevel(0).fillna(0.0) * 100.0\n", "df_risk = df_risk.clip(5.0, np.inf)\n", "\n", "# ## backtest the raw alpha\n", "xy1, df_alpha1, df_label1 = pe.prepare_xy(df_alpha, df_label)\n", "dtv1 = pe.summary_turnover(df_alpha1)\n", "ret1, ic1 = pe.backtest(xy1, num_alpha=1)\n", "df_summary1 = pe.summary(ret1, ic1, df_alpha1)\n", "\n", "# loading exposure / covariance data\n", "exposure = loader.load_asset_exposure(sDate, eDate, cfg, barraids)\n", "covariance = loader.load_factor_covariance(sDate, eDate, cfg)\n", "opt_score = theo_opt.calc_theo_opt_alpha(df_alpha1, exposure, covariance, df_risk)\n", "\n", "## backtest the optimized alpha\n", "xy2, df_alpha2, df_label2 = pe.prepare_xy(opt_score, df_label)\n", "dtv2 = pe.summary_turnover(df_alpha2)\n", "ret2, ic2 = pe.backtest(xy2, num_alpha=1)\n", "df_summary2 = pe.summary(ret2, ic2, df_alpha2)\n", "\n", "df_summary1_ = df_summary1.reset_index()\n", "df_summary2_ = df_summary2.reset_index()\n", "df_ret1_ = ret1.reset_index()\n", "df_ret2_ = ret2.reset_index()\n", "df_dtv1_ = dtv1.to_frame(name='dtv').reset_index().iloc[1:]\n", "df_dtv2_ = dtv2.to_frame(name='dtv').reset_index().iloc[1:]\n", "\n", "report_file = './gitrepo/barra_demo/report/report.spraw2_tw4.html'\n", "html_reporter.report(\n", "    cfg,\n", "    df_summary_raw=df_summary1_,\n", "    df_summary_theo=df_summary2_,\n", "    df_ret_raw=df_ret1_,\n", "    df_dtv_raw=df_dtv1_,\n", "    df_ret_theo=df_ret2_,\n", "    df_dtv_theo=df_dtv2_,\n", "    report_file=report_file,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "pd.read_parquet('/mnt/sda/NAS/ShareFolder/lishuanglin/test/fitting/EUTR/EU1.SP/EU1.SP1.01/alpha/2024-01-02.parquet')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "from tqdm.auto import tqdm\n", "\n", "barra_product = 'GEM3'\n", "factor_group_name = 'euronext'\n", "alpha_group_name = 'euronext'\n", "alpha_name = 'factor_timing6'\n", "\n", "puda_root = f'/mnt/sda/NAS/ShareFolder/pengpuda/daily_update_factor/euronext/{alpha_name}/EU1/raw/'\n", "user_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/test/'\n", "alpha_root = f'{user_root}/fitting/{barra_product}/{factor_group_name}/{alpha_name}/alpha/'\n", "\n", "os.makedirs(alpha_root, exist_ok=True)\n", "dates = sorted([s.split('.')[0] for s in os.listdir(puda_root) if s.endswith('.parquet')])\n", "\n", "for date in tqdm(dates):\n", "    alpha = pd.read_parquet(f'{puda_root}{date}.parquet')\n", "    alpha.index.name = 'BarraID'\n", "    next_day = calendar.next_trading_days('EU1', date, 1)[0]\n", "    alpha.to_parquet(f'{alpha_root}{next_day}.parquet')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# info data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### history"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "from joblib import Parallel, delayed\n", "from tqdm.auto import tqdm\n", "\n", "\n", "class InfoDataProcessor:\n", "    def __init__(self, info_root, barra_product):\n", "        self.info_root = info_root\n", "        self.barra_product = barra_product\n", "\n", "        if self.barra_product == 'CNTR':\n", "            self.PRODUCT = 'SMD'\n", "        else:\n", "            self.PRODUCT = 'GMD'\n", "\n", "        if self.barra_product in {'EUTR','CNTR'}:\n", "            self.product = f'{self.barra_product}D'\n", "        else:\n", "            self.product = f'{self.barra_product}S'\n", "        \n", "        if self.barra_product in ['EUE4']:\n", "            self.product = 'EUE4BAS'\n", "        \n", "    def load_DlyRet(self, date):\n", "        date_ = date.replace('-','')\n", "        year = date.split('-')[0]\n", "        if self.barra_product in ['EUE4']: product = 'EUE4BAS'\n", "        else: product = self.barra_product\n", "        group = f'{self.PRODUCT}_{product}_100_D_{year}'\n", "        folders = sorted([s for s in os.listdir(f'{self.info_root}') if s.startswith(group)])\n", "        if len(folders) == 0:\n", "            raise ValueError(f'No data for {date} in {self.info_root}')\n", "\n", "        for folder in folders:\n", "            filename = f'{product}_100_Asset_DlySpecRet_{date_}.csv'\n", "            filepath = f'{self.info_root}{folder}/{filename}'\n", "            if os.path.exists(filepath):\n", "                df = pd.read_csv(filepath).drop(columns=['DataDate'])\n", "                return df.rename(columns={'Barrid': 'BarraID'})\n", "\n", "    def load_Exposure(self, date, load_etf_exposure=False):\n", "        date_ = date.replace('-','')\n", "        year = date.split('-')[0]\n", "        if self.barra_product in ['EUE4']: product = 'EUE4BASS'\n", "        else: product = self.product\n", "        group = f'{self.PRODUCT}_{product}_100_D_{year}'\n", "        folders = sorted([s for s in os.listdir(f'{self.info_root}') if s.startswith(group)])\n", "\n", "        if len(folders) == 0:\n", "            raise ValueError(f'No data for {date} in {self.info_root}{folders[0]}')\n", "\n", "        for folder in folders:\n", "            filename = f'{product}_100_Asset_Exposure_{date_}.csv'\n", "            filepath = f'{self.info_root}{folder}/{filename}'\n", "            if os.path.exists(filepath):\n", "                df = pd.read_csv(filepath).drop(columns=['DataDate'])\n", "                df = df.rename(columns={'Barrid': 'BarraID'})\n", "\n", "                if load_etf_exposure:\n", "                    filename = f'{product}_ETF_100_Asset_Exposure_{date_}.csv'\n", "                    filepath = f'{self.info_root}{folder}/{filename}'\n", "                    if os.path.exists(filepath):\n", "                        df_etf = pd.read_csv(filepath).drop(columns=['DataDate'])\n", "                        df_etf = df_etf.rename(columns={'Barrid': 'BarraID'})\n", "                        df = pd.concat([df, df_etf], axis=0)\n", "                \n", "                return df\n", "            \n", "            \n", "\n", "    def load_cov_mat(self, date: str):\n", "        date_ = date.replace('-','')\n", "        year = date.split('-')[0]\n", "        if self.barra_product in ['EUE4']: product = 'EUE4BASS'\n", "        elif self.barra_product in ['CNTR', 'EUTR']: product = f'{self.barra_product}D'\n", "        else: product = f'{self.barra_product}S'\n", "        group = f'{self.PRODUCT}_{product}_100_D_{year}'\n", "        folders = sorted([s for s in os.listdir(f'{self.info_root}') if s.startswith(group)])\n", "        if len(folders) == 0:\n", "            raise ValueError(f'No data for {date} in {self.info_root}')\n", "        for folder in folders:\n", "            filename = f'{product}_100_Covariance_{date_}.csv'\n", "            filepath = f'{self.info_root}{folder}/{filename}'\n", "            if os.path.exists(filepath):\n", "                cov_mat = pd.read_csv(filepath)\n", "                cov_mat = cov_mat.pivot(index='Factor1', columns='Factor2', values='VarCovar')\n", "                return cov_mat.where(cov_mat.notna(), cov_mat.T)\n", "\n", "\n", "    def load_Price(self, date):\n", "        date_ = date.replace('-','')\n", "        year = date.split('-')[0]\n", "        if self.barra_product in ['EUE4']: product = 'EUE4BAS'\n", "        else: product = self.barra_product\n", "        group = f'{self.PRODUCT}_{product}_100_D_{year}'\n", "        folders = sorted([s for s in os.listdir(f'{self.info_root}') if s.startswith(group)])\n", "\n", "        if len(folders) == 0:\n", "            raise ValueError(f'No data for {date} in {self.info_root}')\n", "\n", "        for folder in folders:\n", "            filename = f'{self.barra_product}_Daily_Asset_Price_{date_}.csv'\n", "            filepath = f'{self.info_root}{folder}/{filename}'\n", "            if os.path.exists(filepath):\n", "                df = pd.read_csv(filepath).drop(columns=['DataDate'])\n", "                return df.rename(columns={'Barrid': 'BarraID'})\n", "        \n", "    def load_ESTU(self, date):\n", "        date_ = date.replace('-','')\n", "        year = date.split('-')[0]\n", "        if self.barra_product in ['EUE4']: product = 'EUE4BAS'\n", "        else: product = self.barra_product\n", "        group = f'{self.PRODUCT}_{product}_100_D_{year}'\n", "        folders = sorted([s for s in os.listdir(f'{self.info_root}') if s.startswith(group)])\n", "\n", "        for folder in folders:\n", "            filename = f'{self.barra_product}_ESTU_POR_{date_}.csv'\n", "            filepath = f'{self.info_root}{folder}/{filename}'\n", "            if os.path.exists(filepath):\n", "                df = pd.read_csv(filepath)\n", "                return df.rename(columns={'Barrid': 'BarraID'})\n", "\n", "    def load_MarketData(self, date):\n", "        date_ = date.replace('-','')\n", "        year = date.split('-')[0]\n", "        if self.barra_product in ['EUE4']: product = 'EUE4BAS'\n", "        else: product = self.barra_product\n", "        group = f'{self.PRODUCT}_{product}_100_D_{year}'\n", "        folders = sorted([s for s in os.listdir(f'{self.info_root}') if s.startswith(group)])\n", "\n", "        for folder in folders:\n", "            filename = f'{self.barra_product}_Market_Data_{date_}.csv'\n", "            filepath = f'{self.info_root}{folder}/{filename}'\n", "            if os.path.exists(filepath):\n", "                df = pd.read_csv(filepath).drop(columns=['DataDate'])\n", "                return df.rename(columns={'Barrid': 'BarraID'})\n", "    \n", "    def load_Rates(self, date):\n", "        date_ = date.replace('-','')\n", "        year = date.split('-')[0]\n", "        if self.barra_product in ['EUE4']: product = 'EUE4BAS' \n", "        else: product = self.barra_product\n", "        group = f'{self.PRODUCT}_{product}_100_D_{year}'\n", "        folders = sorted([s for s in os.listdir(f'{self.info_root}') if s.startswith(group)])\n", "\n", "        for folder in folders:\n", "            filename = f'{self.barra_product}_Rates_{date_}.csv'\n", "            filepath = f'{self.info_root}{folder}/{filename}'\n", "            if os.path.exists(filepath):\n", "                df = pd.read_csv(filepath).drop(columns=['DataDate'])\n", "                return df.rename(columns={'Barrid': 'BarraID'})\n", "        \n", "    def load_Descriptor(self, date):\n", "        date_ = date.replace('-','')\n", "        year = date.split('-')[0]\n", "        group = f'{self.PRODUCT}_{self.barra_product}_100_Descriptor_D_{year}'\n", "        folders = sorted([s for s in os.listdir(f'{self.info_root}') if s.startswith(group)])\n", "\n", "        for folder in folders:\n", "            filename = f'{self.barra_product}_100_Asset_Descriptor_{date_}.csv'\n", "            filepath = f'{self.info_root}{folder}/{filename}'\n", "            if os.path.exists(filepath):\n", "                df = pd.read_csv(filepath).drop(columns=['DataDate'])\n", "                return df.rename(columns={'Barrid': 'BarraID'})\n", "    \n", "    def load_Data(self, date):\n", "        date_ = date.replace('-','')\n", "        year = date.split('-')[0]\n", "        if self.barra_product in ['EUE4']: product = 'EUE4BASS'\n", "        else: product = self.product\n", "        group = f'{self.PRODUCT}_{product}_100_D_{year}'\n", "        folders = sorted([s for s in os.listdir(f'{self.info_root}') if s.startswith(group)])\n", "\n", "        if len(folders) == 0:\n", "            raise ValueError(f'No data for {date} in {self.info_root}')\n", "\n", "        for folder in folders:\n", "            filename = f'{product}_100_Asset_Data_{date_}.csv'\n", "            filepath = f'{self.info_root}{folder}/{filename}'\n", "            if os.path.exists(filepath):\n", "                df = pd.read_csv(filepath).drop(columns=['DataDate'])\n", "                return df.rename(columns={'Barrid': 'BarraID'})\n", "\n", "    def load_id_map(self, date):\n", "        if self.barra_product in {'EUTR','EULT'}:\n", "            map_root = f'/mnt/sda/NAS/Global/Barra/barra_parsed/eu/{self.barra_product.lower()}/'\n", "        # elif self.barra_product in ['EUE4']:\n", "        #     map_root = f'/mnt/sda/NAS/Global/Barra/barra_parsed/eue4bas/'\n", "        else:\n", "            map_root = f'/mnt/sda/NAS/Global/Barra/barra_parsed/{self.barra_product.lower()}/'\n", "        map_folders = sorted([s for s in os.listdir(map_root) \\\n", "                              if s.startswith(f'{self.PRODUCT}_{self.barra_product}_LOCALID_ID')])\n", "        map_dir = f'{map_root}{map_folders[-1]}/'\n", "        file = [s for s in os.listdir(map_dir) if 'LOCALID_Asset_ID' in s][0]\n", "        map_path = f'{map_dir}{file}'\n", "        id_map = pd.read_csv(map_path,dtype=str)\n", "        id_map[['StartDate','EndDate']] = id_map[['StartDate','EndDate']].apply(lambda x: pd.to_datetime(x.str[:-2],format='%Y%m%d'))\n", "        id_map = id_map.loc[(id_map['StartDate']<=date) & (id_map['EndDate']>=date)].rename(columns={'Barrid': 'BarraID'})\n", "        return id_map[['BarraID','AssetID']]\n", "    \n", "    def load_OpMIC_map(self):\n", "        xchg_map_path = '/mnt/sda/NAS/Global/mapping/ISO10383_MIC.csv'\n", "        df = pd.read_csv(xchg_map_path).rename(columns={'OPERATING MIC': 'OpMIC'})\n", "        return df[['MIC','OpMIC']]\n", "\n", "    def load_ISIN(self, date):\n", "        isin_root = '/mnt/sda/NAS/Global/Barra/barra_parsed/barradirect/isin/'\n", "        isin_folders = sorted([s for s in os.listdir(isin_root) if s.startswith('ISIN_FULL_WE')])\n", "        isin_dir = f'{isin_root}{isin_folders[-1]}/'\n", "        file = [s for s in os.listdir(isin_dir) if 'ISIN_FULL_WE' in s][0]\n", "\n", "        filepath = f'{isin_dir}{file}'\n", "        if os.path.exists(filepath):\n", "            df = pd.read_csv(filepath).drop(columns=['Start Date','End Date'])\n", "            return df.rename(columns={'#Barra ID': 'BarraID'})\n", "\n", "    def load_RIC(self, date):\n", "        ric_root = '/mnt/sda/NAS/Global/Barra/barra_parsed/barradirect/ric/'\n", "        ric_folders = sorted([s for s in os.listdir(ric_root) if s.startswith('RIC_FULL_WE')])\n", "        ric_dir = f'{ric_root}{ric_folders[-1]}/'\n", "        file = [s for s in os.listdir(ric_dir) if 'RIC_FULL_WE' in s][0]\n", "\n", "        filepath = f'{ric_dir}{file}'\n", "        if os.path.exists(filepath):\n", "            df = pd.read_csv(filepath).drop(columns=['Start Date','End Date'])\n", "            return df.rename(columns={'#Barra ID': 'BarraID'})\n", "\n", "    def load_EID(self, date):\n", "        usecols = ['#BARRA ID', 'MIC', 'Country of Exposure']\n", "        eid_root = '/mnt/sda/NAS/Global/Barra/barra_parsed/barradirect/equityidentity/'\n", "        eid_folders = sorted([s for s in os.listdir(eid_root) if s.startswith('EquityIdentity_FULL_WE')])\n", "        eid_dir = f'{eid_root}{eid_folders[-1]}/'\n", "        file = [s for s in os.listdir(eid_dir) if 'EquityIdentity_FULL_WE' in s][0]\n", "\n", "        filepath = f'{eid_dir}{file}'\n", "        if os.path.exists(filepath):\n", "            df = pd.read_csv(filepath,usecols=usecols)\n", "            return df.rename(columns={'#BARRA ID': 'BarraID', 'Country of Exposure':'Country_of_Exposure'})\n", "    \n", "    def load_CTRY_MAP(self, date):\n", "        ctry_root = '/mnt/sda/NAS/Global/Barra/barra_parsed/barradirect/'\n", "        folders = sorted([s for s in os.listdir(ctry_root) if s.startswith('Country_Master_WE')])\n", "        ctry_dir = f'{ctry_root}{folders[-1]}/'\n", "        file = [s for s in os.listdir(ctry_dir) if 'Country_Master_WE' in s][0]\n", "\n", "        filepath = f'{ctry_dir}{file}'\n", "        if os.path.exists(filepath):\n", "            df = pd.read_csv(filepath,usecols=['CountrySymbol (ISO2)', 'CountrySymbol (ISO3)']).dropna().drop_duplicates()\n", "            df = df.rename(columns={'CountrySymbol (ISO2)': 'Country_ISO2', 'CountrySymbol (ISO3)': 'Country_ISO3'})\n", "            return dict(zip(df['Country_ISO2'].tolist(), df['Country_ISO3'].tolist()))\n", "\n", "\n", "def get_info_indcell(dly_exp, indset):\n", "    info = dly_exp.loc[dly_exp['Factor'].isin(indset)]\n", "    info = info.drop_duplicates(['BarraID'])\n", "    return info.iloc[:, :2].rename(columns={'Factor': 'indcell'})\n", "\n", "\n", "def get_info_region(dly_exp, region_set):\n", "    info = dly_exp.loc[dly_exp['Factor'].isin(region_set)]\n", "    info = info.drop_duplicates(['BarraID'])\n", "    return info.iloc[:, :2].rename(columns={'Factor': 'regioncell'})\n", "\n", "\n", "def get_info_style(dly_exp, style_set):\n", "    info = dly_exp.loc[dly_exp['Factor'].isin(style_set)]\n", "    info_style = info.set_index(['BarraID','Factor']).unstack(1).droplevel(0,axis=1)\n", "    return info_style.reset_index().dropna(axis=0)\n", "\n", "\n", "def get_info_desc(dly_desc):\n", "    return dly_desc.drop(columns='DescriptorType').set_index(['BarraID','Descriptor'])\\\n", "        .unstack(1).droplevel(0,axis=1).reset_index().dropna(axis=0)\n", "\n", "\n", "def get_ind_style_region_sets(barra_product):\n", "\n", "\tindset = pd.read_csv(f'{saveroot}ind_set.csv', dtype=str)[barra_product].dropna().tolist()\n", "\tstyle_set = pd.read_csv(f'{saveroot}style_set.csv', dtype=str)[barra_product].dropna().tolist()\n", "\n", "\tregion_set_path = '/mnt/sda/NAS/ShareFolder/xielan/fundamental_info/barrainfo/regionset.txt'\n", "\tregion_set = pd.read_csv(region_set_path, sep='\\t').iloc[:, 0]\n", "\n", "\tif barra_product in {'GEM3', 'EULT'}: suffix = 'S_'\n", "\telif bar<PERSON>_product in {'EUTR', 'CNTR'}: suffix = 'D_'\n", "\telif barra_product in ['EUE4']: suffix = 'BASS_'\n", "\n", "\tindset = [f'{barra_product}{suffix}' + s for s in indset]\n", "\tstyle_set = [f'{barra_product}{suffix}' + s for s in style_set]\n", "\tregion_set = set(f'{barra_product}{suffix}' + region_set)\n", "\n", "\treturn indset, style_set, region_set\n", "\n", "\n", "info_root = '/mnt/sda/NAS/Global/Barra/barra_parsed/'\n", "saveroot = '/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/'\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "daily_root = '/mnt/sda/NAS/Global/Barra/barra_parsed/history/gem3/sm/daily/'\n", "\n", "idp = InfoDataProcessor(daily_root, 'GEM3')\n", "idp.load_cov_mat('2024-02-05')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_1day_info(date, history_root, barra_product='GEM3'):\n", "\n", "    if barra_product == 'EUE4':\n", "        daily_root = f'{history_root}eue4bas/sm/daily/'\n", "    else:\n", "        daily_root = f'{history_root}{barra_product.lower()}/sm/daily/'\n", "\n", "    idp = InfoDataProcessor(daily_root, barra_product)\n", "    dly_ret = idp.load_DlyRet(date)\n", "\n", "    dly_exp = idp.load_Exposure(date)\n", "    dly_data = idp.load_Data(date)\n", "\n", "    dly_pce = idp.load_Price(date)\n", "\n", "    dly_estu = idp.load_ESTU(date)\n", "    dly_mkt = idp.load_MarketData(date)\n", "    dly_rate = idp.load_Rates(date)\n", "\n", "    dly_desc = idp.load_Descriptor(date)\n", "    if dly_desc is not None:\n", "        dly_desc = dly_desc[dly_desc['DescriptorType']=='STD']\n", "\n", "    id_map = idp.load_id_map(date)\n", "    isin = idp.load_ISIN(date)\n", "    ric = idp.load_RIC(date)\n", "    eid = idp.load_EID(date)\n", "    \n", "    opmic = idp.load_OpMIC_map()\n", "    eid = eid.merge(opmic, on='MIC',how='outer')\n", "\n", "    ctry_map = idp.load_CTRY_MAP(date)\n", "\n", "\n", "    indset, style_set, region_set = get_ind_style_region_sets(barra_product)\n", "    info_ind =  get_info_indcell(dly_exp, indset)\n", "    info_region = get_info_region(dly_exp, region_set)\n", "    info_style = get_info_style(dly_exp, style_set)\n", "    \n", "    if dly_desc is not None:\n", "        info_desc = get_info_desc(dly_desc)\n", "    else:\n", "        info_desc = None\n", "    \n", "    df_pce = dly_pce.merge(dly_rate, on='Currency')\n", "\n", "    info = \\\n", "        info_ind.merge(info_region, on='BarraID',how='outer')\\\n", "        .merge(isin, on='BarraID',how='outer')\\\n", "        .merge(ric, on='BarraID',how='outer')\\\n", "        .merge(eid, on='BarraID',how='outer')\\\n", "        .merge(id_map, on='BarraID',how='outer')\n", "\n", "    info = info.drop_duplicates().dropna(subset='indcell')\n", "\n", "    info['LocalID'] = info['AssetID'].str[2:]\n", "    info['Country_ISO2'] = info['AssetID'].str[:2]\n", "    info['Country_ISO3'] = info['Country_ISO2'].apply(ctry_map.get)\n", "\n", "    if info_desc is not None:\n", "        return info.merge(info_style, on='BarraID',how='outer')\\\n", "            .merge(dly_ret, on='BarraID',how='outer')\\\n", "            .merge(dly_estu, on='BarraID',how='outer')\\\n", "            .merge(df_pce, on='BarraID',how='outer')\\\n", "            .merge(dly_mkt, on='BarraID',how='outer')\\\n", "            .merge(dly_data, on='BarraID',how='outer')\\\n", "            .merge(info_desc, on='BarraID',how='outer')\n", "    elif info_desc is None:\n", "        return info.merge(info_style, on='BarraID',how='outer')\\\n", "            .merge(dly_ret, on='BarraID',how='outer')\\\n", "            .merge(dly_estu, on='BarraID',how='outer')\\\n", "            .merge(df_pce, on='BarraID',how='outer')\\\n", "            .merge(dly_mkt, on='BarraID',how='outer')\\\n", "            .merge(dly_data, on='BarraID',how='outer')\n", "\n", "\n", "history_root = '/mnt/sda/NAS/Global/Barra/barra_parsed/history/'\n", "saveroot = '/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/'\n", "saveroot_fcov = '/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/factor_cov/'\n", "saveroot_fexp = '/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/factor_exp/'\n", "\n", "def run_1day(date):\n", "    for barra_product in ('GEM3','EULT','EUTR','CNTR', 'EUE4')[:]:\n", "        savedir = f'{saveroot}{barra_product}/'\n", "        if not os.path.exists(savedir): os.makedirs(savedir, exist_ok=True)\n", "        if os.path.exists(f'{savedir}{date}.parquet'): continue\n", "        try:\n", "            df_info = get_1day_info(date, history_root, barra_product)\n", "            df_info = df_info.loc[~df_info['indcell'].isna()]\n", "            df_info.to_parquet(f'{savedir}{date}.parquet')\n", "        except Exception as err:\n", "            print('wrong', date, barra_product, err, end='\\r')\n", "            continue\n", "\n", "def run_1day_fcov(date):\n", "    for barra_product in ('GEM3','EULT','EUTR','CNTR', 'EUE4')[:]:\n", "        if not os.path.exists(f'{saveroot_fcov}{barra_product}/'): \n", "            os.makedirs(f'{saveroot_fcov}{barra_product}/', exist_ok=True)\n", "        if not os.path.exists(f'{saveroot_fexp}{barra_product}/'): \n", "            os.makedirs(f'{saveroot_fexp}{barra_product}/', exist_ok=True)\n", "\n", "        if barra_product == 'EUE4':\n", "            daily_root = f'{history_root}eue4bas/sm/daily/'\n", "        else:\n", "            daily_root = f'{history_root}{barra_product.lower()}/sm/daily/'\n", "        idp = InfoDataProcessor(daily_root, barra_product)\n", "        try:\n", "            cov_mat = idp.load_cov_mat(date)\n", "            full_exp = idp.load_Exposure(date, load_etf_exposure=False)\n", "            if cov_mat is not None:\n", "                cov_mat.to_parquet(f'{saveroot_fcov}{barra_product}/{date}.parquet')\n", "                full_exp.to_parquet(f'{saveroot_fexp}{barra_product}/{date}.parquet')\n", "        except Exception as err:\n", "            print('wrong', date, barra_product, err, end='\\r')\n", "            continue\n", "\n", "dates = pd.date_range('2015-01-01','2024-10-18').astype(str).tolist()\n", "\n", "# with Parallel(n_jobs=10, backend='multiprocessing') as para:\n", "#     _ = para(delayed(run_1day)(date) for date in tqdm(dates))\n", "\n", "\n", "with Parallel(n_jobs=10, backend='multiprocessing') as para:\n", "    _ = para(delayed(run_1day_fcov)(date) for date in tqdm(dates[:]))\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["daily_root = f'/mnt/sda/NAS/Global/Barra/barra_parsed/history/gem3/sm/daily/'\n", "idp = InfoDataProcessor(daily_root, 'GEM3')\n", "idp.load_cov_mat('2015-01-02')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["fcov = pd.read_parquet('/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/factor_cov/GEM3/2020-02-17.parquet')\n", "all_factors = fcov.columns.tolist()\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["a = pd.read_csv('/mnt/sda/NAS/Global/Barra/barra_parsed/history/gem3/sm/daily/GMD_GEM3S_100_D_2020_1/GEM3S_100_Asset_Exposure_20200217.csv')\n", "c = set(all_factors) - set(a['Factor'].unique())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b = pd.read_csv('/mnt/sda/NAS/Global/Barra/barra_parsed/history/gem3/sm/daily/GMD_GEM3S_100_D_2020_1/GEM3S_ETF_100_Asset_Exposure_20200217.csv')\n", "c.intersection(b['Factor'].unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["set(all_factors) - set(b['Factor'].unique()) - set(a['Factor'].unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["set.union(set(a['Factor'].unique()), set(b['Factor'].unique())) - set(all_factors)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["daily_root = '/mnt/sda/NAS/Global/Barra/barra_parsed/history/eue4bas/sm/daily/'\n", "idp = InfoDataProcessor(daily_root, 'EUE4')\n", "idp.load_Exposure('2020-02-17')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.to_datetime('2018-01-01').weekday()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### daily"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/NAS/ShareFolder/lishuanglin/share/to_wangxiang/gathered/update_cntr_20250430.py'\n", "import os\n", "import time\n", "import datetime\n", "import pandas as pd\n", "import numpy as np\n", "from tqdm.auto import tqdm\n", "from joblib import Parallel, delayed\n", "\n", "\n", "class InfoDataProcessorDaily:\n", "    def __init__(self, info_root, barra_product):\n", "        self.info_root = info_root\n", "        self.barra_product = barra_product\n", "\n", "        if self.barra_product == \"CNTR\":\n", "            self.PRODUCT = \"SMD\"\n", "        else:\n", "            self.PRODUCT = \"GMD\"\n", "\n", "        if self.barra_product in {\"EUTR\", \"CNTR\"}:\n", "            self.product = f\"{self.barra_product}D\"\n", "        else:\n", "            self.product = f\"{self.barra_product}S\"\n", "\n", "        if self.barra_product in (\"EUTR\", \"EULT\"):\n", "            self.info_root = f\"{self.info_root}eu/\"\n", "\n", "        if self.barra_product == \"EUE4\":\n", "            self.product = self.barra_product\n", "\n", "    def load_DlyRet(self, date):\n", "        date_ = date.replace(\"-\", \"\")\n", "        if self.barra_product == \"EUE4\":\n", "            product = \"EUE4BASS\"\n", "        else:\n", "            product = self.product\n", "        folder = f\"{self.PRODUCT}_{product}_100_{date_[2:]}\"\n", "\n", "        time_elasped = 0\n", "        while True:\n", "            if (date == today) and (pd.to_datetime(date).weekday() in list(range(1,6))):\n", "                if not os.path.exists(\n", "                    os.path.join(self.info_root, self.barra_product.lower(), folder)\n", "                ):\n", "                    time.sleep(10)\n", "                    time_elasped += 10\n", "                    print(f\"{folder} not exist, {time_elasped} seconds waited!\", flush=True)\n", "                    if time_elasped / 60 > 10:\n", "                        raise FileNotFoundError(f\"{folder} not generated!\")\n", "                else:\n", "                    break\n", "            else:\n", "                break\n", "\n", "        filename = f\"{product}_100_Asset_DlySpecRet_{date_}.csv\"\n", "        filepath = f\"{self.info_root}{self.barra_product.lower()}/{folder}/{filename}\"\n", "        filename_ = f\"{product[:-1]}_100_Asset_DlySpecRet_{date_}.csv\"\n", "        filepath_ = f\"{self.info_root}{self.barra_product.lower()}/{folder}/{filename_}\"\n", "        if os.path.exists(filepath):\n", "            df = pd.read_csv(filepath).drop(columns=[\"DataDate\"])\n", "            return df.rename(columns={\"Barrid\": \"BarraID\"})\n", "        elif os.path.exists(filepath_):\n", "            df = pd.read_csv(filepath_).drop(columns=[\"DataDate\"])\n", "            return df.rename(columns={\"Barrid\": \"BarraID\"})\n", "        else:\n", "            print(f\"file not exist {filepath}\")\n", "\n", "    def load_Exposure(self, date, load_etf_exposure=False):\n", "        date_ = date.replace(\"-\", \"\")\n", "        if self.barra_product == \"EUE4\":\n", "            product = \"EUE4BASS\"\n", "        else:\n", "            product = self.product\n", "        folder = f\"{self.PRODUCT}_{product}_100_{date_[2:]}\"\n", "\n", "        filename = f\"{product}_100_Asset_Exposure_{date_}.csv\"\n", "        filepath = f\"{self.info_root}{self.barra_product.lower()}/{folder}/{filename}\"\n", "        if os.path.exists(filepath):\n", "            df = pd.read_csv(filepath).drop(columns=[\"DataDate\"])\n", "            df = df.rename(columns={\"Barrid\": \"BarraID\"})\n", "        else:\n", "            print(f\"file not exist {filepath}\")\n", "\n", "        if load_etf_exposure:\n", "            filename = f\"{product}_ETF_100_Asset_Exposure_{date_}.csv\"\n", "            filepath = (\n", "                f\"{self.info_root}{self.barra_product.lower()}/{folder}/{filename}\"\n", "            )\n", "            if os.path.exists(filepath):\n", "                df_etf = pd.read_csv(filepath).drop(columns=[\"DataDate\"])\n", "                df_etf = df_etf.rename(columns={\"Barrid\": \"BarraID\"})\n", "                df = pd.concat([df, df_etf], axis=0)\n", "            return df\n", "        else:\n", "            return df\n", "\n", "    def load_cov_mat(self, date: str):\n", "        date_ = date.replace(\"-\", \"\")\n", "        if self.barra_product in [\"EUE4\"]:\n", "            product = \"EUE4BASS\"\n", "        elif self.barra_product in [\"CNTR\", \"EUTR\"]:\n", "            product = f\"{self.barra_product}D\"\n", "        else:\n", "            product = f\"{self.barra_product}S\"\n", "        folder = f\"{self.PRODUCT}_{product}_100_{date_[2:]}\"\n", "        filename = f\"{product}_100_Covariance_{date_}.csv\"\n", "        filepath = f\"{self.info_root}{self.barra_product.lower()}/{folder}/{filename}\"\n", "        if os.path.exists(filepath):\n", "            cov_mat = pd.read_csv(filepath)\n", "            cov_mat = cov_mat.pivot(\n", "                index=\"Factor1\", columns=\"Factor2\", values=\"VarCovar\"\n", "            )\n", "            return cov_mat.where(cov_mat.notna(), cov_mat.T)\n", "        else:\n", "            raise ValueError(f\"No data for {date} in {self.info_root}\")\n", "\n", "    def load_Price(self, date):\n", "        date_ = date.replace(\"-\", \"\")\n", "        if self.barra_product == \"EUE4\":\n", "            product = \"EUE4BASS\"\n", "        else:\n", "            product = self.product\n", "        folder = f\"{self.PRODUCT}_{product}_100_{date_[2:]}\"\n", "\n", "        filename = f\"{self.barra_product}_Daily_Asset_Price_{date_}.csv\"\n", "        filepath = f\"{self.info_root}{self.barra_product.lower()}/{folder}/{filename}\"\n", "        if os.path.exists(filepath):\n", "            df = pd.read_csv(filepath).drop(columns=[\"DataDate\"])\n", "            return df.rename(columns={\"Barrid\": \"BarraID\"})\n", "        else:\n", "            print(f\"file not exist {filepath}\")\n", "\n", "    def load_ESTU(self, date):\n", "        date_ = date.replace(\"-\", \"\")\n", "        if self.barra_product == \"EUE4\":\n", "            product = \"EUE4BASS\"\n", "        else:\n", "            product = self.product\n", "        folder = f\"{self.PRODUCT}_{product}_100_{date_[2:]}\"\n", "\n", "        filename = f\"{self.barra_product}_ESTU_POR_{date_}.csv\"\n", "        filepath = f\"{self.info_root}{self.barra_product.lower()}/{folder}/{filename}\"\n", "        if os.path.exists(filepath):\n", "            df = pd.read_csv(filepath)\n", "            return df.rename(columns={\"Barrid\": \"BarraID\"})\n", "        else:\n", "            print(f\"file not exist {filepath}\")\n", "\n", "    def load_MarketData(self, date):\n", "        date_ = date.replace(\"-\", \"\")\n", "        folder = f\"{self.PRODUCT}_{self.barra_product}_Market_Data_{date_[2:]}\"\n", "        filename = f\"{self.barra_product}_Market_Data_{date_}.csv\"\n", "        filepath = f\"{self.info_root}{self.barra_product.lower()}/{folder}/{filename}\"\n", "        if os.path.exists(filepath):\n", "            df = pd.read_csv(filepath).drop(columns=[\"DataDate\"])\n", "            return df.rename(columns={\"Barrid\": \"BarraID\"})\n", "        else:\n", "            print(f\"file not exist {filepath}\")\n", "\n", "    def load_Rates(self, date):\n", "        date_ = date.replace(\"-\", \"\")\n", "        if self.barra_product == \"EUE4\":\n", "            product = \"EUE4BASS\"\n", "        else:\n", "            product = self.product\n", "        folder = f\"{self.PRODUCT}_{product}_100_{date_[2:]}\"\n", "\n", "        filename = f\"{self.barra_product}_Rates_{date_}.csv\"\n", "        filepath = f\"{self.info_root}{self.barra_product.lower()}/{folder}/{filename}\"\n", "        if os.path.exists(filepath):\n", "            df = pd.read_csv(filepath).drop(columns=[\"DataDate\"])\n", "            return df.rename(columns={\"Barrid\": \"BarraID\"})\n", "        else:\n", "            print(f\"file not exist {filepath}\")\n", "\n", "    def load_Descriptor(self, date):\n", "        date_ = date.replace(\"-\", \"\")\n", "        date.split(\"-\")[0]\n", "        folder = f\"{self.PRODUCT}_{self.barra_product}_100_Descriptor_{date_[2:]}\"\n", "\n", "        filename = f\"{self.barra_product}_100_Asset_Descriptor_{date_}.csv\"\n", "        filepath = f\"{self.info_root}{self.barra_product.lower()}/{folder}/{filename}\"\n", "        if os.path.exists(filepath):\n", "            df = pd.read_csv(filepath).drop(columns=[\"DataDate\"])\n", "            return df.rename(columns={\"Barrid\": \"BarraID\"})\n", "        else:\n", "            print(f\"file not exist {filepath}\", flush=True)\n", "\n", "    def load_Data(self, date):\n", "        date_ = date.replace(\"-\", \"\")\n", "        if self.barra_product == \"EUE4\":\n", "            product = \"EUE4BASS\"\n", "        else:\n", "            product = self.product\n", "        folder = f\"{self.PRODUCT}_{product}_100_{date_[2:]}\"\n", "\n", "        filename = f\"{product}_100_Asset_Data_{date_}.csv\"\n", "        filepath = f\"{self.info_root}{self.barra_product.lower()}/{folder}/{filename}\"\n", "        if os.path.exists(filepath):\n", "            df = pd.read_csv(filepath).drop(columns=[\"DataDate\"])\n", "            return df.rename(columns={\"Barrid\": \"BarraID\"})\n", "        else:\n", "            print(f\"file not exist {filepath}\")\n", "\n", "    def load_id_map(self, date):\n", "        \"\"\"map barrid and assetid\"\"\"\n", "        date = date.replace(\"-\", \"\")\n", "        folder = f\"{self.PRODUCT}_{self.barra_product}_LOCALID_ID_{date[2:]}\"\n", "        if self.barra_product == \"CNTR\":\n", "            map_prefix = \"CHN\"\n", "        elif self.barra_product == \"GEM3\":\n", "            map_prefix = \"GEM\"\n", "        else:\n", "            map_prefix = self.barra_product\n", "        filename = f\"{map_prefix}_LOCALID_Asset_ID_{date}.csv\"\n", "        map_path = f\"{self.info_root}{self.barra_product.lower()}/{folder}/{filename}\"\n", "        id_map = pd.read_csv(map_path, dtype=str)\n", "        id_map[[\"StartDate\", \"EndDate\"]] = id_map[[\"StartDate\", \"EndDate\"]].apply(\n", "            lambda x: pd.to_datetime(x.str[:-2], format=\"%Y%m%d\")\n", "        )\n", "        id_map = id_map.loc[\n", "            (id_map[\"StartDate\"] <= date) & (id_map[\"EndDate\"] >= date)\n", "        ].rename(columns={\"Barrid\": \"BarraID\"})\n", "        return id_map[[\"BarraID\", \"AssetID\"]]\n", "\n", "    def load_OpMIC_map(self):\n", "        \"\"\"MIC and operating MIC\"\"\"\n", "        xchg_map_path = \"/mnt/sda/NAS/Global/mapping/ISO10383_MIC.csv\"\n", "        df = pd.read_csv(xchg_map_path).rename(columns={\"OPERATING MIC\": \"OpMIC\"})\n", "        return df[[\"MIC\", \"OpMIC\"]]\n", "\n", "    def load_ISIN(self, date):\n", "        \"\"\"map barraid and isin\"\"\"\n", "        isin_root = \"/mnt/sda/NAS/Global/Barra/barra_parsed/barradirect/isin/\"\n", "        isin_folders = sorted(\n", "            [s for s in os.listdir(isin_root) if s.startswith(\"ISIN_FULL_WE\")]\n", "        )\n", "        isin_dir = f\"{isin_root}{isin_folders[-1]}/\"\n", "        file = [s for s in os.listdir(isin_dir) if \"ISIN_FULL_WE\" in s][0]\n", "\n", "        filepath = f\"{isin_dir}{file}\"\n", "        if os.path.exists(filepath):\n", "            df = pd.read_csv(filepath).drop(columns=[\"Start Date\", \"End Date\"])\n", "            return df.rename(columns={\"#Barra ID\": \"BarraID\"})\n", "\n", "    def load_RIC(self, date):\n", "        ric_root = \"/mnt/sda/NAS/Global/Barra/barra_parsed/barradirect/ric/\"\n", "        ric_folders = sorted(\n", "            [s for s in os.listdir(ric_root) if s.startswith(\"RIC_FULL_WE\")]\n", "        )\n", "        ric_dir = f\"{ric_root}{ric_folders[-1]}/\"\n", "        file = [s for s in os.listdir(ric_dir) if \"RIC_FULL_WE\" in s][0]\n", "\n", "        filepath = f\"{ric_dir}{file}\"\n", "        if os.path.exists(filepath):\n", "            df = pd.read_csv(filepath).drop(columns=[\"Start Date\", \"End Date\"])\n", "            return df.rename(columns={\"#Barra ID\": \"BarraID\"})\n", "\n", "    def load_EID(self, date):\n", "        \"map barraid and mic and country_of_exposure\"\n", "        usecols = [\"#BARRA ID\", \"MIC\", \"Country of Exposure\"]\n", "        eid_root = \"/mnt/sda/NAS/Global/Barra/barra_parsed/barradirect/equityidentity/\"\n", "        eid_folders = sorted(\n", "            [s for s in os.listdir(eid_root) if s.startswith(\"EquityIdentity_FULL_WE\")]\n", "        )\n", "        eid_dir = f\"{eid_root}{eid_folders[-1]}/\"\n", "        file = [s for s in os.listdir(eid_dir) if \"EquityIdentity_FULL_WE\" in s][0]\n", "\n", "        filepath = f\"{eid_dir}{file}\"\n", "        if os.path.exists(filepath):\n", "            df = pd.read_csv(filepath, usecols=usecols)\n", "            return df.rename(\n", "                columns={\n", "                    \"#BARRA ID\": \"BarraID\",\n", "                    \"Country of Exposure\": \"Country_of_Exposure\",\n", "                }\n", "            )\n", "\n", "    def load_CTRY_MAP(self, date):\n", "        \"\"\"map country name of ISO2 AND ISO3\"\"\"\n", "        ctry_root = \"/mnt/sda/NAS/Global/Barra/barra_parsed/barradirect/\"\n", "        folders = sorted(\n", "            [s for s in os.listdir(ctry_root) if s.startswith(\"Country_Master_WE\")]\n", "        )\n", "        ctry_dir = f\"{ctry_root}{folders[-1]}/\"\n", "        file = [s for s in os.listdir(ctry_dir) if \"Country_Master_WE\" in s][0]\n", "\n", "        filepath = f\"{ctry_dir}{file}\"\n", "        if os.path.exists(filepath):\n", "            df = (\n", "                pd.read_csv(\n", "                    filepath, usecols=[\"CountrySymbol (ISO2)\", \"CountrySymbol (ISO3)\"]\n", "                )\n", "                .dropna()\n", "                .drop_duplicates()\n", "            )\n", "            df = df.rename(\n", "                columns={\n", "                    \"CountrySymbol (ISO2)\": \"Country_ISO2\",\n", "                    \"CountrySymbol (ISO3)\": \"Country_ISO3\",\n", "                }\n", "            )\n", "            return dict(zip(df[\"Country_ISO2\"].tolist(), df[\"Country_ISO3\"].tolist()))\n", "\n", "\n", "# barra_products = ['GEM3','CNTR']\n", "# parsed_root = '/mnt/sda/NAS/Global/Barra/barra_parsed/'\n", "# idp = InfoDataProcessorDaily(parsed_root, barra_products[1])\n", "\n", "\n", "def get_info_indcell(dly_exp, indset):\n", "    info = dly_exp.loc[dly_exp[\"Factor\"].isin(indset)]\n", "    info = info.drop_duplicates([\"BarraID\"])\n", "    return info.iloc[:, :2].rename(columns={\"Factor\": \"indcell\"})\n", "\n", "\n", "def get_info_region(dly_exp, region_set):\n", "    info = dly_exp.loc[dly_exp[\"Factor\"].isin(region_set)]\n", "    info = info.drop_duplicates([\"BarraID\"])\n", "    return info.iloc[:, :2].rename(columns={\"Factor\": \"regioncell\"})\n", "\n", "\n", "def get_info_style(dly_exp, style_set):\n", "    info = dly_exp.loc[dly_exp[\"Factor\"].isin(style_set)]\n", "    info_style = info.set_index([\"BarraID\", \"Factor\"]).unstack(1).droplevel(0, axis=1)\n", "    return info_style.reset_index().dropna(axis=0)\n", "\n", "\n", "def get_info_desc(dly_desc):\n", "    return (\n", "        dly_desc.drop(columns=\"DescriptorType\")\n", "        .set_index([\"BarraID\", \"Descriptor\"])\n", "        .unstack(1)\n", "        .droplevel(0, axis=1)\n", "        .reset_index()\n", "        .dropna(axis=0)\n", "    )\n", "\n", "\n", "def get_ind_style_region_sets(barra_product):\n", "    indset = (\n", "        pd.read_csv(f\"{saveroot}ind_set.csv\", dtype=str)[barra_product]\n", "        .dropna()\n", "        .tolist()\n", "    )\n", "    style_set = (\n", "        pd.read_csv(f\"{saveroot}style_set.csv\", dtype=str)[barra_product]\n", "        .dropna()\n", "        .tolist()\n", "    )\n", "\n", "    region_set_path = (\n", "        \"/mnt/sda/NAS/ShareFolder/xielan/fundamental_info/barrainfo/regionset.txt\"\n", "    )\n", "    region_set = pd.read_csv(region_set_path, sep=\"\\t\").iloc[:, 0]\n", "\n", "    if barra_product in {\"GEM3\", \"EULT\"}:\n", "        suffix = \"S_\"\n", "    elif <PERSON>_product in {\"EUTR\", \"CNTR\"}:\n", "        suffix = \"D_\"\n", "    elif <PERSON>_product in [\"EUE4\"]:\n", "        suffix = \"BASS_\"\n", "\n", "    indset = [f\"{barra_product}{suffix}\" + s for s in indset]\n", "    style_set = [f\"{barra_product}{suffix}\" + s for s in style_set]\n", "    region_set = list(set(f\"{barra_product}{suffix}\" + region_set))\n", "\n", "    return indset, style_set, region_set\n", "\n", "\n", "def get_1day_info_updated(date, info_root, barra_product=\"GEM3\"):\n", "    daily_root = f\"{info_root}\"\n", "    idp = InfoDataProcessorDaily(daily_root, barra_product)\n", "\n", "    dly_ret = idp.load_DlyRet(date)\n", "    dly_exp = idp.load_Exposure(date)\n", "    dly_data = idp.load_Data(date)\n", "    dly_pce = idp.load_Price(date)\n", "    dly_estu = idp.load_ESTU(date)\n", "    dly_mkt = idp.load_MarketData(date)\n", "    dly_rate = idp.load_Rates(date)\n", "    dly_desc = idp.load_Descriptor(date)\n", "    if dly_desc is not None:\n", "        dly_desc = dly_desc[dly_desc[\"DescriptorType\"] == \"STD\"]\n", "\n", "    id_map = idp.load_id_map(date)\n", "    isin = idp.load_ISIN(date)\n", "    ric = idp.load_RIC(date)\n", "    eid = idp.load_EID(date)\n", "\n", "    opmic = idp.load_OpMIC_map()\n", "    eid = eid.merge(opmic, on=\"MIC\", how=\"outer\")\n", "\n", "    ctry_map = idp.load_CTRY_MAP(date)\n", "\n", "    indset, style_set, region_set = get_ind_style_region_sets(barra_product)\n", "    info_ind = get_info_indcell(dly_exp, indset)\n", "    info_region = get_info_region(dly_exp, region_set)\n", "    info_style = get_info_style(dly_exp, style_set)\n", "\n", "    if dly_desc is not None:\n", "        info_desc = get_info_desc(dly_desc)\n", "    else:\n", "        info_desc = None\n", "\n", "    df_pce = dly_pce.merge(dly_rate, on=\"Currency\")\n", "\n", "    info = (\n", "        info_ind.merge(info_region, on=\"BarraID\", how=\"outer\")\n", "        .merge(isin, on=\"BarraID\", how=\"outer\")\n", "        .merge(ric, on=\"BarraID\", how=\"outer\")\n", "        .merge(eid, on=\"BarraID\", how=\"outer\")\n", "        .merge(id_map, on=\"BarraID\", how=\"outer\")\n", "    )\n", "\n", "    info = info.drop_duplicates().dropna(subset=\"indcell\")\n", "\n", "    info[\"LocalID\"] = info[\"AssetID\"].str[2:]\n", "    info[\"Country_ISO2\"] = info[\"AssetID\"].str[:2]\n", "    info[\"Country_ISO3\"] = info[\"Country_ISO2\"].apply(ctry_map.get)\n", "\n", "    if info_desc is not None:\n", "        return (\n", "            info.merge(info_style, on=\"BarraID\", how=\"outer\")\n", "            .merge(dly_ret, on=\"BarraID\", how=\"outer\")\n", "            .merge(dly_estu, on=\"BarraID\", how=\"outer\")\n", "            .merge(df_pce, on=\"BarraID\", how=\"outer\")\n", "            .merge(dly_mkt, on=\"BarraID\", how=\"outer\")\n", "            .merge(dly_data, on=\"BarraID\", how=\"outer\")\n", "            .merge(info_desc, on=\"BarraID\", how=\"outer\")\n", "        )\n", "    elif info_desc is None:\n", "        return (\n", "            info.merge(info_style, on=\"BarraID\", how=\"outer\")\n", "            .merge(dly_ret, on=\"BarraID\", how=\"outer\")\n", "            .merge(dly_estu, on=\"BarraID\", how=\"outer\")\n", "            .merge(df_pce, on=\"BarraID\", how=\"outer\")\n", "            .merge(dly_mkt, on=\"BarraID\", how=\"outer\")\n", "            .merge(dly_data, on=\"BarraID\", how=\"outer\")\n", "        )\n", "\n", "\n", "info_root = \"/mnt/sda/NAS/Global/Barra/barra_parsed/\"\n", "saveroot = \"/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/\"\n", "saveroot_fcov = (\n", "    \"/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/factor_cov/\"\n", ")\n", "saveroot_fexp = (\n", "    \"/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/factor_exp/\"\n", ")\n", "\n", "try:\n", "    os.system(f\"cd {info_root}\")\n", "    time.sleep(5.0)\n", "except:\n", "    raise FileNotFoundError(\"info_root may not be mounted!\")\n", "\n", "\n", "def run_1day(date):\n", "    for barra_product in (\"CNTR\", \"GEM3\", \"EUTR\", \"EULT\")[:1]:\n", "        if barra_product == 'CNTR':\n", "            if date not in cn_tddays:\n", "                continue\n", "        savedir = f\"{saveroot}{barra_product}/\"\n", "        if not os.path.exists(savedir):\n", "            os.makedirs(savedir, exist_ok=True)\n", "        if os.path.exists(f\"{savedir}{date}.parquet\"):\n", "            print(f\"file already exists for {barra_product, date}\", flush=True)\n", "            continue\n", "        try:\n", "            df_info = get_1day_info_updated(date, info_root, barra_product)\n", "            df_info = df_info.loc[~df_info[\"indcell\"].isna()]\n", "            df_info.to_parquet(f\"{savedir}{date}.parquet\")\n", "            print(f\"file updated for {barra_product, date}\")\n", "            # display.clear_output()\n", "        except Exception as err:\n", "            print(\"wrong\", date, barra_product, err)\n", "            continue\n", "\n", "\n", "def run_1day_fcov(date):\n", "    for barra_product in (\"CNTR\", \"GEM3\", \"EUTR\", \"EULT\")[:1]:\n", "        if barra_product == 'CNTR':\n", "            if date not in cn_tddays:\n", "                continue\n", "\n", "        if not os.path.exists(f\"{saveroot_fcov}{barra_product}/\"):\n", "            os.makedirs(f\"{saveroot_fcov}{barra_product}/\", exist_ok=True)\n", "        if not os.path.exists(f\"{saveroot_fexp}{barra_product}/\"):\n", "            os.makedirs(f\"{saveroot_fexp}{barra_product}/\", exist_ok=True)\n", "\n", "        cov_dir = f\"{saveroot_fcov}{barra_product}/\"\n", "        exp_dir = f\"{saveroot_fexp}{barra_product}/\"\n", "        if os.path.exists(f\"{cov_dir}{date}.parquet\") & os.path.exists(\n", "            f\"{exp_dir}{date}.parquet\"\n", "        ):\n", "            print(f\"fcov_file already exists for {barra_product, date}\", flush=True, end=\"\\r\")\n", "            continue\n", "\n", "        if barra_product == \"EUE4\":\n", "            daily_root = f\"{info_root}eue4/\"\n", "        else:\n", "            daily_root = f\"{info_root}\"\n", "\n", "        idp = InfoDataProcessorDaily(daily_root, barra_product)\n", "\n", "        try:\n", "            cov_mat = idp.load_cov_mat(date)\n", "            full_exp = idp.load_Exposure(date, load_etf_exposure=True)\n", "            if cov_mat is not None:\n", "                cov_mat.to_parquet(f\"{cov_dir}/{date}.parquet\")\n", "                full_exp.to_parquet(f\"{exp_dir}/{date}.parquet\")\n", "            print(f\"cov_exp_mat file updated for {barra_product, date}\", end=\"\\r\", flush=True)\n", "            # display.clear_output()\n", "        except Exception as err:\n", "            print(\"wrong\", date, barra_product, err)\n", "            continue\n", "\n", "\n", "from shennong.utils import trading_days  # noqa: E402\n", "\n", "sDate = '2025-04-01'\n", "today = datetime.date.today().strftime(\"%Y-%m-%d\")\n", "cn_tddays = pd.date_range(sDate, today).astype(str).tolist()\n", "\n", "# cn_tddays = trading_days.load(region_product='cn_ashare', start_datetime=sDate, end_datetime=today)\n", "\n", "# with Parallel(n_jobs=1, backend='multiprocessing') as para:\n", "#     _ = para(delayed(run_1day)(date) for date in cn_tddays)\n", "\n", "# with Parallel(n_jobs=1, backend=\"multiprocessing\") as para:\n", "#     _ = para(delayed(run_1day_fcov)(date) for date in tqdm(cn_tddays))\n", "\n", "# for i in range(1, len(cn_tddays)):\n", "#     date = cn_tddays[i-1] # update with last date\n", "#     run_1day(date)\n", "#     run_1day_fcov(date)\n", "\n", "date = cn_tddays[-2]\n", "run_1day(date)\n", "run_1day_fcov(date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 126, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### universe"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### univserse1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %load /mnt/sda/NAS/ShareFolder/lishuanglin/share/to_wangxiang/gathered/update_barrauniverse_20241118.py\n", "import datetime, os\n", "from shennong.utils import trading_days\n", "from joblib import Parallel, delayed\n", "import pandas as pd\n", "universe_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/universe/'\n", "barra_product = 'GEM3'\n", "\n", "def get_1day_full_universe(barra_product: str, date: str):\n", "    info_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/'\n", "    filepath = f'{info_root}{barra_product}/{date}.parquet'\n", "    if not os.path.exists(filepath):\n", "        raise FileExistsError(f'file not exists: {filepath}')\n", "    a = pd.read_parquet(filepath)\n", "    cols = ['BarraID', 'Country_of_Exposure', 'LocalID','MIC', 'Price', \\\n", "            'Capt', 'USDxrate','DailyVolume',]\n", "    a = a[cols].rename(columns={'Capt': 'MarketCap','Country_of_Exposure': 'MarketExposure'})\n", "    a['Turnover'] = a['Price'] * a['DailyVolume']\n", "    a['Turnover'] = a['Turnover'] * a['USDxrate'] / 100.0 \n", "    a['MarketCap'] = a['MarketCap'] * a['USDxrate'] / 100.0 \n", "    a['date'] = date\n", "    a = a.drop(columns=['Price','DailyVolume','USDxrate'])\n", "    # print(f'loading {date} finished', end='\\r')\n", "    return a.loc[a['Turnover'] > 0.0].drop_duplicates()\n", "\n", "\n", "def update_1day(date):\n", "    today = datetime.date.today().strftime('%Y-%m-%d')\n", "    dates = trading_days.load(region_product='cn_ashare', \\\n", "        start_datetime='2023-01-01',end_datetime=today)\n", "\n", "    idx = dates.index(date)\n", "    dates = dates[idx-150:idx+1]\n", "    last_10_dates = dates[-11:-1]\n", "\n", "\n", "    with Parallel(n_jobs=10, backend='multiprocessing') as para:\n", "        results = para(delayed(get_1day_full_universe)(barra_product, date) for date in dates)\n", "\n", "    print('loading finished')\n", "\n", "    a = pd.concat(results)\n", "    result = a.groupby(by='BarraID').agg({\n", "        'MarketExposure':'last',\n", "        'LocalID':'last',\n", "        'MIC':'last',\n", "        'Turnover': lambda x: x[-126:].mean(),\n", "        'MarketCap':lambda x: x[-126:].mean(),\n", "        }).reset_index()\n", "        \n", "    result1 = result.loc[(result['Turnover'] > 1.0e6) & \\\n", "        (result['MarketCap'] > 2.0e8),'BarraID'].tolist()\n", "    \n", "    print('calculating finished')\n", "\n", "    last_10_universes = []\n", "    for date_ in last_10_dates:\n", "        dt = pd.read_csv(f'{universe_root}{date_}.csv')\n", "        dt['date'] = date_\n", "        last_10_universes.append(dt)\n", "\n", "    last_10_universes = pd.concat(last_10_universes)#.groupby('BarraID').count().sort_values('MIC')\n", "\n", "    b = last_10_universes.loc[~last_10_universes['BarraID'].isin(result['BarraID'])].groupby('BarraID').count()\n", "    last_tobe_kept = b[b['MIC'] < 10].index.tolist()\n", "\n", "    universe = result1 + last_tobe_kept\n", "    universe = result.loc[result['BarraID'].isin(universe)].iloc[:,:-2].reset_index(drop=True)\n", "    return universe.loc[~universe['MIC'].isnull()]\n", "\n", "\n", "today = datetime.date.today().strftime('%Y-%m-%d')\n", "tmp_dates = trading_days.load(region_product='cn_ashare', \\\n", "    start_datetime='2024-11-01',end_datetime=today)\n", "\n", "# for date in tmp_dates[:]:\n", "#     if os.path.exists(f'{universe_root}{date}.csv'): continue\n", "#     universe = update_1day(date)\n", "#     universe.to_csv(f'{universe_root}{date}.csv',index=False)\n", "#     print(f'universe updated for {date}')\n", "\n", "date = tmp_dates[-2]\n", "print('updating',date)\n", "if os.path.exists(f'{universe_root}{date}.csv'): \n", "    print(f'universe already generated for {date}')\n", "    pass\n", "else:\n", "    universe = update_1day(date)\n", "    universe.to_csv(f'{universe_root}{date}.csv',index=False)\n", "    print(f'universe updated for {date}')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### universe2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%writefile '/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/update_universe.py'\n", "# coding = utf-8\n", "import datetime, os\n", "import pandas as pd\n", "from tqdm.auto import tqdm\n", "from joblib import Parallel, delayed\n", "from shennong.utils import trading_days\n", "\n", "universe_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/universe/'\n", "os.makedirs(universe_root, exist_ok=True)\n", "\n", "barra_product = 'GEM3'  ## corresponding global\n", "\n", "## get global info for daily universe calculation\n", "def get_1day_info(barra_product: str, date: str):\n", "\tinfo_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/'\n", "\tid_map_root = '/mnt/sda/NAS/Global/mapping/barra_mapping/'\n", "\tfilepath = f'{info_root}{barra_product}/{date}.parquet'\n", "\tif not os.path.exists(filepath):\n", "\t\traise FileExistsError(f'file not exists: {filepath}')\n", "\tcols = ['BarraID', 'Country_of_Exposure', 'LocalID','MIC', 'OpMIC', 'Price', \\\n", "\t\t\t'Capt', 'USDxrate','DailyVolume', 'Country_ISO3','ISIN']\n", "\ta = pd.read_parquet(filepath, columns=cols)\n", "\ta = a.rename(columns={'Capt': 'MarketCap','Country_of_Exposure': 'MarketExposure'})\n", "\ta['Turnover'] = a['Price'] * a['DailyVolume']\n", "\ta['Turnover'] = a['Turnover'] * a['USDxrate'] / 100.0 \n", "\ta['MarketCap'] = a['MarketCap'] * a['USDxrate'] / 100.0 \n", "\ta['date'] = date\n", "\ta = a.drop(columns=['Price','DailyVolume','USDxrate'])\n", "\t# print(f'loading {date} finished', end='\\r')\n", "\ta = a.loc[a['Turnover'] > 0.0].drop_duplicates()\n", "\ta = a.loc[~(a['OpMIC'].isna() | a['LocalID'].isna())]\n", "\n", "\tid_map = pd.read_csv(f'{id_map_root}/{date}.csv', usecols=['Barrid', 'Root_BARRA_ID'])\n", "\tid_map = id_map.loc[id_map['Barrid'].isin(a['BarraID'])]\n", "\tid_map = id_map.rename(columns={'Root_BARRA_ID': 'RootID', 'Barrid': 'BarraID'})\n", "\treturn pd.merge(a, id_map, on='BarraID', how='left')\n", "\n", "\n", "\n", "## udpate daily global universe\n", "def get_global_universe(date: str, lookback: int=126, keep_days: int=10):\n", "\ttoday = datetime.date.today().strftime('%Y-%m-%d')\n", "\tdates = pd.bdate_range('2012-01-01', today).astype(str).tolist()\n", "\tidx = dates.index(date)\n", "\tdates = dates[idx-lookback-10:idx] ## the lastest date should be excluded\n", "\n", "\twith Parallel(n_jobs=1, backend='multiprocessing') as para:\n", "\t\tresults = para(delayed(get_1day_info)(barra_product, date) for date in dates)\n", "\tprint('loading finished',end='\\r')\n", "\n", "\ta = pd.concat(results, axis=0)\n", "\n", "\tresult = a.groupby(by='BarraID').agg({\n", "\t\t'MarketExposure':'last',\n", "\t\t'LocalID':'last',\n", "\t\t'MIC':'last',\n", "\t\t'OpMIC': 'last',\n", "\t\t'Country_ISO3': 'last',\n", "\t\t'ISIN': 'last',\n", "\t\t'date': 'last',\n", "\t\t'RootID': 'last',\n", "\t\t'Turnover': lambda x: x[-lookback:].mean(), \n", "\t\t'MarketCap':lambda x: x[-lookback:].mean(),\n", "\t\t}).reset_index()\n", "\t\t\n", "\t## mean daily turnover and market cap filtering\n", "\tresult1 = result.loc[(result['Turnover'] > 1.0e6) & (result['MarketCap'] > 2.0e8), 'BarraID'].tolist()\n", "\tprint('calculating finished',end='\\r')\n", "\n", "\n", "\t## filter out if not in universe over keep_days\n", "\tlatest_dates = dates[-30:-1] ## more days to cover long holidays\n", "\tlatest_univs = []\n", "\tfor date_ in latest_dates:\n", "\t\tif os.path.exists(f'{universe_root}{date_}.csv'):\n", "\t\t\tdt = pd.read_csv(f'{universe_root}{date_}.csv')\n", "\t\t\tdt['date'] = date_\n", "\t\t\tlatest_univs.append(dt)\n", "\n", "\tif len(latest_univs)>0:\n", "\t\tlatest_univs = pd.concat(latest_univs) # only keep last keep_days universe files\n", "\t\tb = latest_univs.loc[~latest_univs['BarraID'].isin(result['BarraID'])]\n", "\t\t## only count last kepp_days records\n", "\t\tb = b.groupby('BarraID', sort=False).apply(lambda x: x[-keep_days:].count())\n", "\t\tlast_tobe_kept = b[b['Country_ISO3'] < keep_days].index.tolist()\n", "\telse:\n", "\t\tlast_tobe_kept = []\n", "\n", "\tuniverse = result1 + last_tobe_kept\n", "\tuniverse = result.loc[result['BarraID'].isin(universe)].iloc[:,:-2].reset_index(drop=True)\n", "\treturn universe\n", "\n", "\n", "def update_daily(date: str, lookback: int=126, keep_days: int=10, debug: bool=False):\n", "\tif debug:\n", "\t\tuniverse = get_global_universe(date, lookback=lookback, keep_days=keep_days)\n", "\t\tprint(f'universe updated for {date}',end='\\r')\n", "\t\treturn universe\n", "\telse:\n", "\t\tif os.path.exists(f'{universe_root}global/{date}.csv'): \n", "\t\t\tprint(f'universe already generated for {date}')\n", "\t\t\treturn None\n", "\t\tuniverse = get_global_universe(date, lookback=lookback, keep_days=keep_days)\n", "\t\tuniverse.to_csv(f'{universe_root}global/{date}.csv',index=False)\n", "\t\tfor group, country_set in univ_sets.items():\n", "\t\t\tif group == 'global': continue\n", "\t\t\tuniv_ = universe.loc[universe['Country_ISO3'].isin(country_set)].reset_index(drop=True)\n", "\t\t\tif len(univ_) > 0: ## only save valid universe\n", "\t\t\t\tuniv_.to_csv(f'{universe_root}{group}/{date}.csv', index=False)\n", "\t\tprint(f'universe updated for {date}',end='\\n')\n", "\n", "\n", "univ_sets = {\n", "\t'global': [],\n", "\t'EU1': [\"AUT\", \"B<PERSON>\", \"CHE\", \"CZE\", \"DEU\", \"DNK\", \"ESP\", \"FIN\", \"FRA\", \"GBR\", \"GRC\", \n", "\t\t \"HUN\", \"IRL\", \"ITA\", \"NLD\", \"NOR\", \"POL\", \"PRT\", \"SWE\"],\n", "\t'cn_ashare': ['CHN']\n", "}\n", "\n", "for group in univ_sets.keys():\n", "\tif not os.path.exists(f'{universe_root}{group}/'): \n", "\t\tos.makedirs(f'{universe_root}{group}/', exist_ok=True)\n", "\n", "## all possible businiess days for global trading days\n", "today = datetime.date.today().strftime('%Y-%m-%d')\n", "dates = pd.bdate_range('2016-01-01',today).astype(str).tolist()\n", "\n", "# for date in dates[:]: \n", "# \tuniverse = update_daily(date, debug=False)\n", "\n", "with Parallel(n_jobs=10, backend='loky') as para:\n", "\tresults = para(delayed(update_daily)(date, debug=False) for date in tqdm(dates))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "rawbase", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}