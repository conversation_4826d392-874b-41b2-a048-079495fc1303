# %%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/Loader.py'
from shennong.utils import trading_days, symbol
import polars as pl
import pandas as pd, numpy as np
import matplotlib.pyplot as plt

import os, h5py
from joblib import Parallel, delayed

class Loader:
    def __init__(self, **kwargs):
        self.__dict__.update(kwargs)

    def load_style_indus(self, barr_product: str, 
                         feature_index: pd.MultiIndex, 
                         barrainfo_path=None):
        dates = sorted(feature_index.get_level_values('date').unique().tolist())
        syms = sorted(feature_index.get_level_values('symbol').unique().tolist())
        localids = [s.split('.')[0] for s in syms]
        maps = {localid: sym for localid, sym in zip(localids, syms)}
        start_date, end_date = dates[0], dates[-1]

        from joblib import Parallel, delayed
        import warnings
        warnings.filterwarnings('ignore')

        barrainfo_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/'
        id_map_root = '/mnt/sda/NAS/ShareFolder/xielan/global/id_mapping/'

        if barrainfo_path is None:
            barrainfo_path = f'{barrainfo_root}{barr_product}/'

        def load_1day(date):
            df_info = pd.read_parquet(f'{barrainfo_path}{date}.parquet')
            df_style_indus = df_info.loc[df_info['LocalID'].isin(localids), usecols+['LocalID']]
            df_style_indus['LocalID'] = df_style_indus['LocalID'].apply(lambda x: maps[x])
            df_style_indus['date'] = date
            df_style_indus.rename(columns={'LocalID':'symbol'},inplace=True)
            df_style_indus = df_style_indus.set_index(['date','symbol']).drop_duplicates(keep='first')
            # print(f'loading df_style, {date}', end='\r')
            return df_style_indus.loc[df_style_indus.index.isin(feature_index)]

        dates = sorted([s.split('.')[0] for s in os.listdir(barrainfo_path)])
        dates = [d for d in dates if d >= start_date and d <= end_date]
        df_info = pd.read_parquet(f'{barrainfo_path}{dates[0]}.parquet')
        usecols = df_info.columns[df_info.columns.str.startswith(f'{barr_product}')].tolist() + ['indcell']

        num_workers = max(len(dates) // 50, 1)
        with Parallel(n_jobs=num_workers, backend='loky') as para:
            df_info = para(delayed(load_1day)(date) for date in dates)
        df_info = pd.concat(df_info, axis=0).dropna(how='all')
        df_info = df_info[~df_info.index.duplicated(keep='first')]
        return (df_info, df_info[['indcell']])


    def load_label(self, barra_product: str,
                   start_date: str,
                   end_date: str,
                   label_list=['H0','H1','H1_5'],
                   usecols=['SpecificReturn','DlyReturn%']):
        import warnings
        from joblib import Parallel, delayed
        import re
        import itertools
        warnings.filterwarnings('ignore')
        barrainfo_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/'
        usecols_info = ['LocalID','OpMIC']+usecols

        dates = sorted([s.split('.')[0] for s in os.listdir(f'{barrainfo_root}{barra_product}/')])
        cdates = [d for d in dates if d >= start_date and d <= end_date]
        start, end = max(dates.index(cdates[0])-5, 0), dates.index(cdates[-1])+5
        dates = dates[start:end]

        def load_1day(date):
            if not os.path.exists(f'{barrainfo_root}{barra_product}/{date}.parquet'):
                return None
            df_info = pd.read_parquet(f'{barrainfo_root}{barra_product}/{date}.parquet')[usecols_info]
            df_info.dropna(subset=['OpMIC','LocalID'],inplace=True)
            df_info['symbol'] = df_info['LocalID'] + '.' + df_info['OpMIC']
            df_info['date'] = date
            df_info = df_info[['date','symbol']+usecols]
            # print(f'loading label {date}', end='\r')
            return df_info.set_index(['date','symbol']).drop_duplicates()

        num_workers = max(len(dates) // 40, 1)
        with Parallel(n_jobs=num_workers, backend='loky') as para:
            labels = para(delayed(load_1day)(date) for date in dates)

        df_label = pd.concat(labels) #.rename(columns={usecol:'H0'})
        df_label = df_label[~df_label.index.duplicated(keep='first')]
        label_list=[i for i in label_list if bool(re.match(r'^H\d+(?:_\d+)?$',i))] # label必须满足H+number或H+numbe_number格式

        def label_func(df,label,usecol):# 对usecols进行label转换
            if bool(re.match(r'H(\d+)$', label)): # 如果label是H0则自动补齐为H0_0
                match = re.match(r'H(\d+)$', label)
                new_label = f"H{match.group(1)}_{match.group(1)}"
            else:new_label = label
            match = re.match(r'^H(\d+)_(\d+)$', new_label) # 识别buy_lag,start_lag,得到window和lag
            window = int(match.group(2))-int(match.group(1))+1
            lag = -int(match.group(2))
            result = df.groupby(level='symbol').apply(lambda x: x.rolling(window,min_periods=1).mean()\
                .shift(lag)).droplevel(-1).swaplevel('symbol','date')
            result.name = f'{usecol}_{label}'
            return result
        
        grids = list(itertools.product(label_list, usecols))
        with Parallel(n_jobs=len(grids), backend='loky') as para:
            result_label = para(delayed(label_func)(df_label[i[1]],i[0],i[1]) for i in grids)
        result_label=pd.concat(result_label,axis=1).reindex(df_label.index)

        return result_label.loc[start_date:end_date]


    def load(self, load_path: str, 
             start_date: str, 
             end_date: str, 
             *, 
             file_format='.csv', 
             pred=False,
             feature_list=None):
        from joblib import Parallel, delayed
        import warnings
        warnings.filterwarnings('ignore')
        
        def load_1day(date):
            if not os.path.exists(f'{load_path}/{date}{file_format}'): 
                return None
            if file_format == '.csv':
                feature_1day = pd.read_csv(f'{load_path}/{date}.csv')
            elif file_format == '.parquet':
                feature_1day = pd.read_parquet(f'{load_path}/{date}.parquet')
            elif file_format == '.feather':
                feature_1day = pd.read_feather(f'{load_path}/{date}.feather')
            elif file_format == '.pkl':
                feature_1day = pd.read_pickle(f'{load_path}/{date}.pkl')

            feature_1day['date'] = date
            print(f'loading feature {date}', end='\r')
            return feature_1day if feature_list is None else feature_1day[feature_list]

        possible_symbol_cols = ['symbol','code','fullcode', 'ticker_exchange_factset']
        if os.path.isdir(load_path):
            if file_format != '.h5':
                dates = sorted([s.split('.')[0] for s in os.listdir(load_path) if s.endswith(file_format)])
                dates = [dt for dt in dates if dt >= start_date and dt <= end_date]
                if len(dates) == 0: 
                    raise ValueError(f'no date in {load_path} between {start_date} and {end_date}')

                num_workers = max(len(dates) // 200, 1)
                with Parallel(n_jobs=num_workers, backend='loky') as para:
                    results = para(delayed(load_1day)(date) for date in dates)
                df_feature = pd.concat([s for s in results if s is not None])

                if 'symbol' not in df_feature.index.names:
                    assert len(set(possible_symbol_cols)\
                            .intersection(df_feature.columns.str.lower()))>0,\
                                f'[symbol | code | fullcode] ticker_exchange_factset | should be in column names'
                    sym_col = [col for col in df_feature.columns if col.lower() in possible_symbol_cols][0]
                    df_feature = df_feature.rename(columns={sym_col:'symbol'})
                    df_feature = df_feature.set_index(['date','symbol']).sort_index()
                else:
                    df_feature = df_feature.reset_index().set_index(['date','symbol']).sort_index()
            
            elif file_format == '.h5':
                from shennong.stk import bar
                key_list = self.key_list if feature_list is None else {self.key_list: feature_list}
                a = bar.load(region_product=self.region_product,
                             freq=self.freq,
                             load_root=self.feature_root,
                             start_datetime=start_date,
                             end_datetime=end_date,
                             key_list=key_list,
                             verbose=False).transpose('DATETIME', 'SYMBOL', 'KEY')
                dates = a['DATETIME'].dt.date.astype(str).to_numpy()
                syms = [s.replace('.SZ','.XSHE').replace('.SH','.XSHG') for 
                        s in a['SYMBOL'].to_numpy()]
                keys = a['KEY'].to_numpy()
                dt = a.values.reshape(len(dates)*len(syms),len(keys))
                index = pd.MultiIndex.from_product([dates,syms],names=['date','symbol'])
                df_feature = pd.DataFrame(dt,index=index,columns=keys).dropna(how='all').sort_index()

        elif os.path.isfile(load_path):
            df_feature = pd.read_csv(load_path)
            if not pred:
                assert 'symbol' in df_feature.columns, 'symbol should be in column names'
                assert 'date' in df_feature.columns, 'date should be in column names'
                df_feature = df_feature.set_index(['date','symbol']).sort_index()
            elif pred:
                df_feature = df_feature.set_index('date').stack()
                return df_feature.to_frame(name='pred').rename_axis(['date','symbol'])
        else:
            raise ValueError('load_path should be folder or file path')

        return df_feature.loc[start_date:end_date]



import pandas as pd
pd.read_parquet('/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/EUE4/2024-08-29.parquet').columns



%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/backtest/Preprocessor.py'

import warnings
import pandas as pd
import numpy as np
import os, time, datetime, h5py

def add_univer_mask(df: pd.DataFrame, cfg: dict):
    warnings.filterwarnings('ignore')
    barraids = df.index.get_level_values('BarraID')
    date = df.name
    df = df.droplevel(0)

    factor_group_name = cfg.get('factor_group_name', None)
    if factor_group_name is None:
        raise ValueError('factor_group_name is None')

    def load_daily_universe(date):
        # universe_root = f'/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/universe/{factor_group_name}/'
        universe_root = f'/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/universe/global/'
        universe = pd.read_csv(f'{universe_root}{date}.csv')
        return universe['BarraID'].tolist()

    try:
        universe = load_daily_universe(date)
        df['universe_mask'] = barraids.isin(universe).astype(np.float64)
    except:
        df['universe_mask'] = 0.0
    return df


def calc_upper_lower(df: pd.DataFrame):
    median_ = df.median()
    mad = df.sub(median_, axis=1).abs().median()
    upper, lower = median_ + 3 * mad, median_ - 3 * mad
    return (upper, lower)


def rm_outlier(df: pd.DataFrame):
    upper, lower = calc_upper_lower(df)
    # df = df.where(df <= upper, upper, axis=1)\
    #     .where(df >= lower, lower, axis=1)
    df = df.mask(df >= upper, upper, axis=1).mask(df <= lower, lower, axis=1)
    return df


def neutralize_(df_factor, df_style):
    from sklearn.linear_model import LinearRegression
    df_factor = df_factor.copy()
    df_factor = df_factor.loc[df_factor['universe_mask']>0].drop('universe_mask',axis=1)
    df_style = df_style.loc[df_style.index.isin(df_factor.index)]

    types = df_style.dtypes
    cats = types[types=='object'].index.tolist()
    if len(cats) > 0:
        df_style = pd.get_dummies(df_style,columns=cats,drop_first=False).astype(np.float64)

    rows = df_factor.index.intersection(df_style.index)
    df_factor, df_style = df_factor.loc[rows], df_style.loc[rows]
    style_columns = df_style.columns.tolist()

    df_full = pd.concat([df_style, df_factor], axis=1)
    df_full[np.isnan(df_full)] = 0.0
    
    def neut(df: pd.DataFrame, x_columns: list[str]):
        x = df[x_columns]
        y = df[[s for s in df.columns if s not in x_columns]]
        lr = LinearRegression()
        lr.fit(x, y)
        return y.sub(lr.predict(x))
    df_neutral = df_full.groupby(level='date').apply(lambda df: neut(df, style_columns)).droplevel(0)
    df_neutral.columns = ['neutral_' + x for x in df_neutral.columns]
    return df_neutral.groupby('date').apply(add_univer_mask)


def neutralize(df_factor: pd.DataFrame, df_style: pd.DataFrame, option='indus_and_style'):
    """
    df_factor: factor dataframe;
    df_style: first column as indcell, others as style columns;
    option: 
        1. indus: for industry only neutralization;
        2. style: for style only neutralization;
        3. indus_and_style: for industry and style neutralization at the same time
        4. indus_and_style_by_country: option 3 for each country
    """
    from sklearn.linear_model import LinearRegression
    df_factor = df_factor.copy()
    df_style = df_style.loc[df_style.index.isin(df_factor.index)]
    rows = df_factor.index.intersection(df_style.index)
    df_factor, df_style = df_factor.loc[rows], df_style.loc[rows]

    types = df_style.dtypes
    cats = types[types=='object'].index.tolist()

    if option == 'indus' and len(cats)>0:               ## convert indecell to dummy industry columns
        df_style = pd.get_dummies(df_style[['indcell']],columns=cats, drop_first=False).astype(np.float64)
    elif option == 'style':
        df_style = df_style.iloc[:, 1:]
    elif option.startswith('indus_and_style') and len(cats)>0:  ## convert indecell to dummy industry columns
        df_style = pd.get_dummies(df_style,columns=cats,drop_first=False).astype(np.float64)

    df_full = pd.concat([df_style, df_factor], axis=1)
    df_full[np.isnan(df_full)] = 0.0
    
    def neut(df: pd.DataFrame, x_columns: list[str]):
        x = df[x_columns]
        y = df[[s for s in df.columns if s not in x_columns]]
        lr = LinearRegression()
        lr.fit(x, y)
        return y.sub(lr.predict(x))
    
    def neut_by_country(df: pd.DataFrame, x_columns: list[str]):
        '''left for neutralization by count for option=4'''
        pass

    style_columns = df_style.columns.tolist()
    df_neu = df_full.groupby(level='date').apply(lambda df: neut(df, style_columns)).droplevel(0)
    df_neu.columns = ['neu_' + x for x in df_neu.columns]
    return df_neu


pd.read_csv('/mnt/sda/NAS/ShareFolder/xielan/global/universe/2023-01-03.csv')

%%writefile '/mnt/sda/home/<USER>/working/gitrepo/euronext_4xl/Evaluator.py'
import pandas as pd
import numpy as np

class Evaluator:
	def init(self, config):
		self.__dict__.update(config)

	def calc_alpha(self, df_factor: pd.DataFrame, df_label: pd.DataFrame):
		
		df_factor = df_factor.loc[df_factor['universe_mask']>0].drop('universe_mask',axis=1)
		df_label = df_label.loc[df_label['universe_mask']>0].drop('universe_mask',axis=1)
		cindex = df_factor.index.intersection(df_label.index)
		cdates = cindex.get_level_values('date').unique()

		df_alpha = []
		for lname in df_label.columns:
			results = {}
			for fname in df_factor.columns[:]:
				results[fname] = []
				for date in cdates:
					f_ = df_factor.loc[date][fname].copy()
					l_ = df_label.loc[date][lname].copy()
					f_ -= f_.mean()
					f_ /= f_.abs().sum()
					l_.index.name = f_.index.name
					alpha = (f_ * l_).sum()
					results[fname].append(alpha)
				results[fname] = pd.Series(results[fname],name=fname,index=cdates)

			alpha = pd.concat(results, axis=1)
			mindex = pd.MultiIndex.from_product([cdates,df_factor.columns])
			df_alpha.append(pd.Series(alpha.values.reshape(-1),index=mindex,name=lname))
		return pd.concat(df_alpha, axis=1).rename_axis(['date', 'factor'])
	
	def calc_ic(self, df_factor: pd.DataFrame, df_label: pd.DataFrame, corr_method='spearman'):
		df_factor = df_factor.loc[df_factor['universe_mask']>0].drop('universe_mask',axis=1)
		df_label = df_label.loc[df_label['universe_mask']>0].drop('universe_mask',axis=1)
		df_full = pd.concat([df_factor, df_label], axis=1)
		
		# ic
		df_ic = df_full.groupby(level='date').corr(method=corr_method)
		return df_ic.loc[(slice(None), df_factor.columns), df_label.columns].rename_axis(['date', 'factor'])
	
	def calc_indus_exposure(self, df_factor: pd.DataFrame, df_indus: pd.DataFrame):

		df_new = df_factor.copy()
		cindex = df_new.index.intersection(df_indus.index)
		df_new.loc[cindex, 'indus'] = df_indus.loc[cindex, 'indcell'].values
		df_new = df_new.dropna(subset=['indus'])
		df_new = df_new.loc[df_new['universe_mask']>0].drop('universe_mask',axis=1)


		def each_industry(df):
			indus = df.name
			a = df.agg(['max', 'min'])
			a.index  = ['factor_max', 'factor_min']
			b = df.groupby(level='date').mean().agg(['max', 'min'])
			b.index = ['mean_max', 'mean_min']
			return pd.concat([a, b])

		return df_new.groupby('indus').apply(each_industry).rename_axis(['industry','eval'])
	
	def calc_intra_corr(self, df_factor: pd.DataFrame, df_style: pd.DataFrame, corr_method='spearman'):
		df_factor = df_factor.loc[df_factor['universe_mask']>0].drop('universe_mask',axis=1)

		types = df_style.dtypes
		cats = types[~(types=='object')].index.tolist()
		df_style = df_style[cats]

		rows = df_factor.index.intersection(df_style.index)
		df_factor, df_style = df_factor.loc[rows], df_style.loc[rows]
		style_columns = df_style.columns.tolist()

		df_full = pd.concat([df_style, df_factor], axis=1)
		
		df_ic = df_full.groupby(level='date').corr(method=corr_method)
		return df_ic.loc[(slice(None), df_factor.columns), df_style.columns].rename_axis(['date', 'factor'])



%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/backtest/Stabilizer.py'
import pandas as pd
import numpy as np
import polars as pl
# from yrqtlib.utils import tools


def npcorr4(a: np.ndarray, 
            b: np.ndarray, 
            amask: np.ndarray, 
            bmask: np.ndarray):
    cnt = ((~amask)&(~bmask)).sum(axis=0)
    meanx = a.sum(axis=0)/cnt 
    meany = b.sum(axis=0)/cnt 
    cov = (a * b).sum(axis=0)/cnt - meanx*meany 
    x_sqrd, y_sqrd = a**2, b**2
    stdx = np.sqrt(x_sqrd.sum(axis=0)/cnt - meanx**2)
    stdy = np.sqrt(y_sqrd.sum(axis=0)/cnt - meany**2)
    return np.true_divide(cov,stdx*stdy)


## correlation computation using numpy vectorization
def corrTwoDf(f: pd.DataFrame, 
              l: pd.DataFrame, 
              axis='row', 
              method='spearman'):
    if axis == 'row': a, b = f.copy().T, l.copy().T
    elif axis == 'column': a, b = f.copy(), l.copy()

    rows, cols = a.index.intersection(b.index), a.columns.intersection(b.columns)
    a, b = a.loc[rows, cols], b.loc[rows, cols]
    index = a.columns
    amask, bmask = np.isnan(a), np.isnan(b)
    a[bmask], b[amask] = np.nan, np.nan
    if method == 'spearman': 
        a = pl.LazyFrame(a.values).with_columns(pl.all().rank()).collect().to_numpy()
        b = pl.LazyFrame(b.values).with_columns(pl.all().rank()).collect().to_numpy()
    a[amask|bmask], b[amask|bmask] = 0.0, 0.0 # set to 0 for summation
    return pd.Series(npcorr4(a, b, amask, bmask), index=index)


class Stabilizer:
    def init(self, config):
        self.__dict__.update(config)
    
    def ACF_cs(self, df_factor: pd.DataFrame, nlags: int=20):
        def acf(col):
            tmp = df_factor[col].unstack(1)
            corrs = [corrTwoDf(tmp, tmp.shift(i), axis='row').mean() for i in range(1,nlags+1)]
            return pd.Series(corrs, index=range(1,nlags+1))

        df_factor = df_factor.copy()
        df_factor = df_factor.loc[df_factor['universe_mask']>0].drop('universe_mask',axis=1)
        acfs = [acf(col) for col in df_factor.columns]
        df_acf = pd.concat(acfs, axis=1,keys=df_factor.columns)
        df_acf.index.name = 'nlags'
        return df_acf
    

    def ACF_1day(self, df_factor: pd.DataFrame):
        df_factor = df_factor.copy()
        df_factor = df_factor.loc[df_factor['universe_mask']>0].drop('universe_mask',axis=1)
        df_factor = df_factor.loc[~df_factor.index.duplicated()] ## filter out duplicated records
        dates = sorted(df_factor.index.get_level_values('date').unique().tolist())

        corrs = {}
        for i, date in enumerate(dates):
            if i == 0: continue
            two_dates = dates[i-1:i+1]
            smp = df_factor.loc[two_dates].swapaxes(0,1).stack('BarraID')
            corrs[date] = smp.groupby(level=0).apply(lambda x: x.corr().iloc[0,1])
            
        return pd.concat(corrs,axis=1, names=['date']).T


    def hist(self, df_factor: pd.DataFrame):
        df_factor = df_factor.loc[df_factor['universe_mask']>0].drop('universe_mask',axis=1)
        return df_factor.reset_index(drop=True)
    

    def coverage(self, df_factor: pd.DataFrame):
        df_factor = df_factor.loc[df_factor['universe_mask']>0].drop('universe_mask',axis=1)
        return df_factor.groupby(level='date').apply(lambda x: x.count()/len(x))


    def valid_counts(self, df_factor: pd.DataFrame):
        df_factor = df_factor.loc[df_factor['universe_mask']>0].drop('universe_mask',axis=1)
        return df_factor.groupby(level='date').apply(lambda x: x.count())


    def quantile(self, df_factor: pd.DataFrame, qtile_ticks: list):
        def qtile(df: pd.DataFrame):
            mean_, std_ = df.mean(), df.std()
            tmp = pd.concat([mean_, mean_+std_, mean_-std_], axis = 1, \
                            keys=['mean','mean_plus_std','mean_minus_std']).T
            qtile = df.quantile(qtile_ticks)
            return pd.concat([tmp, qtile], axis = 0)
        
        # turnover
        df_factor = df_factor.loc[df_factor['universe_mask']>0].drop('universe_mask',axis=1)
        df_factor = df_factor.copy()
        return df_factor.groupby('date').apply(qtile).rename_axis(['date', 'eval'])
        


%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/backtest/reporter.py'
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import warnings, os
from matplotlib.backends.backend_pdf import PdfPages
from multiprocessing import Lock
lock = Lock()


plt.rcParams['figure.figsize'] = (12,6)
plt.rcParams['axes.grid'] = True

def gen_csv(results, report_folder='./'): 
    import os
    if not os.path.exists(report_folder):
        os.makedirs(report_folder)
    for mname, metric in results.items():
        os.makedirs(os.path.join(report_folder, mname), exist_ok=True)
        for fname, df in metric.items():
            file_path = os.path.join(report_folder, mname, f'{fname}.csv')
            df.to_csv(file_path)


def box_plot(df: pd.DataFrame, figsize=(12,6)):
    fig, ax = plt.subplots(figsize=figsize)
    for i, col in enumerate(df.columns):
        max2 = df[col].iloc[0]
        min2 = df[col].iloc[1]
        max1 = df[col].iloc[2]
        min1 = df[col].iloc[3]
        
        ax.plot([i, i], [min2, max2], color='black', lw=1.5)  # Vertical line for whisker
        ax.add_patch(plt.Rectangle((i - 0.2, min1), 0.4, max1 - min1, fill=True, 
                                   color='lightblue', edgecolor='blue', zorder=2))

        ax.hlines(max2, i - 0.15, i + 0.15, color='black', lw=1.5)  # Bar at max2
        ax.hlines(min2, i - 0.15, i + 0.15, color='black', lw=1.5)  # Bar at min2

    ax.set_xticks(range(len(df.columns)))
    ax.set_xticklabels(df.columns, rotation=30, ha='right')
    ax.set_ylabel('Values')
    plt.tight_layout()
    return fig, ax


def gen_pdf_by_metric(results, report_file='./report.pdf'): 
    from matplotlib.backends.backend_pdf import PdfPages
    warnings.filterwarnings('ignore')

    with PdfPages(report_file) as pdf:  # noqa: PLR1702
        for mname, metric in results.items():
            if mname in {'stab'}:
                for fname, df in metric.items():
                    if fname.startswith('df_acf'):
                        for factor in df.columns:
                            fig, ax = plt.subplots(figsize=(12, 6))
                            df[factor].plot(ax=ax, marker='o', grid=True)
                            ax.set_title(f'ACF-{factor}')
                            pdf.savefig(fig,bbox_inches='tight')
                            plt.close(fig)
                    elif fname.startswith('df_hist'):
                        for factor in df.columns:
                            fig, ax = plt.subplots(figsize=(12, 6))
                            df[factor].hist(ax=ax, bins=100)
                            ax.set_title(f'Histogram-{factor}')
                            pdf.savefig(fig,bbox_inches='tight')
                            plt.close(fig)
                    elif fname.startswith('df_quantile'):
                        for factor in df.columns:
                            fig, ax = plt.subplots(figsize=(12, 6))
                            df[factor].unstack(1).plot(ax=ax, grid=True, label=True)
                            ax.set_title(f'Quantile-{factor}')
                            pdf.savefig(fig,bbox_inches='tight')
                            plt.close(fig)
                    elif fname.startswith('df_cov'):
                        for factor in df.columns:
                            fig, ax = plt.subplots(figsize=(12, 6))
                            df[factor].plot(ax=ax, grid=True)
                            ax.set_title(f'COVERAGE-{factor}')
                            pdf.savefig(fig,bbox_inches='tight')
                            plt.close(fig)
            if mname in {'raw_eva', 'neu_eva'}:
                for fname, df in metric.items():
                    if fname in {'df_alpha', 'df_ic', 'df_corr'}:
                        for factor in df.index.get_level_values(1).unique():
                            if fname == 'df_alpha':
                                title = f'ALPHA-{factor}'
                            elif fname == 'df_ic':
                                title = f'IC-{factor}'
                            elif fname == 'df_corr':
                                title = f'CORR-{factor}'
                            fig, ax = plt.subplots(figsize=(12, 6))
                            tmp = df.loc[(slice(None),factor),:].droplevel(1)
                            if 'corr' not in fname:
                                tmp.cumsum().plot(ax=ax, grid=True, label=True)
                            else:
                                tmp.plot(ax=ax, grid=True, label=True)
                            ax.set_title(title)
                            pdf.savefig(fig,bbox_inches='tight')
                            plt.close(fig)
                    if fname in {'df_exposure'}:
                        for factor in df.columns:
                            # fig, ax = plt.subplots(figsize=(12, 6))
                            tmp = df[factor].unstack(1).T
                            fig, ax = box_plot(tmp, figsize=(12,6))
                            # tmp.boxplot(ax=ax, grid=True, rot=45)
                            ax.set_title(f'EXPOSURE-{factor}')
                            pdf.savefig(fig,bbox_inches='tight')
                            plt.close(fig)


def _plot_acf_2pdf(pdf, se, figsize=(12, 6), desc='ACF'):
    fig, ax = plt.subplots(figsize=figsize)
    se.plot(ax=ax, marker='o', grid=True)
    ax.set_title(f'{desc}-{se.name}')
    pdf.savefig(fig,bbox_inches='tight')
    plt.close(fig)


def _plot_hist_2pdf(pdf, se, figsize=(12, 6), desc=''):
    fig, ax = plt.subplots(figsize=(12, 6))
    se.hist(ax=ax, bins=100)
    ax.set_title(f'Histogram-{se.name} {desc}')
    pdf.savefig(fig,bbox_inches='tight')
    plt.close(fig)


def _plot_qtile_2pdf(pdf, se, figsize=(12, 6)):
    fig, ax = plt.subplots(figsize=(12, 6))
    se.unstack(1).plot(ax=ax, grid=True, label=True)
    ax.set_title(f'Quantile-{se.name}')
    pdf.savefig(fig,bbox_inches='tight')
    plt.close(fig)


def _plot_cov_2pdf(pdf, se, figsize=(12, 6), desc='COVERAGE'):
    fig, ax = plt.subplots(figsize=(12, 6))
    se.plot(ax=ax, grid=True)
    ax.set_title(f'{desc}-{se.name}')
    pdf.savefig(fig,bbox_inches='tight')
    plt.close(fig)


def _plot_ts_2pdf(pdf, se, fname, factor, figsize=(12, 6)):
    fig, ax = plt.subplots(figsize=figsize)
    if fname == 'df_corr':
        se.plot(ax=ax, grid=True, label=True)
    else:
        se.cumsum().plot(ax=ax, grid=True, label=True)
    if fname == 'df_alpha':
        title = f'ALPHA-{factor}'
        tmp = se.cumsum()
        ann = (se.sum() * 250 / len(tmp)).to_numpy()
        std = (se.std() * np.sqrt(250)).to_numpy()
        shp = ann / std
        title = f'{title}  \
            Ann: {ann[0]: .2%}, {ann[1]: .2%}\n \
            Std: {std[0]: .2%}, {std[1]: .2%} \
            Sharpe: {shp[0]: .3f}, {shp[1]: .3f}\n\
            '
    elif fname == 'df_ic':
        mean_ = se.mean().tolist()
        std_ = se.std().tolist()
        title = f'IC-{factor} \
            Mean: {mean_[0]: .3f}, {mean_[1]: .3f} \
            Std: {std_[0]: .3f}, {std_[1]: .3f}'
    elif fname == 'df_corr':
        title = f'CORR-{factor}'

    ax.set_title(title)
    pdf.savefig(fig,bbox_inches='tight')
    plt.close(fig)


def _plot_exposure_2pdf(pdf, se, figsize=(12, 6)):
    tmp = se.unstack(1).T
    fig, ax = box_plot(tmp, figsize=(12,6))
    ax.set_title(f'EXPOSURE-{se.name}')
    pdf.savefig(fig,bbox_inches='tight')
    plt.close(fig)


_plot_map = {
    'df_acf': _plot_acf_2pdf,
    'df_acf1': _plot_acf_2pdf,
    'df_hist': _plot_hist_2pdf,
    'df_hist2': _plot_hist_2pdf,
    'df_quantile': _plot_qtile_2pdf,
    'df_cov': _plot_cov_2pdf,
    'df_non_nan': _plot_cov_2pdf,
    'df_alpha': _plot_ts_2pdf,
    'df_ic': _plot_ts_2pdf,
    'df_corr': _plot_ts_2pdf,
    'df_exposure': _plot_exposure_2pdf,
    }


def gen_pdf_by_factor(results, report_folder='./report/'): 
    from matplotlib.backends.backend_pdf import PdfPages
    if not os.path.exists(report_folder):
        os.makedirs(report_folder, exist_ok=True)

    warnings.filterwarnings('ignore')
    factors = results['stab']['df_acf'].columns.tolist()

    for factor in factors:  # noqa: PLR1702
        # with lock:
        #     if not os.path.exists(f'{report_folder}by_factor/'):
        #         os.makedirs(f'{report_folder}by_factor/')
        with PdfPages(f'{report_folder}{factor}.pdf') as pdf:  # noqa: PLR1702
            for mname, metric in results.items():
                if mname in {'stab'}:
                    for fname, df in metric.items():
                        if fname == 'df_hist2':
                            _plot_map[fname](pdf, df[factor], figsize=(12,6),desc='winsorized')
                        elif fname == 'df_acf1':
                            _plot_map[fname](pdf, df[factor], figsize=(12,6),desc='LAG_1DAY_ACF')
                        elif fname == 'df_non_nan':
                            _plot_map[fname](pdf, df[factor], figsize=(12,6),desc='VALID_COUNTS')
                        else:
                            _plot_map[fname](pdf, df[factor], figsize=(12, 6))
                if mname in {'raw_eva', 'neu_eva'}:
                    nfactor = f'neutral_{factor}' if mname == 'neu_eva' else factor
                    for fname, df in metric.items():
                        if fname in {'df_alpha', 'df_ic', 'df_corr'}:
                            se = df.loc[(slice(None),nfactor),:].droplevel(1)
                            _plot_map[fname](pdf, se, fname, nfactor, figsize=(12, 6))
                        if fname in {'df_exposure'}:
                            _plot_map[fname](pdf, df[nfactor], figsize=(12, 6))


def calc_ann_alpha(df):
    df = df.copy()
    years = df.index.get_level_values('date').unique().str.split('-').str[0].unique().tolist()

    ann_results = {'return':{}, 'sharpe':{}}
    for year in years:
        df_year = df.loc[df.index.get_level_values('date').str.startswith(year)]

        ann_results['return'][year] = df_year.mean() * 250
        ann_results['sharpe'][year] = df_year.mean() * 250 / (df_year.std()*np.sqrt(250))

    ann_results['return']['all_years'] = df.mean() * 250
    ann_results['sharpe']['all_years'] = df.mean() * 250 / (df.std()*np.sqrt(250))
        
    for key, value in ann_results.items():
        ann_results[key] = pd.concat(value, axis=1, names=['year']).T
    
    return pd.concat(ann_results, axis=0, names=['annual'])


def calc_ann_ic(df):
    df = df.copy()
    years = df.index.get_level_values('date').unique().str.split('-').str[0].unique().tolist()

    ann_results = {'mean':{}, 'icir':{}}

    for year in years:
        df_year = df.loc[df.index.get_level_values('date').str.startswith(year)]
        ann_results['mean'][year] = df_year.mean()
        ann_results['icir'][year] = df_year.mean() / df_year.std()

    ann_results['mean']['all_years'] = df.mean()
    ann_results['icir']['all_years'] = df.mean() / df.std()

    for key, value in ann_results.items():
        ann_results[key] = pd.concat(value, axis=1, names=['year']).T
        
    return pd.concat(ann_results, axis=0, names=['annual'])


def annual_report(result, report_folder='./'):
    evas, reports = ['raw_eva'], {}
    if 'neu_eva' in result.keys():
        evas = ['raw_eva', 'neu_eva']
    
    for eva in evas:
        reports[eva] = {}
        for metric in ['df_alpha', 'df_ic']:
            df = result[eva][metric]
            if metric == 'df_ic':
                reports[eva][metric] = df.groupby(level=1).apply(calc_ann_ic)
            elif metric == 'df_alpha':
                reports[eva][metric] = df.groupby(level=1).apply(calc_ann_alpha)
            else:
                pass
        reports[eva] = pd.concat(reports[eva], axis=0, names=['metric'])
    reports = pd.concat(reports, axis=0, names=['eva'])
    reports.to_csv(f'{report_folder}annual_report.csv')
    return reports


def report_summary(annual_report, report_folder='./report/'):
    ret = annual_report.xs(('all_years','return','df_alpha'), level=('year','annual','metric')).droplevel(0)
    shp = annual_report.xs(('all_years','sharpe','df_alpha'), level=('year','annual','metric')).droplevel(0)
    mean = annual_report.xs(('all_years','mean','df_ic'), level=('year','annual','metric')).droplevel(0)
    icir = annual_report.xs(('all_years','icir','df_ic'), level=('year','annual','metric')).droplevel(0)
    
    ret = ret.sort_values(by='SpecificReturn_H1', key=abs)
    shp,mean, icir = shp.loc[ret.index], mean.loc[ret.index], icir.loc[ret.index]    
    
    results = {'ret': ret, 'shp': shp, 'mean': mean, 'icir': icir}

    with PdfPages(report_folder + 'summary.pdf') as pdf:
        for key, df in results.items():
            fig, ax = plt.subplots()
            plt.gca().xaxis.set_label_position('top')
            width_per_row = 0.25
            df.plot.barh(figsize=(8,len(ret)*width_per_row),grid=True,ax=ax)
            ax.yaxis.tick_right()
            ax.xaxis.tick_top()
            ax.set_title(key)
            plt.legend(loc='center left')
            pdf.savefig(fig,bbox_inches='tight')
            plt.close(fig)


%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/backtest.py'
# coding = utf-8
import sys, argparse, logging
import os, json, pickle, time

os.environ['NUMEXPR_MAX_THREADS'] = '250'

import pandas as pd
import numpy as np
from barra.Evaluator import Evaluator
from barra.Stabilizer import Stabilizer
from barra.Logger import Logger
from barra.Loader import Loader
import barra.Preprocessor as prep

from barra import reporter
from shennong.stk import bar
from shennong.utils import key_group_manager as sn_km
from joblib import Parallel, delayed

parser = argparse.ArgumentParser()
parser.add_argument('--config', type=str, default='./config/')
parser.add_argument('--timetag', type=str, default=time.strftime('%Y%m%d_%H%M'))
args = parser.parse_args()


def load_config(config_filepath: str):
	logger.info(f'loading config from {config_filepath}')
	with open(config_filepath, 'r', encoding='utf-8') as f:
		return json.load(f)


def work_flow(df_factor, df_label, df_style, df_indus):
	eva, stab = Evaluator(), Stabilizer()
	xchg = df_factor.index.get_level_values('symbol')[-1].split('.')[-1]

	logger.info(f'calculation for {xchg} starts.')

	df_acf = stab.ACF_cs(df_factor, 20)
	df_hist = stab.hist(df_factor)
	df_cov = stab.coverage(df_factor)
	df_quantile = stab.quantile(df_factor, [0.01, 0.15, 0.5, 0.85, 0.99])
	logger.info('feature stability calculation done!')

	df_factor = df_factor.groupby(level='date').apply(prep.rm_outlier).droplevel(0)
	df_hist2 = stab.hist(df_factor)

	df_factor = df_factor.groupby('date').apply(prep.add_univer_mask)
	logger.info('feature outlier removed!')

	df_alpha = eva.calc_alpha(df_factor, df_label)
	df_ic = eva.calc_ic(df_factor, df_label, corr_method)
	df_exposure = eva.calc_indus_exposure(df_factor, df_indus)
	df_corr = eva.calc_intra_corr(df_factor, df_style, corr_method)
	logger.info('raw feature evaluation done!')

	df_neutral = prep.neutralize(df_factor, df_style)
	df_neutral = df_neutral.groupby('date').apply(prep.add_univer_mask)

	df_alpha_ = eva.calc_alpha(df_neutral, df_label)
	df_ic_ = eva.calc_ic(df_neutral, df_label, corr_method)
	df_exposure_ = eva.calc_indus_exposure(df_neutral, df_indus)
	df_corr_ = eva.calc_intra_corr(df_neutral, df_style, corr_method)
	logger.info('neutralized evaluation done!')

	return {
		'stab': {'df_acf': df_acf, 'df_hist': df_hist, 'df_hist2': df_hist2, 'df_cov': df_cov, 'df_quantile': df_quantile},
		'raw_eva': {'df_alpha': df_alpha, 'df_ic': df_ic, 'df_exposure': df_exposure, 'df_corr': df_corr},
		'neu_eva': {'df_alpha': df_alpha_, 'df_ic': df_ic_, 'df_exposure': df_exposure_, 'df_corr': df_corr_},
	}


def run_1group(feature, df_label, df_style, df_indus, group_name: str, group_xchgs: list, partition=0):
	if group_xchgs is None:
		return None
	if len(group_xchgs) == 0:
		return None

	group_xchgs = [s.upper() for s in group_xchgs]

	if group_name == 'all_market':
		f, l, style, indus = feature, df_label, df_style, df_indus
	else:
		f = feature.loc[feature.index.get_level_values('symbol').str.split('.').str[-1].isin(group_xchgs)]
		l = df_label.loc[df_label.index.get_level_values('symbol').str.split('.').str[-1].isin(group_xchgs)]
		style = df_style.loc[df_style.index.get_level_values('symbol').str.split('.').str[-1].isin(group_xchgs)]
		indus = df_indus.loc[df_indus.index.get_level_values('symbol').str.split('.').str[-1].isin(group_xchgs)]
	logging.info(f'\n{group_name}: {f.shape}, {l.shape}, {style.shape}, {indus.shape}')
	if f.shape[0] * l.shape[0] * style.shape[0] * indus.shape[0] == 0:
		logger.info('one of feature, label, style, indus is empty, do nothing, exit!')
		return None
	if (len(f) == 0) | (len(l) == 0):
		return None
	result = work_flow(f, l, style, indus)

	report_folder = f'./report/{save_tag}_{time_tag}/{group_name}_{partition}/'
	reporter.gen_csv(result, report_folder=report_folder)
	logger.info(f'csv report generated for ({group_name})')
	reporter.gen_pdf_by_factor(result, report_folder=report_folder)
	logger.info(f'pdf report generated for ({group_name})')
	ann_report = reporter.annual_report(result, report_folder=report_folder)
	reporter.report_summary(ann_report, report_folder=report_folder)


def run_task(rank):
	feature_list = feature_name_lists[int(rank)]
	logger.info(f'loading features [num_feature {len(feature_list)}]')
	for f in feature_list[:]: logger.info(f)
	feature = loader.load(feature_root, start_date, end_date, 
					   file_format=feature_file_format, pred=pred,feature_list=feature_list)

	if np.isinf(feature).sum().sum() > 0:
		logger.info('feature contains inf or -inf, replace with NAN')
		feature.replace([np.inf, -np.inf], np.nan, inplace=True)

	num_opmic = len(feature.index[0][-1].split('.')[-1])
	if num_opmic == 3:
		nidx = [(s[0], s[1].replace('.', '.X')) for s in feature.index]
		feature.index = pd.Index(nidx).rename(['date', 'symbol'])

	FEATURE_COUNTS = len(feature.columns)
	logger.info(f'loading feature done, feature shape: {feature.shape}')
	# logger.info(f'feature names: {feature.columns.tolist()}')

	if 'universe_mask' not in feature.columns:
		feature = feature.groupby('date').apply(prep.add_univer_mask)
	logger.info(f'adding universe mask done, feature shape: {feature.shape}')

	## step 3: load style factors and indus
	df_style, df_indus = loader.load_style_indus(barra_product, feature.index)
	logger.info(f'loading style and indus factors done')

	## step 4: calculate label
	df_label = (
		loader.load_label(barra_product, start_date, end_date, 
					label_list=label_list, usecols=label_usecols) / 100.0
	)
	logger.info(f'loading label done, label shape: {df_label.shape}')

	# all_market = []
	# for value in groups.values():
	# 	all_market.extend(value)
	# groups['all_market'] = all_market

	if 'universe_mask' not in df_label.columns:
		df_label = df_label.groupby('date').apply(prep.add_univer_mask)
	logger.info(f'adding universe mask done, df_label shape: {df_label.shape}')

	for key, value in groups.items():
		run_1group(feature, df_label, df_style, df_indus, key, value, partition=rank)

	# num_workers = len(groups)
	# with Parallel(n_jobs=num_workers, backend='loky') as para:
	# 	_ = para(
	# 		delayed(run_1group)(feature, df_label, df_style, df_indus, key, value) for key, value in groups.items()
	# 	)


logger = Logger()
config_filepath = args.config
time_tag = args.timetag
save_tag = config_filepath.split('/')[-1].split('.')[0]

params = load_config(config_filepath)
loader = Loader(**params)

barra_product = params['barra_product']
start_date, end_date = params['start_date'], params['end_date']
feature_file_format = params['feature_file_format']
groups = params['groups']
label_list = params['label_list']
label_usecols = params['label_usecols']
pred = params['pred']
corr_method = params['correlation_method']

feature_root = params['feature_root']
region_product = params['region_product']
freq = params['freq']
key_list = params['key_list']


feature_names = sn_km.get_key_list(freq=freq,
						  key_group=key_list,
						  region_product=region_product,
						  load_root=feature_root)

logger.info(f'feature_names: {feature_names}')
feature_name_lists = []
batch_feature_num = 20

if len(feature_names) % batch_feature_num == 0:
	num_array = len(feature_names) // batch_feature_num
else:
	num_array = len(feature_names) // batch_feature_num + 1 


for i in range(0,len(feature_names),batch_feature_num):
	feature_name_lists.append(feature_names[i:i+batch_feature_num])  # noqa: PERF401

if num_array <= 1 and params['use_slurm']:
	logger.info(f'num_feature ({len(feature_names)}) less than 20, no need to use slurm!')
	params['use_slurm'] = False

if __name__ == '__main__':
	if not params['use_slurm']:
		with Parallel(n_jobs=4, backend='multiprocessing') as para:
			_ = para(delayed(run_task)(i) for i in range(len(feature_name_lists[:])))

	elif params['use_slurm']:
		import socket
		job_id = os.environ["SLURM_ARRAY_JOB_ID"]
		rank = os.environ["SLURM_ARRAY_TASK_ID"]
		size = os.environ["SLURM_ARRAY_TASK_COUNT"]
		name = socket.gethostname()
		print(time_tag, __name__,job_id,rank,size,name,'###########\n\n')
		run_task(rank)



%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/backtest2.py'
# coding = utf-8

import os
import time
import socket
import subprocess
import numpy as np
import pandas as pd
import barra.utils.processor as prep

from barra.data import loader
from barra.utils import tools
from barra.utils import calendar
from barra.backtest import reporter
from barra.backtest.Evaluator import Evaluator
from barra.backtest.Stabilizer import Stabilizer


def work_flow(df_factor, df_label, df_style, df_indus):
    eva, stab = Evaluator(), Stabilizer()
    tools.Logger.info(f"calculation for {factor_name} starts.", end="\n")

    df_acf = stab.ACF_cs(df_factor, 20)
    df_acf1 = stab.ACF_1day(df_factor)

    df_hist = stab.hist(df_factor)
    df_cov = stab.coverage(df_factor)
    df_non_nan = stab.valid_counts(df_factor)

    df_quantile = stab.quantile(df_factor, [0.01, 0.15, 0.5, 0.85, 0.99])
    tools.Logger.info("feature stability calculation done!", end="\n")

    df_factor = df_factor.groupby(level="date").apply(prep.winsorize).droplevel(0)
    df_hist2 = stab.hist(df_factor)

    df_factor = df_factor.groupby("date").apply(lambda x: prep.add_univer_mask(x, cfg))
    tools.Logger.info("feature winsorized", end="\n")

    df_alpha = eva.calc_alpha(df_factor, df_label)
    df_ic = eva.calc_ic(df_factor, df_label, corr_method)
    df_exposure = eva.calc_indus_exposure(df_factor, df_indus)
    df_corr = eva.calc_intra_corr(df_factor, df_style, corr_method)
    tools.Logger.info("raw feature evaluation done!", end="\n")

    if not cfg.get("backtest_neu"):
        return {
            "stab": {
                "df_acf": df_acf,
                "df_acf1": df_acf1,
                "df_hist": df_hist,
                "df_hist2": df_hist2,
                "df_cov": df_cov,
                "df_non_nan": df_non_nan,
                "df_quantile": df_quantile,
            },
            "raw_eva": {
                "df_alpha": df_alpha,
                "df_ic": df_ic,
                "df_exposure": df_exposure,
                "df_corr": df_corr,
            },
        }
    else:
        df_neutral = prep.neutralize(df_factor, df_style)
        df_neutral = df_neutral.groupby("date").apply(
            lambda x: prep.add_univer_mask(x, cfg)
        )

        df_alpha_ = eva.calc_alpha(df_neutral, df_label)
        df_ic_ = eva.calc_ic(df_neutral, df_label, corr_method)
        df_exposure_ = eva.calc_indus_exposure(df_neutral, df_indus)
        df_corr_ = eva.calc_intra_corr(df_neutral, df_style, corr_method)
        tools.Logger.info("neutralized evaluation done!", end="\n")

        return {
            "stab": {
                "df_acf": df_acf,
                "df_hist": df_hist,
                "df_hist2": df_hist2,
                "df_cov": df_cov,
                "df_quantile": df_quantile,
            },
            "raw_eva": {
                "df_alpha": df_alpha,
                "df_ic": df_ic,
                "df_exposure": df_exposure,
                "df_corr": df_corr,
            },
            "neu_eva": {
                "df_alpha": df_alpha_,
                "df_ic": df_ic_,
                "df_exposure": df_exposure_,
                "df_corr": df_corr_,
            },
        }


def run_1group(feature, df_label, df_style, df_indus):
    f_, l_, style, indus = feature, df_label, df_style, df_indus
    tools.Logger.info(
        f"{factor_name}: {f_.shape}, {l_.shape}, {style.shape}, {indus.shape}", end="\n"
    )
    if f_.shape[0] * l_.shape[0] * style.shape[0] * indus.shape[0] == 0:
        tools.Logger.info(
            "one of feature, label, style, indus is empty, do nothing, exit!", end="\n"
        )
        return None
    if (len(f_) == 0) | (len(l_) == 0):
        return None
    result = work_flow(f_, l_, style, indus)

    report_folder = cfg.get("report_folder")
    reporter.gen_csv(result, report_folder=report_folder)
    tools.Logger.info(f"csv report generated for ({factor_group_name})", end="\n")
    reporter.gen_pdf_by_factor(result, report_folder=report_folder)
    tools.Logger.info(f"pdf report generated for ({factor_group_name})", end="\n")
    ann_report = reporter.annual_report(result, report_folder=report_folder)
    # reporter.report_summary(ann_report, report_folder=report_folder)


def run_task(cfg: dict, factor_list: list = []):
    tools.Logger.info(f"loading factor: {factor_name}")
    sDate, eDate = cfg.get("sDate"), cfg.get("eDate")
    floader = loader.FactorLoader("./configs/user.path.json")

    factor_saveroot = cfg.get("factor_saveroot")
    feature = floader.load(
        sDate=sDate,
        eDate=eDate,
        factor_name=cfg.get("factor_name"),
        factor_group_name=cfg.get("factor_group_name"),
        factor_type=cfg.get("factor_type"),
        backfill=cfg.get("backfill"),
        factor_root=f"{factor_saveroot}",
    )
    feature = feature.loc[~feature.index.duplicated()]  ## remove duplicated entries
    feature = prep.check_universe_mask(feature, cfg)

    ## if feature contains inf or -inf, replace with NAN
    if np.isinf(feature).sum().sum() > 0:
        tools.Logger.info("feature contains inf or -inf, replace with NAN")
        feature.replace([np.inf, -np.inf], np.nan, inplace=True)

    ## load industry and style factors for neutralization and exposure calculation
    dloader = loader.DataLoader()
    df_indus_style = dloader.load_indus_style_factors(sDate, eDate, cfg)
    df_indus_style = df_indus_style.loc[~df_indus_style.index.duplicated()]
    tools.Logger.info("loading style and indus factors done")

    ## load label for alpha calculation
    df_label, risk = loader.load_label(sDate, eDate, cfg)
    df_label = df_label.loc[~df_label.index.duplicated()]
    df_label = prep.check_universe_mask(df_label, cfg)
    tools.Logger.info(f"loading label done, label shape: {df_label.shape}")

    ## df_indus_style, first column is indcell factor as string, rest are indus factors as float
    run_1group(
        feature, df_label, df_indus_style.iloc[:, 1:], df_indus_style.iloc[:, [0]]
    )


if tools.is_notebook():
    prefix = "/mnt/sda/home/<USER>/working/gitrepo/barra_demo/"
    config_filepath = prefix + "configs/backtest/xielan.puda.json"
    time_tag = time.strftime("%Y%m%d_%H%M%S")
    save_tag = "xielan.puda"
    user_path = prefix + "configs/user.path.json"
    country_path = prefix + "configs/country.set.json"
else:
    ## reading command line arguments, config file path and time tag
    args = tools.parse_dynamic_args()
    assert hasattr(args, "config"), "config file path must be provided!"
    config_filepath = args.config
    time_tag = (
        args.timetag if hasattr(args, "timetag") else time.strftime("%Y%m%d_%H%M%S")
    )
    save_tag = config_filepath.split("/")[-1].split(".json")[0]
    user_path = "./configs/user.path.json"
    country_path = "./configs/country.set.json"


## load backtest config file
cfg = loader.load_config(config_filepath)
factor_group_name = cfg.get("factor_group_name")
factor_name = cfg.get("factor_name")
corr_method = cfg.get("correlation_method")

## dynamically add factor_saveroot and report_folder to cfg for user convenience
user_root = tools.parse_user_root(user_path)
cfg["factor_saveroot"] = f"{user_root}factor/{cfg.get('barra_product')}/"
cfg["ctry_list"] = loader.load_config(country_path)[cfg.get("country_set")]
cfg["report_folder"] = (
    f"{cfg.get('factor_saveroot')}{factor_group_name}/{factor_name}/report_{time_tag}/"
)
cfg['backfill'] = None
ip = socket.gethostbyname(socket.gethostname())
factor_names = loader.peek_factor_names(cfg)
if len(factor_names) > 30: # need to generate slurm script
    if 'SLURM_JOB_ID' in os.environ: 
        print(os.environ["SLURM_JOB_ID"], os.environ["SLURM_ARRAY_TASK_ID"], flush=True)
    num_array = int(np.ceil(len(factor_names) / 30))
    factor_names_list = np.array_split(factor_names, num_array)
    task_map = {str(i + 1): factor_names_list[i] for i in range(num_array)}


def main():
    if cfg["use_slurm"]: 
        ## generate slurm script if factor number is more than 30
        if ('SLURM_JOB_ID' not in os.environ): # not in slurm cluster
            if len(factor_names) > 30: # need to generate slurm script
                assert ip == '************', "slurm scripts should be generated on SLURM MANAGER (************)"
                pyargs = f'{os.path.abspath(__file__)} --config {config_filepath} --timetag {time_tag}'
                job_name = 'bt2_' + cfg.get('factor_name')
                script = tools.generate_slurm_commands("AMD_R", 60, num_array, pyargs=pyargs, job_name=job_name)
                print(script)
                subprocess.run(["sbatch"],input=script.encode("utf-8"),check=True)
            else:
                if ip == '************': # not in slurm cluster, but running on SLURM MANAGER
                    tools.Logger.info('No tasks to be run on SLURM MANAGER, exit!',end='\n')
                    exit()
                else: # not in slurm cluster, and not running on SLURM MANAGER
                    run_task(cfg, factor_list=factor_names)
        elif 'SLURM_JOB_ID' in os.environ: # already in slurm cluster, run separate task
            run_task(cfg, factor_list=task_map[os.environ["SLURM_ARRAY_TASK_ID"]])
    else:  ## run locally
        run_task(cfg, factor_list=factor_names)


if __name__ == "__main__":
    main()


import os
import pandas as pd
pd.read_parquet('/mnt/sda/NAS/ShareFolder/pengpuda/factor_analysis/euronext/SP/features/EUTR/buysideInst/EU1/raw/2024-01-02.parquet')

os.listdir('/mnt/sda/NAS/ShareFolder/pengpuda/factor_analysis/euronext/SP/features/EUTR/SP_SET_RAW/')

%%writefile '/mnt/sda/home/<USER>/working/slurm/test.py'
import os
import socket
import subprocess


def main():
    if "SLURM_JOB_ID" in os.environ:
        run_core_computation()
    else:
        submit_via_stdin()


def submit_via_stdin():
    # 构建 Slurm 脚本内容

    slurm_script = generate_slurm_commands("AMD_R", ncpus=1, array=5)
    # 通过管道直接提交（无需落盘）
    subprocess.run(
        ["sbatch"],
        input=slurm_script.encode("utf-8"),  # 必须编码为字节流
        check=True,  # 检查命令执行结果
    )


def generate_slurm_commands(
    partition: str, ncpus: int, array: int, mem_per_cpu: int = 4
):
    slurm_script = f"""#!/bin/bash
#SBATCH --job-name=direct_submit
#SBATCH --output=slurm_log/%A_%a.out
#SBATCH --partition={partition}
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task={ncpus}
#SBATCH --mem-per-cpu={mem_per_cpu}G
#SBATCH --array=1-{array}

source /mnt/sda/NAS/ShareFolder/lishuanglin/share/miniconda3/etc/profile.d/conda.sh
conda activate barra_base

python {os.path.abspath(__file__)} --config {config_filepath} --timetag {time_tag}
"""
    return slurm_script


def run_core_computation():
    print("核心计算执行中...")
    print(socket.gethostname())
    print(socket.gethostbyname(socket.gethostname()))
    for env in os.environ:
        if env.startswith("SLURM"):
            print(env, os.environ[env])


if __name__ == "__main__":
    main()







%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/fg.py'

import os
import sys
import time
import json
import socket
import argparse
from shennong.utils import key_group_manager as sn_km

parser = argparse.ArgumentParser()
parser.add_argument("--config", type=str, default="./config/")
args = parser.parse_args()


def load_config(config_filepath: str):
    print(f"loading config from {config_filepath}")
    with open(config_filepath, "r", encoding="utf-8") as f:
        return json.load(f)


config_filepath = args.config
save_tag = config_filepath.split("/")[-1].split(".")[0]
time_tag = time.strftime("%Y%m%d_%H%M%S")

params = load_config(config_filepath)
freq = params["freq"]
key_list = params["key_list"]
region_product = params["region_product"]
feature_root = params["feature_root"]

feature_names = sn_km.get_key_list(
    freq=freq, key_group=key_list, region_product=region_product, load_root=feature_root
)

feature_name_lists = []
batch_feature_num = 20

if len(feature_names) % batch_feature_num == 0:
    num_array = len(feature_names) // batch_feature_num
else:
    num_array = len(feature_names) // batch_feature_num + 1

use_slurm = params["use_slurm"]

if use_slurm:
    ip = socket.gethostbyname(socket.gethostname())
    assert ip=='************', "slurm jobs should be allocated on SLURM MANAGER (************)"
    
    if not os.path.exists("./tmp/"):
        os.makedirs("./tmp/", exist_ok=True)

    with open(f"./tmp/{save_tag}_{time_tag}.sh", "w") as f:
        f.write("#!/bin/bash\n")
        f.write(f"#SBATCH -o ./tmp/slurm_log/{save_tag}_{time_tag}/%A_%a.out\n")
        f.write("#SBATCH --partition=AMD_R\n")
        f.write("#SBATCH --nodes=1\n")
        f.write("#SBATCH --ntasks-per-node=1\n")
        f.write("#SBATCH --cpus-per-task=60\n")
        f.write("#SBATCH --mem=100G\n")
        f.write(f"#SBATCH --array=0-{num_array - 1}\n\n")

        f.write(
            "source /mnt/sda/NAS/ShareFolder/lishuanglin/share/miniconda3/etc/profile.d/conda.sh\n"
        )
        f.write("conda activate barra_base\n")
        f.write(
            'echo -e "slurm_id: ${SLURM_ARRAY_TASK_ID} \\nconda_path: `which conda`"\n'
        )
        f.write('echo -e "script_path: ./backtest.py"\n')
        f.write(
            f"python backtest.py --config ./configs/{save_tag}.json --timetag {time_tag}\n"
        )

    print(
        f"slurm script genereated [./tmp/{save_tag}_{time_tag}.sh]: {os.path.exists(f'./tmp/{save_tag}_{time_tag}.sh')}"
    )
    os.system(f"sbatch ./tmp/{save_tag}_{time_tag}.sh")

elif not use_slurm:
    os.system(f"python ./backtest.py --config {config_filepath} --timetag {time_tag}")



%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/fg2.py'
# coding = utf-8
import os
import sys
import time
import json
import argparse
from barra.data import loader

parser = argparse.ArgumentParser()
parser.add_argument('--config', type=str, default='./config/')
args = parser.parse_args()


config_filepath = args.config
save_tag = config_filepath.split('/')[-1].split('.')[0]
time_tag = time.strftime('%Y%m%d_%H%M%S')

cfg = loader.load_config(config_filepath)
os.system(f"python ./backtest2.py --config {config_filepath} --timetag {time_tag}")





# %%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/fg.py'

import os
import sys
import time
import json
import socket
import argparse
from barra.data import loader

parser = argparse.ArgumentParser()
parser.add_argument("--config", type=str, default="./config/")
args = parser.parse_args()


config_filepath = args.config
save_tag = config_filepath.split("/")[-1].split(".")[0]
time_tag = time.strftime("%Y%m%d_%H%M%S")

params = loader.load_config(config_filepath)
feature_root = params["feature_root"]
batch_feature_num = 20

if len(feature_names) % batch_feature_num == 0:
    num_array = len(feature_names) // batch_feature_num
else:
    num_array = len(feature_names) // batch_feature_num + 1

use_slurm = params["use_slurm"]

if use_slurm:
    ip = socket.gethostbyname(socket.gethostname())
    assert ip=='************', "slurm jobs should be allocated on SLURM MANAGER (************)"
    
    if not os.path.exists("./tmp/"):
        os.makedirs("./tmp/", exist_ok=True)

    with open(f"./tmp/{save_tag}_{time_tag}.sh", "w") as f:
        f.write("#!/bin/bash\n")
        f.write(f"#SBATCH -o ./tmp/slurm_log/{save_tag}_{time_tag}/%A_%a.out\n")
        f.write("#SBATCH --partition=AMD_R\n")
        f.write("#SBATCH --nodes=1\n")
        f.write("#SBATCH --ntasks-per-node=1\n")
        f.write("#SBATCH --cpus-per-task=60\n")
        f.write("#SBATCH --mem=100G\n")
        f.write(f"#SBATCH --array=0-{num_array - 1}\n\n")

        f.write(
            "source /mnt/sda/NAS/ShareFolder/lishuanglin/share/miniconda3/etc/profile.d/conda.sh\n"
        )
        f.write("conda activate barra_base\n")
        f.write(
            'echo -e "slurm_id: ${SLURM_ARRAY_TASK_ID} \\nconda_path: `which conda`"\n'
        )
        f.write('echo -e "script_path: ./backtest.py"\n')
        f.write(
            f"python backtest.py --config ./configs/{save_tag}.json --timetag {time_tag}\n"
        )

    print(
        f"slurm script genereated [./tmp/{save_tag}_{time_tag}.sh]: {os.path.exists(f'./tmp/{save_tag}_{time_tag}.sh')}"
    )
    os.system(f"sbatch ./tmp/{save_tag}_{time_tag}.sh")

elif not use_slurm:
    os.system(f"python ./backtest.py --config {config_filepath} --timetag {time_tag}")

# pd.read_parquet('/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/EUTR/2024-01-02.parquet')

import os
import pandas as pd
from tqdm.notebook import tqdm

# feature_root = '/mnt/sda/NAS/ShareFolder/xielan/tmpdata/otheralpha/xfc/'
# saveroot = '/mnt/sda/NAS/ShareFolder/lishuanglin/test/factor/CNTR/xielan/xfc/raw/'

feature_root = '/mnt/sda/NAS/ShareFolder/pengpuda/temp/to_xielan/feature/factset_cnashare_sample/'
saveroot = '/mnt/sda/NAS/ShareFolder/lishuanglin/test/factor/CNTR/xielan/puda/raw/'

universe_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/universe/cn_ashare/'

def from_ashare(feature_root: str, date: str):
    df = pd.read_parquet(f'{feature_root}{date}.parquet')
    df.index = df.index.str.replace('.S', '.XS')
    df.dropna(how='all', inplace=True)

    universe = pd.read_csv(f'{universe_root}{date}.csv', dtype=str)
    universe['index'] = universe['LocalID'] + '.' + universe['OpMIC']
    mgd = df.reset_index().merge(universe[['BarraID','index']], on='index', how='left').set_index('BarraID')
    return mgd.loc[~mgd.index.isna()].drop_duplicates().drop(columns=['index'])


def convert_csv2parquet(feature_root: str, date: str):
    df = pd.read_csv(f'{feature_root}{date}.csv')
    df[['LocalID','OpMIC']] = df['symbol'].str.split('.', expand=True)
    df['OpMIC'] = df['OpMIC'].map({'SZ':'XSHE', 'SH': 'XSHG'})

    univ = pd.read_csv(f'{universe_root}{date}.csv', dtype=str)
    mgd = df.merge(univ, on=['LocalID', 'OpMIC']).set_index('BarraID')
    return mgd.loc[:, df.columns[1:-2]]


dates = sorted([s.split('.')[0] for s in os.listdir(feature_root)])
if not os.path.exists(saveroot): os.makedirs(saveroot, exist_ok=True)


for date in dates:
    if os.path.exists(f'{saveroot}{date}.parquet'): continue
    try:
        df = from_ashare(feature_root, date)
    except Exception as err:
        print(err)
        continue
    df.to_parquet(f'{saveroot}{date}.parquet_020000')
    print(f'file saved for {date} / {dates[-1]}', end='\r')



pd.read_parquet('/mnt/sda/NAS/ShareFolder/lishuanglin/test/factor/CNTR/xielan/puda/raw/2024-01-02.parquet_020000')



from barra.data.loader import load_config, FactorLoader
sDate, eDate = '2022-01-01', '2022-08-30'


user_path_cfg = '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/configs/user.path.json'
# cfg = load_config('/mnt/sda/home/<USER>/working/gitrepo/barra_demo/configs/fc/fc.wmmom.eu1.01.json')

froot = '/mnt/sda/NAS/ShareFolder/lishuanglin/test/factor/CNTR/'
fd = FactorLoader(user_path_cfg)
df_factor = fd.load(sDate, eDate, 'xfc','xielan','raw',factor_root=froot)
df_factor = df_factor.loc[~df_factor.index.duplicated()]


df_factor









%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/utils/tools.py'

# coding = utf-8
__all__ = [
    "parallel",
    "parse_user_name",
    "parse_user_root",
    "parse_dynamic_args",
    "is_notebook",
    "generate_slurm_commands",
    "Logger",
]

import os
import sys
import getpass
import datetime
import argparse
from tqdm.auto import tqdm
from joblib import Parallel, delayed


## parallel execution for multiple arguments
def parallel(
    func: callable,
    list_args: list[tuple],
    njobs: int = 10,
    backend: str = "multiprocessing",
    progress_bar: bool = False,
    desc: str = "",
):
    """Executes a function in parallel across multiple arguments.

    Args:
            func (callable): Function to execute in parallel
            list_args (list[tuple]): List of argument tuples to pass to func, e.g. [('a',), ('b',)]
            njobs (int, optional): Number of parallel jobs. Defaults to 10.
            backend (str, optional): Joblib parallel backend. Defaults to 'multiprocessing'.
            progress_bar (bool, optional): Whether to show progress bar. Defaults to False.
            desc (str, optional): Description for progress bar. Defaults to ''.

    Returns:
            list: Results from parallel execution of func across list_args
    """
    list_args = tqdm(list_args, desc=desc, disable=not progress_bar)
    with Parallel(n_jobs=njobs, backend=backend) as para:
        return para(delayed(func)(*arg) for arg in list_args)


## parse user name and root
def parse_user_name():
    """parse user name from current system"""
    return getpass.getuser()


def parse_user_root(user_path: str = "./configs/user.path.json"):
    """parse user root from user_path.json, which is like:
    {
            'lishuanglin': '/mnt/sda/NAS/ShareFolder/lishuanglin/test/',
    }
    """
    from ..data import loader

    user_path_configs = loader.load_config(user_path)
    user_name = parse_user_name()
    if user_name not in user_path_configs.keys():
        raise Exception(
            f"no save path found for user {user_name} in {user_path_configs}"
        )
    else:
        return user_path_configs.get(user_name, None)


## parse dynamic command line arguments
def parse_dynamic_args():
    """
    Parses command-line arguments dynamically without pre-defining them.

    Handles arguments in the format --key value or -k value.
    Handles boolean flags like --flag or -f (assigned True).

    Typically used in python scripts whose args are passed from command line (e.g. in bash).

    Returns:
            argparse.Namespace: An object containing parsed arguments as attributes.
                                                    Keys are derived from argument names (leading hyphens removed).
                                                    Values are strings, or True for boolean flags.
    """
    args_dict = {}
    argv = sys.argv[1:]  ## the first arg is the script name
    i = 0
    while i < len(argv):
        arg = argv[i]
        if arg.startswith("-"):
            key = arg.lstrip("-")

            if i + 1 < len(argv) and not argv[i + 1].startswith("-"):
                value = argv[i + 1]
                args_dict[key] = value
                i += 2
            else:
                args_dict[key] = True
                i += 1
        else:
            print(f"Warning: Ignoring non-keyed argument '{arg}'")
            i += 1

    return argparse.Namespace(**args_dict)


def is_notebook():
    """check if the script is running in a notebook"""
    return "ipykernel" in sys.modules


def generate_slurm_commands(
    partition: str, ncpus: int, array: int, mem_per_cpu: int = 4, pyargs: str = "", job_name: str = ""
):
    """generate slurm script for parallel execution with slurm cluster
    Args:
            partition (str): slurm partition
            ncpus (int): number of cpus per task
            array (int): number of tasks to run in parallel
            mem_per_cpu (int, optional): memory per cpu. Defaults to 4.

    Returns:
            str: slurm script
    """
    slurm_script = f"""#!/bin/bash
#SBATCH --output=slurm_log/%A_%a.out
#SBATCH --partition={partition}
#SBATCH --job-name={job_name if job_name != '' else 'sbatch.sh'}
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task={ncpus}
#SBATCH --mem-per-cpu={mem_per_cpu}G
#SBATCH --array=1-{array}

source /mnt/sda/NAS/ShareFolder/lishuanglin/share/miniconda3/etc/profile.d/conda.sh
conda activate barra_base

python {pyargs}"""
    return slurm_script


class Logger:
    def __init__(self) -> None:
        pass

    @staticmethod
    def info(message: str, end: str = "\r"):
        """log info message, automatically add timestamp"""
        now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{now}] {message}", flush=True, end=end)



pd.read_parquet('/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/CNTR/2024-01-02.parquet',columns=['SpecRisk%','BarraID','LocalID','OpMIC'])

%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/utils/processor.py'
# coding = utf-8

import warnings
import numpy as np
import pandas as pd
from .tools import Logger
from sklearn.linear_model import LinearRegression


def add_univer_mask(df: pd.DataFrame, cfg: dict):
    """add universe mask for filtering
    Args:
            df (pd.DataFrame): Input dataframe with BarraID index,
                            the name of df should be date, typically used as pd.groupby.apply(add_univer_mask)
            cfg (dict): Configuration dictionary containing factor_group_name

    Returns:
            pd.DataFrame: DataFrame with added universe_mask column
    """
    warnings.filterwarnings("ignore")
    barraids = df.index.get_level_values("BarraID")
    date = df.name
    df = df.droplevel(0)

    factor_group_name = cfg.get("factor_group_name", None)
    if factor_group_name is None:
        raise ValueError("factor_group_name is None")

    def load_daily_universe(date):
        univ_root = "/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/universe/"
        universe_name = cfg.get("universe", "global")
        universe_root = f"{univ_root}{universe_name}/"
        universe = pd.read_csv(f"{universe_root}{date}.csv")
        return universe["BarraID"].tolist()

    try:
        universe = load_daily_universe(date)
        df["universe_mask"] = barraids.isin(universe).astype(np.float64)
    except:  # noqa: E722
        df["universe_mask"] = 0.0
    return df


def add_rootid_mask(df: pd.DataFrame, cfg: dict):
    """add universe mask for filtering
    Args:
            df (pd.DataFrame): Input dataframe with BarraID index,
                            the name of df should be date, typically used as pd.groupby.apply(add_univer_mask)
            cfg (dict): Configuration dictionary containing factor_group_name

    Returns:
            pd.DataFrame: DataFrame with added rootid_mask column
    """
    warnings.filterwarnings("ignore")
    barraids = df.index.get_level_values("BarraID")
    date = df.name
    df = df.droplevel(0)

    factor_group_name = cfg.get("factor_group_name", None)
    if factor_group_name is None:
        raise ValueError("factor_group_name is None")

    def filter_barraids(date):
        univ_root = "/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/universe/"
        universe_name = cfg.get("universe", "global")
        universe_root = f"{univ_root}{universe_name}/"
        universe = pd.read_csv(f"{universe_root}{date}.csv")
        return universe.loc[
            universe["BarraID"] == universe["RootID"], "BarraID"
        ].tolist()

    try:
        filtered_barraids = filter_barraids(date)
        df["rootid_mask"] = barraids.isin(filtered_barraids).astype(np.float64)
    except:  # noqa: E722
        df["rootid_mask"] = 0.0
    return df


def check_universe_mask(df: pd.DataFrame, cfg: dict):
    """check if universe mask is already added, if not, add it
    Args:
            df (pd.DataFrame): Input dataframe with MultiIndex (date, BarraID)
            cfg (dict): Configuration dictionary containing factor_group_name
    Returns:
            pd.DataFrame: DataFrame with added universe_mask column
    """

    if "universe_mask" not in df.columns:
        df = df.groupby("date").apply(lambda x: add_univer_mask(x, cfg))
        Logger.info("adding universe mask done")
    return df

def check_rootid_mask(df: pd.DataFrame, cfg: dict):
    """check if rootid mask is already added, if not, add it
    Args:
            df (pd.DataFrame): Input dataframe with MultiIndex (date, BarraID)
            cfg (dict): Configuration dictionary containing factor_group_name
    Returns:
            pd.DataFrame: DataFrame with added rootid_mask column
    """

    if "rootid_mask" not in df.columns:
        df = df.groupby("date").apply(lambda x: add_rootid_mask(x, cfg))
        Logger.info("adding rootid mask done")
    return df

def as_weight(df: pd.DataFrame):
    """convert raw factor as weight (score), typically used in groupby.apply
    Args:
            df (pd.DataFrame): Input dataframe

    Returns:
            pd.DataFrame: Normalized weights that sum to 1
    """
    return df.div(df.abs().sum())


def demean(df: pd.DataFrame):
    """demean each column of df, typically used in groupby.apply
    Args:
            df (pd.DataFrame): Input dataframe

    Returns:
            pd.DataFrame: Demeaned dataframe
    """
    return df.sub(df.mean())


def zscore(df: pd.DataFrame):
    """standardize each column of df to a normal distribution, typically used in groupby.apply
    Args:
            df (pd.DataFrame): Input dataframe

    Returns:
            pd.DataFrame: Standardized dataframe with mean 0 and std 1
    """
    return df.sub(df.mean()).div(df.std())


def winsorize_with_mad(df: pd.DataFrame, sigma: int = 4):
    """winsorize each column of df using median absolute deviation to recap outliers, typically used in groupby.apply
    Args:
            df (pd.DataFrame): Input dataframe
            sigma (int): Number of MAD for winsorization bounds. Defaults to 4.

    Returns:
            pd.DataFrame: Winsorized dataframe using median absolute deviation
    """
    median_ = df.median()
    mad = df.sub(median_).abs().median()
    upper = median_ + sigma * mad
    lower = median_ - sigma * mad
    return df.mask(df >= upper, upper, axis=1).mask(df <= lower, lower, axis=1)


def winsorize_with_std(df: pd.DataFrame, sigma: int = 4):
    """winsorize each column of df using standard deviation to recap outliers, typically used in groupby.apply
    Args:
            df (pd.DataFrame): Input dataframe
            sigma (int): Number of std for winsorization bounds. Defaults to 4.

    Returns:
            pd.DataFrame: Winsorized dataframe using standard deviation
    """
    mean_, std_ = df.mean(), df.std()
    upper = mean_ + sigma * std_
    lower = mean_ - sigma * std_
    return df.mask(df >= upper, upper, axis=1).mask(df <= lower, lower, axis=1)


def winsorize_by_pct(df: pd.DataFrame, percentile: float = 0.01):
    """winsorize each column of df using percentiles to recap outliers, typically used in groupby.apply
    Args:
            df (pd.DataFrame): Input dataframe
            percentile (float): Percentile for winsorization bounds. Defaults to 0.01 could work fine in most cases.

    Returns:
            pd.DataFrame: Winsorized dataframe using percentiles
    """
    df_ = df.copy()
    upper = df_.quantile(1 - percentile)
    lower = df_.quantile(percentile)
    return df_.mask(df_ >= upper, upper, axis=1).mask(df_ <= lower, lower, axis=1)


def winsorize(
    df: pd.DataFrame,
    method: str = "median_mad",
    sigma: int = 4,
    percentile: float = 0.01,
):
    """winsorize each column of df using specified method and bounds to recap outliers, typically used in groupby.apply
    Args:
            df (pd.DataFrame): Input dataframe
            method (str): Winsorization method ('median_mad', 'mean_std', or 'percentile'). Defaults to 'median_mad'.
            sigma (int): Number of std/mad for bounds. Defaults to 4.
            percentile (float): Percentile for bounds. Defaults to 0.01.

    Returns:
            pd.DataFrame: Winsorized dataframe using specified method
    """
    if "universe_mask" in df.columns:
        df_ = df.drop(columns=["universe_mask"])
    else:
        df_ = df.copy()

    if method == "median_mad":
        df_ = winsorize_with_mad(df_, sigma)
    elif method == "mean_std":
        df_ = winsorize_with_std(df_, sigma)
    elif method == "percentile":
        df_ = winsorize_by_pct(df_, percentile)

    if "universe_mask" in df.columns:
        return pd.concat([df_, df[["universe_mask"]]], axis=1)
    else:
        return df_


def neutralize(df_factor: pd.DataFrame, df_style, option="indus_and_style"):
    """neutralize factor by industry and style
    Args:
            df_factor (pd.DataFrame): Factor dataframe
            df_style (pd.DataFrame): Style dataframe with first column as indcell
            option (str): Neutralization type ('indus', 'style', 'indus_and_style', or 'indus_and_style_by_country'). Defaults to 'indus_and_style'.

    Returns:
            pd.DataFrame: Neutralized factor dataframe
    """
    df_factor = df_factor.copy()
    df_style = df_style.loc[df_style.index.isin(df_factor.index)]
    rows = df_factor.index.intersection(df_style.index)
    df_factor, df_style = df_factor.loc[rows], df_style.loc[rows]

    types = df_style.dtypes
    cats = types[types == "object"].index.tolist()

    if (
        option == "indus" and len(cats) > 0
    ):  ## convert indecell to dummy industry columns
        df_style = pd.get_dummies(
            df_style[["indcell"]], columns=cats, drop_first=False
        ).astype(np.float64)
    elif option == "style":
        df_style = df_style.iloc[:, 1:]
    elif (
        option.startswith("indus_and_style") and len(cats) > 0
    ):  ## convert indecell to dummy industry columns
        df_style = pd.get_dummies(df_style, columns=cats, drop_first=False).astype(
            np.float64
        )

    df_full = pd.concat([df_style, df_factor], axis=1)
    df_full[np.isnan(df_full)] = 0.0

    def neut(df: pd.DataFrame, x_columns: list[str]):
        x = df[x_columns]
        y = df[[s for s in df.columns if s not in x_columns]]
        lr = LinearRegression()
        lr.fit(x, y)
        return y.sub(lr.predict(x))

    def neut_by_country(df: pd.DataFrame, x_columns: list[str]):
        """left for neutralization by county for option=4"""
        pass

    style_columns = df_style.columns.tolist()
    df_neu = (
        df_full.groupby(level="date")
        .apply(lambda df: neut(df, style_columns))
        .droplevel(0)
    )
    df_neu.columns = ["neu_" + x for x in df_neu.columns]
    return df_neu


%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/utils/calendar.py'

# coding = utf-8
import datetime
import pandas as pd
from shennong.utils import trading_days


def get_trading_days_by_country(region_product='cn_ashare'):
	"""
	Args:
	    region_product (str): Region/product code to get trading days for. Defaults to 'cn_ashare'.

	Returns:
	    list: List of trading days for the specified region/product
	"""
	return trading_days.load(region_product=region_product, verbose=False)


def get_trading_days_by_region(region_product='euro_next'):
	"""
	Args:
	    region_product (str): Region code to get trading days for. Defaults to 'euro_next'.

	Returns:
	    list: List of trading days for the specified region, filtered to business days
	"""
	region_map = {
		'euro_next': ['pt_lis', 'nl_ams', 'be_bru', 'ie_dub', 'no_osl', 'it_mil', 'fr_par'],
		'EU1': [
			'at_xbo',
			'be_bru',
			'ch_swx',
			'cz_pra',
			'de_fra',
			'de_ber',
			'de_etr',
			'dk_cse',
			'es_bmex',
			'fi_hel',
			'fr_par',
			'gb_lon',
			'gr_asex',
			'hu_bud',
			'ie_dub',
			'it_mil',
			'nl_ams',
			'no_osl',
			'pl_war',
			'pt_lis',
			'se_sto',
		],
	}
	if region_product in {'euronext', 'euro_next'}:
		region_product = 'euro_next'
	elif region_product in {'EU1', 'eu1'}:
		region_product = 'EU1'
	region_products = region_map.get(region_product, [])
	assert len(region_products) > 0, f'wrong cfg for {region_product}'

	td_days = []
	for rp in region_products:
		td_days.append(get_trading_days_by_country(rp))

	# Get union of trading days across all region products
	if len(td_days) > 0:
		td_days = list(set.union(*[set(days) for days in td_days]))
	td_days = sorted(set(td_days))
	business_days = pd.bdate_range('2001-01-01', '2050-12-31').astype(str).tolist()
	return sorted(set(td_days).intersection(set(business_days)))


def get_trading_days(region_product: str):
	"""load all trading days since 2012 for a given region/product
	Args:
	    region_product (str): Region/product str to get trading days for

	Returns:
	    list: List of trading days for the specified region/product
	"""
	if region_product == 'global':
		today = str(datetime.date.today())
		return pd.bdate_range('2012-01-01', today).astype(str).tolist()
	elif region_product in ['euro_next', 'euronext', 'EU1', 'eu1']:
		return get_trading_days_by_region(region_product)
	else:
		return get_trading_days_by_country(region_product)


def next_trading_days(region_product: str, date: str, lookforward: int = 1):
	"""get next few trading days after a given date
	Args:
	    region_product (str): Region/product code to get trading days for
	    date (str): Reference date
	    lookforward (int): Number of trading days to look forward. Defaults to 1.

	Returns:
	    list: List of next trading day(s) after the reference date
	"""
	td_days = get_trading_days(region_product)
	for i, dt in enumerate(td_days):
		if date == td_days[0]:
			return td_days[1]
		if date == td_days[-1]:
			print(f'{date} is the latest trading day!')
			return None

		if td_days[i - 1] <= date < dt:
			return td_days[i : i + lookforward]


def previous_trading_days(region_product: str, date: str, lookback: int = 1):
	"""get previous few trading days before a given date
	Args:
	    region_product (str): Region/product code to get trading days for
	    date (str): Reference date
	    lookback (int): Number of trading days to look back. Defaults to 1.

	Returns:
	    list: List of previous trading day(s) before the reference date
	"""
	td_days = get_trading_days(region_product)
	for i, dt in enumerate(td_days):
		if date == td_days[0]:
			print(f'{date} is the first trading day!')
			return None
		if date == td_days[-1]:
			return td_days[-2]

		if td_days[i - 1] < date <= dt:
			return td_days[i - lookback + 1 : i + 1]


def trading_days_between(region_product: str, sDate: str, eDate: str):
	"""get all trading days between two dates inclusive
	Args:
	    region_product (str): Region/product code to get trading days for
	    sDate (str): Start date
	    eDate (str): End date

	Returns:
	    list: List of trading days between start and end dates inclusive
	"""
	assert sDate <= eDate, f'{sDate} should be earlier than {eDate}!'
	td_days = get_trading_days(region_product)
	dates_between = []
	for dt in td_days:
		if sDate <= dt <= eDate:
			dates_between.append(dt)
	if len(dates_between) == 0:
		print(f'no effective trading days between {sDate} and {eDate}! please check input!')
	return dates_between



%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/utils/converter.py'

def to_date_with_slash(date: str):
    """convert date from YYYYMMDD to YYYY-MM-DD
    Args:
        date (str): Date string in YYYYMMDD format

    Returns:
        str: Date string in YYYY-MM-DD format
    """
    assert len(date) == 8, f'wrong date format {date}, should be in YYYYMMDD'
    return f'{date[0:4]}-{date[4:6]}-{date[6:8]}'

def to_date_without_slash(date: str):
    """convert date from YYYY-MM-DD to YYYYMMDD
    Args:
        date (str): Date string in YYYY-MM-DD format

    Returns:
        str: Date string in YYYYMMDD format
    """
    assert '-' in date, f'wrong date format {date}, should be in YYYY-MM-DD'
    return date.replace('-','')

%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/data/loader.py'
# coding = utf-8
import os
import re
import json
import getpass
import warnings
import itertools
import numpy as np
import polars as pl
import pandas as pd
from tqdm.auto import tqdm

from ..utils import tools
from ..utils import calendar

os.environ["NUMEXPR_MAX_THREADS"] = "200"


def load_config(cfg_filepath: str, verbose: bool = False):
    """read config from json file
    Args:
        cfg_filepath (str): Path to the config file to load
        verbose (bool, optional): Whether to print loading message. Defaults to False.

    Returns:
        dict: Loaded config dictionary from JSON file
    """
    if verbose:
        tools.Logger.info(f"loading config from {cfg_filepath}", end="\n")
    with open(cfg_filepath, "r", encoding="utf-8") as f:
        return json.load(f)


class DataLoader:
    def __init__(
        self,
    ):
        pass

    def load_1day_info(
        self, fund_dir: str, date: str, cfg: dict = {}, columns: list[str] = []
    ):
        """load 1 day cached info from parquet file
        Args:
            fund_dir (str): Directory path containing parquet files
            date (str): Date string to load data for
            cfg (dict, optional): Configuration dictionary. Defaults to {}.
            columns (list[str], optional): List of columns to load. Defaults to [].

        Returns:
            pd.DataFrame: DataFrame containing the loaded data, filtered based on config,
                         with BarraID as index. Returns None if file doesn't exist.
        """
        if not os.path.exists(f"{fund_dir}{date}.parquet"):
            return None
        if len(columns) == 0:
            a = pd.read_parquet(f"{fund_dir}{date}.parquet")  ## read all columns
        else:
            a = pd.read_parquet(
                f"{fund_dir}{date}.parquet", columns=columns
            )  ## read specified columns

        a.set_index("BarraID", inplace=True)
        if (len(cfg) == 0) | (cfg.get("country_set") == ""):
            return a.loc[~a.index.isna()]  ## filterout invalid records

        ## filter with country_set
        if cfg.get("country_set") != "":
            ctry_list = cfg.get("ctry_list", None)  ## load in fc.py before calculation
            if ctry_list is None:
                raise ValueError("country list is not prepared!")

            ## filter out non-trading or unwanted countries
            excluded = [s[1:] for s in ctry_list if s.startswith("-")]
            cond1 = a["Country_ISO3"].isin(ctry_list) & (
                ~a["Country_ISO3"].isin(excluded)
            )
            cond2 = ~(a["Country_of_Exposure"].isin(excluded))
            return a.loc[cond1 & cond2]

    def load_fundamental(
        self, sDate: str, eDate: str, cfg: dict, load_root: str = "DEFAULT"
    ):
        """load multiple days fundamental barra info from parquet files
        Args:
            sDate (str): Start date for loading data
            eDate (str): End date for loading data
            cfg (dict): Configuration dictionary containing:
                - preload_days: Number of days to preload
                - barra_product: Barra product name (e.g., EUTR, GEM3, CNTR)
                - use_info_columns: Columns used to calculate factors
                - load_ind_style: Whether to load industry style factors
            load_root (str, optional): Root directory for loading data. Defaults to "DEFAULT".

        Returns:
            pd.DataFrame: DataFrame containing fundamental data with MultiIndex (date, BarraID),
                         with duplicates removed
        """
        preload_days = cfg.get("preload_days")  ## number of days to preload
        barra_product = cfg.get("barra_product")  ## e.g., EUTR, GEM3, CNTR
        info_columns = cfg.get("use_info_columns")  ## columns used to calculate factors
        if load_root == "DEFAULT":
            load_root = (
                "/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/"
            )
        fund_dir = f"{load_root}{barra_product}/"

        all_dates = sorted([s.split(".")[0] for s in os.listdir(fund_dir)])
        dates = [s for s in all_dates if s >= sDate and s < eDate]
        start, end = all_dates.index(dates[0]), all_dates.index(dates[-1])
        dates = all_dates[max(0, start - preload_days) : end]

        if cfg.get("load_ind_style"):
            sample = self.load_1day_info(fund_dir, dates[0], cfg)
            style_cols = sample.columns[
                sample.columns.str.startswith(barra_product)
            ].tolist()
            info_columns = list(set(info_columns).union(set(style_cols)))
            info_columns = ["indcell", "regioncell"] + info_columns

        ## parallelly loading
        njobs = max(1, len(dates) // 30)
        tools.Logger.info(
            f"loading fundamental data from {sDate} to {eDate} ...",
            end="\n",
        )
        tasks = [(fund_dir, date, cfg, info_columns) for date in dates]
        datas = tools.parallel(self.load_1day_info, tasks, njobs, "loky")
        datas = {date: value for date, value in zip(dates, datas) if value is not None}
        datas = pd.concat(datas, names=["date"])
        return datas.loc[~datas.index.duplicated()]

    def load_indus_style_factors(
        self, sDate: str, eDate: str, cfg: dict, load_root: str = "DEFAULT"
    ):
        """load multiple days industry and style factors from parquet files
        Args:
            sDate (str): Start date for loading data
            eDate (str): End date for loading data
            cfg (dict): Configuration dictionary containing:
                - preload_days: Number of days to preload
                - barra_product: Barra product name (e.g., EUTR, GEM3, CNTR)
            load_root (str, optional): Root directory for loading data. Defaults to "DEFAULT".

        Returns:
            pd.DataFrame: DataFrame containing indus and style factors with MultiIndex (date, BarraID),
                         with duplicates removed
        """
        preload_days = cfg.get("preload_days")  ## number of days to preload
        barra_product = cfg.get("barra_product")  ## e.g., EUTR, GEM3, CNTR
        if load_root == "DEFAULT":
            load_root = (
                "/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/"
            )
        fund_dir = f"{load_root}{barra_product}/"

        all_dates = sorted([s.split(".")[0] for s in os.listdir(fund_dir)])
        dates = [s for s in all_dates if s >= sDate and s < eDate]
        start, end = all_dates.index(dates[0]), all_dates.index(dates[-1])
        dates = all_dates[max(0, start - preload_days) : end]

        ## read a sample to get indus_style_columns
        sample = self.load_1day_info(fund_dir, dates[0], cfg)
        style_cols = sample.columns[
            sample.columns.str.startswith(barra_product)
        ].tolist()

        ## add Country_ISO3 and Country_of_Exposure for filtering
        info_columns = [
            "BarraID",
            "Country_ISO3",
            "Country_of_Exposure",
            "indcell",
        ] + style_cols

        ## parallelly loading
        njobs = max(1, len(dates) // 30)
        tools.Logger.info(
            f"loading indus and style factors from {sDate} to {eDate} ...",
            end="\n",
        )
        tasks = [(fund_dir, date, cfg, info_columns) for date in dates]
        datas = tools.parallel(
            self.load_1day_info,
            tasks,
            njobs,
            "loky",
            True,
            "loading_indus_style_factors",
        )
        datas = {date: value for date, value in zip(dates, datas) if value is not None}
        if len(datas) == 0:
            return None
        return pd.concat(datas, names=["date"]).drop(
            columns=["Country_ISO3", "Country_of_Exposure"]
        )

    def load_universe(
        self, sDate: str, eDate: str, cfg: dict, load_root: str = "DEFAULT"
    ):
        """load universe from csv files
        Args:
            sDate (str): Start date for loading data
            eDate (str): End date for loading data
            cfg (dict): Configuration dictionary containing:
                - universe_set: Universe set name (e.g., global, EU1, euro_next, cn, etc.)
            load_root (str, optional): Root directory for loading data. Defaults to "DEFAULT".

        Returns:
            pd.DataFrame: DataFrame containing universe data with MultiIndex (date, BarraID),
                         with duplicates removed
        """
        tools.Logger.info(f"loading universe for {sDate} to {eDate} ...", end="\n")
        if load_root == "DEFAULT":
            univ_root = "/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/universe/"

        universe_set = "global"
        if cfg.get("universe_set") != "":
            universe_set = cfg.get("universe_set")

        univ_root = f"{univ_root}{universe_set}/"

        all_dates = sorted([s.split(".")[0] for s in os.listdir(univ_root)])
        dates = [s for s in all_dates if sDate <= s <= eDate]

        univ_dict = {
            date: pd.read_csv(f"{univ_root}{date}.csv").set_index("BarraID")
            for date in dates
        }
        return pd.concat(univ_dict, names=["date"])


class FactorLoader:
    def __init__(self, user_path: str = "./configs/user.path.json"):
        self.user_path = user_path
        self.user_name = getpass.getuser()

    def _load(self, filepath: str):
        """load factor from parquet file
        Args:
            filepath (str): Path to the parquet file to load

        Returns:
            pd.DataFrame: DataFrame loaded from parquet file, filtered by factor_list if specified.
                         Returns None if file does not exist.
        """
        ## check file exists
        if not os.path.exists(filepath):
            return None
        if len(self.factor_list) > 0:
            return pd.read_parquet(filepath, columns=self.factor_list)
        else:
            return pd.read_parquet(filepath)

    def load_(
        self,
        sDate: str,
        eDate: str,
        factor_name: str,
        factor_group_name: str,
        factor_type: str = "raw",
        factor_root: str = "DEFAULT",
        backfill="020000",
    ):
        """load multiple days factor from parquet files
        Args:
            sDate (str): Start date for loading data
            eDate (str): End date for loading data
            factor_name (str): Name of the factor to load
            factor_group_name (str): Name of the factor group
            factor_type (str, optional): Type of factor data to load. Defaults to "raw".
            factor_root (str, optional): Root directory for loading data. Defaults to "DEFAULT".
            backfill (str, optional): Backfill timestamp filter. Defaults to "020000".

        Returns:
            pd.DataFrame: DataFrame containing factor data with MultiIndex (date),
                         or None if no data is found
        """
        if factor_root == "DEFAULT":
            self.user_path_cfg = load_config(self.user_path)
            factor_root = self.user_path_cfg.get(self.user_name, None)
            if factor_root is None:
                raise ValueError(
                    f"no factor_root found for user {self.user_name} in {self.user_path_cfg}"
                )

        factor_root = f"{factor_root}{factor_group_name}/{factor_name}/{factor_type}/"

        all_files = os.listdir(factor_root)  ## list all files
        if backfill is None:
            files = sorted(set(all_files))
        else:
            ## filter by backfill
            files = sorted([s for s in all_files if s.endswith(backfill)])
        files = [s for s in files if sDate <= s.split(".")[0] <= eDate]

        tasks = [(f"{factor_root}{file}",) for file in files]
        features = tools.parallel(self._load, tasks, 1, "loky", True, "loading_factor")

        features = {
            s.split(".")[0]: feature
            for s, feature in zip(files, features)
            if feature is not None
        }
        if len(features) == 0:
            return None
        else:
            return pd.concat(features, names=["date"])

    def load(
        self,
        sDate: str,
        eDate: str,
        factor_name: str,
        factor_group_name: str,
        factor_type: str = "raw",
        factor_root: str = "DEFAULT",
        backfill="020000",
        factor_list: list[str] = [],
    ):
        self.factor_list = factor_list

        factor = []
        for item in ["raw", "neu"]:
            if item in factor_type:
                f = self.load_(
                    sDate,
                    eDate,
                    factor_name,
                    factor_group_name,
                    item,
                    factor_root,
                    backfill,
                )
                factor.append(f)
        factor = [s for s in factor if s is not None]
        if len(factor) == 0:
            return None
        else:
            return pd.concat(factor, axis=1)


def peek_factor_names(cfg: dict):
    """peek factor names for a given config
    Args:
        cfg: dict, config
    Returns:
        list, factor names
    """
    sDate, eDate = cfg.get("sDate"), cfg.get("eDate")
    floader = FactorLoader("./configs/user.path.json")

    factor_saveroot = cfg.get("factor_saveroot")
    td_days = calendar.trading_days_between(cfg.get("calendar"), sDate, eDate)
    load_cfg = {
        "sDate": td_days[0],
        "eDate": td_days[0],
        "factor_name": cfg.get("factor_name"),
        "factor_group_name": cfg.get("factor_group_name"),
        "factor_type": cfg.get("factor_type"),
        "backfill": cfg.get("backfill"),
        "factor_root": f"{factor_saveroot}",
    }
    feature = floader.load(**load_cfg)
    return feature.columns.tolist()


def load_raw_label(
    sDate: str,
    eDate: str,
    cfg: dict,
    barraids: list = [],
):
    """Load raw label data from parquet files.

    Args:
        sDate (str): Start date in YYYY-MM-DD format
        eDate (str): End date in YYYY-MM-DD format
        cfg (dict): Configuration dictionary containing:
            - label_usecols: List of label columns to load
            - label_options: List of label options
            - barra_product: Barra product name
        barraids (list, optional): List of specific BarraIDs to load. Defaults to empty list.

    Returns:
        pd.DataFrame: DataFrame containing raw label data with MultiIndex (date, BarraID)
    """
    # warnings.filterwarnings("ignore", category=UserWarning)
    label_usecols = cfg.get("label_usecols")
    label_options = cfg.get("label_options")
    barra_product = cfg.get("barra_product")

    barrainfo_root = (
        "/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/"
    )
    usecols_info = ["BarraID"] + label_usecols

    preload_days = max([int(s.split("_")[-1]) for s in label_options if "_" in s]) + 5
    dates = sorted(
        [s.split(".")[0] for s in os.listdir(f"{barrainfo_root}{barra_product}/")]
    )
    cdates = [d for d in dates if sDate <= d <= eDate]
    start, end = (
        max(dates.index(cdates[0]) - preload_days, 0),
        dates.index(cdates[-1]) + preload_days,
    )
    dates = dates[start:end]
    td_days = calendar.trading_days_between(cfg.get("calendar"), dates[0], dates[-1])
    dates = [
        s for s in dates if s in td_days
    ]  ## cover more than 5 + largest_horizon days

    def load_1day(date):
        warnings.filterwarnings("ignore", category=UserWarning)
        filepath = f"{barrainfo_root}{barra_product}/{date}.parquet"
        if not os.path.exists(filepath):
            return None
        df_info = pd.read_parquet(filepath, columns=usecols_info)
        if len(barraids) > 0:  ## filter by barraids
            df_info = df_info[df_info["BarraID"].isin(barraids)]
        df_info.dropna(subset=["BarraID"], inplace=True)
        df_info["date"] = date
        label = df_info.set_index(["date", "BarraID"])
        return label.loc[~label.index.duplicated(keep="first")]

    num_workers = max(len(dates) // 40, 1)
    tasks = [(date,) for date in dates]
    labels = tools.parallel(
        load_1day, tasks, num_workers, "loky", True, "loading_label"
    )
    return pd.concat(labels)


## expand label using pandas, runing slow
def expand_label_withpd(df_label: pd.DataFrame, label_options: list[str]):
    """Expand label data using pandas (slower implementation). Runtime efficiency is relatively low.

    Args:
        df_label (pd.DataFrame): Input label DataFrame with MultiIndex (date, BarraID)
        label_options (list[str]): List of label options in format H+number or H+number_number

    Returns:
        pd.DataFrame: Expanded label data with rolling means and shifts applied
    """

    def label_func(df, label, usecol):  # 对usecols进行label转换
        if bool(re.match(r"H(\d+)$", label)):  # 如果label是H0则自动补齐为H0_0
            match = re.match(r"H(\d+)$", label)
            new_label = f"H{match.group(1)}_{match.group(1)}"
        else:
            new_label = label
        match = re.match(
            r"^H(\d+)_(\d+)$", new_label
        )  # 识别 buy_lag, start_lag,得到window和lag
        window = int(match.group(2))  # group: (H1_5,1,5)

        ## no oveerlapping with today close, tomorrow close to close after n days
        lag = -int(match.group(2)) - int(match.group(1))

        if usecol == "SpecificReturn":
            ret_name = "SpecRet"
        elif usecol == "DlyReturn%":
            ret_name = "TotalRet"
        result = (
            df.groupby(level="BarraID")
            .rolling(window, min_periods=1)
            .mean()
            .shift(lag)
            .droplevel(0)
        )
        result.name = f"{ret_name}_{label}"
        return result

    ## label option should be in format of H+number or H+number_number
    label_list = [i for i in label_options if bool(re.match(r"^H\d+(?:_\d+)?$", i))]
    ret_cols = df_label.columns[df_label.columns.str.contains("return|Return")].tolist()
    grids = list(itertools.product(label_list, ret_cols))
    tasks = [(df_label[i[1]], i[0], i[1]) for i in grids]
    expanded = tools.parallel(label_func, tasks, 1, "loky")
    return pd.concat(expanded, axis=1).reindex(df_label.index)


def expand_label(raw_label: pd.Series, label_options: list[str]):
    """Expand label data using polars and numpy (faster implementation).

    Args:
        raw_label (pd.Series): Input label Series with MultiIndex (date, BarraID)
        label_options (list[str]): List of label options in format H+number_number

    Returns:
        pd.DataFrame: Expanded label data with rolling means and shifts applied
    """
    tmp = raw_label.unstack("BarraID")

    def expand_pq(df_label: pl.DataFrame, label_option: str = "H1_1"):
        label_option = label_option.split("_")
        p, q = int(label_option[0][1:]), int(label_option[-1])
        tmp = df_label.with_columns(pl.exclude("date").rolling_mean(q, min_periods=1))
        tmp = tmp.to_pandas().shift(-p - q)
        return tmp.iloc[:, 1:]

    b = pl.from_pandas(tmp.reset_index()).fill_nan(None)
    tasks = [(b, lopt) for lopt in label_options]
    results = tools.parallel(expand_pq, tasks, 1, "threading", True, "expanding_label")

    res = np.stack(results)
    res = res.transpose(1, 2, 0).reshape(-1, len(label_options))

    dates = b["date"].to_list()
    syms = b.columns[1:]
    mindex = pd.MultiIndex.from_product([dates, syms], names=["date", "BarraID"])

    if raw_label.name == "SpecificReturn":
        ret_name = "SpecRet"
    elif raw_label.name == "DlyReturn%":
        ret_name = "TotalRet"
    columns = [f"{ret_name}_H1_{q}" for q in range(1, len(label_options) + 1)]
    res = pd.DataFrame(res, index=mindex, columns=columns)
    return res.loc[raw_label.index]


def load_label(sDate: str, eDate: str, cfg: dict, barraids: list = []):
    """load label data from parquet files
    Args:
        sDate (str): Start date for loading data
        eDate (str): End date for loading data
        cfg (dict): Configuration dictionary
        barraids (list): partial BarraIDs to load if provided to save time, empty for all

    Returns:
        pd.DataFrame: DataFrame containing label data with MultiIndex (date, BarraID),
                     with duplicates removed
    """
    df_label = load_raw_label(sDate, eDate, cfg, barraids) * 0.01
    label_options = cfg.get("label_options")
    # label = expand_label(df_label, label_options).loc[sDate:eDate]
    label = expand_label(df_label.iloc[:, 0], label_options).loc[sDate:eDate]
    risk = df_label.loc[label.index, df_label.columns.str.contains("risk|Risk")]
    risk = risk.loc[label.index]
    return label, risk


def load_asset_exposure(sDate: str, eDate: str, cfg: dict, barraids: list = []):
    """Load asset factor exposure data from parquet files for a date range.

    Args:
        sDate (str): Start date in YYYY-MM-DD format
        eDate (str): End date in YYYY-MM-DD format
        cfg (dict): Configuration dictionary containing:
            - barra_product: Barra product name (e.g., EUTR, GEM3, CNTR)
        barraids (list): partial BarraIDs to load if provided to save time, empty for all

    Returns:
        pd.DataFrame: Multi-index DataFrame with dates, BarraID as indices.
                     Contains factor exposures for each asset over the date range.
                     Returns None if no data found.

    """
    exp_root = "/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/factor_exp/"
    exposure_root = f"{exp_root}{cfg.get('barra_product')}/"
    dates = calendar.trading_days_between(cfg.get("calendar"), sDate, eDate)
    # dates = pd.date_range(start=sDate, end=eDate).astype(str).tolist()

    def load_1day(date: str):
        try:
            fexp = pd.read_parquet(f"{exposure_root}{date}.parquet")
            if len(barraids) > 0:
                fexp = fexp[fexp["BarraID"].isin(barraids)]
            fexp = fexp.set_index(["BarraID", "Factor"]).unstack("Factor")
            return fexp.fillna(0.0).droplevel(0, axis=1)
        except Exception as err:
            return None

    tasks = [(date,) for date in dates]
    fexps = tools.parallel(
        load_1day, tasks, 20, "loky", True, "loading_factor_exposure"
    )
    fexps = {date: fexp for date, fexp in zip(dates, fexps) if fexp is not None}
    return pd.concat(fexps, names=["date"]).fillna(0.0)


def load_factor_covariance(sDate: str, eDate: str, cfg: dict):
    """Load factor covariance data from parquet files for a date range.

    Args:
        sDate (str): Start date in YYYY-MM-DD format
        eDate (str): End date in YYYY-MM-DD format
        cfg (dict): Configuration dictionary containing:
            - barra_product: Barra product name (e.g., EUTR, GEM3, CNTR)

    Returns:
        pd.DataFrame: Multi-index DataFrame with dates as the index.
                     Contains factor covariance matrices for each date.
                     Returns None if no data found.
    """
    cov_root = "/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/factor_cov/"
    covariance_root = f"{cov_root}{cfg.get('barra_product')}/"
    dates = calendar.trading_days_between(cfg.get("calendar"), sDate, eDate)
    cov_mats = {}
    for date in tqdm(dates, desc="loading_factor_covariance"):
        try:
            fexp = pd.read_parquet(f"{covariance_root}{date}.parquet")
            cov_mats[date] = fexp
        except FileNotFoundError as err:
            tools.Logger.info(f"{err}", end="\r")
            continue

    if len(cov_mats) == 0:
        tools.Logger.info(
            f"No covariance data found for the given date range {sDate} to {eDate}!",
            end="\n",
        )
        return None
    else:
        return pd.concat(cov_mats, axis=0, names=["date"])




%load_ext autoreload
%autoreload 2

%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/alpha_fc/FeatureCalculator.py'

# coding = utf-8
import json, os
import datetime
import pandas as pd
import numpy as np
import xarray as xr
from shennong.stk import bar
from ..utils.logging import Logger
from ..utils import processor as prep
from ..data.fc_dataloader import DataLoader
		

class SimpleFeatureCalclator:
	def __init__(self, cfg: dict):
		self.__dict__.update(cfg)
		self.cfg = cfg


	def set_params(self, cfg: dict):
		self.__dict__.update(cfg)


	def _save(self, df: pd.DataFrame, save_root: str, key_group_name: str):
		all_dates = df.index.get_level_values('date').unique()
		for date in all_dates:
			df_ = df.loc[date]
			coords = (('DATETIME',[pd.to_datetime(f'{date} 09:30:00')]), ('SYMBOL',df_.index), ('KEY',df_.columns))
			xr_ = xr.DataArray(np.array([df_.values]), coords=coords)

			xr_ = xr_.transpose('SYMBOL', 'DATETIME','KEY')
			bar.save(entity=xr_,region_product=self.region_product, freq='1day',
					key_group_name=key_group_name, save_root=save_root, filter=None, verbose=False, update_h5_key_group_structure=True)
			Logger.info(f'feature saved for {date}!', end='\r')
	
	
	def _save2(self, df: pd.DataFrame, save_root: str, key_group_name: str):
		factor_names = df.columns.tolist()

		for col in factor_names:
			factor = df[col]

			if col.startswith('neu_'):
				savedir = f'{save_root}{key_group_name}/{col.split("neu_")[-1]}/neu/'
			else:
				savedir = f'{save_root}{key_group_name}/{col}/raw/'
			if not os.path.exists(savedir): os.makedirs(savedir, exist_ok=True)

			all_dates = factor.index.get_level_values('date').unique()
			for date in all_dates:
				df_ = factor.loc[date]
				df_.to_frame().to_parquet(f'{savedir}{date}.parquet_{self.backfill}')


	def calc(self,):
		Logger.info(f'callback to be applied: {self.callback.__name__}', end='\n')
		df = self.callback(self.sDate, self.eDate, self.cfg)
		
		if self.cfg.get('gen_neu_factor'):
			loader = DataLoader()
			df_style = loader.load_indus_style_factors(self.sDate, self.eDate, self.cfg)
			df_ = df.groupby('date').apply(prep.demean).droplevel(0) ## cs demean
			df_ = df.groupby('date').apply(prep.winsorize).droplevel(0) ## cs remove outlier
			neu_factor = prep.neutralize(df_, df_style) ## regression
			neu_factor = neu_factor.groupby('date').apply(prep.winsorize).droplevel(0) ## cs remove outlier
			neu_factor = neu_factor.groupby('date').apply(prep.zscore).droplevel(0) ## cs zscore
			neu_factor.columns = [f'neu_{self.cfg.get("factor_name")}']
			df = pd.concat([df, neu_factor],axis=1).dropna()
		
		df = df.groupby('date').apply(prep.as_weight).droplevel(0) ## output as weights (abs sum to be 1.0)
		df = df.loc[~df.index.duplicated()] ## remove duplicated records
		savedir = f'{self.save_root}{self.barra_product}/'
		self._save2(df, savedir, self.factor_group_name)
		Logger.info(f'saving {self.callback.__name__} finished!', end='\n')





from barra.data.loader import load_config, load_label

ctry_path = '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/configs/country.set.json'
cfg = load_config('/mnt/sda/home/<USER>/working/gitrepo/barra_demo/configs/fc/anlystsn.eu1.01.json')
cfg['ctry_list'] = load_config(ctry_path)['EU1']

# cfg = load_config('/mnt/sda/home/<USER>/working/gitrepo/barra_demo/configs/backtest/bt_demo.json')

# fund_dir=  '/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/EUTR/'
# a = loader.load_universe('2016-01-01','2016-02-01', cfg)
# a = loader.load_fundamental('2024-01-01', '2024-02-01', cfg)

# from barra.data import loader
# user_path = '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/configs/user.path.json'
# fd = loader.FactorLoader(user_path)
# f = fd.load('2024-01-01','2024-06-30', 'WKMOM.EU1.01', 'EU1')

# load_label('2024-01-01','2024-02-01', cfg)


from barra.data import loader
dloader = loader.DataLoader()

info_data = dloader.load_fundamental('2024-01-01', '2024-06-30', cfg)

pd.read_parquet('/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/EUE4/2024-01-01.parquet').columns

%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/fc.py'
# coding = utf-8
import os
import datetime
import argparse
import getpass
import importlib.util as impt

from zoneinfo import ZoneInfo
from barra.data.loader import load_config
from barra.alpha_fc import FeatureCalculator as fc


## parser definition
parser = argparse.ArgumentParser()
parser.add_argument('--config', type=str, default='./config/')
parser.add_argument('--sDate', type=str, default='2024-01-01')
parser.add_argument('--eDate', type=str, default='2024-01-01')
parser.add_argument('--backfill', type=str, default=None)
args = parser.parse_args()

## load config
cfg = load_config(args.config)

## parse user-defined factor save_root
user_save_path_configs = load_config('./configs/user.path.json')
user_name = getpass.getuser()
if user_name not in user_save_path_configs.keys():
	raise Exception(f'no save path found for user {user_name} in {user_save_path_configs}')
else:
	cfg['save_root'] = f'{user_save_path_configs.get(user_name, None)}factor/'

## parse sDate and eDate
if args.sDate > args.eDate:
	raise ValueError('sDate must not be later than eDate')
else:
	cfg['sDate'] = args.sDate
	cfg['eDate'] = args.eDate

## override backfill if specified in terminal
if args.backfill is not None:
	cfg['backfill'] = args.backfill
else:
	## set backfill as nowtime if sDate == eDate, otherwise keep default backfill
	if args.sDate == args.eDate:
		tz = cfg.get('time_zone', None)
		assert tz is not None, 'time_zone not found in config'
		nowtime = datetime.datetime.now(tz=ZoneInfo(tz)).strftime('%H%M%S')
		cfg['backfill'] = nowtime 

## read exchange_list
cfg['ctry_list'] = load_config('./configs/country.set.json')[cfg.get('country_set')]

## initialize feature calculator
sfc = fc.SimpleFeatureCalclator(cfg)

## load callback from script
callback = cfg['callback']
spec = impt.spec_from_file_location(callback, cfg['script_path'])
module = impt.module_from_spec(spec)
spec.loader.exec_module(module)

if hasattr(module, callback):
	sfc.callback = getattr(module, callback)

sfc.calc()

print(args.config)
bt = cfg.get('backtest', None)
if bt:
	cfg_filename = args.config.split('/')[-1].split('.json')[0]
	os.system(f"bash backtest2.sh {cfg_filename}")





%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/fc_scripts/EU1.py'

import pandas as pd
import numpy as np

from barra.data.loader import DataLoader
loader = DataLoader()


def rolling_ema(x, com, span, halflife, alpha, adjust):
	return x.ewm(com=com, span=span, halflife=halflife, alpha=alpha, adjust=adjust).mean().iloc[-1]


def custom_ema(x, halflife, adjust):
	prewt = 0.5 ** ((1 / halflife) * (x.shape[0] - np.arange(1, x.shape[0] + 1)))
	if adjust: prewt /= np.sum(prewt)  # adjust to unity if adjust
	return np.nansum(x * prewt, axis=0) ## calculate last weighted sum


## corresponding factor_name of wkmom.json
def fc_wkmom_score(sDate: str, eDate: str, cfg: dict):
	## load fundamental info data
	info_data = loader.load_fundamental(sDate, eDate, cfg)

	## factor name should be defined in cfg as factor_name
	factor_name = cfg.get('factor_name', None)
	assert factor_name is not None, 'factor_name should be defined in cfg'

	raw_factor = info_data['SpecificReturn'].groupby(level='BarraID').rolling(5).mean().droplevel(0)
	raw_factor = raw_factor.to_frame(name=factor_name)  # series to dataframe

	## filter out duplicated entries
	raw_factor = raw_factor.loc[~raw_factor.index.duplicated()]

	## filter out stocks not in universe
	if cfg.get('universe_set') != '':
		univ_df = loader.load_universe(sDate, eDate, cfg)
		raw_factor = raw_factor.loc[raw_factor.index.intersection(univ_df.index)]

	## raw factor fed back to feature calculator, followed by neutralization and as_weight
	return raw_factor.sort_index()


## corresponding factor_name of wkmom.json
def fc_mnmom_score(sDate: str, eDate: str, cfg: dict):
	## load fundamental info data
	info_data = loader.load_fundamental(sDate, eDate, cfg)

	## factor name should be defined in cfg as factor_name
	factor_name = cfg.get('factor_name', None)
	assert factor_name is not None, 'factor_name should be defined in cfg'

	raw_factor = info_data['SpecificReturn'].groupby(level='BarraID').rolling(5).mean().droplevel(0)
	raw_factor = raw_factor.to_frame(name=factor_name)  # series to dataframe

	## filter out duplicated entries
	raw_factor = raw_factor.loc[~raw_factor.index.duplicated()]

	## filter out stocks not in universe
	if cfg.get('universe_set') != '':
		univ_df = loader.load_universe(sDate, eDate, cfg)
		raw_factor = raw_factor.loc[raw_factor.index.intersection(univ_df.index)]

	## raw factor fed back to feature calculator, followed by neutralization and as_weight
	return raw_factor.sort_index()




## expotential moving average of ANLYSTSN factor difference
def fc_anlystsn_ema_score(sDate: str, eDate: str, cfg: dict):
	## load fundamental info data
	info_data = loader.load_fundamental(sDate, eDate, cfg)

	## factor name should be defined in cfg as factor_name
	factor_name = cfg.get('factor_name', None)
	assert factor_name is not None, 'factor_name should be defined in cfg'
	bp = cfg.get('barra_product', None)
	if bp in {'EUTR'}:
		prefix = f'{bp}D_'
	elif bp in {'EUE4'}:
		prefix = f'{bp}BASS_'
	a = info_data[f'{prefix}ANLYSTSN'].groupby(level='BarraID').diff()

	## parallel calculation with pandarallel
	from pandarallel import pandarallel
	pandarallel.initialize(nb_workers=10)
	raw_factor = a.to_frame().groupby('BarraID')\
		.parallel_apply(lambda x: x.rolling(260)\
				  .apply(lambda y: custom_ema(y, halflife=130, adjust=True), raw=False)).droplevel(0)
	raw_factor.columns = [factor_name]

	## filter out duplicated entries
	raw_factor = raw_factor.loc[~raw_factor.index.duplicated()]

	## filter out stocks not in universe
	if cfg.get('universe_set') != '':
		univ_df = loader.load_universe(sDate, eDate, cfg)
		raw_factor = raw_factor.loc[raw_factor.index.intersection(univ_df.index)]

	## raw factor fed back to feature calculator, followed by neutralization and as_weight
	return raw_factor.sort_index()





%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/batch_fc.sh'
#!/bin/bash

## cfg file names to be applied, spaced with space
cfgs=("fc.wkmom.eu1.01")
sDate="2016-01-01"
eDate="2016-06-30"
backfill="020000"

for i in "${cfgs[@]}";
do
    echo "running ${i}"
    bash fc.sh ${i} ${sDate} ${eDate} ${backfill} &
done
wait








%load_ext autoreload
%autoreload 2

import matplotlib.pyplot as plt

%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/fitting/fitting.py'
# coding = utf-8
__all__ = ['LinearRegressor', 'calc_time_weight', 'calc_cs_time_weight']

import os
import numpy as np
import pandas as pd
from scipy.optimize import minimize, Bounds

from ..utils import tools


def calc_time_weight(length: int, halflife: float) -> np.array:
	"""Calculate time weights using exponential moving average with a given halflife.

	Args:
	    length (int): Number of periods.
	    halflife (float): Halflife parameter for the EMA.

	Returns:
	    pd.Series: Array of time weights, most recent first.
	"""
	# Decay factor per period
	alpha = 1 - np.exp(np.log(0.5) / halflife)
	weights = np.array([(1 - alpha) ** i for i in range(length - 1, -1, -1)])
	weights /= weights.sum()
	return weights


def calc_cs_time_weight(df: pd.DataFrame, halflife: float) -> pd.DataFrame:
	"""
	Generate a cross-sectional time weight for a multi-index DataFrame (date, BarraID).
	The weight for all BarraIDs of the same date is the same, based on the calculate_time_weight function.
	The output is a multi-index DataFrame with one column named 'TimeWeight'.

	Args:
	    df (pd.DataFrame): Multi-index DataFrame with (date, BarraID) as index.
	    halflife (float): Halflife parameter for the EMA.

	Returns:
	    pd.DataFrame: Multi-index DataFrame with one column 'TimeWeight'.
	"""
	dates = df.index.get_level_values('date').unique().sort_values()
	weights = calc_time_weight(len(dates), halflife)
	date_weight_map = dict(zip(dates, weights))
	time_weight = df.index.get_level_values('date').map(date_weight_map)
	return pd.DataFrame({'TimeWeight': time_weight}, index=df.index)


class LinearRegressor:
	def __init__(self, **cfg: dict):
		"""Initialize the LinearRegressor with configuration parameters.

		Args:
			**cfg (dict): Configuration parameters to set as instance attributes

		"""
		self.__dict__.update(**cfg)
		self.fitted = False

	def gradient(self, beta: np.ndarray, *args):
		"""Calculate gradient of loss function with respect to beta parameters.

		Args:
			beta (np.ndarray): Current beta parameter values
			*args: Additional arguments (unused)

		Returns:
			np.ndarray: Gradient vector
		"""
		y_pred = self.x.dot(beta)
		error = y_pred - self.y
		return 2 * self.x.T.dot(error)

	def loss(self, beta: np.ndarray):
		"""Calculate loss function value for given beta parameters.

		Args:
			beta (np.ndarray): Beta parameter values

		Returns:
			float: Loss function value
		"""
		x, y = self.x, self.y

		# model_method = self.reg_settings.get('method')
		sw = (1.0 / self.sw**2).squeeze()
		tw = self.tw.squeeze()
		# if model_method == 'linear':
		# 	x = self.x / np.abs(self.sw)
		# 	y = self.y / np.abs(self.sw)
		# 	sw = np.ones_like(self.sw)
		# elif model_method == 'ridge':
		# 	x, y = self.x, self.y
		# 	sw = (1.0 / self.sw**2).squeeze()

		y_pred = x.dot(beta)
		error = (y - y_pred) ** 2
		l2 = self.reg_settings.get('lambda') * np.sum(np.square(beta))
		if self.reg_settings.get('time_weight', False):
			return np.sum(error * sw * tw) - l2
		else:
			return np.sum(error * sw) - l2

	def fit_(self, x: np.ndarray, y: np.ndarray):
		"""Internal fitting method for single target variable.

		Args:
			x (np.ndarray): Feature matrix
			y (np.ndarray): Target vector

		Returns:
			np.ndarray: Fitted beta parameters
		"""
		self.x = np.hstack([x, np.ones((x.shape[0], 1))])
		self.y = y
		beta_init = np.random.randn(self.x.shape[1])
		dayLB = self.reg_settings.get('dayLB')
		dayUB = self.reg_settings.get('dayUB')
		bounds = Bounds(dayLB, dayUB)
		result = minimize(self.loss, beta_init, method='L-BFGS-B', bounds=bounds, jac=self.gradient)
		# result = minimize(self.loss, beta_init, method='L-BFGS-B', bounds=bounds)
		return result.x

	def fit(
		self,
		df_factor: pd.DataFrame,
		df_label: pd.DataFrame,
		df_risk: pd.DataFrame = None,
	):
		"""Fit the model to training data.

		Args:
			df_factor (pd.DataFrame): Feature dataframe
			df_label (pd.DataFrame): Target dataframe
			df_risk (pd.DataFrame, optional): Risk weights dataframe. Defaults to None.
		"""
		factor_names = df_factor.columns.tolist()
		self.label_names = df_label.columns.tolist()
		rows = df_factor.index.intersection(df_label.index)
		x, y = df_factor.loc[rows], df_label.loc[rows].fillna(0.0)
		x = x.loc[~x.isna().all(axis=1)].fillna(0.0)
		y = y.loc[x.index]

		if df_risk is not None:
			self.sw = df_risk.loc[x.index].values  ## get sample_weight
		else:
			self.sw = np.ones_like(x.index)

		self.tw = calc_cs_time_weight(x, self.default_settings.get('halflife'))
		## convert dataframe to numpy for fitting
		x_, y_ = x.values, y.values

		betas = []
		for i in range(y_.shape[1]):
			beta = self.fit_(x_, y_[:, i])
			betas.append(beta)
			tools.Logger.info(f'fitted: {i + 1}/{y_.shape[-1]}', end='\r')

		rows, cols = self.label_names, factor_names + ['intercept']
		self.beta = pd.DataFrame(np.array(betas), index=rows, columns=cols).T

		self.fitted = True
		self.x, self.y = x, y

	def predict(self, df_factor: pd.DataFrame):
		"""Make predictions using fitted model.

		Args:
			df_factor (pd.DataFrame): Feature dataframe for prediction

		Returns:
			pd.DataFrame: Predicted values
		"""
		index = df_factor.index
		x = df_factor.copy()
		# x['intercept'] = 1.0

		infered = x.dot(self.beta.iloc[:-1, :])
		self.alpha = pd.DataFrame(infered, index=index, columns=self.label_names)
		return self.alpha

	def save_results(self, user_root: str, date: str, debug: bool = False, verbose: bool = False):
		"""Save fitted model results to files.

		Args:
			user_root (str): Root directory for saving files
			date (str): Date string for file naming
			debug (bool, optional): Whether to save debug info. Defaults to False.
			verbose (bool, optional): Whether to print save messages. Defaults to False.
		"""
		alphadir = f'{user_root}fitting/{self.barra_product}/{self.alpha_group_name}/{self.alpha_name}/alpha/'
		betadir = f'{user_root}fitting/{self.barra_product}/{self.alpha_group_name}/{self.alpha_name}/beta/'
		xydir = f'{user_root}fitting/{self.barra_product}/{self.alpha_group_name}/{self.alpha_name}/xy/'
		xstddir = f'{user_root}fitting/{self.barra_product}/{self.alpha_group_name}/{self.alpha_name}/xstd/'

		if not os.path.exists(alphadir):
			os.makedirs(alphadir, exist_ok=True)
		self.alpha.to_parquet(f'{alphadir}{date}.parquet')

		if not os.path.exists(betadir):
			os.makedirs(betadir, exist_ok=True)
		beta_index = pd.MultiIndex.from_product([[date], self.beta.index])
		beta_index.names = ['date', 'factor']
		self.beta.set_index(beta_index).to_parquet(f'{betadir}{date}.parquet')

		if not os.path.exists(xstddir):
			os.makedirs(xstddir, exist_ok=True)
		self.xstd = self.x.std(axis=0).to_frame(name='x_std')
		xstd_index = pd.MultiIndex.from_product([[date], self.xstd.index])
		xstd_index.names = ['date', 'factor']
		self.xstd.set_index(xstd_index).to_parquet(f'{xstddir}{date}.parquet')

		if debug:
			if not os.path.exists(xydir):
				os.makedirs(xydir, exist_ok=True)
			xy = pd.concat([self.x, self.y, self.sw], axis=1)
			xy.to_parquet(f'{xydir}{date}.parquet')

		if verbose:
			tools.Logger.info(f'alpha saved for [{date}] at [{alphadir}]', end='\n')


# %%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/fit.py'

# coding = utf-8
import os
import sys
import time
import socket
import datetime
import subprocess

os.environ['NUMEXPR_MAX_THREADS'] = '200'

import numpy as np
import pandas as pd

from barra.utils import tools
from barra.utils import calendar
from barra.utils import processor as prep
from barra.data import loader
from barra.fitting import fitting
from barra.fitting.alpha_reporter import AlphaReporter
ip = socket.gethostbyname((socket.gethostname()))


def parse_factor_settings(input_settings: dict):
	return {k: v for k, v in input_settings.items() if k != 'Intercept'}


def parse_intercept_settings(input_settings: dict):
	return input_settings.get('Intercept')


t0 = time.time()
prefix = './'
prefix = '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/'
user_path_json = f'{prefix}configs/user.path.json'
user_root = tools.parse_user_root(user_path_json)
floader = loader.FactorLoader(user_path_json)

# fit_cfg_file = f'{prefix}configs/fitting/eu1.01.json'
# fit_cfg_file = f'{prefix}configs/fitting/buyside.json'
# fit_cfg_file = f'{prefix}configs/fitting/mom.json'
fit_cfg_file = f'{prefix}configs/fitting/mom2.json'
# fit_cfg_file = f'{prefix}configs/fitting/analyst.json'
# fit_cfg_file = f'{prefix}configs/fitting/spraw.json'
# fit_cfg_file = f'{prefix}configs/fitting/spraw2_tw4.json'
fit_cfg = loader.load_config(fit_cfg_file)

input_settings = fit_cfg.get('input', None)
factor_settings = parse_factor_settings(input_settings)
reg_settings = fit_cfg.get('reg_settings', None)
default = fit_cfg.get('default_settings', None)


barra_product = fit_cfg.get('barra_product')
fgroup_name = fit_cfg.get('factor_group_name', None)
algroup_name = fit_cfg.get('alpha_group_name', None)
alname = fit_cfg.get('alpha_name', None)

half_life = default.get('halflife', None)
lookback = half_life * default.get('number_ff_HL', None)
horizons = range(1, int(default.get('predict_horizon', None)) + 1)
label_options = [f'H1_{i}' for i in horizons]

return_map = {
	'idio': ['SpecificReturn', 'SpecRisk%'],
	'total': ['DlyReturn%', 'TotalRisk%'],
}


def prepare_factor(fit_cfg: dict, sample: bool = False):
	sDate, eDate = fit_cfg.get('sDate'), fit_cfg.get('eDate')
	td_days = calendar.previous_trading_days(fit_cfg.get('calendar'), sDate, lookback=lookback + 5)
	sDate = td_days[0]

	# sp.set1
	# factor_list = [
	# 	"5YRel_PTMargin",
	# 	"OCFRatio",
	# 	"IndRel_FCFEV",
	# 	"5YRel_FCFP",
	# 	"FCFEV",
	# 	"6MChgTgtPrcGapEMA",
	# 	"EP",
	# 	"RelPrStr_12M",
	# 	"Amihud",
	# 	"HL1M",
	# ]

	# buyesideInst
	# factor_list = [
	# 	'VWAF_1_Day_Change_s5',
	# 	'InventoryIncrease_ratio_m5',
	# 	'Transactions_pervolume',
	# 	'Short_Loan_Quantity_as_of_Shares_Outstanding',
	# 	'short_ratio_m5',
	# 	'DIPS',
	# 	'DIMV',
	# 	'BO_On_Loan_Value_Rank_1_Market_Share_m5',
	# 	'DNS',
	# 	'DPS',
	# 	# 'DCBS',
	# 	'Short_Utilisation_m5',
	# 	'Broker_Demand_ratio_m5',
	# 	'Active_Short_Utilisation_m5',
	# 	'BOInventoryValue_on_mv_m5dividem130',
	# 	'SL_Tenure_m5',
	# 	'BOInventoryValue_on_mv_m5',
	# 	'Inactive_Agents_m5dm130',
	# ]

	## for illustration, need to adapt to real use cases
	tools.Logger.info(f'loading factor for {sDate} - {eDate} ...', end='\n')
	# momentum & splib
	factor_list = []
	if sample:
		eDate = sDate
	return floader.load(
		sDate,
		eDate,
		'EU1',
		'SP_SET_RAW', # 'SP_SET_RAW', 'mom
		'raw',
		factor_root=factor_root,
		backfill=None,
		factor_list=factor_list,
	)


def prepare_label(fit_cfg: dict):
	sDate, eDate = fit_cfg.get('sDate'), fit_cfg.get('eDate')
	td_days = calendar.previous_trading_days(fit_cfg.get('calendar'), sDate, lookback=lookback + 5)
	sDate = td_days[0]

	label_cfg = {
		'label_usecols': return_map.get(reg_settings.get('return_and_risk_type')),
		'label_options': label_options,
		'barra_product': barra_product,
		'calendar': fit_cfg.get('calendar'),
	}

	tools.Logger.info(f'loading label and risk from {sDate} to {eDate} ...', end='\n')
	df_label, risk = loader.load_label(sDate, eDate, label_cfg)

	return df_label, risk


def fit_1day(date: str, df_factor: pd.DataFrame, df_label: pd.DataFrame, df_risk: pd.DataFrame):
	lr = fitting.LinearRegressor(**fit_cfg)
	# lr = LinearRegressor(**fit_cfg)
	user_root = tools.parse_user_root(user_path_json)
	alpha_dir = f'{user_root}fitting/{barra_product}/{algroup_name}/{alname}/alpha/'
	# if os.path.exists(f'{alpha_dir}{date}.parquet'):
	# 	return None

	# train with data before yesterday and yesterday's factor, label and risk
	train_dates = calendar.previous_trading_days(fit_cfg.get('calendar'), date, lookback=lookback + 2)[:-2]
	feature = df_factor.loc[train_dates[0] : train_dates[-1]].copy()
	label = df_label.loc[train_dates[0] : train_dates[-1]].copy()
	risk = df_risk.loc[train_dates[0] : train_dates[-1]].copy()

	# print(f'fitting for {date}!', end='\n')
	lr.fit(feature, label, risk)

	# use yesterday's factor to predict today's alpha
	yesterday = calendar.previous_trading_days(fit_cfg.get('calendar'), date, lookback=2)[-2]
	if yesterday not in cdates:
		return None
	next_factor = df_factor.loc[yesterday].copy()
	if (next_factor is None) | (len(next_factor) == 0):
		return None

	lr.predict(next_factor.fillna(0.0))
	lr.alpha['date'] = date
	lr.alpha = lr.alpha.set_index('date', append=True).swaplevel(0, 1)
	lr.alpha.index.names = ['date', 'BarraID']
	tools.Logger.info(f'fitting finished for {date}!', end='\r')

	lr.save_results(user_root, date)

	if date in dates[-1:]:  ## only report at the last day
		files = os.listdir(alpha_dir)
		if len(files) > 0:
			alpha_reporter = AlphaReporter(alpha_dir)
			alpha_reporter.report()


# outlier winsorization
def yprep(df: pd.DataFrame):
	df = prep.winsorize(df, 'percentile', percentile=0.05)
	return df


def xprep(df: pd.DataFrame):  # one group
	df = prep.winsorize(df, 'percentile', percentile=0.05)
	df = prep.winsorize(df, 'median_mad', sigma=4)
	df = prep.zscore(df)
	return df


factor_root = '/mnt/sda/NAS/ShareFolder/pengpuda/factor_analysis/euronext/SP/features/EUTR/'
df_factor = prepare_factor(fit_cfg, sample=False)
# drop_names = ['OEA', '5YRel_OEA', 'ROA', '5YRel_ROA']
# if len(set(drop_names).intersection(set(df_factor.columns)))>0:
# 	df_factor = df_factor.drop(drop_names, axis=1)
num_factor = df_factor.shape[1]

num_array = 40
dates = calendar.trading_days_between(fit_cfg['calendar'], fit_cfg['sDate'], fit_cfg['eDate'])
cdates = df_factor.index.get_level_values('date').unique().tolist()
dates = [date for date in dates if date in cdates]
dates_list = np.array_split(dates, num_array)
task_map = {str(i + 1): dates_list[i] for i in range(num_array)}

if (num_factor >= 50) & ('SLURM_JOB_ID' in os.environ): # not in slurm cluster
	df_label, df_risk = prepare_label(fit_cfg)

	# Get common index across all 3 dataframes
	common_idx = df_factor.index.intersection(df_label.index).intersection(df_risk.index)
	df_factor = df_factor.loc[common_idx]
	df_label = df_label.loc[common_idx] * 100  # in unit percentage
	df_risk = df_risk.loc[common_idx] * 100  # in unit percentage


	df_label = df_label.groupby('date').apply(yprep).droplevel(0)
	df_risk = df_risk.groupby('BarraID').shift(1)
	df_risk = df_risk.groupby('date').apply(yprep).droplevel(0)
	df_factor = df_factor.groupby(level='date').apply(xprep).droplevel(0)



def run_task(dates, njobs=5):
	tasks = [(date, df_factor, df_label, df_risk) for date in dates[:]]
	_ = tools.parallel(fit_1day, tasks, njobs=njobs, backend='loky', progress_bar=True, desc='fitting')


def main():
	if num_factor >= 50:
		if 'SLURM_JOB_ID' not in os.environ:  ## not in a slurm cluster
			assert ip == '************', 'slurm scripts should be generated on SLURM MANAGER'
			pyargs = f'{os.path.abspath(__file__)}'
			job_name = 'fit_' + fit_cfg.get('alpha_name')
			script = tools.generate_slurm_commands('AMD_R', 50, num_array, 10, pyargs=pyargs, job_name=job_name)
			print(script)
			subprocess.run(['sbatch'], input=script.encode('utf-8'), check=True)
		elif 'SLURM_JOB_ID' in os.environ:  # already in slurm cluster
			run_task(dates=task_map[os.environ['SLURM_ARRAY_TASK_ID']], njobs=45)
	else:
		assert ip != '************', 'standalone mode should not be run on SLURM MANAGER'
		_ = run_task(dates, 50)


if __name__ == "__main__":
	main()
	t1 = time.time()
	print(f'time elasped: {(t1 - t0):.2f}')

drop_names = ['OEA', '5YRel_OEA', 'ROA', '5YRel_ROA']
df_factor[drop_names].hist(bins=100);

df_factor_ = df_factor.groupby(level='date').apply(xprep).droplevel(0)


df_factor_[drop_names].hist(bins=100);

from barra.fitting.alpha_reporter import AlphaReporter
alpha_dir = '/mnt/sda/NAS/ShareFolder/lishuanglin/test/fitting/GEM3/momentum/EU1.momentum2/alpha/'
alpha_reporter = AlphaReporter(alpha_dir)
alpha_reporter.report()




%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/fitting/alpha_reporter.py'
import os
import datetime
import pandas as pd
from barra.utils import tools
import matplotlib.pyplot as plt
from matplotlib.backends.backend_pdf import PdfPages
from multiprocessing import Lock

plt.rcParams['figure.figsize'] = (12, 6)
plt.rcParams['axes.grid'] = True
plt.rcParams['legend.loc'] = 'lower left'



class AlphaReporter:
	def __init__(self, alpha_dir: str):
		self.alpha_dir = alpha_dir
		self.beta_dir = self.alpha_dir.replace('alpha', 'beta')
		self.xstd_dir = self.alpha_dir.replace('alpha', 'xstd')

	def read(self, file_dir: str, date: str):
		filepath = f'{file_dir}{date}.parquet'
		if not os.path.exists(filepath):
			return None
		return pd.read_parquet(filepath)

	def read_files(self, file_dir: str):
		dates = sorted([s.split('.')[0] for s in os.listdir(file_dir)])
		datas = [self.read(file_dir, date) for date in dates]
		datas = [value for value in datas if value is not None]
		if len(datas) == 0: return None
		return pd.concat(datas)

	def report_beta(self, pdf, df: pd.DataFrame, data_type: str):
		b = df.iloc[:, 0].unstack('factor')
		fnames = b.columns.tolist()
		fnames_group = [fnames[i : i + 5] for i in range(0, len(fnames), 5)]
		for fgroup in fnames_group:
			fig, ax = plt.subplots()
			b.loc[:, fgroup].plot(alpha=0.8, ax=ax, title=f'{data_type}')
			plt.legend()
			pdf.savefig(fig, bbox_inches='tight')
			plt.close()

	def report_alpha(self, pdf, alpha: pd.DataFrame):
		a = alpha.iloc[:, 0].unstack(0)
		b = a.agg(['mean', 'max', 'min', 'std']).T
		b[['qtile0.1', 'qtile0.9']] = a.quantile([0.1, 0.9]).T
		fig, ax = plt.subplots()
		b[['mean', 'max', 'min', 'qtile0.1', 'qtile0.9']].plot(ax=ax)
		plt.title('alpha -statistics')
		pdf.savefig(fig, bbox_inches='tight')
		plt.close()

		fig, ax = plt.subplots()
		b[['std']].plot(ax=ax)
		plt.title('alpha -std')
		pdf.savefig(fig, bbox_inches='tight')
		plt.close()

		fig, ax = plt.subplots()
		alpha.groupby('date').count().plot(title='valid_count', ax=ax)
		pdf.savefig(fig, bbox_inches='tight')
		plt.close()

		fig, ax = plt.subplots()
		(~(alpha==0.0)).groupby('date').sum().plot(title='valid_score_count', ax=ax)
		pdf.savefig(fig, bbox_inches='tight')
		plt.close()



	def report_(self, alpha, beta, xstd):
		report_folder = f'{self.alpha_dir.replace("alpha/", "")}'
		time_tag = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
  
		with Lock():
			with PdfPages(f'{report_folder}stats_report_{time_tag}.pdf') as pdf:
				self.report_alpha(pdf, alpha)
				self.report_beta(pdf, beta, 'beta')
				self.report_beta(pdf, xstd, 'xstd')

	def report(
		self,
	):
		self.alpha = self.read_files(self.alpha_dir)
		self.beta = self.read_files(self.beta_dir)
		self.xstd = self.read_files(self.xstd_dir)
		self.report_(self.alpha, self.beta, self.xstd)
		tools.Logger.info(f'report pdf file generated at {self.alpha_dir.replace("alpha/", "")}', end='\n')



if __name__ == "__main__":
	fitting_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/test/fitting/'
	barra_product = 'EUTR'
	alpha_group_name = 'EU1.SP'
	alpha_name = 'EU1.SP1.01'

	alpha_dir = f'{fitting_root}{barra_product}/{alpha_group_name}/{alpha_name}/alpha/'

	areporter = AlphaReporter(alpha_dir)
	areporter.report()



%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/portfolio/theo_opt.py'
# coding = utf-8

import numpy as np
import pandas as pd
from ..utils import tools


def calc_asset_cov_mat(
    exp_mat: pd.DataFrame, fcov_mat: pd.DataFrame, srisk_vector: pd.DataFrame
) -> pd.DataFrame:
    """
    calculate asset covariance matrix

    Args:
        exp_mat (pd.DataFrame): exposure matrix (Barrid, risk factors)
        fcov_mat (pd.DataFrame): factor covariance matrix (risk factors, risk factors), symmetric
        srisk_vector (pd.DataFrame): idio risk vector (Barrid, )

    Returns:
        pd.DataFrame: asset covariance matrix (Barrid, Barrid)
        v = exp_matrix @ fcov_matrix @ exp_matrix.T + diag(srisk_vector^2)
    """
    barraids = exp_mat.index.tolist()
    exp_mat = exp_mat[fcov_mat.columns].to_numpy()  ## align risk factor names
    srisk = srisk_vector.loc[barraids].to_numpy().squeeze()  ## align barra id
    v1 = np.linalg.multi_dot([exp_mat, fcov_mat, np.transpose(exp_mat)])
    v2 = np.diag(np.power(srisk, 2))
    return pd.DataFrame(v1 + v2, index=barraids, columns=barraids)


def inverse(matrix: pd.DataFrame, TOL: float = 1e-8) -> pd.DataFrame:
    """
    inverse of a symmetric 2D matrix

    Args:
        matrix (pd.DataFrame): 2D symmetric matrix
        TOL (float, optional): rcond. Defaults to 1e-8.

    Returns:
        pd.DataFrame: inverse matrix of matrix
    """
    assert matrix.shape[0] == matrix.shape[1], "Matrix must be square"
    assert np.allclose(matrix, matrix.T), "Matrix must be symmetric"

    indexs = matrix.index.tolist()
    matrix = matrix.to_numpy()
    invertable = np.linalg.det(matrix)

    if invertable:
        inv_matrix = np.linalg.inv(matrix)
    else:
        inv_matrix = np.linalg.pinv(matrix, rcond=TOL, hermitian=False)

    return pd.DataFrame(inv_matrix, index=indexs, columns=indexs)


def calc_asset_cov_mat_inv(
    exp_mat: pd.DataFrame,
    fcov_mat: pd.DataFrame,
    srisk_vector: pd.DataFrame,
    is_fcov_inverted: bool = False,
    TOL: float = 1e-8,
) -> pd.DataFrame:
    """has same result as invert_matrix(calc_asset_cov_mat()), but since calc_asset_cov_mat() returns
    a large matrix and it's slow to calculate the inverse matrix, so use an alternative
    method to get the same result as invert_matrix(calc_asset_cov_mat())

    Args:
        exp_mat (pd.DataFrame): (Barrid, risk factors)
        fcov_mat (pd.DataFrame): (risk factors, risk factors)
        srisk_vector (pd.DataFrame): (Barrid, )
        is_fcov_inverted (bool, optional): is fcov already inverted before passing in.

    Returns:
        pd.DataFrame: inverse of asset covariance matrix (Barrid, Barrid)
    """

    """
    1. srisk_dinv = diag(srisk_vector^-2)  (Barrid, Barrid)
    """
    barraids = exp_mat.index.tolist()
    exp_mat = exp_mat[fcov_mat.columns].to_numpy()  ## align risk factor names
    srisk = srisk_vector.loc[barraids].to_numpy().squeeze()  ## align barra id

    with np.errstate(divide="ignore"):
        srisk_dinv = np.diag(
            np.where(
                np.abs(srisk) > TOL,
                np.power(srisk, -2),
                0,
            )
        )

    """
    2. fcov = inversed factor covariance matrix  (risk factor, risk factor)
    fcov2 = exp.T * dinv * exp + fcov  (risk factor, risk factor)
    fcov3 = ginv(fcov2, tol=1e-8)  (risk factor, risk factor)
    """
    # if fcov_mat is inverted, just use it as fcov
    # if fcov_mat is not inverted, then invert it
    fcov = fcov_mat if is_fcov_inverted else inverse(matrix=fcov_mat, TOL=TOL)
    fcov2 = np.linalg.multi_dot([np.transpose(exp_mat), srisk_dinv, exp_mat]) + fcov
    fcov3 = np.linalg.pinv(fcov2, rcond=TOL, hermitian=False)

    """
    3. dx = srisk_dinv * exp  (Barrid, risk factors)
    """
    dx = srisk_dinv.dot(exp_mat)

    """
    4. v = srisk_dinv - (dx * fcov3 * dx.T)  (Barrid, Barrid)
    """
    v = srisk_dinv - np.linalg.multi_dot([dx, fcov3, dx.T])

    return pd.DataFrame(v, index=barraids, columns=barraids)


def calc_theo_opt_wt(
    exp_mat: pd.DataFrame,
    fcov_mat: pd.DataFrame,
    srisk_vector: pd.DataFrame,
    score: pd.DataFrame,
    is_fcov_inverted: bool,
    method: str = "complete",
) -> pd.DataFrame:
    """
    calculate theoretical optimal weight

    Args:
        exp_mat (pd.DataFrame): (Barrid, risk factors)
        fcov_mat (pd.DataFrame): (risk factors, risk factors)
        srisk_vector (pd.DataFrame): (Barrid, ), specRisk or totalRisk
        score (pd.DataFrame): (Barrid, alps)
        is_fcov_inversed (bool, optional): is fcov already inversed.

    Returns:
        pd.DataFrame: theoretical optimal portfolio weight (Barrid, )
    """

    if method == "woodbury":
        asset_cov_inv = calc_asset_cov_mat_inv(
            exp_mat=exp_mat,
            fcov_mat=fcov_mat,
            srisk_vector=srisk_vector,
            is_fcov_inverted=is_fcov_inverted,
        )
    elif method == "complete":
        asset_cov_inv = inverse(calc_asset_cov_mat(exp_mat, fcov_mat, srisk_vector))

    assert np.allclose(asset_cov_inv, asset_cov_inv.T, rtol=0.01), (
        "asset_cov_inv is not symmetric"
    )

    ## calculated optimized weight
    h = asset_cov_inv.dot(score)
    return h.div(np.sum(np.abs(h), axis=0))


def calc_theo_opt_alpha_1day(
    date: str,
    exposure: pd.DataFrame,
    covariance: pd.DataFrame,
    df_risk: pd.DataFrame,
    df_alpha: pd.DataFrame,
):
    """calculate theoretical optimal asset weights for a single day
    Args:
        date (str): Target date for calculation in 'YYYY-MM-DD' format
        exposure (pd.DataFrame): Factor exposure matrix for assets (assets, factor)
        covariance (pd.DataFrame): Factor covariance matrix, should be symmetric
        df_risk (pd.DataFrame): Specific risk data for assets (assets, risk_name)
        df_alpha (pd.DataFrame): Alpha signals/scores for assets (assets, alpha_name)

    Returns:
        pd.DataFrame: Optimal theoretical portfolio weights calculated by calc_theo_opt_wt function
    """
    score_ = df_alpha.loc[date, :]
    fcov_ = covariance.loc[date, :]

    srisk_ = df_risk.loc[date, :].loc[score_.index]
    exp_mat_ = exposure.loc[date, :].loc[score_.index,:]
    exp_mat = pd.DataFrame(
        np.ones((exp_mat_.shape[0], fcov_.shape[1])),
        # np.zeros((exp_mat_.shape[0], fcov_.shape[1])),
        index=exp_mat_.index,
        columns=fcov_.columns,
    )
    exp_mat.loc[exp_mat_.index, exp_mat_.columns] = exp_mat_
    return calc_theo_opt_wt(exp_mat, fcov_, srisk_, score_, False, "woodbury")
    # return calc_theo_opt_wt(exp_mat, fcov_, srisk_, score_, False, "complete")


def calc_theo_opt_alpha(
    df_alpha: pd.DataFrame,
    exposure: pd.DataFrame,
    covariance: pd.DataFrame,
    df_risk: pd.DataFrame,
):
    """Calculate optimal theoretical alpha weights for multiple dates in parallel.

    Args:
        df_alpha (pd.DataFrame): Alpha signals/scores with MultiIndex ((date, BarraID),)
        exposure (pd.DataFrame): Factor exposure matrix with MultiIndex ((date, BarraID), factor)
        covariance (pd.DataFrame): Factor covariance matrix with MultiIndex ((date, factor), factor)
        df_risk (pd.DataFrame): Specific risk data with MultiIndex (date, BarraID)

    Returns:
        pd.DataFrame: Concatenated optimal weights for all dates with MultiIndex (date, BarraID)
    """
    df_alpha = df_alpha.copy()
    dates = df_alpha.index.get_level_values("date").unique()
    tasks = [(date, exposure, covariance, df_risk, df_alpha) for date in dates]
    results = tools.parallel(
        calc_theo_opt_alpha_1day, tasks, 5, "threading", True, "theo_opt"
    )
    results = {date: result for date, result in zip(dates, results)}
    return pd.concat(results, names=["date", "BarraID"])


%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/portfolio/portfolio_evaluator.py'
# coding = utf-8
import os
import numpy as np
import pandas as pd
from tqdm.auto import tqdm
from ..utils import processor as prep


def load_alpha(alpha_root: str, sDate: str, eDate: str) -> pd.DataFrame:
    """
    Load and concatenate alpha data from parquet files in the specified directory.

    Args:
        alpha_root (str): Directory path containing alpha parquet files named as 'date.parquet'

    Returns:
        pandas.DataFrame: DataFrame with MultiIndex ['date', 'BarraID']

    """
    dates = sorted([s.split(".")[0] for s in os.listdir(alpha_root)])
    dates = [date for date in dates if date >= sDate and date <= eDate]
    alphas = {}
    for date in tqdm(dates, desc="loading_alpha"):
        alphas[date] = pd.read_parquet(f"{alpha_root}{date}.parquet")
    alphas = pd.concat(alphas, names=["date"])
    if (
        len(alphas.index.names) == 3
    ):  ## (date, date, BarraID) index should remove the first date level
        alphas = alphas.droplevel(0)
        alphas.index.names = ["date", "BarraID"]
    return alphas


def prepare_xy(df_alpha, df_label):
    """
    Prepare alpha and label data for backtesting by aligning indexes and preprocessing.

    Args:
        df_alpha (pd.DataFrame): Alpha data with MultiIndex ['date', 'BarraID']
        df_label (pd.DataFrame): Label data with MultiIndex ['date', 'BarraID']

    Returns:
        tuple:
            - xy (pd.DataFrame): Joined and preprocessed alpha and label data
            - df_alpha (pd.DataFrame): Preprocessed alpha data
            - df_label (pd.DataFrame): Preprocessed label data

    """
    common_index = df_alpha.index.intersection(df_label.index)
    df_alpha = df_alpha.loc[common_index]
    df_label = df_label.loc[common_index]

    df_alpha = df_alpha.groupby("date").apply(lambda x: prep.as_weight(x)).droplevel(0)
    df_label = (
        df_label.groupby(level="date")
        .apply(lambda x: prep.winsorize(x, "percentile"))
        .droplevel(0)
    )

    if len(df_alpha.columns.intersection(df_label.columns)) > 0:
        assert "alpha name overlap with label names"
    xy = df_alpha.join(df_label, how="left").dropna()
    return xy, df_alpha, df_label


def calc_ret(xy: pd.DataFrame, num_alpha: int = 1):
    """
    Calculate returns for each alpha-label combination.

    Args:
        xy (pd.DataFrame): DataFrame containing alpha and label data
        num_alpha (int): Number of alpha columns in the DataFrame

    Returns:
        pd.DataFrame: Returns for each alpha-label combination, indexed by date

    """
    alpha_names = xy.columns[:num_alpha]
    label_names = xy.columns[num_alpha:]

    rets = []
    for label in label_names:
        x, y = xy[alpha_names], xy[label]
        ret = x.mul(y.to_numpy(), axis=0).sum(axis=0)
        ret /= x.abs().sum(axis=0)
        rets.append(ret)
    return pd.concat(rets, axis=1, keys=label_names)


def backtest(xy: pd.DataFrame, num_alpha: int = 1, corr_method: str = "pearson"):
    """
    Perform backtesting analysis by calculating information coefficients (IC) and returns.

    Args:
        xy (pd.DataFrame): DataFrame containing alpha and label data, with date index
        num_alpha (int): Number of alpha columns in the DataFrame

    Returns:
        tuple: A tuple containing:
            - ret (pd.DataFrame): Returns for each alpha combination, indexed by date and alpha
            - ic (pd.DataFrame): IC between alphas and labels, indexed by date and alpha

    """
    ic = xy.groupby("date").apply(
        lambda x: x.corr(method=corr_method).iloc[:num_alpha, num_alpha:]
    )
    ic.index.names = ["date", "alpha"]
    ic = ic.swaplevel(0, 1)
    ret = xy.groupby("date").apply(calc_ret)
    ret.index.names = ["date", "alpha"]
    ret = ret.swaplevel(0, 1)
    return ret, ic


def summary_turnover(df_alpha: pd.DataFrame):
    """calculate turnover for each date
    Args:
        df_alpha (pd.DataFrame): Alpha dataframe with dates and symbols as multi-index

    Returns:
        pd.Series: Turnover values for each date with alpha and date as index
    """
    # Reshape to have dates as columns
    weights = df_alpha.unstack(level=0)

    # Shift columns by 1 to get yesterday's weights
    weights_yesterday = weights.shift(1, axis=1).fillna(0.0)
    turnovers = (weights - weights_yesterday).abs().sum()
    turnovers.index.names = ["alpha", "date"]
    return turnovers


def calc_AvgDailyTvr(df_alpha: pd.DataFrame):
    """Calculate average daily turnover by year.

    Args:
        df_alpha (pd.DataFrame): Alpha dataframe with dates and symbols as multi-index

    Returns:
        pd.DataFrame: Average daily turnover for each year and overall, with years as index
            and 'dayTvr' as column
    """
    tvr = summary_turnover(df_alpha).droplevel(0)
    tvr = tvr.to_frame("dayTvr")
    tvr["year"] = pd.to_datetime(tvr.index).year
    tvr = tvr.groupby("year").mean()
    tvr.loc["all"] = tvr.mean(axis=0)
    return tvr


def calc_breath(df_alpha: pd.DataFrame):
    """calculate breath for each date, breath is the number of alpha for each date
    Args:
        df_alpha (pd.DataFrame): Alpha dataframe with dates and symbols as multi-index

    Returns:
        pd.DataFrame: Breath for each date and year
    """
    breath = df_alpha.groupby("date").count()
    breath["year"] = pd.to_datetime(breath.index).year
    breath = breath.groupby("year").mean().astype(int)
    breath.loc["all"] = df_alpha.groupby("date").count().mean().astype(int)
    return breath


def calc_days(df_alpha: pd.DataFrame):
    """calculate days for each year
    Args:
        df_alpha (pd.DataFrame): Alpha dataframe with dates and symbols as multi-index

    Returns:
        pd.DataFrame: Days for each year
    """
    df_alpha = df_alpha.copy()
    df_alpha = df_alpha.groupby("date").count()
    df_alpha["year"] = pd.to_datetime(df_alpha.index.get_level_values("date")).year
    days = df_alpha.groupby("year").count()
    days.loc["all"] = df_alpha.count()
    return days


def summary_max_drawdown(ret: pd.DataFrame):
    """calculate max drawdown
    Args:
        ret (pd.DataFrame): Returns dataframe with dates as index and return horizons as columns

    Returns:
        pd.Series: Maximum drawdown values for 1-day and 5-day horizons
    """
    ret = ret.loc[:, ret.columns.str.contains("_1$|_5$")].copy()
    cum_rets = (1 + ret).cumprod()
    running_max = cum_rets.expanding().max()
    drawdowns = cum_rets / running_max - 1
    res = drawdowns.min()
    res.index = ["maxDD_H1", "maxDD_H5"]
    return res


def summary_ann_and_sharpe(ret: pd.DataFrame):
    """calculate annualized return and sharpe ratio for H1 and H5
    Args:
        ret (pd.DataFrame): Returns dataframe with dates as index and return horizons as columns

    Returns:
        pd.Series: Annualized returns and Sharpe ratios for 1-day and 5-day horizons
    """
    ret = ret.loc[:, ret.columns.str.contains("_1$|_5$")].copy()
    ann = ret.sum() * 252 / len(ret)
    shp = ann.div(ret.std().to_numpy()) / np.sqrt(252)
    res = pd.concat([ann, shp], axis=0)
    res.index = ["RetH1", "RetH5", "SharpeH1", "SharpeH5"]
    res = res.loc[["SharpeH1", "RetH1", "SharpeH5", "RetH5"]]
    res["SharpeH5"] = res["SharpeH5"] / np.sqrt(5)
    return res


def summary_ic(ic: pd.DataFrame):
    """calculate mean IC and ICIR for H1 and H5
    Args:
        ic (pd.DataFrame): Information coefficient dataframe with dates as index and horizons as columns

    Returns:
        pd.Series: Mean IC and ICIR values for 1-day and 5-day horizons
    """
    ic = ic.loc[:, ic.columns.str.contains("_1$|_5$")].copy()
    res = pd.concat([ic.mean(), ic.std()])
    res.index = ["IC_H1", "IC_H5", "ICIR_H1", "ICIR_H5"]
    return res.loc[["IC_H1", "ICIR_H1", "IC_H5", "ICIR_H5"]]


def summary(ret: pd.DataFrame, ic: pd.DataFrame, df_alpha: pd.DataFrame):
    """summary returns, ic, max drawdown for each alpha and year
    Args:
        ret (pd.DataFrame): Returns dataframe with alpha and dates as multi-index
        ic (pd.DataFrame): Information coefficient dataframe with alpha and dates as multi-index
        df_alpha (pd.DataFrame): Alpha dataframe with dates and symbols as multi-index
    Returns:
        pd.DataFrame: Summary statistics including returns, Sharpe ratios, IC, ICIR and max drawdowns
                     grouped by alpha and year
    """
    alpha_names = ic.index.get_level_values("alpha").unique().tolist()

    results = {}
    for alpha_name in alpha_names:
        ret = ret.xs(alpha_name, level="alpha").copy()
        ic = ic.xs(alpha_name, level="alpha").copy()

        ## add year tag
        ic.loc[:, "year"] = pd.to_datetime(ic.index).to_period("Y")
        ret.loc[:, "year"] = pd.to_datetime(ret.index).to_period("Y")

        ## calc ann and sharpe
        res0 = summary_ann_and_sharpe(ret.drop(columns=["year"])).to_frame("all").T
        res1 = ret.groupby("year").apply(summary_ann_and_sharpe, include_groups=False)
        ret_ = pd.concat([res1, res0], axis=0)

        ## calc ic
        ic0 = summary_ic(ic.drop(columns=["year"])).to_frame("all").T
        ic1 = ic.groupby("year").apply(summary_ic, include_groups=False)
        ic_ = pd.concat([ic0, ic1], axis=0)

        ## calc max drawdown
        dd0 = summary_max_drawdown(ret.drop(columns=["year"])).to_frame("all").T
        dd1 = ret.groupby("year").apply(summary_max_drawdown, include_groups=False)
        dd_ = pd.concat([dd1, dd0], axis=0)

        results[alpha_name] = pd.concat([ret_, ic_, dd_], axis=1)

    results = pd.concat(results, axis=0, names=["alpha", "year"])

    breath = calc_breath(df_alpha)
    dtv = calc_AvgDailyTvr(df_alpha)
    days = calc_days(df_alpha)
    results["dayTvr"] = dtv["dayTvr"].values
    results["breath"] = breath.iloc[:, 0].values
    results["days"] = days.iloc[:, 0].values
    return results


from barra.portfolio import portfolio_evaluator as pe

alpha_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/test/fitting/EUTR/EU1.SP/EU1.SP1.01/alpha/'
sDate, eDate = '2018-01-01', '2024-06-30'
df_alpha = pe.load_alpha(alpha_root, sDate, eDate)
df_alpha.columns = ['alpha_test']

import barra.utils.processor as prep

hn_alpha = pd.read_pickle("/mnt/sda/NAS/ShareFolder/huaneng/dirt/europe_a/T5_Netural_Standard.pkl")
hn_alpha.columns.name = 'symbol'

hn_alpha.dropna(axis=1,how='all').shape

def reset_barraid_as_symbol(df_alpha:pd.DataFrame):
    alpha = df_alpha.stack('symbol').to_frame('alpha').reset_index('symbol')
    alpha['BarraID'] = np.nan
    dates = df_alpha.index.get_level_values('date').unique()
    mapping_root = '/mnt/sda/NAS/Global/mapping/bmll_barra_map/'
    for date in tqdm(dates,desc='load_mapping'):  
        dt = pd.read_csv(f'{mapping_root}{date}.csv',usecols=['Ticker_MIC','Barrid'])
        mapped = dict(zip(dt['Ticker_MIC'],dt['Barrid']))
        alpha.loc[date,'BarraID'] = alpha.loc[date,'symbol'].map(mapped)
    return alpha.loc[alpha['BarraID'].notna(),['BarraID','alpha']].set_index('BarraID',append=True)
    

hnalpha = reset_barraid_as_symbol(hn_alpha)
        
cfg = {'barra_product': 'EUTR', 'calendar': 'EU1', 'factor_group_name': 'EU1'}
hnalpha = hnalpha.groupby('date').apply(lambda x: prep.add_univer_mask(x, cfg))
hnalpha = hnalpha.loc[hnalpha['universe_mask']==1.0]

from barra.data import loader
return_map = {
    'idio': ['SpecificReturn', 'SpecRisk%'],
    'total': ['DlyReturn%', 'TotalRisk%'],
}

label_cfg = {
    'label_usecols': return_map.get('idio'), 
    'label_options': [f'H1_{i}' for i in range(1,21)], 
    'barra_product': 'EUTR',
    'calendar': 'EU1',
    }

sDate, eDate = df_alpha.index.get_level_values('date').unique()[[0,-1]].tolist()
df_label, df_risk = loader.load_label(sDate, eDate, label_cfg)

from barra.utils import processor as prep
import numpy as np


xy, df_alpha, df_label = pe.prepare_xy(hnalpha, df_label)
dtv = pe.summary_turnover(df_alpha)
ret, ic = pe.backtest(xy, num_alpha=1, corr_method='spearman')
df_summary = pe.summary(ret, ic)




%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/portfolio/pdf_reporter.py'
# coding = utf-8
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_pdf import PdfPages


def plot_cum_ret(ret: pd.DataFrame, pdf: PdfPages):
    """ plot cumulative return for each alpha
    Args:
        ret: DataFrame containing returns data with alpha and date multi-index
        pdf: PdfPages object to save plots to

    Returns:
        None
    """
    ret = ret.loc[:, ret.columns.str.contains("_1$|_5$|_10$")].copy()
    for alpha_name in ret.index.get_level_values("alpha").unique():
        ret_ = ret.xs(alpha_name, level="alpha").copy()
        cum_ret = ret_.add(1.0).cumprod().sub(1.0)

        fig, ax = plt.subplots()
        cum_ret.plot(grid=True, rot=30, figsize=(10, 6), alpha=0.8, ax=ax)
        plt.title(f"Portfolio Performance - {alpha_name}")
        plt.legend(loc="upper left")
        plt.tight_layout()
        pdf.savefig(fig, bbox_inches="tight")
        plt.close()


def plot_sharpe(ret: pd.DataFrame, pdf: PdfPages):
    """ plot sharpe ratio for each alpha for each horizon
    Args:
        ret: DataFrame containing returns data with alpha and date multi-index
        pdf: PdfPages object to save plots to

    Returns:
        None
    """
    ret = ret.copy()
    for alpha_name in ret.index.get_level_values("alpha").unique():
        ret_ = ret.xs(alpha_name, level="alpha").copy()
        ann = ret_.sum() * 252 / len(ret_)
        shp = ann.div(ret_.std().to_numpy()) / np.sqrt(252)
        scale = [np.sqrt(int(col.split('_')[-1])) for col in shp.index]
        shp = shp.div(scale, axis=0)

        fig, ax = plt.subplots()
        shp.plot(kind="bar", figsize=(10, 6), grid=True, alpha=0.9, ax=ax)
        plt.title(f"Sharpe Ratio - {alpha_name}")
        plt.xticks(range(len(shp)), [str(s) for s in range(1, len(shp) + 1)])
        plt.xlabel("Horizon")
        plt.ylabel("Sharpe")
        plt.tight_layout()
        pdf.savefig(fig, bbox_inches="tight")
        plt.close()


def plot_risk(ret: pd.DataFrame):
    """ left to be implemented
    Args:
        ret: DataFrame containing returns data with alpha and date multi-index

    Returns:
        None
    """
    pass


def plot_turnover(dtv: pd.Series, pdf: PdfPages):
    """ plot daily turnover for each alpha
    Args:
        dtv: Series containing turnover data with alpha and date multi-index
        pdf: PdfPages object to save plots to

    Returns:
        None
    """
    dtv = dtv.copy()
    for alpha_name in dtv.index.get_level_values("alpha").unique():
        dtv_ = dtv.xs(alpha_name, level="alpha").copy()

        fig, ax = plt.subplots()
        dtv_.plot(figsize=(10, 6), grid=True, alpha=0.9, ax=ax)
        plt.title(f"Daily Turnover - {alpha_name}")
        plt.tight_layout()
        pdf.savefig(fig, bbox_inches="tight")
        plt.close()


def plot_monthly_ret(ret: pd.DataFrame, pdf: PdfPages):
    """ plot monthly return for each alpha for H1 and H5
    Args:
        ret: DataFrame containing returns data with alpha and date multi-index
        pdf: PdfPages object to save plots to

    Returns:
        None
    """
    ret = ret.loc[:, ret.columns.str.contains("_1$|_5$")].copy()
    for alpha_name in ret.index.get_level_values("alpha").unique():
        ret_ = ret.xs(alpha_name, level="alpha").copy()
        ret_["month"] = pd.to_datetime(ret_.index.get_level_values("date")).to_period(
            "M"
        )
        mret = ret_.groupby(["month"]).apply(lambda x: x.add(1.0).prod().sub(1.0))
        mret.index = mret.index.astype(str)
        fig, ax = plt.subplots()
        mret.plot(
            marker="o", linestyle="--", figsize=(10, 6), grid=True, alpha=0.8, ax=ax
        )
        plt.title(f"Monthly Return - {alpha_name}")
        plt.tight_layout()
        plt.legend(loc="upper left")
        pdf.savefig(fig, bbox_inches="tight")
        plt.close()


def report(ret: pd.DataFrame, dtv: pd.Series, report_folder: str = ".", savetag: str=''):
    """ generate report for backtest results and save to file
    Args:
        ret: DataFrame containing returns data with alpha and date multi-index
        dtv: Series containing turnover data with alpha and date multi-index
        report_folder: String path to folder where report should be saved
        savetag: String tag to append to report filename

    Returns:
        None
    """
    if not os.path.exists(report_folder):
        os.makedirs(report_folder, exist_ok=True)
    with PdfPages(f"{report_folder}/portfolio_{savetag}.pdf") as pdf:
        plot_cum_ret(ret, pdf)
        plot_sharpe(ret, pdf)
        plot_turnover(dtv, pdf)
        plot_monthly_ret(ret, pdf)
    



from barra.data import loader
from barra.portfolio import portfolio_reporter as pr

pr.report(
    ret,
    dtv,
    report_folder="/mnt/sda/NAS/ShareFolder/lishuanglin/test/fitting/EUTR/",
    savetag="hnalpha",
)

%%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra/portfolio/html_reporter.py'
# coding = utf-8

import json
import jinja2
import numpy as np
import pandas as pd
from datetime import datetime
import plotly.graph_objects as go
from plotly.subplots import make_subplots

__author__ = "lishuanglin"
__version__ = "0.1.1"
__all__ = ['report']


def add_line(
    fig, x, y, row, col, line_name, mode="lines", dash="solid", showlegend=True
):
    """Add a line trace to a plotly figure
    
    Args:
        fig: plotly.graph_objects.Figure, the figure to add the line to
        x: array-like, x-axis values
        y: array-like, y-axis values  
        row: int, subplot row number
        col: int, subplot column number
        line_name: str, name of the line for legend
        mode: str, plotting mode ('lines', 'markers', 'lines+markers')
        dash: str, line dash style ('solid', 'dash', 'dot', etc)
        showlegend: bool, whether to show this line in legend
        
    Returns:
        None, modifies figure in place
    """
    fig.add_trace(
        go.Scatter(
            x=x,
            y=y,
            name=line_name,
            mode=mode,
            line=dict(dash=dash),
            showlegend=showlegend,
        ),
        row=row,
        col=col,
    )


def update_yaxes(fig, row, col, title):
    """Update y-axis properties of a plotly subplot
    
    Args:
        fig: plotly.graph_objects.Figure, the figure to update
        row: int, subplot row number
        col: int, subplot column number
        title: str, y-axis title
        
    Returns:
        None, modifies figure in place
    """
    fig.update_yaxes(
        title_text=title,
        row=row,
        col=col,
        gridcolor="lightgrey",
        range=[None, None],
        autorange=True,
        automargin=True,
    )


def create_performance_plots(
    ret_df: pd.DataFrame, dtv_df: pd.DataFrame, alpha_name: str
) -> str:
    """Create four performance plots for an alpha
    Args:
            ret_df: pd.DataFrame, the dataframe containing returns
            dtv_df: pd.DataFrame, the dataframe containing turnovers
            alpha_name: str, the name of the alpha

    Returns:
            str, the HTML string of the performance plots
    """
    # Convert date column and set index
    ret_df["date"] = pd.to_datetime(ret_df["date"])
    dtv_df["date"] = pd.to_datetime(dtv_df["date"])

    # Filter data for the specific alpha
    alpha_ret = ret_df[ret_df["alpha"] == alpha_name].copy()
    alpha_dtv = dtv_df[dtv_df["alpha"] == alpha_name].copy()

    # Get all return columns (contains Ret_)
    ret_cols = [col for col in alpha_ret.columns if "Ret_" in col]

    # Convert data to numeric
    for col in ret_cols:
        alpha_ret[col] = pd.to_numeric(alpha_ret[col], errors="coerce")
    alpha_dtv["dtv"] = pd.to_numeric(alpha_dtv["dtv"], errors="coerce")

    # Create figure with subplots
    fig = make_subplots(
        rows=2,
        cols=2,
        subplot_titles=(
            "Portfolio Performance",
            "Monthly Return",
            "Sharpe Ratio",
            "Daily Turnover",
        ),
        vertical_spacing=0.1,
        horizontal_spacing=0.08,
    )

    # Update figure size and layout
    fig.update_layout(
        width=1200,
        height=800,
        showlegend=True,
        margin=dict(t=40, b=40, l=40, r=40),
        template="plotly_white",
        plot_bgcolor="white",
        modebar=dict(orientation="h", bgcolor="rgba(255, 255, 255, 0.8)"),
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.04,
            xanchor="left",
            x=0.0,
            bgcolor="rgba(255, 255, 255, 0.8)",
            traceorder="normal",
            itemsizing="constant",
            itemwidth=30,
            itemclick="toggle",
            itemdoubleclick="toggleothers",
        ),
    )

    # 1. Portfolio Performance (Cumulative Return, only plots H1, H5 and H10)
    h1_h5_cols = [col for col in ret_cols if col.endswith(("_1", "_5", "_10"))]
    for col in h1_h5_cols:
        cum_ret = alpha_ret[col].cumsum()
        add_line(fig, alpha_ret["date"], cum_ret, 1, 1, col, dash="solid")
    fig.update_xaxes(title_text="Date", row=1, col=1, gridcolor="lightgrey")
    update_yaxes(fig, row=1, col=1, title="Cumulative Return")

    # 2. Monthly Return
    # Set date as index for resampling
    alpha_ret.set_index("date", inplace=True)

    # Calculate monthly returns for H1 and H5
    monthly_rets = {}
    to_plot_cols = [s for s in ret_cols if s.endswith(("_1", "_5"))]
    for col in to_plot_cols:
        if col in ret_cols:
            # Calculate monthly return
            monthly_data = pd.DataFrame(index=alpha_ret.index)
            monthly_data[col] = alpha_ret[col]
            monthly_ret = monthly_data.resample("ME")[col].sum()
            monthly_rets[col] = monthly_ret.dropna()

    # Plot monthly returns
    for col, monthly_ret in monthly_rets.items():
        x, y = monthly_ret.index, monthly_ret
        add_line(fig, x, y, 1, 2, f"{col} Monthly", mode="lines+markers", dash="dash")
    fig.update_xaxes(title_text="Horizon", row=1, col=2, gridcolor="lightgrey")
    update_yaxes(fig, row=1, col=2, title="Monthly Return")

    # 3. Sharpe Ratio
    ret_data = alpha_ret[ret_cols].apply(pd.to_numeric, errors="coerce")
    ann_ret = ret_data.mean() * 252
    sharpe = ann_ret / (ret_data.std() * np.sqrt(252))
    scale = [np.sqrt(int(col.split("_")[-1])) for col in ret_cols]
    sharpe = sharpe / scale

    x_labels = [str(i) for i in range(1, len(ret_cols) + 1)]
    fig.add_trace(go.Bar(x=x_labels, y=sharpe, showlegend=False), row=2, col=1)
    fig.update_xaxes(title_text="Date", row=2, col=1, gridcolor="lightgrey")
    update_yaxes(fig, row=2, col=1, title="Sharpe Ratio")

    # 4. Daily Turnover
    date, dtv = alpha_dtv["date"], alpha_dtv["dtv"]
    add_line(fig, date, dtv, row=2, col=2, line_name="Turnover", showlegend=False)
    fig.update_xaxes(title_text="Date", row=2, col=2, gridcolor="lightgrey")
    update_yaxes(fig, row=2, col=2, title="Turnover")

    fig.update_xaxes(showgrid=True)
    fig.update_yaxes(showgrid=True)
    return fig.to_html(full_html=False, include_plotlyjs="cdn")


def format_percentage(value: float, decimals: int = 2) -> str:
    """Format value as percentage with specified decimal places
    Args:
            value: float, the value to be formatted
            decimals: int, the number of decimal places

    Returns:
            str, the formatted string of the percentage value
    """
    return f"{value * 100:.{decimals}f} %"


def format_float(value: float, decimals: int = 3) -> str:
    """Format float value with 3 decimal places
    Args:
            value: float, the value to be formatted
            decimals: int, the number of decimal places

    Returns:
            str, the formatted string of the float value
    """
    return f"{value:.{decimals}f}"


def config_to_table(config: dict) -> str:
    """Convert config dictionary to HTML table format
    Args:
            config: dict, the config dictionary for portfolio construction

    Returns:
            str, the HTML string of the config table
    """
    keys = list(config.keys())[:-2]
    rows = [keys[i : i + 6] for i in range(0, len(keys), 6)]

    table_html = '<table class="config-table">'
    for row in rows:
        table_html += "<tr>"
        for key in row:
            value = config.get(key, "")
            table_html += f"<td><strong>{key}</strong><br>{value}</td>"
        table_html += "</tr>"
    table_html += "</table>"
    return table_html


def style_table(df: pd.DataFrame, h1_columns: list) -> str:
    """Apply styles to the table
    Args:
            df: 2-dimensional pd.DataFrame, the dataframe to be styled
            h1_columns: list, the columns to be bolded

    Returns:
            str, the HTML string of the styled table
    """
    # Create a copy of the DataFrame
    styled_df = df.copy()

    # Create the HTML table header with dataframe columns
    html = '<table border="1" class="dataframe">\n<thead>\n<tr style="text-align: right;">\n'
    for col in styled_df.columns:
        html += f"<th>{col}</th>"
    html += "</tr>\n</thead>\n<tbody>\n"

    # Add rows
    for idx, row in styled_df.iterrows():
        is_all_row = row["year"] == "all"
        html += "<tr" + (' class="all-row"' if is_all_row else "") + ">"
        for col in styled_df.columns:
            # Add bold style for H1 columns or all row
            is_h1_col = col in h1_columns
            cell_style = (
                ' style="font-weight: bold;"' if (is_h1_col or is_all_row) else ""
            )
            html += f"<td{cell_style}>{row[col]}</td>"
        html += "</tr>\n"

    html += "</tbody>\n</table>"
    return html


def generate_html_report(
    config: dict,
    df_summary_raw: pd.DataFrame,
    df_summary_theo: pd.DataFrame,
    df_ret_raw: pd.DataFrame,
    df_dtv_raw: pd.DataFrame,
    df_ret_theo: pd.DataFrame,
    df_dtv_theo: pd.DataFrame,
    output_file: str,
):
    """Generate HTML report
    Args:
            config: dict, the config dictionary for portfolio construction
            df_summary_raw: pd.DataFrame, summary of performance of raw alpha
            df_summary_theo: pd.DataFrame, summary of performance of theoretical optimization
            df_ret_raw: pd.DataFrame, daily return of raw alpha
            df_dtv_raw: pd.DataFrame, daily turnover of raw alpha
            df_ret_theo: pd.DataFrame, daily return of theoretical optimization
            df_dtv_theo: pd.DataFrame, daily turnover of theoretical optimization
            output_file: str, path to save the html file, e.g. './gitrepo/report_test.html'

    Returns:
            None
    """
    # Create HTML template
    template = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Portfolio Performance Report</title>
        <style>
            body { 
                font-family: Arial, sans-serif; 
                margin: 0;
                display: flex;
            }
            .sidebar {
                width: 150px;
                background-color: #f8f9fa;
                padding: 10px 20px;
                height: 100vh;
                position: fixed;
                overflow-y: auto;
                transition: transform 0.3s ease;
            }
            .sidebar.collapsed {
                transform: translateX(-130px);
            }
            .sidebar-toggle {
                position: absolute;
                right: 10px;
                top: 10px;
                cursor: pointer;
                font-size: 20px;
                z-index: 1000;
            }
            .content {
                margin-left: 160px;
                padding: 10px 40px;
                width: calc(100% - 280px);
                transition: margin-left 0.3s ease;
            }
            .content.expanded {
                margin-left: 70px;
                width: calc(100% - 150px);
            }
            h1, h2, h3 { color: #333; }
            table { 
                border-collapse: collapse; 
                width: 100%; 
                margin: 20px 0; 
            }
            .config-table {
                width: 100%;
                margin: 20px 0;
            }
            .config-table td {
                padding: 10px;
                border: 1px solid #ddd;
                width: 20%;
            }
            th, td { 
                border: 1px solid #ddd; 
                padding: 8px; 
                text-align: left; 
            }
            th { background-color: #f2f2f2; }
            tr:nth-child(even) { background-color: #f9f9f9; }
            .nav-item {
                margin: 5px 0;
                cursor: pointer;
                padding-left: 10px;
            }
            .nav-item:hover {
                color: #007bff;
            }
            .nav-group {
                margin: 15px 0;
            }
            .nav-subitem {
                margin: 5px 0;
                padding-left: 20px;
                cursor: pointer;
                font-size: 0.9em;
            }
            .nav-subitem:hover {
                color: #007bff;
            }
            .section {
                margin-bottom: 40px;
            }
            .h1-column {
                font-weight: bold;
            }
            .all-row {
                font-weight: bold;
            }
            .plot-container {
                margin: 20px 0;
                padding: 10px;
                background-color: white;
                border-radius: 5px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
        </style>
        <script>
            function toggleSidebar() {
                const sidebar = document.querySelector('.sidebar');
                const content = document.querySelector('.content');
                sidebar.classList.toggle('collapsed');
                content.classList.toggle('expanded');
            }
            
            function scrollToSection(sectionId) {
                document.getElementById(sectionId).scrollIntoView({
                    behavior: 'smooth'
                });
            }
            
            window.onload = function() {
                if (document.querySelector('.section')) {
                    document.querySelector('.section').scrollIntoView();
                }
            }
        </script>
    </head>
    <body>
        <div class="sidebar">
            <div class="sidebar-toggle" onclick="toggleSidebar()">☰</div>
            <h2>Navigation</h2>
            {% for alpha in alpha_names %}
            <div class="nav-group">
                <div class="nav-item"><strong>{{ alpha }}</strong></div>
                <div class="nav-subitem" onclick="scrollToSection('{{ alpha }}_config')">Config</div>
                <div class="nav-subitem" onclick="scrollToSection('{{ alpha }}_raw')">Raw Alpha</div>
                <div class="nav-subitem" onclick="scrollToSection('{{ alpha }}_raw_plots')">Raw Plots</div>
                <div class="nav-subitem" onclick="scrollToSection('{{ alpha }}_theo')">Theo Opt</div>
                <div class="nav-subitem" onclick="scrollToSection('{{ alpha }}_theo_plots')">Theo Plots</div>
            </div>
            {% endfor %}
        </div>
        
        <div class="content">
            <h1>Portfolio Performance Report</h1>
            <p>Generated at: {{ timestamp }}</p>
            
            {% for alpha in alpha_names %}
            <div class="section">
                <h2>{{ alpha }}</h2>
                
                <div id="{{ alpha }}_config">
                    <h3>Config</h3>
                    {{ config_tables[alpha] }}
                </div>
                
                <div id="{{ alpha }}_raw">
                    <h3>Raw Alpha Performance</h3>
                    {{ raw_alpha_tables[alpha] }}
                </div>
                
                <div id="{{ alpha }}_raw_plots">
                    <h3>Raw Alpha Plots</h3>
                    <div class="plot-container">
                        {{ raw_plots[alpha] }}
                    </div>
                </div>
                
                <div id="{{ alpha }}_theo">
                    <h3>Theoretical Optimization Performance</h3>
                    {{ theo_opt_tables[alpha] }}
                </div>
                
                <div id="{{ alpha }}_theo_plots">
                    <h3>Theoretical Optimization Plots</h3>
                    <div class="plot-container">
                        {{ theo_plots[alpha] }}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </body>
    </html>
    """

    # Prepare data
    if "alpha" in df_summary_raw.index.names:
        alpha_names = sorted(df_summary_raw.index.get_level_values("alpha").unique())
    else:
        alpha_names = sorted(df_summary_raw["alpha"].unique())

    raw_alpha_tables = {}
    theo_opt_tables = {}
    config_tables = {}
    raw_plots = {}
    theo_plots = {}

    # Define H1 columns
    h1_columns = ["SharpeH1", "RetH1", "IC_H1", "ICIR_H1", "maxDD_H1"]

    # Load configs and format tables for each alpha
    for alpha in alpha_names:
        # Load config and convert to table format
        # config = load_config(alpha.lower())
        config_tables[alpha] = config_to_table(config)

        # Create performance plots
        raw_plots[alpha] = create_performance_plots(df_ret_raw, df_dtv_raw, alpha)
        theo_plots[alpha] = create_performance_plots(
            df_ret_theo, df_dtv_theo, alpha
        )  # Use ret2 and dtv2 for theo plots

        # Filter and format table data
        raw_alpha_data = (
            df_summary_raw[df_summary_raw["alpha"] == alpha]
            .copy()
            .drop(columns=["alpha"])
        )
        theo_opt_data = (
            df_summary_theo[df_summary_theo["alpha"] == alpha]
            .copy()
            .drop(columns=["alpha"])
        )

        # Format percentage columns with 2 decimal places
        percentage_columns = ["RetH1", "RetH5", "maxDD_H1", "maxDD_H5"]
        for col in percentage_columns:
            raw_alpha_data[col] = raw_alpha_data[col].apply(
                lambda x: format_percentage(x, 2)
            )
            theo_opt_data[col] = theo_opt_data[col].apply(
                lambda x: format_percentage(x, 2)
            )

        # Format float columns
        float_columns = [
            "SharpeH1",
            "SharpeH5",
            "IC_H1",
            "ICIR_H1",
            "IC_H5",
            "ICIR_H5",
            "dayTvr",
        ]
        for col in float_columns:
            raw_alpha_data[col] = raw_alpha_data[col].apply(format_float)
            theo_opt_data[col] = theo_opt_data[col].apply(format_float)

        # Generate HTML tables with custom styles
        raw_alpha_tables[alpha] = style_table(raw_alpha_data, h1_columns)
        theo_opt_tables[alpha] = style_table(theo_opt_data, h1_columns)

    # Render template
    template = jinja2.Template(template)
    html_content = template.render(
        timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        alpha_names=alpha_names,
        raw_alpha_tables=raw_alpha_tables,
        theo_opt_tables=theo_opt_tables,
        config_tables=config_tables,
        raw_plots=raw_plots,
        theo_plots=theo_plots,
    )

    # Save HTML file
    with open(output_file, "w", encoding="utf-8") as f:
        f.write(html_content)


def report(
    config: dict,
    df_summary_raw: pd.DataFrame,
    df_summary_theo: pd.DataFrame,
    df_ret_raw: pd.DataFrame,
    df_dtv_raw: pd.DataFrame,
    df_ret_theo: pd.DataFrame,
    df_dtv_theo: pd.DataFrame,
    report_file: str,
):
    """Generate HTML report for portfolio performance
    Args:
            config: dict, the config dictionary for portfolio construction
            df_summary_raw: pd.DataFrame, summary of performance of raw alpha
            df_summary_theo: pd.DataFrame, summary of performance of theoretical optimization
            df_ret_raw: pd.DataFrame, daily return of raw alpha
            df_dtv_raw: pd.DataFrame, daily turnover of raw alpha
            df_ret_theo: pd.DataFrame, daily return of theoretical optimization
            df_dtv_theo: pd.DataFrame, daily turnover of theoretical optimization
            report_file: str, path to save the html file, e.g. './gitrepo/report_test.html'

    Returns:
            None
    """
    # Generate report
    generate_html_report(
        config,
        df_summary_raw,
        df_summary_theo,
        df_ret_raw,
        df_dtv_raw,
        df_ret_theo,
        df_dtv_theo,
        report_file,
    )
    print(f"Report generated at: {report_file}")




# %%writefile '/mnt/sda/home/<USER>/working/gitrepo/barra_demo/port.demo.py'
# coding = utf-8

import os
import time
import numpy as np
import pandas as pd
from tqdm.auto import tqdm

from barra.data import loader
from barra.utils import tools
from barra.utils import processor as prep
from barra.portfolio import theo_opt
from barra.portfolio import portfolio_evaluator as pe

# from barra.portfolio import portfolio_reporter as pr
from barra.portfolio import html_reporter

return_type_map = {"idio": "SpecificReturn", "total": "DlyReturn%"}
risk_type_map = {"idio": "SpecRisk%", "total": "TotalRisk%"}


prefix = "./"
prefix = "/mnt/sda/home/<USER>/working/gitrepo/barra_demo/"
user_path = f"{prefix}configs/user.path.json"
user_root = tools.parse_user_root(user_path)

if tools.is_notebook():
    # cfg = loader.load_config(f"{prefix}configs/portfolio/puda.ftiming.json")
    cfg = loader.load_config(f"{prefix}configs/portfolio/spraw2_tw4.json")
    timetag = time.strftime("%Y%m%d_%H%M%S")
else:
    args = tools.parse_dynamic_args()
    assert args.config is not None, "portfolio config file is required!"
    cfg = loader.load_config(args.config)  ## load portfolio config
    timetag = (
        args.timetag if hasattr(args, "timetag") else time.strftime("%Y%m%d_%H%M%S")
    )


## loading alpha  / factor data
barra_product = cfg.get("barra_product")
alpha_group_name = cfg.get("alpha_group_name")
alpha_name = cfg.get("alpha_name")
alpha_root = (
    f"{user_root}/fitting/{barra_product}/{alpha_group_name}/{alpha_name}/alpha/"
)
sDate, eDate = cfg.get("sDate"), cfg.get("eDate")
df_alpha = pe.load_alpha(alpha_root, sDate, eDate)
df_alpha.columns = [cfg.get("alpha_name")]

## check alpha universe and rootid
df_alpha = prep.check_universe_mask(df_alpha, cfg).fillna(0.0)
df_alpha = df_alpha.loc[df_alpha["universe_mask"] == 1.0].drop(
    columns=["universe_mask"]
)
df_alpha = prep.check_rootid_mask(df_alpha, cfg)
df_alpha = df_alpha.loc[df_alpha["rootid_mask"] == 1.0].drop(columns=["rootid_mask"])
barraids = df_alpha.index.get_level_values("BarraID").unique().tolist()

## loading label / risk data
cfg["label_usecols"] = [
    return_type_map[cfg.get("return_type")],
    risk_type_map[cfg.get("risk_type")],
]
cfg["label_options"] = [f"H1_{i}" for i in range(1, cfg.get("label_horizon") + 1)]
df_label, df_risk = loader.load_label(sDate, eDate, cfg, barraids)


## recap the label and risk
def yprep(df: pd.DataFrame):
    df = prep.winsorize(df, "percentile", percentile=0.01)
    return df


df_label = df_label.groupby("date").apply(yprep).droplevel(0).fillna(0.0)
df_risk = df_risk.groupby("date").apply(yprep).droplevel(0).fillna(0.0) * 100.0
df_risk = df_risk.clip(5.0, np.inf)

# ## backtest the raw alpha
xy1, df_alpha1, df_label1 = pe.prepare_xy(df_alpha, df_label)
dtv1 = pe.summary_turnover(df_alpha1)
ret1, ic1 = pe.backtest(xy1, num_alpha=1)
df_summary1 = pe.summary(ret1, ic1, df_alpha1)

# loading exposure / covariance data
exposure = loader.load_asset_exposure(sDate, eDate, cfg, barraids)
covariance = loader.load_factor_covariance(sDate, eDate, cfg)
opt_score = theo_opt.calc_theo_opt_alpha(df_alpha1, exposure, covariance, df_risk)

## backtest the optimized alpha
xy2, df_alpha2, df_label2 = pe.prepare_xy(opt_score, df_label)
dtv2 = pe.summary_turnover(df_alpha2)
ret2, ic2 = pe.backtest(xy2, num_alpha=1)
df_summary2 = pe.summary(ret2, ic2, df_alpha2)

df_summary1_ = df_summary1.reset_index()
df_summary2_ = df_summary2.reset_index()
df_ret1_ = ret1.reset_index()
df_ret2_ = ret2.reset_index()
df_dtv1_ = dtv1.to_frame(name='dtv').reset_index().iloc[1:]
df_dtv2_ = dtv2.to_frame(name='dtv').reset_index().iloc[1:]

report_file = './gitrepo/barra_demo/report/report.spraw2_tw4.html'
html_reporter.report(
    cfg,
    df_summary_raw=df_summary1_,
    df_summary_theo=df_summary2_,
    df_ret_raw=df_ret1_,
    df_dtv_raw=df_dtv1_,
    df_ret_theo=df_ret2_,
    df_dtv_theo=df_dtv2_,
    report_file=report_file,
)

import pandas as pd
pd.read_parquet('/mnt/sda/NAS/ShareFolder/lishuanglin/test/fitting/EUTR/EU1.SP/EU1.SP1.01/alpha/2024-01-02.parquet')




import os
import pandas as pd
from tqdm.auto import tqdm

barra_product = 'GEM3'
factor_group_name = 'euronext'
alpha_group_name = 'euronext'
alpha_name = 'factor_timing6'

puda_root = f'/mnt/sda/NAS/ShareFolder/pengpuda/daily_update_factor/euronext/{alpha_name}/EU1/raw/'
user_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/test/'
alpha_root = f'{user_root}/fitting/{barra_product}/{factor_group_name}/{alpha_name}/alpha/'

os.makedirs(alpha_root, exist_ok=True)
dates = sorted([s.split('.')[0] for s in os.listdir(puda_root) if s.endswith('.parquet')])

for date in tqdm(dates):
    alpha = pd.read_parquet(f'{puda_root}{date}.parquet')
    alpha.index.name = 'BarraID'
    next_day = calendar.next_trading_days('EU1', date, 1)[0]
    alpha.to_parquet(f'{alpha_root}{next_day}.parquet')







import os
import pandas as pd
from joblib import Parallel, delayed
from tqdm.auto import tqdm


class InfoDataProcessor:
    def __init__(self, info_root, barra_product):
        self.info_root = info_root
        self.barra_product = barra_product

        if self.barra_product == 'CNTR':
            self.PRODUCT = 'SMD'
        else:
            self.PRODUCT = 'GMD'

        if self.barra_product in {'EUTR','CNTR'}:
            self.product = f'{self.barra_product}D'
        else:
            self.product = f'{self.barra_product}S'
        
        if self.barra_product in ['EUE4']:
            self.product = 'EUE4BAS'
        
    def load_DlyRet(self, date):
        date_ = date.replace('-','')
        year = date.split('-')[0]
        if self.barra_product in ['EUE4']: product = 'EUE4BAS'
        else: product = self.barra_product
        group = f'{self.PRODUCT}_{product}_100_D_{year}'
        folders = sorted([s for s in os.listdir(f'{self.info_root}') if s.startswith(group)])
        if len(folders) == 0:
            raise ValueError(f'No data for {date} in {self.info_root}')

        for folder in folders:
            filename = f'{product}_100_Asset_DlySpecRet_{date_}.csv'
            filepath = f'{self.info_root}{folder}/{filename}'
            if os.path.exists(filepath):
                df = pd.read_csv(filepath).drop(columns=['DataDate'])
                return df.rename(columns={'Barrid': 'BarraID'})

    def load_Exposure(self, date, load_etf_exposure=False):
        date_ = date.replace('-','')
        year = date.split('-')[0]
        if self.barra_product in ['EUE4']: product = 'EUE4BASS'
        else: product = self.product
        group = f'{self.PRODUCT}_{product}_100_D_{year}'
        folders = sorted([s for s in os.listdir(f'{self.info_root}') if s.startswith(group)])

        if len(folders) == 0:
            raise ValueError(f'No data for {date} in {self.info_root}{folders[0]}')

        for folder in folders:
            filename = f'{product}_100_Asset_Exposure_{date_}.csv'
            filepath = f'{self.info_root}{folder}/{filename}'
            if os.path.exists(filepath):
                df = pd.read_csv(filepath).drop(columns=['DataDate'])
                df = df.rename(columns={'Barrid': 'BarraID'})

                if load_etf_exposure:
                    filename = f'{product}_ETF_100_Asset_Exposure_{date_}.csv'
                    filepath = f'{self.info_root}{folder}/{filename}'
                    if os.path.exists(filepath):
                        df_etf = pd.read_csv(filepath).drop(columns=['DataDate'])
                        df_etf = df_etf.rename(columns={'Barrid': 'BarraID'})
                        df = pd.concat([df, df_etf], axis=0)
                
                return df
            
            

    def load_cov_mat(self, date: str):
        date_ = date.replace('-','')
        year = date.split('-')[0]
        if self.barra_product in ['EUE4']: product = 'EUE4BASS'
        elif self.barra_product in ['CNTR', 'EUTR']: product = f'{self.barra_product}D'
        else: product = f'{self.barra_product}S'
        group = f'{self.PRODUCT}_{product}_100_D_{year}'
        folders = sorted([s for s in os.listdir(f'{self.info_root}') if s.startswith(group)])
        if len(folders) == 0:
            raise ValueError(f'No data for {date} in {self.info_root}')
        for folder in folders:
            filename = f'{product}_100_Covariance_{date_}.csv'
            filepath = f'{self.info_root}{folder}/{filename}'
            if os.path.exists(filepath):
                cov_mat = pd.read_csv(filepath)
                cov_mat = cov_mat.pivot(index='Factor1', columns='Factor2', values='VarCovar')
                return cov_mat.where(cov_mat.notna(), cov_mat.T)


    def load_Price(self, date):
        date_ = date.replace('-','')
        year = date.split('-')[0]
        if self.barra_product in ['EUE4']: product = 'EUE4BAS'
        else: product = self.barra_product
        group = f'{self.PRODUCT}_{product}_100_D_{year}'
        folders = sorted([s for s in os.listdir(f'{self.info_root}') if s.startswith(group)])

        if len(folders) == 0:
            raise ValueError(f'No data for {date} in {self.info_root}')

        for folder in folders:
            filename = f'{self.barra_product}_Daily_Asset_Price_{date_}.csv'
            filepath = f'{self.info_root}{folder}/{filename}'
            if os.path.exists(filepath):
                df = pd.read_csv(filepath).drop(columns=['DataDate'])
                return df.rename(columns={'Barrid': 'BarraID'})
        
    def load_ESTU(self, date):
        date_ = date.replace('-','')
        year = date.split('-')[0]
        if self.barra_product in ['EUE4']: product = 'EUE4BAS'
        else: product = self.barra_product
        group = f'{self.PRODUCT}_{product}_100_D_{year}'
        folders = sorted([s for s in os.listdir(f'{self.info_root}') if s.startswith(group)])

        for folder in folders:
            filename = f'{self.barra_product}_ESTU_POR_{date_}.csv'
            filepath = f'{self.info_root}{folder}/{filename}'
            if os.path.exists(filepath):
                df = pd.read_csv(filepath)
                return df.rename(columns={'Barrid': 'BarraID'})

    def load_MarketData(self, date):
        date_ = date.replace('-','')
        year = date.split('-')[0]
        if self.barra_product in ['EUE4']: product = 'EUE4BAS'
        else: product = self.barra_product
        group = f'{self.PRODUCT}_{product}_100_D_{year}'
        folders = sorted([s for s in os.listdir(f'{self.info_root}') if s.startswith(group)])

        for folder in folders:
            filename = f'{self.barra_product}_Market_Data_{date_}.csv'
            filepath = f'{self.info_root}{folder}/{filename}'
            if os.path.exists(filepath):
                df = pd.read_csv(filepath).drop(columns=['DataDate'])
                return df.rename(columns={'Barrid': 'BarraID'})
    
    def load_Rates(self, date):
        date_ = date.replace('-','')
        year = date.split('-')[0]
        if self.barra_product in ['EUE4']: product = 'EUE4BAS' 
        else: product = self.barra_product
        group = f'{self.PRODUCT}_{product}_100_D_{year}'
        folders = sorted([s for s in os.listdir(f'{self.info_root}') if s.startswith(group)])

        for folder in folders:
            filename = f'{self.barra_product}_Rates_{date_}.csv'
            filepath = f'{self.info_root}{folder}/{filename}'
            if os.path.exists(filepath):
                df = pd.read_csv(filepath).drop(columns=['DataDate'])
                return df.rename(columns={'Barrid': 'BarraID'})
        
    def load_Descriptor(self, date):
        date_ = date.replace('-','')
        year = date.split('-')[0]
        group = f'{self.PRODUCT}_{self.barra_product}_100_Descriptor_D_{year}'
        folders = sorted([s for s in os.listdir(f'{self.info_root}') if s.startswith(group)])

        for folder in folders:
            filename = f'{self.barra_product}_100_Asset_Descriptor_{date_}.csv'
            filepath = f'{self.info_root}{folder}/{filename}'
            if os.path.exists(filepath):
                df = pd.read_csv(filepath).drop(columns=['DataDate'])
                return df.rename(columns={'Barrid': 'BarraID'})
    
    def load_Data(self, date):
        date_ = date.replace('-','')
        year = date.split('-')[0]
        if self.barra_product in ['EUE4']: product = 'EUE4BASS'
        else: product = self.product
        group = f'{self.PRODUCT}_{product}_100_D_{year}'
        folders = sorted([s for s in os.listdir(f'{self.info_root}') if s.startswith(group)])

        if len(folders) == 0:
            raise ValueError(f'No data for {date} in {self.info_root}')

        for folder in folders:
            filename = f'{product}_100_Asset_Data_{date_}.csv'
            filepath = f'{self.info_root}{folder}/{filename}'
            if os.path.exists(filepath):
                df = pd.read_csv(filepath).drop(columns=['DataDate'])
                return df.rename(columns={'Barrid': 'BarraID'})

    def load_id_map(self, date):
        if self.barra_product in {'EUTR','EULT'}:
            map_root = f'/mnt/sda/NAS/Global/Barra/barra_parsed/eu/{self.barra_product.lower()}/'
        # elif self.barra_product in ['EUE4']:
        #     map_root = f'/mnt/sda/NAS/Global/Barra/barra_parsed/eue4bas/'
        else:
            map_root = f'/mnt/sda/NAS/Global/Barra/barra_parsed/{self.barra_product.lower()}/'
        map_folders = sorted([s for s in os.listdir(map_root) \
                              if s.startswith(f'{self.PRODUCT}_{self.barra_product}_LOCALID_ID')])
        map_dir = f'{map_root}{map_folders[-1]}/'
        file = [s for s in os.listdir(map_dir) if 'LOCALID_Asset_ID' in s][0]
        map_path = f'{map_dir}{file}'
        id_map = pd.read_csv(map_path,dtype=str)
        id_map[['StartDate','EndDate']] = id_map[['StartDate','EndDate']].apply(lambda x: pd.to_datetime(x.str[:-2],format='%Y%m%d'))
        id_map = id_map.loc[(id_map['StartDate']<=date) & (id_map['EndDate']>=date)].rename(columns={'Barrid': 'BarraID'})
        return id_map[['BarraID','AssetID']]
    
    def load_OpMIC_map(self):
        xchg_map_path = '/mnt/sda/NAS/Global/mapping/ISO10383_MIC.csv'
        df = pd.read_csv(xchg_map_path).rename(columns={'OPERATING MIC': 'OpMIC'})
        return df[['MIC','OpMIC']]

    def load_ISIN(self, date):
        isin_root = '/mnt/sda/NAS/Global/Barra/barra_parsed/barradirect/isin/'
        isin_folders = sorted([s for s in os.listdir(isin_root) if s.startswith('ISIN_FULL_WE')])
        isin_dir = f'{isin_root}{isin_folders[-1]}/'
        file = [s for s in os.listdir(isin_dir) if 'ISIN_FULL_WE' in s][0]

        filepath = f'{isin_dir}{file}'
        if os.path.exists(filepath):
            df = pd.read_csv(filepath).drop(columns=['Start Date','End Date'])
            return df.rename(columns={'#Barra ID': 'BarraID'})

    def load_RIC(self, date):
        ric_root = '/mnt/sda/NAS/Global/Barra/barra_parsed/barradirect/ric/'
        ric_folders = sorted([s for s in os.listdir(ric_root) if s.startswith('RIC_FULL_WE')])
        ric_dir = f'{ric_root}{ric_folders[-1]}/'
        file = [s for s in os.listdir(ric_dir) if 'RIC_FULL_WE' in s][0]

        filepath = f'{ric_dir}{file}'
        if os.path.exists(filepath):
            df = pd.read_csv(filepath).drop(columns=['Start Date','End Date'])
            return df.rename(columns={'#Barra ID': 'BarraID'})

    def load_EID(self, date):
        usecols = ['#BARRA ID', 'MIC', 'Country of Exposure']
        eid_root = '/mnt/sda/NAS/Global/Barra/barra_parsed/barradirect/equityidentity/'
        eid_folders = sorted([s for s in os.listdir(eid_root) if s.startswith('EquityIdentity_FULL_WE')])
        eid_dir = f'{eid_root}{eid_folders[-1]}/'
        file = [s for s in os.listdir(eid_dir) if 'EquityIdentity_FULL_WE' in s][0]

        filepath = f'{eid_dir}{file}'
        if os.path.exists(filepath):
            df = pd.read_csv(filepath,usecols=usecols)
            return df.rename(columns={'#BARRA ID': 'BarraID', 'Country of Exposure':'Country_of_Exposure'})
    
    def load_CTRY_MAP(self, date):
        ctry_root = '/mnt/sda/NAS/Global/Barra/barra_parsed/barradirect/'
        folders = sorted([s for s in os.listdir(ctry_root) if s.startswith('Country_Master_WE')])
        ctry_dir = f'{ctry_root}{folders[-1]}/'
        file = [s for s in os.listdir(ctry_dir) if 'Country_Master_WE' in s][0]

        filepath = f'{ctry_dir}{file}'
        if os.path.exists(filepath):
            df = pd.read_csv(filepath,usecols=['CountrySymbol (ISO2)', 'CountrySymbol (ISO3)']).dropna().drop_duplicates()
            df = df.rename(columns={'CountrySymbol (ISO2)': 'Country_ISO2', 'CountrySymbol (ISO3)': 'Country_ISO3'})
            return dict(zip(df['Country_ISO2'].tolist(), df['Country_ISO3'].tolist()))


def get_info_indcell(dly_exp, indset):
    info = dly_exp.loc[dly_exp['Factor'].isin(indset)]
    info = info.drop_duplicates(['BarraID'])
    return info.iloc[:, :2].rename(columns={'Factor': 'indcell'})


def get_info_region(dly_exp, region_set):
    info = dly_exp.loc[dly_exp['Factor'].isin(region_set)]
    info = info.drop_duplicates(['BarraID'])
    return info.iloc[:, :2].rename(columns={'Factor': 'regioncell'})


def get_info_style(dly_exp, style_set):
    info = dly_exp.loc[dly_exp['Factor'].isin(style_set)]
    info_style = info.set_index(['BarraID','Factor']).unstack(1).droplevel(0,axis=1)
    return info_style.reset_index().dropna(axis=0)


def get_info_desc(dly_desc):
    return dly_desc.drop(columns='DescriptorType').set_index(['BarraID','Descriptor'])\
        .unstack(1).droplevel(0,axis=1).reset_index().dropna(axis=0)


def get_ind_style_region_sets(barra_product):

	indset = pd.read_csv(f'{saveroot}ind_set.csv', dtype=str)[barra_product].dropna().tolist()
	style_set = pd.read_csv(f'{saveroot}style_set.csv', dtype=str)[barra_product].dropna().tolist()

	region_set_path = '/mnt/sda/NAS/ShareFolder/xielan/fundamental_info/barrainfo/regionset.txt'
	region_set = pd.read_csv(region_set_path, sep='\t').iloc[:, 0]

	if barra_product in {'GEM3', 'EULT'}: suffix = 'S_'
	elif barra_product in {'EUTR', 'CNTR'}: suffix = 'D_'
	elif barra_product in ['EUE4']: suffix = 'BASS_'

	indset = [f'{barra_product}{suffix}' + s for s in indset]
	style_set = [f'{barra_product}{suffix}' + s for s in style_set]
	region_set = set(f'{barra_product}{suffix}' + region_set)

	return indset, style_set, region_set


info_root = '/mnt/sda/NAS/Global/Barra/barra_parsed/'
saveroot = '/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/'



daily_root = '/mnt/sda/NAS/Global/Barra/barra_parsed/history/gem3/sm/daily/'

idp = InfoDataProcessor(daily_root, 'GEM3')
idp.load_cov_mat('2024-02-05')

def get_1day_info(date, history_root, barra_product='GEM3'):

    if barra_product == 'EUE4':
        daily_root = f'{history_root}eue4bas/sm/daily/'
    else:
        daily_root = f'{history_root}{barra_product.lower()}/sm/daily/'

    idp = InfoDataProcessor(daily_root, barra_product)
    dly_ret = idp.load_DlyRet(date)

    dly_exp = idp.load_Exposure(date)
    dly_data = idp.load_Data(date)

    dly_pce = idp.load_Price(date)

    dly_estu = idp.load_ESTU(date)
    dly_mkt = idp.load_MarketData(date)
    dly_rate = idp.load_Rates(date)

    dly_desc = idp.load_Descriptor(date)
    if dly_desc is not None:
        dly_desc = dly_desc[dly_desc['DescriptorType']=='STD']

    id_map = idp.load_id_map(date)
    isin = idp.load_ISIN(date)
    ric = idp.load_RIC(date)
    eid = idp.load_EID(date)
    
    opmic = idp.load_OpMIC_map()
    eid = eid.merge(opmic, on='MIC',how='outer')

    ctry_map = idp.load_CTRY_MAP(date)


    indset, style_set, region_set = get_ind_style_region_sets(barra_product)
    info_ind =  get_info_indcell(dly_exp, indset)
    info_region = get_info_region(dly_exp, region_set)
    info_style = get_info_style(dly_exp, style_set)
    
    if dly_desc is not None:
        info_desc = get_info_desc(dly_desc)
    else:
        info_desc = None
    
    df_pce = dly_pce.merge(dly_rate, on='Currency')

    info = \
        info_ind.merge(info_region, on='BarraID',how='outer')\
        .merge(isin, on='BarraID',how='outer')\
        .merge(ric, on='BarraID',how='outer')\
        .merge(eid, on='BarraID',how='outer')\
        .merge(id_map, on='BarraID',how='outer')

    info = info.drop_duplicates().dropna(subset='indcell')

    info['LocalID'] = info['AssetID'].str[2:]
    info['Country_ISO2'] = info['AssetID'].str[:2]
    info['Country_ISO3'] = info['Country_ISO2'].apply(ctry_map.get)

    if info_desc is not None:
        return info.merge(info_style, on='BarraID',how='outer')\
            .merge(dly_ret, on='BarraID',how='outer')\
            .merge(dly_estu, on='BarraID',how='outer')\
            .merge(df_pce, on='BarraID',how='outer')\
            .merge(dly_mkt, on='BarraID',how='outer')\
            .merge(dly_data, on='BarraID',how='outer')\
            .merge(info_desc, on='BarraID',how='outer')
    elif info_desc is None:
        return info.merge(info_style, on='BarraID',how='outer')\
            .merge(dly_ret, on='BarraID',how='outer')\
            .merge(dly_estu, on='BarraID',how='outer')\
            .merge(df_pce, on='BarraID',how='outer')\
            .merge(dly_mkt, on='BarraID',how='outer')\
            .merge(dly_data, on='BarraID',how='outer')


history_root = '/mnt/sda/NAS/Global/Barra/barra_parsed/history/'
saveroot = '/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/'
saveroot_fcov = '/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/factor_cov/'
saveroot_fexp = '/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/factor_exp/'

def run_1day(date):
    for barra_product in ('GEM3','EULT','EUTR','CNTR', 'EUE4')[:]:
        savedir = f'{saveroot}{barra_product}/'
        if not os.path.exists(savedir): os.makedirs(savedir, exist_ok=True)
        if os.path.exists(f'{savedir}{date}.parquet'): continue
        try:
            df_info = get_1day_info(date, history_root, barra_product)
            df_info = df_info.loc[~df_info['indcell'].isna()]
            df_info.to_parquet(f'{savedir}{date}.parquet')
        except Exception as err:
            print('wrong', date, barra_product, err, end='\r')
            continue

def run_1day_fcov(date):
    for barra_product in ('GEM3','EULT','EUTR','CNTR', 'EUE4')[:]:
        if not os.path.exists(f'{saveroot_fcov}{barra_product}/'): 
            os.makedirs(f'{saveroot_fcov}{barra_product}/', exist_ok=True)
        if not os.path.exists(f'{saveroot_fexp}{barra_product}/'): 
            os.makedirs(f'{saveroot_fexp}{barra_product}/', exist_ok=True)

        if barra_product == 'EUE4':
            daily_root = f'{history_root}eue4bas/sm/daily/'
        else:
            daily_root = f'{history_root}{barra_product.lower()}/sm/daily/'
        idp = InfoDataProcessor(daily_root, barra_product)
        try:
            cov_mat = idp.load_cov_mat(date)
            full_exp = idp.load_Exposure(date, load_etf_exposure=False)
            if cov_mat is not None:
                cov_mat.to_parquet(f'{saveroot_fcov}{barra_product}/{date}.parquet')
                full_exp.to_parquet(f'{saveroot_fexp}{barra_product}/{date}.parquet')
        except Exception as err:
            print('wrong', date, barra_product, err, end='\r')
            continue

dates = pd.date_range('2015-01-01','2024-10-18').astype(str).tolist()

# with Parallel(n_jobs=10, backend='multiprocessing') as para:
#     _ = para(delayed(run_1day)(date) for date in tqdm(dates))


with Parallel(n_jobs=10, backend='multiprocessing') as para:
    _ = para(delayed(run_1day_fcov)(date) for date in tqdm(dates[:]))



daily_root = f'/mnt/sda/NAS/Global/Barra/barra_parsed/history/gem3/sm/daily/'
idp = InfoDataProcessor(daily_root, 'GEM3')
idp.load_cov_mat('2015-01-02')



fcov = pd.read_parquet('/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/factor_cov/GEM3/2020-02-17.parquet')
all_factors = fcov.columns.tolist()


a = pd.read_csv('/mnt/sda/NAS/Global/Barra/barra_parsed/history/gem3/sm/daily/GMD_GEM3S_100_D_2020_1/GEM3S_100_Asset_Exposure_20200217.csv')
c = set(all_factors) - set(a['Factor'].unique())


b = pd.read_csv('/mnt/sda/NAS/Global/Barra/barra_parsed/history/gem3/sm/daily/GMD_GEM3S_100_D_2020_1/GEM3S_ETF_100_Asset_Exposure_20200217.csv')
c.intersection(b['Factor'].unique())

set(all_factors) - set(b['Factor'].unique()) - set(a['Factor'].unique())

c

set.union(set(a['Factor'].unique()), set(b['Factor'].unique())) - set(all_factors)





daily_root = '/mnt/sda/NAS/Global/Barra/barra_parsed/history/eue4bas/sm/daily/'
idp = InfoDataProcessor(daily_root, 'EUE4')
idp.load_Exposure('2020-02-17')

pd.to_datetime('2018-01-01').weekday()

%%writefile '/mnt/sda/NAS/ShareFolder/lishuanglin/share/to_wangxiang/gathered/update_cntr_20250430.py'
import os
import time
import datetime
import pandas as pd
import numpy as np
from tqdm.auto import tqdm
from joblib import Parallel, delayed


class InfoDataProcessorDaily:
    def __init__(self, info_root, barra_product):
        self.info_root = info_root
        self.barra_product = barra_product

        if self.barra_product == "CNTR":
            self.PRODUCT = "SMD"
        else:
            self.PRODUCT = "GMD"

        if self.barra_product in {"EUTR", "CNTR"}:
            self.product = f"{self.barra_product}D"
        else:
            self.product = f"{self.barra_product}S"

        if self.barra_product in ("EUTR", "EULT"):
            self.info_root = f"{self.info_root}eu/"

        if self.barra_product == "EUE4":
            self.product = self.barra_product

    def load_DlyRet(self, date):
        date_ = date.replace("-", "")
        if self.barra_product == "EUE4":
            product = "EUE4BASS"
        else:
            product = self.product
        folder = f"{self.PRODUCT}_{product}_100_{date_[2:]}"

        time_elasped = 0
        while True:
            if (date == today) and (pd.to_datetime(date).weekday() in list(range(1,6))):
                if not os.path.exists(
                    os.path.join(self.info_root, self.barra_product.lower(), folder)
                ):
                    time.sleep(10)
                    time_elasped += 10
                    print(f"{folder} not exist, {time_elasped} seconds waited!", flush=True)
                    if time_elasped / 60 > 10:
                        raise FileNotFoundError(f"{folder} not generated!")
                else:
                    break
            else:
                break

        filename = f"{product}_100_Asset_DlySpecRet_{date_}.csv"
        filepath = f"{self.info_root}{self.barra_product.lower()}/{folder}/{filename}"
        filename_ = f"{product[:-1]}_100_Asset_DlySpecRet_{date_}.csv"
        filepath_ = f"{self.info_root}{self.barra_product.lower()}/{folder}/{filename_}"
        if os.path.exists(filepath):
            df = pd.read_csv(filepath).drop(columns=["DataDate"])
            return df.rename(columns={"Barrid": "BarraID"})
        elif os.path.exists(filepath_):
            df = pd.read_csv(filepath_).drop(columns=["DataDate"])
            return df.rename(columns={"Barrid": "BarraID"})
        else:
            print(f"file not exist {filepath}")

    def load_Exposure(self, date, load_etf_exposure=False):
        date_ = date.replace("-", "")
        if self.barra_product == "EUE4":
            product = "EUE4BASS"
        else:
            product = self.product
        folder = f"{self.PRODUCT}_{product}_100_{date_[2:]}"

        filename = f"{product}_100_Asset_Exposure_{date_}.csv"
        filepath = f"{self.info_root}{self.barra_product.lower()}/{folder}/{filename}"
        if os.path.exists(filepath):
            df = pd.read_csv(filepath).drop(columns=["DataDate"])
            df = df.rename(columns={"Barrid": "BarraID"})
        else:
            print(f"file not exist {filepath}")

        if load_etf_exposure:
            filename = f"{product}_ETF_100_Asset_Exposure_{date_}.csv"
            filepath = (
                f"{self.info_root}{self.barra_product.lower()}/{folder}/{filename}"
            )
            if os.path.exists(filepath):
                df_etf = pd.read_csv(filepath).drop(columns=["DataDate"])
                df_etf = df_etf.rename(columns={"Barrid": "BarraID"})
                df = pd.concat([df, df_etf], axis=0)
            return df
        else:
            return df

    def load_cov_mat(self, date: str):
        date_ = date.replace("-", "")
        if self.barra_product in ["EUE4"]:
            product = "EUE4BASS"
        elif self.barra_product in ["CNTR", "EUTR"]:
            product = f"{self.barra_product}D"
        else:
            product = f"{self.barra_product}S"
        folder = f"{self.PRODUCT}_{product}_100_{date_[2:]}"
        filename = f"{product}_100_Covariance_{date_}.csv"
        filepath = f"{self.info_root}{self.barra_product.lower()}/{folder}/{filename}"
        if os.path.exists(filepath):
            cov_mat = pd.read_csv(filepath)
            cov_mat = cov_mat.pivot(
                index="Factor1", columns="Factor2", values="VarCovar"
            )
            return cov_mat.where(cov_mat.notna(), cov_mat.T)
        else:
            raise ValueError(f"No data for {date} in {self.info_root}")

    def load_Price(self, date):
        date_ = date.replace("-", "")
        if self.barra_product == "EUE4":
            product = "EUE4BASS"
        else:
            product = self.product
        folder = f"{self.PRODUCT}_{product}_100_{date_[2:]}"

        filename = f"{self.barra_product}_Daily_Asset_Price_{date_}.csv"
        filepath = f"{self.info_root}{self.barra_product.lower()}/{folder}/{filename}"
        if os.path.exists(filepath):
            df = pd.read_csv(filepath).drop(columns=["DataDate"])
            return df.rename(columns={"Barrid": "BarraID"})
        else:
            print(f"file not exist {filepath}")

    def load_ESTU(self, date):
        date_ = date.replace("-", "")
        if self.barra_product == "EUE4":
            product = "EUE4BASS"
        else:
            product = self.product
        folder = f"{self.PRODUCT}_{product}_100_{date_[2:]}"

        filename = f"{self.barra_product}_ESTU_POR_{date_}.csv"
        filepath = f"{self.info_root}{self.barra_product.lower()}/{folder}/{filename}"
        if os.path.exists(filepath):
            df = pd.read_csv(filepath)
            return df.rename(columns={"Barrid": "BarraID"})
        else:
            print(f"file not exist {filepath}")

    def load_MarketData(self, date):
        date_ = date.replace("-", "")
        folder = f"{self.PRODUCT}_{self.barra_product}_Market_Data_{date_[2:]}"
        filename = f"{self.barra_product}_Market_Data_{date_}.csv"
        filepath = f"{self.info_root}{self.barra_product.lower()}/{folder}/{filename}"
        if os.path.exists(filepath):
            df = pd.read_csv(filepath).drop(columns=["DataDate"])
            return df.rename(columns={"Barrid": "BarraID"})
        else:
            print(f"file not exist {filepath}")

    def load_Rates(self, date):
        date_ = date.replace("-", "")
        if self.barra_product == "EUE4":
            product = "EUE4BASS"
        else:
            product = self.product
        folder = f"{self.PRODUCT}_{product}_100_{date_[2:]}"

        filename = f"{self.barra_product}_Rates_{date_}.csv"
        filepath = f"{self.info_root}{self.barra_product.lower()}/{folder}/{filename}"
        if os.path.exists(filepath):
            df = pd.read_csv(filepath).drop(columns=["DataDate"])
            return df.rename(columns={"Barrid": "BarraID"})
        else:
            print(f"file not exist {filepath}")

    def load_Descriptor(self, date):
        date_ = date.replace("-", "")
        date.split("-")[0]
        folder = f"{self.PRODUCT}_{self.barra_product}_100_Descriptor_{date_[2:]}"

        filename = f"{self.barra_product}_100_Asset_Descriptor_{date_}.csv"
        filepath = f"{self.info_root}{self.barra_product.lower()}/{folder}/{filename}"
        if os.path.exists(filepath):
            df = pd.read_csv(filepath).drop(columns=["DataDate"])
            return df.rename(columns={"Barrid": "BarraID"})
        else:
            print(f"file not exist {filepath}", flush=True)

    def load_Data(self, date):
        date_ = date.replace("-", "")
        if self.barra_product == "EUE4":
            product = "EUE4BASS"
        else:
            product = self.product
        folder = f"{self.PRODUCT}_{product}_100_{date_[2:]}"

        filename = f"{product}_100_Asset_Data_{date_}.csv"
        filepath = f"{self.info_root}{self.barra_product.lower()}/{folder}/{filename}"
        if os.path.exists(filepath):
            df = pd.read_csv(filepath).drop(columns=["DataDate"])
            return df.rename(columns={"Barrid": "BarraID"})
        else:
            print(f"file not exist {filepath}")

    def load_id_map(self, date):
        """map barrid and assetid"""
        date = date.replace("-", "")
        folder = f"{self.PRODUCT}_{self.barra_product}_LOCALID_ID_{date[2:]}"
        if self.barra_product == "CNTR":
            map_prefix = "CHN"
        elif self.barra_product == "GEM3":
            map_prefix = "GEM"
        else:
            map_prefix = self.barra_product
        filename = f"{map_prefix}_LOCALID_Asset_ID_{date}.csv"
        map_path = f"{self.info_root}{self.barra_product.lower()}/{folder}/{filename}"
        id_map = pd.read_csv(map_path, dtype=str)
        id_map[["StartDate", "EndDate"]] = id_map[["StartDate", "EndDate"]].apply(
            lambda x: pd.to_datetime(x.str[:-2], format="%Y%m%d")
        )
        id_map = id_map.loc[
            (id_map["StartDate"] <= date) & (id_map["EndDate"] >= date)
        ].rename(columns={"Barrid": "BarraID"})
        return id_map[["BarraID", "AssetID"]]

    def load_OpMIC_map(self):
        """MIC and operating MIC"""
        xchg_map_path = "/mnt/sda/NAS/Global/mapping/ISO10383_MIC.csv"
        df = pd.read_csv(xchg_map_path).rename(columns={"OPERATING MIC": "OpMIC"})
        return df[["MIC", "OpMIC"]]

    def load_ISIN(self, date):
        """map barraid and isin"""
        isin_root = "/mnt/sda/NAS/Global/Barra/barra_parsed/barradirect/isin/"
        isin_folders = sorted(
            [s for s in os.listdir(isin_root) if s.startswith("ISIN_FULL_WE")]
        )
        isin_dir = f"{isin_root}{isin_folders[-1]}/"
        file = [s for s in os.listdir(isin_dir) if "ISIN_FULL_WE" in s][0]

        filepath = f"{isin_dir}{file}"
        if os.path.exists(filepath):
            df = pd.read_csv(filepath).drop(columns=["Start Date", "End Date"])
            return df.rename(columns={"#Barra ID": "BarraID"})

    def load_RIC(self, date):
        ric_root = "/mnt/sda/NAS/Global/Barra/barra_parsed/barradirect/ric/"
        ric_folders = sorted(
            [s for s in os.listdir(ric_root) if s.startswith("RIC_FULL_WE")]
        )
        ric_dir = f"{ric_root}{ric_folders[-1]}/"
        file = [s for s in os.listdir(ric_dir) if "RIC_FULL_WE" in s][0]

        filepath = f"{ric_dir}{file}"
        if os.path.exists(filepath):
            df = pd.read_csv(filepath).drop(columns=["Start Date", "End Date"])
            return df.rename(columns={"#Barra ID": "BarraID"})

    def load_EID(self, date):
        "map barraid and mic and country_of_exposure"
        usecols = ["#BARRA ID", "MIC", "Country of Exposure"]
        eid_root = "/mnt/sda/NAS/Global/Barra/barra_parsed/barradirect/equityidentity/"
        eid_folders = sorted(
            [s for s in os.listdir(eid_root) if s.startswith("EquityIdentity_FULL_WE")]
        )
        eid_dir = f"{eid_root}{eid_folders[-1]}/"
        file = [s for s in os.listdir(eid_dir) if "EquityIdentity_FULL_WE" in s][0]

        filepath = f"{eid_dir}{file}"
        if os.path.exists(filepath):
            df = pd.read_csv(filepath, usecols=usecols)
            return df.rename(
                columns={
                    "#BARRA ID": "BarraID",
                    "Country of Exposure": "Country_of_Exposure",
                }
            )

    def load_CTRY_MAP(self, date):
        """map country name of ISO2 AND ISO3"""
        ctry_root = "/mnt/sda/NAS/Global/Barra/barra_parsed/barradirect/"
        folders = sorted(
            [s for s in os.listdir(ctry_root) if s.startswith("Country_Master_WE")]
        )
        ctry_dir = f"{ctry_root}{folders[-1]}/"
        file = [s for s in os.listdir(ctry_dir) if "Country_Master_WE" in s][0]

        filepath = f"{ctry_dir}{file}"
        if os.path.exists(filepath):
            df = (
                pd.read_csv(
                    filepath, usecols=["CountrySymbol (ISO2)", "CountrySymbol (ISO3)"]
                )
                .dropna()
                .drop_duplicates()
            )
            df = df.rename(
                columns={
                    "CountrySymbol (ISO2)": "Country_ISO2",
                    "CountrySymbol (ISO3)": "Country_ISO3",
                }
            )
            return dict(zip(df["Country_ISO2"].tolist(), df["Country_ISO3"].tolist()))


# barra_products = ['GEM3','CNTR']
# parsed_root = '/mnt/sda/NAS/Global/Barra/barra_parsed/'
# idp = InfoDataProcessorDaily(parsed_root, barra_products[1])


def get_info_indcell(dly_exp, indset):
    info = dly_exp.loc[dly_exp["Factor"].isin(indset)]
    info = info.drop_duplicates(["BarraID"])
    return info.iloc[:, :2].rename(columns={"Factor": "indcell"})


def get_info_region(dly_exp, region_set):
    info = dly_exp.loc[dly_exp["Factor"].isin(region_set)]
    info = info.drop_duplicates(["BarraID"])
    return info.iloc[:, :2].rename(columns={"Factor": "regioncell"})


def get_info_style(dly_exp, style_set):
    info = dly_exp.loc[dly_exp["Factor"].isin(style_set)]
    info_style = info.set_index(["BarraID", "Factor"]).unstack(1).droplevel(0, axis=1)
    return info_style.reset_index().dropna(axis=0)


def get_info_desc(dly_desc):
    return (
        dly_desc.drop(columns="DescriptorType")
        .set_index(["BarraID", "Descriptor"])
        .unstack(1)
        .droplevel(0, axis=1)
        .reset_index()
        .dropna(axis=0)
    )


def get_ind_style_region_sets(barra_product):
    indset = (
        pd.read_csv(f"{saveroot}ind_set.csv", dtype=str)[barra_product]
        .dropna()
        .tolist()
    )
    style_set = (
        pd.read_csv(f"{saveroot}style_set.csv", dtype=str)[barra_product]
        .dropna()
        .tolist()
    )

    region_set_path = (
        "/mnt/sda/NAS/ShareFolder/xielan/fundamental_info/barrainfo/regionset.txt"
    )
    region_set = pd.read_csv(region_set_path, sep="\t").iloc[:, 0]

    if barra_product in {"GEM3", "EULT"}:
        suffix = "S_"
    elif barra_product in {"EUTR", "CNTR"}:
        suffix = "D_"
    elif barra_product in ["EUE4"]:
        suffix = "BASS_"

    indset = [f"{barra_product}{suffix}" + s for s in indset]
    style_set = [f"{barra_product}{suffix}" + s for s in style_set]
    region_set = list(set(f"{barra_product}{suffix}" + region_set))

    return indset, style_set, region_set


def get_1day_info_updated(date, info_root, barra_product="GEM3"):
    daily_root = f"{info_root}"
    idp = InfoDataProcessorDaily(daily_root, barra_product)

    dly_ret = idp.load_DlyRet(date)
    dly_exp = idp.load_Exposure(date)
    dly_data = idp.load_Data(date)
    dly_pce = idp.load_Price(date)
    dly_estu = idp.load_ESTU(date)
    dly_mkt = idp.load_MarketData(date)
    dly_rate = idp.load_Rates(date)
    dly_desc = idp.load_Descriptor(date)
    if dly_desc is not None:
        dly_desc = dly_desc[dly_desc["DescriptorType"] == "STD"]

    id_map = idp.load_id_map(date)
    isin = idp.load_ISIN(date)
    ric = idp.load_RIC(date)
    eid = idp.load_EID(date)

    opmic = idp.load_OpMIC_map()
    eid = eid.merge(opmic, on="MIC", how="outer")

    ctry_map = idp.load_CTRY_MAP(date)

    indset, style_set, region_set = get_ind_style_region_sets(barra_product)
    info_ind = get_info_indcell(dly_exp, indset)
    info_region = get_info_region(dly_exp, region_set)
    info_style = get_info_style(dly_exp, style_set)

    if dly_desc is not None:
        info_desc = get_info_desc(dly_desc)
    else:
        info_desc = None

    df_pce = dly_pce.merge(dly_rate, on="Currency")

    info = (
        info_ind.merge(info_region, on="BarraID", how="outer")
        .merge(isin, on="BarraID", how="outer")
        .merge(ric, on="BarraID", how="outer")
        .merge(eid, on="BarraID", how="outer")
        .merge(id_map, on="BarraID", how="outer")
    )

    info = info.drop_duplicates().dropna(subset="indcell")

    info["LocalID"] = info["AssetID"].str[2:]
    info["Country_ISO2"] = info["AssetID"].str[:2]
    info["Country_ISO3"] = info["Country_ISO2"].apply(ctry_map.get)

    if info_desc is not None:
        return (
            info.merge(info_style, on="BarraID", how="outer")
            .merge(dly_ret, on="BarraID", how="outer")
            .merge(dly_estu, on="BarraID", how="outer")
            .merge(df_pce, on="BarraID", how="outer")
            .merge(dly_mkt, on="BarraID", how="outer")
            .merge(dly_data, on="BarraID", how="outer")
            .merge(info_desc, on="BarraID", how="outer")
        )
    elif info_desc is None:
        return (
            info.merge(info_style, on="BarraID", how="outer")
            .merge(dly_ret, on="BarraID", how="outer")
            .merge(dly_estu, on="BarraID", how="outer")
            .merge(df_pce, on="BarraID", how="outer")
            .merge(dly_mkt, on="BarraID", how="outer")
            .merge(dly_data, on="BarraID", how="outer")
        )


info_root = "/mnt/sda/NAS/Global/Barra/barra_parsed/"
saveroot = "/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/"
saveroot_fcov = (
    "/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/factor_cov/"
)
saveroot_fexp = (
    "/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/factor_exp/"
)

try:
    os.system(f"cd {info_root}")
    time.sleep(5.0)
except:
    raise FileNotFoundError("info_root may not be mounted!")


def run_1day(date):
    for barra_product in ("CNTR", "GEM3", "EUTR", "EULT")[:1]:
        if barra_product == 'CNTR':
            if date not in cn_tddays:
                continue
        savedir = f"{saveroot}{barra_product}/"
        if not os.path.exists(savedir):
            os.makedirs(savedir, exist_ok=True)
        if os.path.exists(f"{savedir}{date}.parquet"):
            print(f"file already exists for {barra_product, date}", flush=True)
            continue
        try:
            df_info = get_1day_info_updated(date, info_root, barra_product)
            df_info = df_info.loc[~df_info["indcell"].isna()]
            df_info.to_parquet(f"{savedir}{date}.parquet")
            print(f"file updated for {barra_product, date}")
            # display.clear_output()
        except Exception as err:
            print("wrong", date, barra_product, err)
            continue


def run_1day_fcov(date):
    for barra_product in ("CNTR", "GEM3", "EUTR", "EULT")[:1]:
        if barra_product == 'CNTR':
            if date not in cn_tddays:
                continue

        if not os.path.exists(f"{saveroot_fcov}{barra_product}/"):
            os.makedirs(f"{saveroot_fcov}{barra_product}/", exist_ok=True)
        if not os.path.exists(f"{saveroot_fexp}{barra_product}/"):
            os.makedirs(f"{saveroot_fexp}{barra_product}/", exist_ok=True)

        cov_dir = f"{saveroot_fcov}{barra_product}/"
        exp_dir = f"{saveroot_fexp}{barra_product}/"
        if os.path.exists(f"{cov_dir}{date}.parquet") & os.path.exists(
            f"{exp_dir}{date}.parquet"
        ):
            print(f"fcov_file already exists for {barra_product, date}", flush=True, end="\r")
            continue

        if barra_product == "EUE4":
            daily_root = f"{info_root}eue4/"
        else:
            daily_root = f"{info_root}"

        idp = InfoDataProcessorDaily(daily_root, barra_product)

        try:
            cov_mat = idp.load_cov_mat(date)
            full_exp = idp.load_Exposure(date, load_etf_exposure=True)
            if cov_mat is not None:
                cov_mat.to_parquet(f"{cov_dir}/{date}.parquet")
                full_exp.to_parquet(f"{exp_dir}/{date}.parquet")
            print(f"cov_exp_mat file updated for {barra_product, date}", end="\r", flush=True)
            # display.clear_output()
        except Exception as err:
            print("wrong", date, barra_product, err)
            continue


from shennong.utils import trading_days  # noqa: E402

sDate = '2025-04-01'
today = datetime.date.today().strftime("%Y-%m-%d")
cn_tddays = pd.date_range(sDate, today).astype(str).tolist()

# cn_tddays = trading_days.load(region_product='cn_ashare', start_datetime=sDate, end_datetime=today)

# with Parallel(n_jobs=1, backend='multiprocessing') as para:
#     _ = para(delayed(run_1day)(date) for date in cn_tddays)

# with Parallel(n_jobs=1, backend="multiprocessing") as para:
#     _ = para(delayed(run_1day_fcov)(date) for date in tqdm(cn_tddays))

# for i in range(1, len(cn_tddays)):
#     date = cn_tddays[i-1] # update with last date
#     run_1day(date)
#     run_1day_fcov(date)

date = cn_tddays[-2]
run_1day(date)
run_1day_fcov(date)













# %load /mnt/sda/NAS/ShareFolder/lishuanglin/share/to_wangxiang/gathered/update_barrauniverse_20241118.py
import datetime, os
from shennong.utils import trading_days
from joblib import Parallel, delayed
import pandas as pd
universe_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/universe/'
barra_product = 'GEM3'

def get_1day_full_universe(barra_product: str, date: str):
    info_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/'
    filepath = f'{info_root}{barra_product}/{date}.parquet'
    if not os.path.exists(filepath):
        raise FileExistsError(f'file not exists: {filepath}')
    a = pd.read_parquet(filepath)
    cols = ['BarraID', 'Country_of_Exposure', 'LocalID','MIC', 'Price', \
            'Capt', 'USDxrate','DailyVolume',]
    a = a[cols].rename(columns={'Capt': 'MarketCap','Country_of_Exposure': 'MarketExposure'})
    a['Turnover'] = a['Price'] * a['DailyVolume']
    a['Turnover'] = a['Turnover'] * a['USDxrate'] / 100.0 
    a['MarketCap'] = a['MarketCap'] * a['USDxrate'] / 100.0 
    a['date'] = date
    a = a.drop(columns=['Price','DailyVolume','USDxrate'])
    # print(f'loading {date} finished', end='\r')
    return a.loc[a['Turnover'] > 0.0].drop_duplicates()


def update_1day(date):
    today = datetime.date.today().strftime('%Y-%m-%d')
    dates = trading_days.load(region_product='cn_ashare', \
        start_datetime='2023-01-01',end_datetime=today)

    idx = dates.index(date)
    dates = dates[idx-150:idx+1]
    last_10_dates = dates[-11:-1]


    with Parallel(n_jobs=10, backend='multiprocessing') as para:
        results = para(delayed(get_1day_full_universe)(barra_product, date) for date in dates)

    print('loading finished')

    a = pd.concat(results)
    result = a.groupby(by='BarraID').agg({
        'MarketExposure':'last',
        'LocalID':'last',
        'MIC':'last',
        'Turnover': lambda x: x[-126:].mean(),
        'MarketCap':lambda x: x[-126:].mean(),
        }).reset_index()
        
    result1 = result.loc[(result['Turnover'] > 1.0e6) & \
        (result['MarketCap'] > 2.0e8),'BarraID'].tolist()
    
    print('calculating finished')

    last_10_universes = []
    for date_ in last_10_dates:
        dt = pd.read_csv(f'{universe_root}{date_}.csv')
        dt['date'] = date_
        last_10_universes.append(dt)

    last_10_universes = pd.concat(last_10_universes)#.groupby('BarraID').count().sort_values('MIC')

    b = last_10_universes.loc[~last_10_universes['BarraID'].isin(result['BarraID'])].groupby('BarraID').count()
    last_tobe_kept = b[b['MIC'] < 10].index.tolist()

    universe = result1 + last_tobe_kept
    universe = result.loc[result['BarraID'].isin(universe)].iloc[:,:-2].reset_index(drop=True)
    return universe.loc[~universe['MIC'].isnull()]


today = datetime.date.today().strftime('%Y-%m-%d')
tmp_dates = trading_days.load(region_product='cn_ashare', \
    start_datetime='2024-11-01',end_datetime=today)

# for date in tmp_dates[:]:
#     if os.path.exists(f'{universe_root}{date}.csv'): continue
#     universe = update_1day(date)
#     universe.to_csv(f'{universe_root}{date}.csv',index=False)
#     print(f'universe updated for {date}')

date = tmp_dates[-2]
print('updating',date)
if os.path.exists(f'{universe_root}{date}.csv'): 
    print(f'universe already generated for {date}')
    pass
else:
    universe = update_1day(date)
    universe.to_csv(f'{universe_root}{date}.csv',index=False)
    print(f'universe updated for {date}')


# %%writefile '/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/update_universe.py'
# coding = utf-8
import datetime, os
import pandas as pd
from tqdm.auto import tqdm
from joblib import Parallel, delayed
from shennong.utils import trading_days

universe_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/universe/'
os.makedirs(universe_root, exist_ok=True)

barra_product = 'GEM3'  ## corresponding global

## get global info for daily universe calculation
def get_1day_info(barra_product: str, date: str):
	info_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/share/fundamental_info/barrainfo/'
	id_map_root = '/mnt/sda/NAS/Global/mapping/barra_mapping/'
	filepath = f'{info_root}{barra_product}/{date}.parquet'
	if not os.path.exists(filepath):
		raise FileExistsError(f'file not exists: {filepath}')
	cols = ['BarraID', 'Country_of_Exposure', 'LocalID','MIC', 'OpMIC', 'Price', \
			'Capt', 'USDxrate','DailyVolume', 'Country_ISO3','ISIN']
	a = pd.read_parquet(filepath, columns=cols)
	a = a.rename(columns={'Capt': 'MarketCap','Country_of_Exposure': 'MarketExposure'})
	a['Turnover'] = a['Price'] * a['DailyVolume']
	a['Turnover'] = a['Turnover'] * a['USDxrate'] / 100.0 
	a['MarketCap'] = a['MarketCap'] * a['USDxrate'] / 100.0 
	a['date'] = date
	a = a.drop(columns=['Price','DailyVolume','USDxrate'])
	# print(f'loading {date} finished', end='\r')
	a = a.loc[a['Turnover'] > 0.0].drop_duplicates()
	a = a.loc[~(a['OpMIC'].isna() | a['LocalID'].isna())]

	id_map = pd.read_csv(f'{id_map_root}/{date}.csv', usecols=['Barrid', 'Root_BARRA_ID'])
	id_map = id_map.loc[id_map['Barrid'].isin(a['BarraID'])]
	id_map = id_map.rename(columns={'Root_BARRA_ID': 'RootID', 'Barrid': 'BarraID'})
	return pd.merge(a, id_map, on='BarraID', how='left')



## udpate daily global universe
def get_global_universe(date: str, lookback: int=126, keep_days: int=10):
	today = datetime.date.today().strftime('%Y-%m-%d')
	dates = pd.bdate_range('2012-01-01', today).astype(str).tolist()
	idx = dates.index(date)
	dates = dates[idx-lookback-10:idx] ## the lastest date should be excluded

	with Parallel(n_jobs=1, backend='multiprocessing') as para:
		results = para(delayed(get_1day_info)(barra_product, date) for date in dates)
	print('loading finished',end='\r')

	a = pd.concat(results, axis=0)

	result = a.groupby(by='BarraID').agg({
		'MarketExposure':'last',
		'LocalID':'last',
		'MIC':'last',
		'OpMIC': 'last',
		'Country_ISO3': 'last',
		'ISIN': 'last',
		'date': 'last',
		'RootID': 'last',
		'Turnover': lambda x: x[-lookback:].mean(), 
		'MarketCap':lambda x: x[-lookback:].mean(),
		}).reset_index()
		
	## mean daily turnover and market cap filtering
	result1 = result.loc[(result['Turnover'] > 1.0e6) & (result['MarketCap'] > 2.0e8), 'BarraID'].tolist()
	print('calculating finished',end='\r')


	## filter out if not in universe over keep_days
	latest_dates = dates[-30:-1] ## more days to cover long holidays
	latest_univs = []
	for date_ in latest_dates:
		if os.path.exists(f'{universe_root}{date_}.csv'):
			dt = pd.read_csv(f'{universe_root}{date_}.csv')
			dt['date'] = date_
			latest_univs.append(dt)

	if len(latest_univs)>0:
		latest_univs = pd.concat(latest_univs) # only keep last keep_days universe files
		b = latest_univs.loc[~latest_univs['BarraID'].isin(result['BarraID'])]
		## only count last kepp_days records
		b = b.groupby('BarraID', sort=False).apply(lambda x: x[-keep_days:].count())
		last_tobe_kept = b[b['Country_ISO3'] < keep_days].index.tolist()
	else:
		last_tobe_kept = []

	universe = result1 + last_tobe_kept
	universe = result.loc[result['BarraID'].isin(universe)].iloc[:,:-2].reset_index(drop=True)
	return universe


def update_daily(date: str, lookback: int=126, keep_days: int=10, debug: bool=False):
	if debug:
		universe = get_global_universe(date, lookback=lookback, keep_days=keep_days)
		print(f'universe updated for {date}',end='\r')
		return universe
	else:
		if os.path.exists(f'{universe_root}global/{date}.csv'): 
			print(f'universe already generated for {date}')
			return None
		universe = get_global_universe(date, lookback=lookback, keep_days=keep_days)
		universe.to_csv(f'{universe_root}global/{date}.csv',index=False)
		for group, country_set in univ_sets.items():
			if group == 'global': continue
			univ_ = universe.loc[universe['Country_ISO3'].isin(country_set)].reset_index(drop=True)
			if len(univ_) > 0: ## only save valid universe
				univ_.to_csv(f'{universe_root}{group}/{date}.csv', index=False)
		print(f'universe updated for {date}',end='\n')


univ_sets = {
	'global': [],
	'EU1': ["AUT", "BEL", "CHE", "CZE", "DEU", "DNK", "ESP", "FIN", "FRA", "GBR", "GRC", 
		 "HUN", "IRL", "ITA", "NLD", "NOR", "POL", "PRT", "SWE"],
	'cn_ashare': ['CHN']
}

for group in univ_sets.keys():
	if not os.path.exists(f'{universe_root}{group}/'): 
		os.makedirs(f'{universe_root}{group}/', exist_ok=True)

## all possible businiess days for global trading days
today = datetime.date.today().strftime('%Y-%m-%d')
dates = pd.bdate_range('2016-01-01',today).astype(str).tolist()

# for date in dates[:]: 
# 	universe = update_daily(date, debug=False)

with Parallel(n_jobs=10, backend='loky') as para:
	results = para(delayed(update_daily)(date, debug=False) for date in tqdm(dates))








