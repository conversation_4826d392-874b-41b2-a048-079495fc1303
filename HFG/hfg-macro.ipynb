{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## pre-requisite"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import pandas as pd\n", "from tqdm.auto import tqdm\n", "\n", "from yrqtlib.utils import add_methods_to_pandas"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## backtest.py"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Overwriting ./hfg/backtest.py\n"]}], "source": ["%%writefile './hfg/backtest.py'\n", "# coding = utf-8\n", "import os\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "from tqdm.auto import tqdm\n", "\n", "def load_label(\n", "\tlabel_root: str,\n", "\tsDate: str,\n", "\teDate: str,\n", "\tref_price: str = 'vwap',\n", "\tbuyLag: int = 0,\n", "\tretLag: int = 1,\n", "\tfreq: str = '1min',\n", ") -> pd.Series:\n", "\t\"\"\"calculate label from parquet files\n", "\tArgs:\n", "\t\tlabel_root: str, path to label files, typically the minbar root\n", "\t\tsDate: str, start date\n", "\t\teDate: str, end date\n", "\t\tref_price: str, reference price\n", "\t\tbuyLag: int, buy lag\n", "\t\tretLag: int, return lag\n", "\t\tfreq: str, frequency\n", "\tReturns:\n", "\t\tpd.Series: label\n", "\t\"\"\"\n", "\tlabels = []\n", "\tdates = sorted(os.listdir(label_root))\n", "\tfor date in tqdm(dates, desc='loading label', miniters=len(dates) // 50, position=0, leave=False):\n", "\t\tif date < sDate or date > eDate:\n", "\t\t\tcontinue\n", "\t\tlabel = pd.read_parquet(f'{label_root}/{date}', columns=[ref_price])\n", "\t\tlabels.append(label)\n", "\tprice_bar = pd.concat(labels)[ref_price]\n", "\tprice_bar = price_bar.loc[~price_bar.index.duplicated()].sort_index()\n", "\tif freq != '1min':\n", "\t\tprice_bar = price_bar.resample(freq).mean()\n", "\tlabel = price_bar.div(price_bar.shift(retLag)).sub(1.0).shift(-buyLag - retLag)\n", "\tlabel[np.isinf(label)] = np.nan  ## set inf to nan\n", "\tmul = int(freq.split('min')[0]) / 1\n", "\tlabel = label.clip(lower=-0.02 * mul, upper=0.02 * mul)  ## clip to [-2%, 2%] for each minute\n", "\tlast_smooth_px = price_bar.rolling(1000, min_periods=1).mean()\n", "\tlabel = label.where(price_bar >= 0.1 * last_smooth_px, np.nan)\n", "\treturn label * 100.0  ## in unit of percentage\n", "\n", "\n", "def backtest(\n", "\tx: pd.Series,\n", "\ty: pd.Series,\n", "\tlong_threshold: float,\n", "\tshort_threshold: float,\n", "\tfee: float = 1.0e-3,\n", "\tdesc: str = 'backtesting',\n", ") -> pd.Series:\n", "\t\"\"\"\n", "\tBacktest strategy based on signal threshold\n", "\n", "\tArgs:\n", "\t\tx: Input signal series\n", "\t\ty: Return series\n", "\t\tbuy_threshold: Signal threshold for buying\n", "\t\tsell_threshold: Signal threshold for selling\n", "\n", "\tReturns:\n", "\t\tpd.Series: Position series (1 for long, 0 for flat)\n", "\t\"\"\"\n", "\tcindex = x.index.intersection(y.index)\n", "\tif len(cindex) == 0:\n", "\t\traise ValueError('no common index between feature and label')\n", "\tx = x.loc[cindex]\n", "\ty = y.loc[cindex]\n", "\tposition = np.zeros_like(y)\n", "\tx_ = x.copy().values\n", "\ty_ = y.copy().values\n", "\tassert long_threshold > short_threshold, 'long_threshold must > short_threshold'\n", "\n", "\tminiters = min(len(x) // 50, len(x))\n", "\tfor i in tqdm(range(1, len(x)), desc=desc, miniters=miniters, position=0, leave=False):\n", "\t\t# Keep previous position by default\n", "\t\tposition[i] = position[i - 1]\n", "\n", "\t\tif x_[i] >= long_threshold:  ## a new long signal\n", "\t\t\tif position[i] == 0:  # open a long position if empty\n", "\t\t\t\tposition[i] = 1\n", "\t\t\t\ty_[i] = y_[i] - fee\n", "\t\telif x_[i] < long_threshold:\n", "\t\t\tif position[i] == 1:  ## close a long position if any\n", "\t\t\t\tposition[i] = 0\n", "\t\t\t\ty_[i] = y_[i] - fee\n", "\t\t\tif x_[i] <= short_threshold:  ## a new short signal\n", "\t\t\t\tif position[i] == 0:  # open a short position if empty\n", "\t\t\t\t\tposition[i] = -1\n", "\t\t\t\t\ty_[i] = y_[i] + fee\n", "\t\t\telif x_[i] > short_threshold:\n", "\t\t\t\tif position[i] == -1:  ## close a short position if any\n", "\t\t\t\t\tposition[i] = 0\n", "\t\t\t\t\ty_[i] = y_[i] + fee\n", "\treturn pd.Series(y_, index=y.index).mul(position), pd.Series(position, index=y.index)\n", "\n", "\n", "def calc_ann_sharpe(ret: pd.Series, period: str = '1min') -> float:\n", "\t\"\"\"calculate annualized sharpe ratio with return series\n", "\tArgs:\n", "\t\tret: pd.Series, return series\n", "\t\tperiod: str, frequency\n", "\tReturns:\n", "\t\tfloat: annualized sharpe ratio\n", "\t\"\"\"\n", "\tret = ret.copy()\n", "\tnum_per_year = 365 * 24 * 60 / int(period.replace('min', ''))\n", "\tann = ret.mean() * num_per_year\n", "\tstd_ann = ret.std() * np.sqrt(num_per_year)\n", "\treturn ann, ann / std_ann\n", "\n", "\n", "def calc_ann_calmar(ret: pd.Series, period: str = '1min') -> tuple[float, float]:\n", "\t\"\"\"calculate annualized calmar ratio and max drawdown with return series\n", "\tArgs:\n", "\t\tret: pd.Series, return series\n", "\t\tperiod: str, frequency\n", "\tReturns:\n", "\t\tfloat: annualized calmar ratio\n", "\t\tfloat: max drawdown\n", "\t\"\"\"\n", "\tcumret = ret.cumsum()\n", "\tnum_per_year = 365 * 24 * 60 / int(period.replace('min', ''))\n", "\tmaxdd = (cumret.expanding(min_periods=1).max() - cumret).max()  ## max drawdown\n", "\tyearRet = ret.mean() * num_per_year\n", "\tcr = yearRet / (maxdd + 0.0001)\n", "\n", "\treturn cr, maxdd\n", "\n", "\n", "def tradesCount(position: pd.Series) -> tuple[int, float]:\n", "\t\"\"\"calculate number of trades and average holding time\n", "\tArgs:\n", "\t\tposition: pd.Series, position series\n", "\tReturns:\n", "\t\tint: number of trades\n", "\t\tfloat: average holding time\n", "\t\"\"\"\n", "\tpositionChg = ((position != position.shift(1)) & (position != 0)).sum()  # 开仓次数\n", "\tndates = len(position.index.normalize().unique())\n", "\tavgHoldtime = (position != 0).sum() / np.maximum(positionChg, 1)  # 平均持仓周期\n", "\treturn (positionChg/ndates, avgHoldtime)\n", "\n", "\n", "def calculate_trading_metrics(position: pd.Series, y: pd.Series) -> tuple[float, float, float]:\n", "\t\"\"\"calculate trading metrics\n", "\tArgs:\n", "\t\tposition: pd.Series, position series (1: long, -1: short, 0: cash)\n", "\t\ty: pd.Series, return series\n", "\tReturns:\n", "\t\ttuple: (average net profit per trade, win rate, profit loss ratio)\n", "\t\"\"\"\n", "\tposition = np.array(position, dtype=int)\n", "\ty = np.array(y, dtype=float)\n", "\ttransactions = []  # 存储每笔交易的净利润\n", "\tcurrent_position = 0  # 当前持仓状态\n", "\tstart_idx = None  # 开仓索引\n", "\n", "\tfor i, pos in enumerate(position):\n", "\t\tif pos != 0:\n", "\t\t\tif current_position == 0:\n", "\t\t\t\t# 新开仓\n", "\t\t\t\tstart_idx = i\n", "\t\t\t\tcurrent_position = pos\n", "\t\t\telif pos != current_position:\n", "\t\t\t\t# 反向平仓并开新仓\n", "\t\t\t\tend_idx = i - 1\n", "\t\t\t\tif start_idx is not None:\n", "\t\t\t\t\tprofit = np.nansum(y[start_idx : end_idx + 1])\n", "\t\t\t\t\ttransactions.append(profit)\n", "\t\t\t\tstart_idx = i\n", "\t\t\t\tcurrent_position = pos\n", "\t\telse:\n", "\t\t\tif current_position != 0:\n", "\t\t\t\t# 平仓到空仓\n", "\t\t\t\tend_idx = i - 1\n", "\t\t\t\tif start_idx is not None:\n", "\t\t\t\t\tprofit = np.nansum(y[start_idx : end_idx + 1])\n", "\t\t\t\t\ttransactions.append(profit)\n", "\t\t\t\tcurrent_position = 0\n", "\t\t\t\tstart_idx = None\n", "\n", "\t# 处理末尾未平仓持仓\n", "\tif current_position != 0 and start_idx is not None:\n", "\t\tpass  # 忽略未平仓交易\n", "\n", "\tavg_net_profit = 0.0\n", "\twin_rate = 0.0\n", "\tpl_ratio = 0.0\n", "\n", "\tif transactions:\n", "\t\t# 计算平均单笔净利润\n", "\t\tavg_net_profit = np.mean(transactions) * 1.0e4\n", "\n", "\t\t# 胜率计算\n", "\t\twinning_count = sum(1 for p in transactions if p > 0)\n", "\t\twin_rate = winning_count / len(transactions)\n", "\n", "\t\t# 盈亏比计算\n", "\t\twinning_profits = [p for p in transactions if p > 0]\n", "\t\tlosing_profits = [p for p in transactions if p < 0]\n", "\t\tif losing_profits:\n", "\t\t\tavg_win = np.mean(winning_profits) if winning_profits else 0.0\n", "\t\t\tavg_loss = abs(np.mean(losing_profits))\n", "\t\t\tpl_ratio = avg_win / avg_loss if avg_loss != 0 else 0.0\n", "\n", "\treturn (win_rate, avg_net_profit, pl_ratio)\n", "\n", "\n", "def get_summary(ret: pd.Series, position: pd.Series, period: str = '1min') -> pd.Series:\n", "\t\"\"\"calculate summary metrics\n", "\tArgs:\n", "\t\tret: pd.Series, return series\n", "\t\tposition: pd.Series, position series\n", "\t\tperiod: str, frequency of return series, e.g., 1min denotes checking whether trading every minute\n", "\tReturns:\n", "\t\tpd.Series, summary metrics\n", "   \n", "\t\"\"\"\n", "\tann, sharpe = calc_ann_sharpe(ret, period)\n", "\tcalmar, maxdd = calc_ann_calmar(ret, period)\n", "\ttradeCount, avgHoldtime = tradesCount(position)\n", "\tavg_net_profit, win_rate, pl_ratio = calculate_trading_metrics(position, ret)\n", "\treturn pd.Series(\n", "\t\t[\n", "\t\t\tann,\n", "\t\t\tsharpe,\n", "\t\t\tcalmar,\n", "\t\t\tmaxdd,\n", "\t\t\ttradeCount,\n", "\t\t\tavgHoldtime,\n", "\t\t\tavg_net_profit,\n", "\t\t\twin_rate,\n", "\t\t\tpl_ratio,\n", "\t\t],\n", "\t\tindex=[\n", "\t\t\t'Ann',\n", "\t\t\t'Sharpe',\n", "\t\t\t'<PERSON><PERSON>',\n", "\t\t\t'<PERSON><PERSON>',\n", "\t\t\t'TradeCount',\n", "\t\t\t'AvgHoldTime',\n", "\t\t\t'WinRate',\n", "\t\t\t'PPT(bp)',\n", "\t\t\t'PLR',\n", "\t\t],\n", "\t)\n", "\n", "\n", "def plot_returns(rets: pd.DataFrame, figsize: tuple = (12, 6)):\n", "\t\"\"\"plot returns\n", "\tArgs:\n", "\t\trets: pd.<PERSON><PERSON><PERSON>e, returns\n", "\t\tfigsize: tuple, figure size\n", "\t\"\"\"\n", "\n", "\tdef plot_1feature(rets: pd.DataFrame, title: str = 'Cummulative Returns'):\n", "\t\trets = rets.resample('1D').sum().cumsum()\n", "\t\tfig = plt.figure(figsize=figsize)\n", "\t\tfor col in rets.columns:\n", "\t\t\tplt.plot(rets[col], label=col)\n", "\t\tplt.title(title)\n", "\t\tplt.legend(loc='best')\n", "\t\tplt.grid()\n", "\t\tplt.show()\n", "\n", "\tif len(rets.index.names) > 1:\n", "\t\tfeature_names = rets.index.get_level_values('feature').unique()\n", "\t\tfor feature_name in feature_names:\n", "\t\t\tplot_1feature(rets.loc[feature_name], title=f'{feature_name}')\n", "\telse:\n", "\t\tplot_1feature(rets, title=rets.index.name)\n", "\n", "\n", "def run_backtest(cfg: dict):\n", "\t\"\"\"run backtest\n", "\tArgs:\n", "\t\tcfg: dict, configuration, should have the following keys:\n", "\t\t\t- feature: pd.Series or pd.DataFrame, feature\n", "\t\t\t- label: pd.Series, label, typically the 1 minute return using vwap or twap\n", "\t\t\t- long_threshold: float, threshold for opening and closing long position\n", "\t\t\t- short_threshold: float, threshold for opening and closing short position\n", "\t\t\t- fees: list, fees, e.g., [0.0e-4, 2.0e-4, 5.0e-4] for futures\n", "\tReturns:\n", "\t\tpd.<PERSON><PERSON><PERSON><PERSON>, returns\n", "\t\tpd.DataFrame, summary metrics\n", "\t\t\t- 'Ann': annualized return,\n", "\t\t\t- 'Sharpe': sharpe ratio,\n", "\t\t\t- 'Calmar': kalma<PERSON>,\n", "\t\t\t- 'MaxDD': max draw down during backtesting,\n", "\t\t\t- 'TradeCount': total counts of closed transaction,\n", "\t\t\t- 'AvgHoldTime': average hoding time,\n", "\t\t\t- 'WinRate': percentage of win on all trades,\n", "\t\t\t- 'PPT(bp)': profit per trade in unit of bp,\n", "\t\t\t- 'PLR': profit to loss ratio,\n", "\t\t\t- 'IC': correlation between signal and return,\n", "\t\t\t- 'ACF1': signal autocorrelation with lag 1,\n", "\t\t\t- 'ACF2': signal autocorrelation with lag 2,\n", "\t\t\t- 'ACF5': signal autocorrelation with lag 5,\n", "\t\"\"\"\n", "\tfees = cfg['fees']\n", "\tfeature, label = cfg['feature'], cfg['label']\n", "\tlth, sth = cfg['long_threshold'], cfg['short_threshold']\n", "\n", "\tdef run_backtest_series(x, y, title: str = 'Cummulative Returns'):\n", "\t\trets, summs = {}, {}\n", "\t\tfor fee in fees:\n", "\t\t\tret, position = backtest(x, y, lth, sth, fee, desc=f'backtesting [fee = {round(fee * 1e4, 1)} bp]')\n", "\t\t\tret_sum = get_summary(ret, position, cfg.get('period', '1min'))\n", "\t\t\trets[f'{round(fee * 1e4, 1)} bp'] = ret\n", "\t\t\tsumms[f'{round(fee * 1e4, 1)} bp'] = ret_sum\n", "\n", "\t\trets = pd.concat(rets, axis=1)\n", "\t\trets.index.name = 'cost'\n", "\t\tsummary = pd.concat(summs, axis=1).T\n", "\t\tsummary.index.name = 'cost'\n", "\t\tsummary['IC'] = x.corr(y)\n", "\t\tsummary['ACF1'] = x.corr(x.shift(1))\n", "\t\tsummary['ACF2'] = x.corr(x.shift(2))\n", "\t\tsummary['ACF5'] = x.corr(x.shift(5))\n", "\t\treturn rets, summary\n", "\n", "\tif isinstance(feature, pd.Series):\n", "\t\tprint(f'backtesting {feature.name}', flush=True, end='\\r')\n", "\t\treturn run_backtest_series(feature, label)\n", "\telif isinstance(feature, pd.DataFrame):\n", "\t\trets, summary = {}, {}\n", "\t\tfor col in feature.columns:\n", "\t\t\tprint(f'backtesting {col}', flush=True, end='\\r')\n", "\t\t\trets[col], summary[col] = run_backtest_series(feature[col], label)\n", "\t\treturn pd.concat(rets, names=['feature']), pd.concat(summary, names=['feature'])\n", "\telse:\n", "\t\traise ValueError(f'feature must be a pd.Series or pd.DataFrame, but got {type(feature)}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## calc_label"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def calc_label(\n", "\tminbar: pd.Data<PERSON><PERSON>e,\n", "\tref_price: str = 'vwap',\n", "\tbuyLag: int = 0,\n", "\thorizons: list[int] = [1, 2, 5, 10, 20, 30],\n", "\tlogRet: bool = True,\n", "\tzscore: bool = True,\n", "\tzswindow: int = 1440,\n", "):\n", "\t\"\"\"Calculate forward-looking price change labels for multiple time horizons.\n", "\n", "\tArgs:\n", "\t\tminbar (pd.DataFrame): DataFrame of 1 minute bar containing price columns vwap or twap\n", "\t\tref_price (str, optional): Reference price column to use for calculations. Defaults to 'vwap'.\n", "\t\tbuyLag (int, optional): Number of periods to delay the label calculation. Defaults to 0.\n", "\t\thorizons (list[int], optional): List of forward-looking horizons (in minutes) to calculate returns.\n", "\t\t\t\t\t\t\tDefaults to [1, 2, 5, 10, 20, 30].\n", "\t\tlogRet (bool, optional): Whether to calculate logarithmic returns. If False, uses arithmetic returns.\n", "\t\t\t\t\t\t\tDefaults to <PERSON>.\n", "\t\tzscore (bool, optional): Whether to standardize returns using rolling z-score normalization.\n", "\t\t\t\t\t\t\tDefaults to <PERSON>.\n", "\t\tzswindow (int, optional): Rolling window size (in minutes) for z-score normalization.\n", "\t\t\t\t\t\t\tDefaults to 1440 (24 hours).\n", "\n", "\tReturns:\n", "\t\tpd.DataFrame: DataFrame containing calculated labels for each horizon, with columns named\n", "\t\t\t\t\t 'label_{ref_price}_min_{horizon}'. Returns are optionally z-scored.\n", "\t\"\"\"\n", "\tref_px = minbar[ref_price].copy().bfill()\n", "\tref_px = ref_px.where(ref_px > 0, np.nan)\n", "\tlabels = []\n", "\tfor horizon in horizons:\n", "\t\tlabel = ref_px.div(ref_px.shift(horizon)).shift(-buyLag - horizon)\n", "\t\tif not logRet:\n", "\t\t\tlabel = label.sub(1.0)\n", "\t\telse:\n", "\t\t\tlabel = np.log(label)\n", "\t\tlabel.name = f'label_{ref_price}_min_{horizon}'\n", "\t\tlabels.append(label)\n", "\tlabels = pd.concat(labels, axis=1)\n", "\tif zscore:\n", "\t\tmiu_ = labels.rolling(zswindow, min_periods=1).mean()\n", "\t\tstd_ = labels.rolling(zswindow, min_periods=1).std()\n", "\t\tlabels = labels.sub(miu_).div(std_)\n", "\treturn labels\n", "\n", "\n", "def calc_spec_label(\n", "\tlabel: pd.DataFrame, xrisk: pd.DataFrame, update_period: int = 10 * 1440, lookback: int = 20 * 1440\n", "):\n", "\t\"\"\"Decompose the return from different perspectives\n", "\tArgs:\n", "\t\tlabel: pd.DataFrame, label\n", "\t\txrisk: pd.DataFrame, specific basis\n", "\t\tupdate_period: the period for updating a risk fitting\n", "\t\tlookbak: the risk fitting window, larger window uses more historical data\n", "\tReturns:\n", "\t\tpd.DataFrame, specific return\n", "\t\"\"\"\n", "\tcrows = label.index.intersection(xrisk.index)\n", "\tx = xrisk.loc[crows].copy().fillna(0.0).sort_index()\n", "\ty = label.loc[crows].copy().fillna(0.0).sort_index()\n", "\n", "\tfrom tqdm.auto import tqdm\n", "\tfrom sklearn.linear_model import LinearRegression\n", "\n", "\tresults = []\n", "\tfor i in tqdm(range(lookback, len(x), update_period)):\n", "\t\txpast, ypast = x.iloc[i - lookback : i], y.iloc[i - lookback : i]\n", "\t\txnext, ynext = x.iloc[i : i + update_period], y.iloc[i : i + update_period]\n", "\t\tlr = LinearRegression()\n", "\t\tlr.fit(xpast, ypast)\n", "\t\tresults.append(ynext.sub(lr.predict(xnext)))\n", "\treturn pd.concat(results)\n", "\n", "\n", "# label = calc_label(minbar, logRet=False, zscore=True)\n", "# spec_ret = calc_spec_label(label, spec_basis, 10 * 1440, 60 * 1440)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "38e6d2e18a2849a1a0ff2131acd17bee", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading GC:   0%|          | 0/2558 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Error on 2023-12-30: [Errno 2] No such file or directory: '/mnt/sda/NAS/ShareFolder/lichengxin/data_universe/US/generic_contract/generic_data_adj//GC/2023/12/30/GC_c1.parquet'\r"]}], "source": ["import pandas as pd\n", "from tqdm.auto import tqdm\n", "\n", "minbar_root = '/mnt/sda/NAS/ShareFolder/lichengxin/data_universe/US/generic_contract/generic_data_adj/'\n", "\n", "\n", "def load_minbar(minbar_root: str, product: str, start_date: str, end_date: str) -> pd.DataFrame:\n", "\t\"\"\"Load generic contract data from parquet files for a specified product and date range.\n", "\n", "\tArgs:\n", "\t    minbar_root: Root directory containing the data\n", "\t    product: Product code (e.g., 'GC' for Gold)\n", "\t    start_date: Start date in 'YYYY-MM-DD' format\n", "\t    end_date: End date in 'YYYY-MM-DD' format\n", "\n", "\tReturns:\n", "\t    pd.DataFrame: Concatenated DataFrame with all contract data\n", "\t\"\"\"\n", "\tresults = []\n", "\tdates = pd.date_range(start_date, end_date).astype(str)\n", "\tfor date in tqdm(dates, desc=f'Loading {product}'):\n", "\t\tyear, month, day = date.split('-')\n", "\t\ttry:\n", "\t\t\tfile_path = f'{minbar_root}/{product}/{year}/{month}/{day}/{product}_c1.parquet'\n", "\t\t\tdt = pd.read_parquet(file_path)\n", "\t\t\tdt.index = dt['TH_BAR_TIME'].astype('datetime64[ms]')\n", "\t\t\tresults.append(dt.loc[dt['TH_BAR_VWAP']>0.0])\n", "\t\texcept Exception as err:\n", "\t\t\tprint(f'Error on {date}: {err}', end='\\r')\n", "\n", "\treturn pd.concat(results) if results else pd.DataFrame()\n", "\n", "product = 'GC'\n", "minbar = load_minbar(minbar_root, product, '2018-01-01', '2025-01-01')\n", "minbar.columns = [s.split('TH_BAR_')[-1].lower() for s in minbar.columns]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['security', 'time', 'type', 'open', 'high', 'low', 'close', 'volume',\n", "       'vwap', 'tick_count', 'open_time', 'high_time', 'low_time',\n", "       'close_time', 'snap', 'snap_time', 'utc_time', 'local_time',\n", "       'mnemonic_code'],\n", "      dtype='object')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["minbar.head().columns\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>side</th>\n", "      <th>price</th>\n", "      <th>volume</th>\n", "      <th>tvr</th>\n", "    </tr>\n", "    <tr>\n", "      <th>datetime</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2025-01-01 00:00:00.053</th>\n", "      <td>5.793018e+09</td>\n", "      <td>1.0</td>\n", "      <td>93548.8</td>\n", "      <td>0.036</td>\n", "      <td>3367.7568</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 00:00:00.053</th>\n", "      <td>5.793018e+09</td>\n", "      <td>1.0</td>\n", "      <td>93548.8</td>\n", "      <td>0.020</td>\n", "      <td>1870.9760</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 00:00:05.051</th>\n", "      <td>5.793018e+09</td>\n", "      <td>-1.0</td>\n", "      <td>93548.7</td>\n", "      <td>0.002</td>\n", "      <td>187.0974</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 00:00:05.051</th>\n", "      <td>5.793018e+09</td>\n", "      <td>-1.0</td>\n", "      <td>93548.7</td>\n", "      <td>0.210</td>\n", "      <td>19645.2270</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 00:00:05.051</th>\n", "      <td>5.793018e+09</td>\n", "      <td>-1.0</td>\n", "      <td>93548.7</td>\n", "      <td>0.236</td>\n", "      <td>22077.4932</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 23:59:59.460</th>\n", "      <td>5.794822e+09</td>\n", "      <td>-1.0</td>\n", "      <td>94580.9</td>\n", "      <td>0.002</td>\n", "      <td>189.1618</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 23:59:59.460</th>\n", "      <td>5.794822e+09</td>\n", "      <td>-1.0</td>\n", "      <td>94580.9</td>\n", "      <td>0.022</td>\n", "      <td>2080.7798</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 23:59:59.460</th>\n", "      <td>5.794822e+09</td>\n", "      <td>-1.0</td>\n", "      <td>94580.9</td>\n", "      <td>0.002</td>\n", "      <td>189.1618</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 23:59:59.460</th>\n", "      <td>5.794822e+09</td>\n", "      <td>-1.0</td>\n", "      <td>94580.9</td>\n", "      <td>0.002</td>\n", "      <td>189.1618</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 23:59:59.460</th>\n", "      <td>5.794822e+09</td>\n", "      <td>-1.0</td>\n", "      <td>94580.9</td>\n", "      <td>0.019</td>\n", "      <td>1797.0371</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1804360 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                                   id  side    price  volume         tvr\n", "datetime                                                                \n", "2025-01-01 00:00:00.053  5.793018e+09   1.0  93548.8   0.036   3367.7568\n", "2025-01-01 00:00:00.053  5.793018e+09   1.0  93548.8   0.020   1870.9760\n", "2025-01-01 00:00:05.051  5.793018e+09  -1.0  93548.7   0.002    187.0974\n", "2025-01-01 00:00:05.051  5.793018e+09  -1.0  93548.7   0.210  19645.2270\n", "2025-01-01 00:00:05.051  5.793018e+09  -1.0  93548.7   0.236  22077.4932\n", "...                               ...   ...      ...     ...         ...\n", "2025-01-01 23:59:59.460  5.794822e+09  -1.0  94580.9   0.002    189.1618\n", "2025-01-01 23:59:59.460  5.794822e+09  -1.0  94580.9   0.022   2080.7798\n", "2025-01-01 23:59:59.460  5.794822e+09  -1.0  94580.9   0.002    189.1618\n", "2025-01-01 23:59:59.460  5.794822e+09  -1.0  94580.9   0.002    189.1618\n", "2025-01-01 23:59:59.460  5.794822e+09  -1.0  94580.9   0.019   1797.0371\n", "\n", "[1804360 rows x 5 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.read_parquet('/mnt/sda/NAS/ShareFolder/lishuanglin/HFG/generic_futures/bitcoin/tick/trade/2025-01-01.parquet')"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["\n", "def save_label(label, label_root, product: str):\n", "    os.makedirs(label_root, exist_ok=True)\n", "    dates = label.index.normalize().unique().astype(str).tolist()\n", "    for date in dates:\n", "        lbl = label.loc[date]\n", "        lbl.to_parquet(os.path.join(label_root, f'{date}.parqeut'))\n", "    print(f'all label save for {product}', end='\\r')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "\n", "label_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/HFG/US/generic_contract/generic_data_adj/'\n", "horizons = [1, 2, 5, 10, 20, 30, 60, 120]\n", "\n", "label_options = {\n", "    'raw_ret': ('vwap', 0, horizons, False, False, 1440),\n", "    'log_ret': ('vwap', 0, horizons, True, False, 1440),\n", "    'zscore_1d_ret': ('vwap', 0, horizons, True, True, 1440),\n", "    'zscore_5d_ret': ('vwap', 0, horizons, True, True, 1440*5),\n", "    'zscore_20d_ret': ('vwap', 0, horizons, True, True, 1440*20),\n", "}\n", "\n", "def gen_label_1product(minbar_root: str, product: str, sDate: str, eDate: str):\n", "    minbar  = load_minbar(minbar_root, product, sDate, eDate)\n", "    minbar.columns = [s.split('TH_BAR_')[-1].lower() for s in minbar.columns]\n", "    for lbl_opt, args in label_options.items():\n", "        label = calc_label(minbar, *args)\n", "        save_dir = os.path.join(label_root, product, 'label', lbl_opt)\n", "        save_label(label, save_dir,  product)\n", "\n", "\n", "products = sorted(os.listdir(minbar_root))\n", "for product in tqdm(products, desc='products'):\n", "    gen_label_1product(minbar_root, product, '2008-01-01', '2025-05-10')\n", "        \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# spec_basis = pd.DataFrame()\n", "# spec_basis['miu'] = minbar['vwap'].bfill().pct_change(60).shift(1)*100.0\n", "# spec_basis['std'] = np.arcsinh(minbar['vwap'].rolling(60, min_periods=1).std().shift(1))\n", "# spec_basis['liquidity'] = np.log1p(minbar['trd_vol_sum']).shift(1)\n", "# spec_basis.plot(kind='box')"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# spec_basis = pd.DataFrame()\n", "# spec_basis['miu'] = minbar['vwap'].bfill().pct_change(60).shift(1)*100.0\n", "# spec_basis['std'] = np.arcsinh(minbar['vwap'].rolling(60, min_periods=1).std().shift(1))\n", "# spec_basis['liquidity'] = np.log1p(minbar['trd_vol_sum']).shift(1)\n", "# spec_basis.plot(kind='box')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [], "source": ["raw_label = calc_label(minbar, ref_price='TH_BAR_VWAP', logRet=True, zscore=False)\n", "zs_label = calc_label(minbar, ref_price='TH_BAR_VWAP', logRet=True, zscore=True)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["\n", "# bt_cfg = {\n", "# \t'feature': feature.iloc[:,:1],\n", "# \t'label': label,\n", "# \t'long_threshold': 3.0,\n", "# \t'short_threshold': -3.0,\n", "# \t'fees': [0.0e-4, 2.0e-4, 5.0e-4], ## typical fees for futures\n", "# }\n", "\n", "# rets, summary = bt.run_backtest(bt_cfg)\n", "# # bt.plot_returns(rets)\n", "# summary\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## functions.py"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Overwriting ./hfg/functions.py\n"]}], "source": ["%%writefile './hfg/functions.py'\n", "# coding = utf-8\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "def fill_inf(df: pd.DataFrame, value: float = np.nan):\n", "    \"\"\"fill inf to value\n", "    Args:\n", "        df: pd.DataFrame, dataframe to fill inf\n", "        value: float, value to fill inf\n", "    Returns:\n", "        pd.DataFrame, dataframe after filling inf\n", "    \"\"\"\n", "    df.values[np.isinf(df.values)] = value\n", "    return df\n", "\n", "\n", "def drop_duplicates(df: pd.DataFrame, on_index: bool = True, keep: str = 'first'):\n", "\t\"\"\"drop duplicates in dataframe index or values\n", "\tArgs:\n", "\t\tdf: pd.DataFrame, dataframe to drop duplicates\n", "\t\ton_index: bool, whether to drop duplicates on index\n", "\t\tkeep: str, keep first or last\n", "\tReturns:\n", "\t\tpd.DataFrame, dataframe after dropping duplicates\n", "\t\"\"\"\n", "\tif on_index:\n", "\t\treturn df.loc[~df.index.duplicated(keep=keep)]\n", "\telse:\n", "\t\treturn df.drop_duplicates(keep=keep)\n", "\n", "\n", "def plot_setting(figsize=(10, 5)):\n", "\t\"\"\"set default plot parameters\n", "\tArgs:\n", "\t\tfigsize (tuple, optional): Figure size. Defaults to (10, 5).\n", "\t\"\"\"\n", "\n", "\tplt.rcParams['figure.figsize'] = figsize\n", "\tplt.rcParams['axes.grid'] = True\n", "\tplt.rcParams['legend.loc'] = 'upper left'\n", "\tplt.rcParams['hist.bins'] = 100\n", "\tplt.rcParams['grid.alpha'] = 0.5\n", "\tplt.rcParams['grid.linestyle'] = '--'\n", "\tplt.rcParams['lines.markersize'] = 3.0\n", "\n", "\n", "def demean(df: pd.DataFrame, window: int = 240):\n", "\t\"\"\"demean dataframe\n", "\tArgs:\n", "\t\tdf: pd.DataFrame, dataframe to demean\n", "\t\twindow: int, window size\n", "\tReturns:\n", "\t\tpd.DataFrame, demeaned dataframe\n", "\t\"\"\"\n", "\treturn df.sub(df.rolling(window, min_periods=1).mean())\n", "\n", "\n", "def zscore(df: pd.DataFrame, window: int = 240):\n", "\t\"\"\"zscore dataframe\n", "\tArgs:\n", "\t\tdf: pd.DataFrame, dataframe to be zscored\n", "\t\twindow: int, window size\n", "\tReturns:\n", "\t\tpd.DataFrame, zscored dataframe\n", "\t\"\"\"\n", "\treturn demean(df, window).div(df.rolling(window, min_periods=1).std())\n", "\n", "\n", "def calc_acf(df: pd.DataFrame, lag: int = 1) -> pd.Series:\n", "    \"\"\"\n", "    Calculate the autocorrelation (ACF) for each column in a DataFrame at a specified lag.\n", "\n", "    Args:\n", "        df (pd.DataFrame): The input DataFrame with time series columns.\n", "        lag (int, optional): The lag at which to compute the autocorrelation. Default is 1.\n", "\n", "    Returns:\n", "        pd.Series: Autocorrelation coefficients for each column.\n", "    \"\"\"\n", "    def acf(se: pd.Series, lag: int = 1) -> float:\n", "        return se.corr(se.shift(lag))\n", "\n", "    return df.apply(lambda col: acf(col, lag=lag))\n", "\n", "def winsorize_mad(df: pd.DataFrame, window: int = 1440*1, sigma: float = 4):\n", "    \"\"\"\n", "    Winsorize a DataFrame using the Median Absolute Deviation (MAD) method over a rolling window.\n", "\n", "    Each value in the DataFrame is clipped to be within [median - sigma * MAD, median + sigma * MAD]\n", "    for the specified rolling window.\n", "\n", "    Parameters\n", "    ----------\n", "    df : pd.DataFrame.\n", "        Input DataFrame to be winsorized.\n", "    window : int, optional\n", "        Size of the rolling window (default is 1440).\n", "    sigma : float, optional\n", "        Number of MADs to use for clipping (default is 4).\n", "\n", "    Returns\n", "    -------\n", "    pd.DataFrame\n", "        Winsorized DataFrame.\n", "    \"\"\"\n", "    df = df.copy()\n", "    median_ = df.rolling(window, min_periods=1).median()\n", "    mad = df.sub(median_).abs().rolling(window, min_periods=1).median()\n", "    df = df.where(df <= median_ + sigma * mad, median_ + sigma * mad)\n", "    df = df.where(df >= median_ - sigma * mad, median_ - sigma * mad)\n", "    return df"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>SECURITY</th>\n", "      <th>TICK_SEQUENCE_NUMBER</th>\n", "      <th>TICK_TYPE</th>\n", "      <th>EVT_QUOTE_BID_TIME</th>\n", "      <th>EVT_QUOTE_BID_PRICE</th>\n", "      <th>EVT_QUOTE_BID_SIZE</th>\n", "      <th>EVT_QUOTE_BID_CONDITION_CODE</th>\n", "      <th>EVT_QUOTE_BID_LOCAL_EXCH_SRC</th>\n", "      <th>UPFRONT_QUOTED_BID_PRICE</th>\n", "      <th>EVT_QUOTE_ASK_TIME</th>\n", "      <th>EVT_QUOTE_ASK_PRICE</th>\n", "      <th>EVT_QUOTE_ASK_SIZE</th>\n", "      <th>EVT_QUOTE_ASK_CONDITION_CODE</th>\n", "      <th>EVT_QUOTE_ASK_LOCAL_EXCH_SRC</th>\n", "      <th>UPFRONT_QUOTED_ASK_PRICE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>GCG5 Comdty</td>\n", "      <td>1</td>\n", "      <td>BID</td>\n", "      <td>2025-01-01T22:15:34.564Z</td>\n", "      <td>2639.5</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>CMX</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>GCG5 Comdty</td>\n", "      <td>1</td>\n", "      <td>BID</td>\n", "      <td>2025-01-01T22:32:38.000Z</td>\n", "      <td>2639.5</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>CMX</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>GCG5 Comdty</td>\n", "      <td>2</td>\n", "      <td>ASK</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-01-01T22:32:38.000Z</td>\n", "      <td>2639.5</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>CMX</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>GCG5 Comdty</td>\n", "      <td>3</td>\n", "      <td>ASK</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-01-01T22:45:00.989Z</td>\n", "      <td>2639.5</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>CMX</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>GCG5 Comdty</td>\n", "      <td>4</td>\n", "      <td>BID</td>\n", "      <td>2025-01-01T22:45:15.822Z</td>\n", "      <td>2639.5</td>\n", "      <td>3.0</td>\n", "      <td>NaN</td>\n", "      <td>CMX</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18415241</th>\n", "      <td>GCG5 Comdty</td>\n", "      <td>535537</td>\n", "      <td>ASK</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-01-31T21:59:59.063Z</td>\n", "      <td>2809.3</td>\n", "      <td>1.0</td>\n", "      <td>i</td>\n", "      <td>CMX</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18415242</th>\n", "      <td>GCG5 Comdty</td>\n", "      <td>535538</td>\n", "      <td>ASK</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-01-31T21:59:59.411Z</td>\n", "      <td>2809.2</td>\n", "      <td>1.0</td>\n", "      <td>i</td>\n", "      <td>CMX</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18415243</th>\n", "      <td>GCG5 Comdty</td>\n", "      <td>535539</td>\n", "      <td>BID</td>\n", "      <td>2025-01-31T22:00:00.000Z</td>\n", "      <td>2802.0</td>\n", "      <td>12.0</td>\n", "      <td>NaN</td>\n", "      <td>CMX</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18415244</th>\n", "      <td>GCG5 Comdty</td>\n", "      <td>535540</td>\n", "      <td>ASK</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-01-31T22:00:00.000Z</td>\n", "      <td>2849.4</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>CMX</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18415245</th>\n", "      <td>GCG5 Comdty</td>\n", "      <td>535541</td>\n", "      <td>BID</td>\n", "      <td>2025-01-31T22:00:00.076Z</td>\n", "      <td>2802.0</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>CMX</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>18415246 rows × 15 columns</p>\n", "</div>"], "text/plain": ["             SECURITY  TICK_SEQUENCE_NUMBER TICK_TYPE  \\\n", "0         GCG5 Comdty                     1       BID   \n", "1         GCG5 Comdty                     1       BID   \n", "2         GCG5 Comdty                     2       ASK   \n", "3         GCG5 Comdty                     3       ASK   \n", "4         GCG5 Comdty                     4       BID   \n", "...               ...                   ...       ...   \n", "18415241  GCG5 Comdty                535537       ASK   \n", "18415242  GCG5 Comdty                535538       ASK   \n", "18415243  GCG5 Comdty                535539       BID   \n", "18415244  GCG5 Comdty                535540       ASK   \n", "18415245  GCG5 Comdty                535541       BID   \n", "\n", "                EVT_QUOTE_BID_TIME  EVT_QUOTE_BID_PRICE  EVT_QUOTE_BID_SIZE  \\\n", "0         2025-01-01T22:15:34.564Z               2639.5                 2.0   \n", "1         2025-01-01T22:32:38.000Z               2639.5                 2.0   \n", "2                              NaN                  NaN                 NaN   \n", "3                              NaN                  NaN                 NaN   \n", "4         2025-01-01T22:45:15.822Z               2639.5                 3.0   \n", "...                            ...                  ...                 ...   \n", "18415241                       NaN                  NaN                 NaN   \n", "18415242                       NaN                  NaN                 NaN   \n", "18415243  2025-01-31T22:00:00.000Z               2802.0                12.0   \n", "18415244                       NaN                  NaN                 NaN   \n", "18415245  2025-01-31T22:00:00.076Z               2802.0                 2.0   \n", "\n", "         EVT_QUOTE_BID_CONDITION_CODE EVT_QUOTE_BID_LOCAL_EXCH_SRC  \\\n", "0                                 NaN                          CMX   \n", "1                                 NaN                          CMX   \n", "2                                 NaN                          NaN   \n", "3                                 NaN                          NaN   \n", "4                                 NaN                          CMX   \n", "...                               ...                          ...   \n", "18415241                          NaN                          NaN   \n", "18415242                          NaN                          NaN   \n", "18415243                          NaN                          CMX   \n", "18415244                          NaN                          NaN   \n", "18415245                          NaN                          CMX   \n", "\n", "          UPFRONT_QUOTED_BID_PRICE        EVT_QUOTE_ASK_TIME  \\\n", "0                              NaN                       NaN   \n", "1                              NaN                       NaN   \n", "2                              NaN  2025-01-01T22:32:38.000Z   \n", "3                              NaN  2025-01-01T22:45:00.989Z   \n", "4                              NaN                       NaN   \n", "...                            ...                       ...   \n", "18415241                       NaN  2025-01-31T21:59:59.063Z   \n", "18415242                       NaN  2025-01-31T21:59:59.411Z   \n", "18415243                       NaN                       NaN   \n", "18415244                       NaN  2025-01-31T22:00:00.000Z   \n", "18415245                       NaN                       NaN   \n", "\n", "          EVT_QUOTE_ASK_PRICE  EVT_QUOTE_ASK_SIZE  \\\n", "0                         NaN                 NaN   \n", "1                         NaN                 NaN   \n", "2                      2639.5                 1.0   \n", "3                      2639.5                 2.0   \n", "4                         NaN                 NaN   \n", "...                       ...                 ...   \n", "18415241               2809.3                 1.0   \n", "18415242               2809.2                 1.0   \n", "18415243                  NaN                 NaN   \n", "18415244               2849.4                 1.0   \n", "18415245                  NaN                 NaN   \n", "\n", "         EVT_QUOTE_ASK_CONDITION_CODE EVT_QUOTE_ASK_LOCAL_EXCH_SRC  \\\n", "0                                 NaN                          NaN   \n", "1                                 NaN                          NaN   \n", "2                                 NaN                          CMX   \n", "3                                 NaN                          CMX   \n", "4                                 NaN                          NaN   \n", "...                               ...                          ...   \n", "18415241                            i                          CMX   \n", "18415242                            i                          CMX   \n", "18415243                          NaN                          NaN   \n", "18415244                          NaN                          CMX   \n", "18415245                          NaN                          NaN   \n", "\n", "          UPFRONT_QUOTED_ASK_PRICE  \n", "0                              NaN  \n", "1                              NaN  \n", "2                              NaN  \n", "3                              NaN  \n", "4                              NaN  \n", "...                            ...  \n", "18415241                       NaN  \n", "18415242                       NaN  \n", "18415243                       NaN  \n", "18415244                       NaN  \n", "18415245                       NaN  \n", "\n", "[18415246 rows x 15 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "# pd.read_parquet('/mnt/sda/NAS/ShareFolder/intern/onolt.k/shared/final/quote/GC/2025/01/10/GCG5.parquet')\n", "pd.read_csv('/mnt/sda/NAS/ShareFolder/intern/onolt.k/shared/organized/quote/GC/2025/01/GCG5.csv')"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>aqty</th>\n", "      <th>apx</th>\n", "      <th>bpx</th>\n", "      <th>bqty</th>\n", "      <th>atv</th>\n", "      <th>btv</th>\n", "    </tr>\n", "    <tr>\n", "      <th>datetime</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2025-01-01 00:00:01.007</th>\n", "      <td>5.474</td>\n", "      <td>93548.8</td>\n", "      <td>93548.7</td>\n", "      <td>0.953</td>\n", "      <td>512086.1312</td>\n", "      <td>89151.9111</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 00:00:05.091</th>\n", "      <td>5.512</td>\n", "      <td>93548.8</td>\n", "      <td>93548.7</td>\n", "      <td>0.011</td>\n", "      <td>515640.9856</td>\n", "      <td>1029.0357</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 00:00:05.142</th>\n", "      <td>5.594</td>\n", "      <td>93548.8</td>\n", "      <td>93529.7</td>\n", "      <td>0.597</td>\n", "      <td>523311.9872</td>\n", "      <td>55837.2309</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 00:00:05.193</th>\n", "      <td>4.517</td>\n", "      <td>93548.8</td>\n", "      <td>93529.6</td>\n", "      <td>0.411</td>\n", "      <td>422559.9296</td>\n", "      <td>38440.6656</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 00:00:05.246</th>\n", "      <td>4.437</td>\n", "      <td>93548.8</td>\n", "      <td>93529.6</td>\n", "      <td>0.335</td>\n", "      <td>415076.0256</td>\n", "      <td>31332.4160</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 23:59:59.372</th>\n", "      <td>4.874</td>\n", "      <td>94581.0</td>\n", "      <td>94580.9</td>\n", "      <td>0.668</td>\n", "      <td>460987.7940</td>\n", "      <td>63180.0412</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 23:59:59.424</th>\n", "      <td>5.090</td>\n", "      <td>94581.0</td>\n", "      <td>94580.9</td>\n", "      <td>0.668</td>\n", "      <td>481417.2900</td>\n", "      <td>63180.0412</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 23:59:59.479</th>\n", "      <td>5.090</td>\n", "      <td>94581.0</td>\n", "      <td>94580.9</td>\n", "      <td>0.603</td>\n", "      <td>481417.2900</td>\n", "      <td>57032.2827</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 23:59:59.583</th>\n", "      <td>4.410</td>\n", "      <td>94581.0</td>\n", "      <td>94580.9</td>\n", "      <td>0.603</td>\n", "      <td>417102.2100</td>\n", "      <td>57032.2827</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 23:59:59.743</th>\n", "      <td>4.421</td>\n", "      <td>94581.0</td>\n", "      <td>94580.9</td>\n", "      <td>0.613</td>\n", "      <td>418142.6010</td>\n", "      <td>57978.0917</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1115023 rows × 6 columns</p>\n", "</div>"], "text/plain": ["                          aqty      apx      bpx   bqty          atv  \\\n", "datetime                                                               \n", "2025-01-01 00:00:01.007  5.474  93548.8  93548.7  0.953  512086.1312   \n", "2025-01-01 00:00:05.091  5.512  93548.8  93548.7  0.011  515640.9856   \n", "2025-01-01 00:00:05.142  5.594  93548.8  93529.7  0.597  523311.9872   \n", "2025-01-01 00:00:05.193  4.517  93548.8  93529.6  0.411  422559.9296   \n", "2025-01-01 00:00:05.246  4.437  93548.8  93529.6  0.335  415076.0256   \n", "...                        ...      ...      ...    ...          ...   \n", "2025-01-01 23:59:59.372  4.874  94581.0  94580.9  0.668  460987.7940   \n", "2025-01-01 23:59:59.424  5.090  94581.0  94580.9  0.668  481417.2900   \n", "2025-01-01 23:59:59.479  5.090  94581.0  94580.9  0.603  481417.2900   \n", "2025-01-01 23:59:59.583  4.410  94581.0  94580.9  0.603  417102.2100   \n", "2025-01-01 23:59:59.743  4.421  94581.0  94580.9  0.613  418142.6010   \n", "\n", "                                btv  \n", "datetime                             \n", "2025-01-01 00:00:01.007  89151.9111  \n", "2025-01-01 00:00:05.091   1029.0357  \n", "2025-01-01 00:00:05.142  55837.2309  \n", "2025-01-01 00:00:05.193  38440.6656  \n", "2025-01-01 00:00:05.246  31332.4160  \n", "...                             ...  \n", "2025-01-01 23:59:59.372  63180.0412  \n", "2025-01-01 23:59:59.424  63180.0412  \n", "2025-01-01 23:59:59.479  57032.2827  \n", "2025-01-01 23:59:59.583  57032.2827  \n", "2025-01-01 23:59:59.743  57978.0917  \n", "\n", "[1115023 rows x 6 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.read_parquet('/mnt/sda/NAS/ShareFolder/lishuanglin/HFG/generic_futures/bitcoin/tick/quote/2025-01-01.parquet')"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>side</th>\n", "      <th>price</th>\n", "      <th>volume</th>\n", "      <th>tvr</th>\n", "    </tr>\n", "    <tr>\n", "      <th>datetime</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2025-01-01 00:00:00.053</th>\n", "      <td>5.793018e+09</td>\n", "      <td>1.0</td>\n", "      <td>93548.8</td>\n", "      <td>0.036</td>\n", "      <td>3367.7568</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 00:00:00.053</th>\n", "      <td>5.793018e+09</td>\n", "      <td>1.0</td>\n", "      <td>93548.8</td>\n", "      <td>0.020</td>\n", "      <td>1870.9760</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 00:00:05.051</th>\n", "      <td>5.793018e+09</td>\n", "      <td>-1.0</td>\n", "      <td>93548.7</td>\n", "      <td>0.002</td>\n", "      <td>187.0974</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 00:00:05.051</th>\n", "      <td>5.793018e+09</td>\n", "      <td>-1.0</td>\n", "      <td>93548.7</td>\n", "      <td>0.210</td>\n", "      <td>19645.2270</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 00:00:05.051</th>\n", "      <td>5.793018e+09</td>\n", "      <td>-1.0</td>\n", "      <td>93548.7</td>\n", "      <td>0.236</td>\n", "      <td>22077.4932</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 23:59:59.460</th>\n", "      <td>5.794822e+09</td>\n", "      <td>-1.0</td>\n", "      <td>94580.9</td>\n", "      <td>0.002</td>\n", "      <td>189.1618</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 23:59:59.460</th>\n", "      <td>5.794822e+09</td>\n", "      <td>-1.0</td>\n", "      <td>94580.9</td>\n", "      <td>0.022</td>\n", "      <td>2080.7798</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 23:59:59.460</th>\n", "      <td>5.794822e+09</td>\n", "      <td>-1.0</td>\n", "      <td>94580.9</td>\n", "      <td>0.002</td>\n", "      <td>189.1618</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 23:59:59.460</th>\n", "      <td>5.794822e+09</td>\n", "      <td>-1.0</td>\n", "      <td>94580.9</td>\n", "      <td>0.002</td>\n", "      <td>189.1618</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 23:59:59.460</th>\n", "      <td>5.794822e+09</td>\n", "      <td>-1.0</td>\n", "      <td>94580.9</td>\n", "      <td>0.019</td>\n", "      <td>1797.0371</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1804360 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                                   id  side    price  volume         tvr\n", "datetime                                                                \n", "2025-01-01 00:00:00.053  5.793018e+09   1.0  93548.8   0.036   3367.7568\n", "2025-01-01 00:00:00.053  5.793018e+09   1.0  93548.8   0.020   1870.9760\n", "2025-01-01 00:00:05.051  5.793018e+09  -1.0  93548.7   0.002    187.0974\n", "2025-01-01 00:00:05.051  5.793018e+09  -1.0  93548.7   0.210  19645.2270\n", "2025-01-01 00:00:05.051  5.793018e+09  -1.0  93548.7   0.236  22077.4932\n", "...                               ...   ...      ...     ...         ...\n", "2025-01-01 23:59:59.460  5.794822e+09  -1.0  94580.9   0.002    189.1618\n", "2025-01-01 23:59:59.460  5.794822e+09  -1.0  94580.9   0.022   2080.7798\n", "2025-01-01 23:59:59.460  5.794822e+09  -1.0  94580.9   0.002    189.1618\n", "2025-01-01 23:59:59.460  5.794822e+09  -1.0  94580.9   0.002    189.1618\n", "2025-01-01 23:59:59.460  5.794822e+09  -1.0  94580.9   0.019   1797.0371\n", "\n", "[1804360 rows x 5 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.read_parquet('/mnt/sda/NAS/ShareFolder/lishuanglin/HFG/generic_futures/bitcoin/tick/trade/2025-01-01.parquet')"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["['trade', 'quote']"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "os.listdir('/mnt/sda/NAS/ShareFolder/intern/onolt.k/shared/flushed')"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>bpx</th>\n", "      <th>bqty</th>\n", "      <th>apx</th>\n", "      <th>aqty</th>\n", "      <th>atv</th>\n", "      <th>btv</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2025-01-10 00:00:00+00:00</th>\n", "      <td>2693.100098</td>\n", "      <td>5.0</td>\n", "      <td>2693.300049</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>13465.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-10 00:01:00+00:00</th>\n", "      <td>2693.375000</td>\n", "      <td>0.0</td>\n", "      <td>2693.500000</td>\n", "      <td>2.0</td>\n", "      <td>5387.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-10 00:02:00+00:00</th>\n", "      <td>2693.199951</td>\n", "      <td>5.0</td>\n", "      <td>2693.466553</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>13466.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-10 00:03:00+00:00</th>\n", "      <td>2692.899902</td>\n", "      <td>0.0</td>\n", "      <td>2693.100098</td>\n", "      <td>6.0</td>\n", "      <td>16158.600586</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-10 00:04:00+00:00</th>\n", "      <td>2693.000000</td>\n", "      <td>0.0</td>\n", "      <td>2693.199951</td>\n", "      <td>1.0</td>\n", "      <td>2693.199951</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-10 21:56:00+00:00</th>\n", "      <td>2716.399902</td>\n", "      <td>0.0</td>\n", "      <td>2716.699951</td>\n", "      <td>4.0</td>\n", "      <td>10866.799805</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-10 21:57:00+00:00</th>\n", "      <td>2716.600098</td>\n", "      <td>0.0</td>\n", "      <td>2716.800049</td>\n", "      <td>2.0</td>\n", "      <td>5433.600098</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-10 21:58:00+00:00</th>\n", "      <td>2716.899902</td>\n", "      <td>4.0</td>\n", "      <td>2717.100098</td>\n", "      <td>2.0</td>\n", "      <td>5434.200195</td>\n", "      <td>10867.599609</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-10 21:59:00+00:00</th>\n", "      <td>2717.300049</td>\n", "      <td>0.0</td>\n", "      <td>2717.500000</td>\n", "      <td>1.0</td>\n", "      <td>2717.500000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-10 22:00:00+00:00</th>\n", "      <td>2717.000000</td>\n", "      <td>2.0</td>\n", "      <td>2717.600098</td>\n", "      <td>1.0</td>\n", "      <td>2717.600098</td>\n", "      <td>5434.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1321 rows × 6 columns</p>\n", "</div>"], "text/plain": ["                                   bpx  bqty          apx  aqty           atv  \\\n", "2025-01-10 00:00:00+00:00  2693.100098   5.0  2693.300049   0.0      0.000000   \n", "2025-01-10 00:01:00+00:00  2693.375000   0.0  2693.500000   2.0   5387.000000   \n", "2025-01-10 00:02:00+00:00  2693.199951   5.0  2693.466553   0.0      0.000000   \n", "2025-01-10 00:03:00+00:00  2692.899902   0.0  2693.100098   6.0  16158.600586   \n", "2025-01-10 00:04:00+00:00  2693.000000   0.0  2693.199951   1.0   2693.199951   \n", "...                                ...   ...          ...   ...           ...   \n", "2025-01-10 21:56:00+00:00  2716.399902   0.0  2716.699951   4.0  10866.799805   \n", "2025-01-10 21:57:00+00:00  2716.600098   0.0  2716.800049   2.0   5433.600098   \n", "2025-01-10 21:58:00+00:00  2716.899902   4.0  2717.100098   2.0   5434.200195   \n", "2025-01-10 21:59:00+00:00  2717.300049   0.0  2717.500000   1.0   2717.500000   \n", "2025-01-10 22:00:00+00:00  2717.000000   2.0  2717.600098   1.0   2717.600098   \n", "\n", "                                    btv  \n", "2025-01-10 00:00:00+00:00  13465.500000  \n", "2025-01-10 00:01:00+00:00      0.000000  \n", "2025-01-10 00:02:00+00:00  13466.000000  \n", "2025-01-10 00:03:00+00:00      0.000000  \n", "2025-01-10 00:04:00+00:00      0.000000  \n", "...                                 ...  \n", "2025-01-10 21:56:00+00:00      0.000000  \n", "2025-01-10 21:57:00+00:00      0.000000  \n", "2025-01-10 21:58:00+00:00  10867.599609  \n", "2025-01-10 21:59:00+00:00      0.000000  \n", "2025-01-10 22:00:00+00:00   5434.000000  \n", "\n", "[1321 rows x 6 columns]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "pd.read_parquet('/mnt/sda/NAS/ShareFolder/intern/onolt.k/shared/flushed/quote/GC/2025/01/10/GCG5.parquet').resample('1min').last()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>bpx</th>\n", "      <th>bqty</th>\n", "      <th>apx</th>\n", "      <th>aqty</th>\n", "      <th>atv</th>\n", "      <th>btv</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2025-01-10 00:00:00+00:00</th>\n", "      <td>2693.199951</td>\n", "      <td>0.0</td>\n", "      <td>2693.500000</td>\n", "      <td>9.0</td>\n", "      <td>24241.500000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-10 00:00:00.001000+00:00</th>\n", "      <td>2693.199951</td>\n", "      <td>0.0</td>\n", "      <td>2693.500000</td>\n", "      <td>7.0</td>\n", "      <td>18854.500000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-10 00:00:00.004000+00:00</th>\n", "      <td>2693.199951</td>\n", "      <td>5.0</td>\n", "      <td>2693.500000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>13466.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-10 00:00:00.008000+00:00</th>\n", "      <td>2693.300049</td>\n", "      <td>1.0</td>\n", "      <td>2693.500000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>2693.300049</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-10 00:00:00.009000+00:00</th>\n", "      <td>2693.300049</td>\n", "      <td>2.0</td>\n", "      <td>2693.500000</td>\n", "      <td>3.0</td>\n", "      <td>8080.500000</td>\n", "      <td>5386.600098</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-10 21:59:59.442000+00:00</th>\n", "      <td>2717.266602</td>\n", "      <td>7.0</td>\n", "      <td>2717.550049</td>\n", "      <td>6.0</td>\n", "      <td>16305.300781</td>\n", "      <td>19020.867188</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-10 21:59:59.443000+00:00</th>\n", "      <td>2717.300049</td>\n", "      <td>3.0</td>\n", "      <td>2717.550049</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>8151.900391</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-10 21:59:59.692000+00:00</th>\n", "      <td>2717.300049</td>\n", "      <td>2.0</td>\n", "      <td>2717.550049</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>5434.600098</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-10 21:59:59.702000+00:00</th>\n", "      <td>2717.300049</td>\n", "      <td>0.0</td>\n", "      <td>2717.500000</td>\n", "      <td>1.0</td>\n", "      <td>2717.500000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-10 22:00:00.076000+00:00</th>\n", "      <td>2717.000000</td>\n", "      <td>2.0</td>\n", "      <td>2717.600098</td>\n", "      <td>1.0</td>\n", "      <td>2717.600098</td>\n", "      <td>5434.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>382448 rows × 6 columns</p>\n", "</div>"], "text/plain": ["                                          bpx  bqty          apx  aqty  \\\n", "2025-01-10 00:00:00+00:00         2693.199951   0.0  2693.500000   9.0   \n", "2025-01-10 00:00:00.001000+00:00  2693.199951   0.0  2693.500000   7.0   \n", "2025-01-10 00:00:00.004000+00:00  2693.199951   5.0  2693.500000   0.0   \n", "2025-01-10 00:00:00.008000+00:00  2693.300049   1.0  2693.500000   0.0   \n", "2025-01-10 00:00:00.009000+00:00  2693.300049   2.0  2693.500000   3.0   \n", "...                                       ...   ...          ...   ...   \n", "2025-01-10 21:59:59.442000+00:00  2717.266602   7.0  2717.550049   6.0   \n", "2025-01-10 21:59:59.443000+00:00  2717.300049   3.0  2717.550049   0.0   \n", "2025-01-10 21:59:59.692000+00:00  2717.300049   2.0  2717.550049   0.0   \n", "2025-01-10 21:59:59.702000+00:00  2717.300049   0.0  2717.500000   1.0   \n", "2025-01-10 22:00:00.076000+00:00  2717.000000   2.0  2717.600098   1.0   \n", "\n", "                                           atv           btv  \n", "2025-01-10 00:00:00+00:00         24241.500000      0.000000  \n", "2025-01-10 00:00:00.001000+00:00  18854.500000      0.000000  \n", "2025-01-10 00:00:00.004000+00:00      0.000000  13466.000000  \n", "2025-01-10 00:00:00.008000+00:00      0.000000   2693.300049  \n", "2025-01-10 00:00:00.009000+00:00   8080.500000   5386.600098  \n", "...                                        ...           ...  \n", "2025-01-10 21:59:59.442000+00:00  16305.300781  19020.867188  \n", "2025-01-10 21:59:59.443000+00:00      0.000000   8151.900391  \n", "2025-01-10 21:59:59.692000+00:00      0.000000   5434.600098  \n", "2025-01-10 21:59:59.702000+00:00   2717.500000      0.000000  \n", "2025-01-10 22:00:00.076000+00:00   2717.600098   5434.000000  \n", "\n", "[382448 rows x 6 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.read_parquet('/mnt/sda/NAS/ShareFolder/intern/onolt.k/shared/flushed/quote/GC/2025/01/10/GCG5.parquet')"]}], "metadata": {"kernelspec": {"display_name": "uvbase", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}