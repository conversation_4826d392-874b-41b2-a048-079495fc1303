{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## pre-requisite"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import pandas as pd\n", "from tqdm.auto import tqdm\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ticker_maps = {\n", "\t## future commodity\n", "\t'future_commodities': {\n", "\t\t'gold': 'GC1',\n", "\t\t'silver': 'SI1',\n", "\t\t'copper': 'HG1',\n", "\t\t'oil': 'CL1',\n", "\t\t'gas': 'NG1',\n", "\t},\n", "\t## stock index\n", "\t'stock_indices': {\n", "\t\t'sp500': 'SPX Index',\n", "\t\t'sptsx': 'SPX Index',\n", "\t\t'nky': 'NKY Index',\n", "\t\t'djst': 'DJST Index',\n", "\t\t'as51': 'AS51 Index',\n", "\t\t'dax': 'DAX Index',\n", "\t\t'ukx': 'UKX Index',\n", "\t\t'ndx': 'NDX Index',\n", "\t\t'calc': 'CAC Index',\n", "\t},\n", "\t## X-rates\n", "\t'x_rates': {\n", "\t\t'jpyusd': 'JPYUSD',\n", "\t\t'eurusd': 'EURUSD',\n", "\t\t'chfusd': 'CHFUSD',\n", "\t\t'gbpusd': 'GBPUSD',\n", "\t\t'cadusd': 'CADUSD',\n", "\t\t'audusd': 'AUDUSD',\n", "\t\t'nzdusd': 'NZDUSD',\n", "\t\t'sekusd': 'SEKUSD',\n", "\t\t'nokusd': 'NOKUSD',\n", "\t\t'usdcnh': 'USDCNH',\n", "\t},\n", "\t## rates\n", "\t'rates': {\n", "\t\t'ussoc': 'USSOC',\n", "\t\t'jysoc': 'JYSOC',\n", "\t\t'cabrover': 'CABROVER',\n", "\t},\n", "}\n", "\n", "files = os.listdir('/mnt/sda/NAS/Global/Bloomberg/TickQuote/2025-01/')\n", "for catory, ticker in ticker_maps.items():\n", "\tfor tkr in ticker.values():\n", "\t\texists = [f for f in files if f.startswith(tkr)]\n", "\t\tif len(exists) == 0:\n", "\t\t\tprint(f'{catory}: {tkr} does not exist')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## data.py"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Writing ./hfg/data.py\n"]}], "source": ["%%writefile './hfg/data.py'\n", "import os\n", "import numpy as np\n", "import pandas as pd\n", "from tqdm.auto import tqdm\n", "\n", "ticker_maps = {\n", "\t## future commodity\n", "\t'future_commodities': {\n", "\t\t'gold': 'GC1',\n", "\t\t'silver': 'SI1',\n", "\t\t'copper': 'HG1',\n", "\t\t'oil': 'CL1',\n", "\t\t'gas': 'NG1',\n", "\t},\n", "\t## stock index\n", "\t'stock_indices': {\n", "\t\t'sp500': 'SPX Index',\n", "\t\t'sptsx': 'SPX Index',\n", "\t\t'nky': 'NKY Index',\n", "\t\t'djst': 'DJST Index',\n", "\t\t'as51': 'AS51 Index',\n", "\t\t'dax': 'DAX Index',\n", "\t\t'ukx': 'UKX Index',\n", "\t\t'ndx': 'NDX Index',\n", "\t\t'calc': 'CAC Index',\n", "\t},\n", "\t## X-rates\n", "\t'x_rates': {\n", "\t\t'jpyusd': 'JPYUSD',\n", "\t\t'eurusd': 'EURUSD',\n", "\t\t'chfusd': 'CHFUSD',\n", "\t\t'gbpusd': 'GBPUSD',\n", "\t\t'cadusd': 'CADUSD',\n", "\t\t'audusd': 'AUDUSD',\n", "\t\t'nzdusd': 'NZDUSD',\n", "\t\t'sekusd': 'SEKUSD',\n", "\t\t'nokusd': 'NOKUSD',\n", "\t\t'usdcnh': 'USDCNH',\n", "\t},\n", "\t## rates\n", "\t'rates': {\n", "\t\t'ussoc': 'USSOC',\n", "\t\t'jysoc': 'JYSOC',\n", "\t\t'cabrover': 'CABROVER',\n", "\t},\n", "}\n", "\n", "\n", "def regenerate_quote(df_quote: pd.DataFrame) -> pd.DataFrame:\n", "\tmgd = pd.DataFrame()\n", "\tmgd['security'] = df_quote['SECURITY'].astype(np.str_)\n", "\tmgd['quote_id'] = df_quote['TICK_SEQUENCE_NUMBER']\n", "\tmgd['side'] = df_quote['TICK_TYPE']  # .map({'BID':1, 'ASK':-1})\n", "\tmgd['datetime'] = (\n", "\t\tdf_quote['EVT_QUOTE_BID_TIME']\n", "\t\t.combine_first(df_quote['EVT_QUOTE_ASK_TIME'])\n", "\t\t.str[:-1]\n", "\t\t.astype('datetime64[ns]')\n", "\t)\n", "\tmgd['price'] = (\n", "\t\tdf_quote['EVT_QUOTE_BID_PRICE']\n", "\t\t.combine_first(df_quote['EVT_QUOTE_ASK_PRICE'])\n", "\t\t.astype(np.float32)\n", "\t)\n", "\tmgd['size'] = (\n", "\t\tdf_quote['EVT_QUOTE_BID_SIZE']\n", "\t\t.combine_first(df_quote['EVT_QUOTE_ASK_SIZE'])\n", "\t\t.astype(np.float32)\n", "\t)\n", "\tmgd['exchange_src'] = (\n", "\t\tdf_quote['EVT_QUOTE_BID_LOCAL_EXCH_SRC']\n", "\t\t.combine_first(df_quote['EVT_QUOTE_ASK_LOCAL_EXCH_SRC'])\n", "\t\t.astype(np.str_)\n", "\t)\n", "\treturn mgd\n", "\n", "\n", "def regenerate_quote_index(df_quote: pd.DataFrame) -> pd.DataFrame:\n", "\tmgd = pd.DataFrame()\n", "\tmgd['security'] = df_quote['SECURITY'].astype(np.str_)\n", "\tmgd['quote_id'] = df_quote['TICK_SEQUENCE_NUMBER']\n", "\tmgd['tick_type'] = df_quote['TICK_TYPE']  # .map({'BID':1, 'ASK':-1})\n", "\tmgd['bid_datetime'] = (\n", "\t\tdf_quote['EVT_QUOTE_BID_TIME'].str[:-1].astype('datetime64[ns]')\n", "\t)\n", "\tmgd['bid_price'] = df_quote['EVT_QUOTE_BID_PRICE'].astype(np.float32)\n", "\tmgd['bid_size'] = df_quote['EVT_QUOTE_BID_SIZE'].astype(np.float32)\n", "\tmgd['ask_datetime'] = (\n", "\t\tdf_quote['EVT_QUOTE_ASK_TIME'].str[:-1].astype('datetime64[ns]')\n", "\t)\n", "\tmgd['ask_price'] = df_quote['EVT_QUOTE_ASK_PRICE'].astype(np.float32)\n", "\tmgd['ask_size'] = df_quote['EVT_QUOTE_ASK_SIZE'].astype(np.float32)\n", "\tmgd['exchange_src'] = (\n", "\t\tdf_quote['EVT_QUOTE_BID_LOCAL_EXCH_SRC']\n", "\t\t.combine_first(df_quote['EVT_QUOTE_ASK_LOCAL_EXCH_SRC'])\n", "\t\t.astype(np.str_)\n", "\t)\n", "\treturn mgd\n", "\n", "\n", "def regenerate_trade(df_trade: pd.DataFrame) -> pd.DataFrame:\n", "\tmgd = pd.DataFrame()\n", "\tmgd['security'] = df_trade['SECURITY'].astype(np.str_)\n", "\tmgd['trade_id'] = df_trade['TICK_SEQUENCE_NUMBER']\n", "\tmgd['trade_type'] = df_trade['TICK_TYPE']\n", "\tmgd['trade_datetime'] = df_trade['EVT_TRADE_TIME'].str[:-1].astype('datetime64[ns]')\n", "\ttry:\n", "\t\tmgd['report_datetime'] = (\n", "\t\t\tdf_trade['TRADE_REPORTED_TIME'].str[:-1].astype('datetime64[ns]')\n", "\t\t)\n", "\texcept:\n", "\t\tmgd['report_datetime'] = pd.NaT\n", "\tmgd['price'] = df_trade['EVT_TRADE_PRICE'].astype(np.float32)\n", "\tmgd['size'] = df_trade['EVT_TRADE_SIZE'].astype(np.float32)\n", "\tmgd['exchange_src'] = df_trade['EVT_TRADE_LOCAL_EXCH_SOURCE'].astype(np.str_)\n", "\treturn mgd\n", "\n", "\n", "def save(saveroot: str, df: pd.DataFrame):\n", "\tos.makedirs(f'{saveroot}', exist_ok=True)\n", "\tdates = df['date'].unique()\n", "\tfor date in dates:\n", "\t\tdf_date = df.loc[df['date'] == date].drop(columns=['date'])\n", "\t\tdf_date.to_parquet(f'{saveroot}/{date}.parquet')\n", "\n", "\n", "def load_minbar(\n", "\tminbar_root: str, sDate: str, eDate: str, columns: list[str]\n", ") -> pd.DataFrame:\n", "\tminbars = []\n", "\tdates = sorted(os.listdir(minbar_root))\n", "\tfor date in tqdm(\n", "\t\tdates, desc='loading minbar', miniters=len(dates) // 50, position=0, leave=False\n", "\t):\n", "\t\tif date < sDate or date > eDate:\n", "\t\t\tcontinue\n", "\t\tminbar = pd.read_parquet(f'{minbar_root}/{date}', columns=columns)\n", "\t\tminbars.append(minbar)\n", "\treturn pd.concat(minbars)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## quote"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile './regen_quote.py'\n", "# coding = utf-8\n", "import os\n", "import data\n", "import warnings\n", "import numpy as np\n", "import pandas as pd\n", "from tqdm.auto import tqdm\n", "from joblib import Parallel, delayed\n", "\n", "def calc_1year_quote(year: str, tick_type: str, product_type: str):\n", "    warnings.filterwarnings('ignore', category=UserWarning)\n", "    if product_type == 'future_commodities': return None ## already processed\n", "    dirs = os.listdir(f'{bloomberg_root}{tick_type_map[tick_type]}/')\n", "    dirs = sorted([d for d in dirs if d.startswith(year)])\n", "    for dir in tqdm(dirs, desc=f'{year}'): ## by month\n", "        files = os.listdir(f'{bloomberg_root}{tick_type_map[tick_type]}/{dir}')\n", "        for product in data.ticker_maps[product_type].values():\n", "            product_files = sorted([f for f in files if f.startswith(product)])\n", "            if len(product_files) == 0:\n", "                continue\n", "            for file in product_files: ## by file\n", "                try:\n", "                    df_quote = pd.read_csv(f'{bloomberg_root}{tick_type_map[tick_type]}/{dir}/{file}', low_memory=False)\n", "                    if product_type == 'stock_indices':\n", "                        quote = data.regenerate_quote_index(df_quote)\n", "                        quote['date'] = quote['bid_datetime'].dt.date.astype(np.str_)\n", "                    else:\n", "                        quote = data.regenerate_quote(df_quote)\n", "                        quote['date'] = quote['datetime'].dt.date.astype(np.str_)\n", "                    data.save(f'{saveroot}/{product_type}/{product}/tick/{tick_type}/', quote)\n", "                except Exception as e:\n", "                    print(f'{year} {dir} {product} {file} {e}')\n", "\n", "\n", "bloomberg_root = '/mnt/sda/NAS/Global/Bloomberg/'\n", "saveroot = '/mnt/sda/NAS/ShareFolder/lishuanglin/HFG/'\n", "tick_type_map = {'quote':'TickQuote', 'trade':'TickTrade'}\n", "years = [str(year) for year in np.arange(2020, 2026)]\n", "tick_type = 'quote'\n", "\n", "for product_type in data.ticker_maps.keys():\n", "    with Parallel(n_jobs=len(years)) as parallel:\n", "        parallel(delayed(calc_1year_quote)(year, tick_type, product_type) for year in tqdm(years, desc=product_type))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>security</th>\n", "      <th>trade_id</th>\n", "      <th>trade_type</th>\n", "      <th>trade_datetime</th>\n", "      <th>report_datetime</th>\n", "      <th>price</th>\n", "      <th>size</th>\n", "      <th>exchange_src</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>GCJ5 COMB Comdty</td>\n", "      <td>1</td>\n", "      <td>NEW</td>\n", "      <td>2025-03-02 22:39:35.000</td>\n", "      <td>2025-03-02 22:39:35.000</td>\n", "      <td>2867.399902</td>\n", "      <td>0.0</td>\n", "      <td>CMX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>GCJ5 COMB Comdty</td>\n", "      <td>3</td>\n", "      <td>NEW</td>\n", "      <td>2025-03-02 22:46:49.000</td>\n", "      <td>2025-03-02 22:46:49.000</td>\n", "      <td>2867.500000</td>\n", "      <td>0.0</td>\n", "      <td>CMX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>GCJ5 COMB Comdty</td>\n", "      <td>7</td>\n", "      <td>NEW</td>\n", "      <td>2025-03-02 22:51:01.000</td>\n", "      <td>2025-03-02 22:51:01.000</td>\n", "      <td>2867.500000</td>\n", "      <td>0.0</td>\n", "      <td>CMX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>GCJ5 COMB Comdty</td>\n", "      <td>10</td>\n", "      <td>NEW</td>\n", "      <td>2025-03-02 22:53:46.000</td>\n", "      <td>2025-03-02 22:53:46.000</td>\n", "      <td>2868.300049</td>\n", "      <td>0.0</td>\n", "      <td>CMX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>GCJ5 COMB Comdty</td>\n", "      <td>13</td>\n", "      <td>NEW</td>\n", "      <td>2025-03-02 22:54:15.000</td>\n", "      <td>2025-03-02 22:54:15.000</td>\n", "      <td>2868.300049</td>\n", "      <td>0.0</td>\n", "      <td>CMX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18954</th>\n", "      <td>GCJ5 COMB Comdty</td>\n", "      <td>55016</td>\n", "      <td>NEW</td>\n", "      <td>2025-03-02 23:59:58.333</td>\n", "      <td>2025-03-02 23:59:58.333</td>\n", "      <td>2882.500000</td>\n", "      <td>1.0</td>\n", "      <td>CMX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18955</th>\n", "      <td>GCJ5 COMB Comdty</td>\n", "      <td>55017</td>\n", "      <td>NEW</td>\n", "      <td>2025-03-02 23:59:58.333</td>\n", "      <td>2025-03-02 23:59:58.333</td>\n", "      <td>2882.500000</td>\n", "      <td>1.0</td>\n", "      <td>CMX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18956</th>\n", "      <td>GCJ5 COMB Comdty</td>\n", "      <td>55030</td>\n", "      <td>NEW</td>\n", "      <td>2025-03-02 23:59:58.481</td>\n", "      <td>2025-03-02 23:59:58.481</td>\n", "      <td>2882.500000</td>\n", "      <td>1.0</td>\n", "      <td>CMX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18957</th>\n", "      <td>GCJ5 COMB Comdty</td>\n", "      <td>55031</td>\n", "      <td>NEW</td>\n", "      <td>2025-03-02 23:59:58.481</td>\n", "      <td>2025-03-02 23:59:58.481</td>\n", "      <td>2882.500000</td>\n", "      <td>1.0</td>\n", "      <td>CMX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18958</th>\n", "      <td>GCJ5 COMB Comdty</td>\n", "      <td>55032</td>\n", "      <td>NEW</td>\n", "      <td>2025-03-02 23:59:58.481</td>\n", "      <td>2025-03-02 23:59:58.481</td>\n", "      <td>2882.500000</td>\n", "      <td>1.0</td>\n", "      <td>CMX</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>18959 rows × 8 columns</p>\n", "</div>"], "text/plain": ["               security  trade_id trade_type          trade_datetime  \\\n", "0      GCJ5 COMB Comdty         1        NEW 2025-03-02 22:39:35.000   \n", "1      GCJ5 COMB Comdty         3        NEW 2025-03-02 22:46:49.000   \n", "2      GCJ5 COMB Comdty         7        NEW 2025-03-02 22:51:01.000   \n", "3      GCJ5 COMB Comdty        10        NEW 2025-03-02 22:53:46.000   \n", "4      GCJ5 COMB Comdty        13        NEW 2025-03-02 22:54:15.000   \n", "...                 ...       ...        ...                     ...   \n", "18954  GCJ5 COMB Comdty     55016        NEW 2025-03-02 23:59:58.333   \n", "18955  GCJ5 COMB Comdty     55017        NEW 2025-03-02 23:59:58.333   \n", "18956  GCJ5 COMB Comdty     55030        NEW 2025-03-02 23:59:58.481   \n", "18957  GCJ5 COMB Comdty     55031        NEW 2025-03-02 23:59:58.481   \n", "18958  GCJ5 COMB Comdty     55032        NEW 2025-03-02 23:59:58.481   \n", "\n", "              report_datetime        price  size exchange_src  \n", "0     2025-03-02 22:39:35.000  2867.399902   0.0          CMX  \n", "1     2025-03-02 22:46:49.000  2867.500000   0.0          CMX  \n", "2     2025-03-02 22:51:01.000  2867.500000   0.0          CMX  \n", "3     2025-03-02 22:53:46.000  2868.300049   0.0          CMX  \n", "4     2025-03-02 22:54:15.000  2868.300049   0.0          CMX  \n", "...                       ...          ...   ...          ...  \n", "18954 2025-03-02 23:59:58.333  2882.500000   1.0          CMX  \n", "18955 2025-03-02 23:59:58.333  2882.500000   1.0          CMX  \n", "18956 2025-03-02 23:59:58.481  2882.500000   1.0          CMX  \n", "18957 2025-03-02 23:59:58.481  2882.500000   1.0          CMX  \n", "18958 2025-03-02 23:59:58.481  2882.500000   1.0          CMX  \n", "\n", "[18959 rows x 8 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "pd.read_parquet('/mnt/sda/NAS/ShareFolder/lishuanglin/HFG/future_commodities/GC1/tick/trade/2025-03-02.parquet')"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>trd_num</th>\n", "      <th>security</th>\n", "      <th>exchange_src</th>\n", "      <th>trd_px_first</th>\n", "      <th>trd_px_max</th>\n", "      <th>trd_px_min</th>\n", "      <th>trd_px_last</th>\n", "      <th>trd_vol_sum</th>\n", "      <th>trd_vol_first</th>\n", "      <th>trd_vol_max</th>\n", "      <th>...</th>\n", "      <th>qt_bid_vol_sum</th>\n", "      <th>qt_bid_vol_first</th>\n", "      <th>qt_bid_vol_max</th>\n", "      <th>qt_bid_vol_min</th>\n", "      <th>qt_bid_vol_last</th>\n", "      <th>qt_bid_tvr_sum</th>\n", "      <th>qt_bid_tvr_first</th>\n", "      <th>qt_bid_tvr_max</th>\n", "      <th>qt_bid_tvr_min</th>\n", "      <th>qt_bid_tvr_last</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2025-03-02 00:00:00</th>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-02 00:01:00</th>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-02 00:02:00</th>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-02 00:03:00</th>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-02 00:04:00</th>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-02 23:55:00</th>\n", "      <td>122.0</td>\n", "      <td>GCJ5 COMB Comdty</td>\n", "      <td>CMX</td>\n", "      <td>2878.899902</td>\n", "      <td>2879.699951</td>\n", "      <td>2878.899902</td>\n", "      <td>2879.100098</td>\n", "      <td>159.0</td>\n", "      <td>1.0</td>\n", "      <td>5.0</td>\n", "      <td>...</td>\n", "      <td>342.0</td>\n", "      <td>1.0</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>7.0</td>\n", "      <td>984697.500</td>\n", "      <td>2878.800049</td>\n", "      <td>28790.000000</td>\n", "      <td>2878.800049</td>\n", "      <td>20153.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-02 23:56:00</th>\n", "      <td>184.0</td>\n", "      <td>GCJ5 COMB Comdty</td>\n", "      <td>CMX</td>\n", "      <td>2879.000000</td>\n", "      <td>2879.800049</td>\n", "      <td>2878.500000</td>\n", "      <td>2879.399902</td>\n", "      <td>308.0</td>\n", "      <td>3.0</td>\n", "      <td>11.0</td>\n", "      <td>...</td>\n", "      <td>301.0</td>\n", "      <td>4.0</td>\n", "      <td>6.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>866597.500</td>\n", "      <td>11516.000000</td>\n", "      <td>17270.398438</td>\n", "      <td>2878.600098</td>\n", "      <td>5758.600098</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-02 23:57:00</th>\n", "      <td>175.0</td>\n", "      <td>GCJ5 COMB Comdty</td>\n", "      <td>CMX</td>\n", "      <td>2879.800049</td>\n", "      <td>2880.899902</td>\n", "      <td>2879.699951</td>\n", "      <td>2880.899902</td>\n", "      <td>327.0</td>\n", "      <td>1.0</td>\n", "      <td>16.0</td>\n", "      <td>...</td>\n", "      <td>362.0</td>\n", "      <td>1.0</td>\n", "      <td>9.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1042573.875</td>\n", "      <td>2879.300049</td>\n", "      <td>25915.500000</td>\n", "      <td>2879.300049</td>\n", "      <td>2880.899902</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-02 23:58:00</th>\n", "      <td>268.0</td>\n", "      <td>GCJ5 COMB Comdty</td>\n", "      <td>CMX</td>\n", "      <td>2880.899902</td>\n", "      <td>2882.000000</td>\n", "      <td>2880.300049</td>\n", "      <td>2882.000000</td>\n", "      <td>321.0</td>\n", "      <td>1.0</td>\n", "      <td>5.0</td>\n", "      <td>...</td>\n", "      <td>667.0</td>\n", "      <td>2.0</td>\n", "      <td>9.0</td>\n", "      <td>1.0</td>\n", "      <td>8.0</td>\n", "      <td>1921553.625</td>\n", "      <td>5761.600098</td>\n", "      <td>25935.298828</td>\n", "      <td>2880.199951</td>\n", "      <td>23056.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-02 23:59:00</th>\n", "      <td>102.0</td>\n", "      <td>GCJ5 COMB Comdty</td>\n", "      <td>CMX</td>\n", "      <td>2882.000000</td>\n", "      <td>2882.699951</td>\n", "      <td>2881.500000</td>\n", "      <td>2882.500000</td>\n", "      <td>138.0</td>\n", "      <td>8.0</td>\n", "      <td>8.0</td>\n", "      <td>...</td>\n", "      <td>277.0</td>\n", "      <td>3.0</td>\n", "      <td>7.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>798294.500</td>\n", "      <td>8645.400391</td>\n", "      <td>20176.099609</td>\n", "      <td>2881.500000</td>\n", "      <td>5764.600098</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1440 rows × 48 columns</p>\n", "</div>"], "text/plain": ["                     trd_num          security exchange_src  trd_px_first  \\\n", "2025-03-02 00:00:00      0.0              None         None           NaN   \n", "2025-03-02 00:01:00      0.0              None         None           NaN   \n", "2025-03-02 00:02:00      0.0              None         None           NaN   \n", "2025-03-02 00:03:00      0.0              None         None           NaN   \n", "2025-03-02 00:04:00      0.0              None         None           NaN   \n", "...                      ...               ...          ...           ...   \n", "2025-03-02 23:55:00    122.0  GCJ5 COMB Comdty          CMX   2878.899902   \n", "2025-03-02 23:56:00    184.0  GCJ5 COMB Comdty          CMX   2879.000000   \n", "2025-03-02 23:57:00    175.0  GCJ5 COMB Comdty          CMX   2879.800049   \n", "2025-03-02 23:58:00    268.0  GCJ5 COMB Comdty          CMX   2880.899902   \n", "2025-03-02 23:59:00    102.0  GCJ5 COMB Comdty          CMX   2882.000000   \n", "\n", "                      trd_px_max   trd_px_min  trd_px_last  trd_vol_sum  \\\n", "2025-03-02 00:00:00          NaN          NaN          NaN          0.0   \n", "2025-03-02 00:01:00          NaN          NaN          NaN          0.0   \n", "2025-03-02 00:02:00          NaN          NaN          NaN          0.0   \n", "2025-03-02 00:03:00          NaN          NaN          NaN          0.0   \n", "2025-03-02 00:04:00          NaN          NaN          NaN          0.0   \n", "...                          ...          ...          ...          ...   \n", "2025-03-02 23:55:00  2879.699951  2878.899902  2879.100098        159.0   \n", "2025-03-02 23:56:00  2879.800049  2878.500000  2879.399902        308.0   \n", "2025-03-02 23:57:00  2880.899902  2879.699951  2880.899902        327.0   \n", "2025-03-02 23:58:00  2882.000000  2880.300049  2882.000000        321.0   \n", "2025-03-02 23:59:00  2882.699951  2881.500000  2882.500000        138.0   \n", "\n", "                     trd_vol_first  trd_vol_max  ...  qt_bid_vol_sum  \\\n", "2025-03-02 00:00:00            NaN          NaN  ...             0.0   \n", "2025-03-02 00:01:00            NaN          NaN  ...             0.0   \n", "2025-03-02 00:02:00            NaN          NaN  ...             0.0   \n", "2025-03-02 00:03:00            NaN          NaN  ...             0.0   \n", "2025-03-02 00:04:00            NaN          NaN  ...             0.0   \n", "...                            ...          ...  ...             ...   \n", "2025-03-02 23:55:00            1.0          5.0  ...           342.0   \n", "2025-03-02 23:56:00            3.0         11.0  ...           301.0   \n", "2025-03-02 23:57:00            1.0         16.0  ...           362.0   \n", "2025-03-02 23:58:00            1.0          5.0  ...           667.0   \n", "2025-03-02 23:59:00            8.0          8.0  ...           277.0   \n", "\n", "                     qt_bid_vol_first  qt_bid_vol_max  qt_bid_vol_min  \\\n", "2025-03-02 00:00:00               NaN             NaN             NaN   \n", "2025-03-02 00:01:00               NaN             NaN             NaN   \n", "2025-03-02 00:02:00               NaN             NaN             NaN   \n", "2025-03-02 00:03:00               NaN             NaN             NaN   \n", "2025-03-02 00:04:00               NaN             NaN             NaN   \n", "...                               ...             ...             ...   \n", "2025-03-02 23:55:00               1.0            10.0             1.0   \n", "2025-03-02 23:56:00               4.0             6.0             1.0   \n", "2025-03-02 23:57:00               1.0             9.0             1.0   \n", "2025-03-02 23:58:00               2.0             9.0             1.0   \n", "2025-03-02 23:59:00               3.0             7.0             1.0   \n", "\n", "                     qt_bid_vol_last  qt_bid_tvr_sum  qt_bid_tvr_first  \\\n", "2025-03-02 00:00:00              NaN           0.000               NaN   \n", "2025-03-02 00:01:00              NaN           0.000               NaN   \n", "2025-03-02 00:02:00              NaN           0.000               NaN   \n", "2025-03-02 00:03:00              NaN           0.000               NaN   \n", "2025-03-02 00:04:00              NaN           0.000               NaN   \n", "...                              ...             ...               ...   \n", "2025-03-02 23:55:00              7.0      984697.500       2878.800049   \n", "2025-03-02 23:56:00              2.0      866597.500      11516.000000   \n", "2025-03-02 23:57:00              1.0     1042573.875       2879.300049   \n", "2025-03-02 23:58:00              8.0     1921553.625       5761.600098   \n", "2025-03-02 23:59:00              2.0      798294.500       8645.400391   \n", "\n", "                     qt_bid_tvr_max  qt_bid_tvr_min  qt_bid_tvr_last  \n", "2025-03-02 00:00:00             NaN             NaN              NaN  \n", "2025-03-02 00:01:00             NaN             NaN              NaN  \n", "2025-03-02 00:02:00             NaN             NaN              NaN  \n", "2025-03-02 00:03:00             NaN             NaN              NaN  \n", "2025-03-02 00:04:00             NaN             NaN              NaN  \n", "...                             ...             ...              ...  \n", "2025-03-02 23:55:00    28790.000000     2878.800049     20153.000000  \n", "2025-03-02 23:56:00    17270.398438     2878.600098      5758.600098  \n", "2025-03-02 23:57:00    25915.500000     2879.300049      2880.899902  \n", "2025-03-02 23:58:00    25935.298828     2880.199951     23056.000000  \n", "2025-03-02 23:59:00    20176.099609     2881.500000      5764.600098  \n", "\n", "[1440 rows x 48 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.read_parquet('/mnt/sda/NAS/ShareFolder/lishuanglin/HFG/future_commodities/GC1/minbar/2025-03-02.parquet')"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## trade"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile './regenerate_trade_index.py'\n", "import os\n", "import data\n", "import numpy as np\n", "import pandas as pd\n", "from tqdm.auto import tqdm\n", "\n", "bloomberg_root = '/mnt/sda/NAS/Global/Bloomberg/'\n", "saveroot = '/mnt/sda/NAS/ShareFolder/lishuanglin/HFG/'\n", "\n", "\n", "tick_type_map = {'quote':'TickQuote', 'trade':'TickTrade'}\n", "years = [str(year) for year in np.arange(2018, 2026)]\n", "tick_type = 'trade'\n", "\n", "product_type = 'stock_indices'\n", "\n", "for year in tqdm(years, desc='years'): ## by year\n", "    dirs = os.listdir(f'{bloomberg_root}{tick_type_map[tick_type]}/')\n", "    dirs = sorted([d for d in dirs if d.startswith(year)])\n", "    for dir in tqdm(dirs, desc=f'{year}'): ## by month\n", "        files = os.listdir(f'{bloomberg_root}{tick_type_map[tick_type]}/{dir}')\n", "        for product in data.ticker_maps[product_type].values():\n", "            files_ = sorted([f for f in files if f.startswith(product)])\n", "            for file in files_: ## by file\n", "                df_trade = pd.read_csv(f'{bloomberg_root}{tick_type_map[tick_type]}/{dir}/{file}')\n", "                trade = data.regenerate_trade(df_trade)\n", "                trade['date'] = trade['trade_datetime'].dt.date.astype(np.str_)\n", "                data.save(f'{saveroot}/{product_type}/{product}/tick/{tick_type}/', trade)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pd.read_csv('/mnt/sda/NAS/Global/Bloomberg/TickTrade/2025-01/EURUSD Curncy_trades_1_1.csv.gz')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## minBar"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Overwriting ./gen_minbar.py\n"]}], "source": ["%%writefile './gen_minbar.py'\n", "# coding = utf-8\n", "import os\n", "import numpy as np\n", "import pandas as pd\n", "from hfg import data\n", "\n", "from tqdm.auto import tqdm\n", "from yrqtlib.utils.logger import Logger\n", "\n", "\n", "def load_tickdata(\n", "\tdates: list[str], hfg_root: str, product_type: str, product: str\n", ") -> tuple[dict, dict]:\n", "\ttrades, quotes = {}, {}\n", "\tfor date in tqdm(\n", "\t\tdates[:],\n", "\t\tdesc=f'loading {product_type} {product}',\n", "\t\tminiters=len(dates) // 50,\n", "\t\tposition=0,\n", "\t\tleave=False,\n", "\t):\n", "\t\ttrade_file_dir = f'{hfg_root}{product_type}/{data.ticker_maps[product_type][product]}/tick/trade/'\n", "\t\tquote_file_dir = f'{hfg_root}{product_type}/{data.ticker_maps[product_type][product]}/tick/quote/'\n", "\t\tif os.path.exists(f'{trade_file_dir}/{date}.parquet'):\n", "\t\t\ttrade = pd.read_parquet(f'{trade_file_dir}/{date}.parquet')\n", "\t\t\tif 'price' in trade.columns:\n", "\t\t\t\ttrade['turnover'] = trade['price'] * trade['size']\n", "\t\t\ttrades[date] = trade.sort_index()\n", "\t\tif os.path.exists(f'{quote_file_dir}/{date}.parquet'):\n", "\t\t\tquote = pd.read_parquet(f'{quote_file_dir}/{date}.parquet')\n", "\t\t\tif 'price' in quote.columns:\n", "\t\t\t\tquote['turnover'] = quote['price'] * quote['size']\n", "\t\t\tquotes[date] = quote.sort_index()\n", "\n", "\tif len(trades) > 0:\n", "\t\ttrades = pd.concat(trades).set_index('trade_datetime')\n", "\tif len(quotes) > 0:\n", "\t\ttry:\n", "\t\t\tquotes = pd.concat(quotes).set_index('datetime')\n", "\t\texcept:\n", "\t\t\tquotes = pd.concat(quotes).set_index('bid_datetime')\n", "\treturn trades, quotes\n", "\n", "\n", "def generate_1minbar(trades: pd.DataFrame, quotes: pd.DataFrame) -> pd.DataFrame:\n", "\tminbars = []\n", "\tif len(trades) > 0:\n", "\t\tprint(trades.shape)\n", "\t\tminbar1 = trades.resample('1min').agg(\n", "\t\t\ttrd_num=('trade_id', 'count'),\n", "\t\t\tsecurity=('security', 'last'),\n", "\t\t\texchange_src=('exchange_src', 'last'),\n", "\t\t\ttrd_px_first=('price', 'first'),\n", "\t\t\ttrd_px_max=('price', 'max'),\n", "\t\t\ttrd_px_min=('price', 'min'),\n", "\t\t\ttrd_px_last=('price', 'last'),\n", "\t\t\ttrd_vol_sum=('size', 'sum'),\n", "\t\t\ttrd_vol_first=('size', 'first'),\n", "\t\t\ttrd_vol_max=('size', 'max'),\n", "\t\t\ttrd_vol_min=('size', 'min'),\n", "\t\t\ttrd_vol_last=('size', 'last'),\n", "\t\t\ttrd_tvr_sum=('turnover', 'sum'),\n", "\t\t\ttrd_tvr_first=('turnover', 'first'),\n", "\t\t\ttrd_tvr_max=('turnover', 'max'),\n", "\t\t\ttrd_tvr_min=('turnover', 'min'),\n", "\t\t\ttwap=('price', 'mean'),\n", "\t\t)\n", "\t\tminbar1['vwap'] = minbar1['trd_tvr_sum'].div(minbar1['trd_vol_sum'])\n", "\t\tminbars.append(minbar1)\n", "\n", "\tif len(quotes) > 0:\n", "\t\tprint(quotes.shape)\n", "\t\tminbar2 = quotes.resample('1min').agg(\n", "\t\t\tqt_num=('quote_id', 'count'),\n", "\t\t\tqt_px_first=('price', 'first'),\n", "\t\t\tqt_px_max=('price', 'max'),\n", "\t\t\tqt_px_min=('price', 'min'),\n", "\t\t\tqt_px_last=('price', 'last'),\n", "\t\t\tqt_vol_sum=('size', 'sum'),\n", "\t\t\tqt_vol_first=('size', 'first'),\n", "\t\t\tqt_vol_last=('size', 'last'),\n", "\t\t\tqt_vol_max=('size', 'max'),\n", "\t\t\tqt_vol_min=('size', 'min'),\n", "\t\t\tqt_tvr_sum=('turnover', 'sum'),\n", "\t\t\tqt_tvr_first=('turnover', 'first'),\n", "\t\t\tqt_tvr_max=('turnover', 'max'),\n", "\t\t\tqt_tvr_min=('turnover', 'min'),\n", "\t\t\tqt_tvr_last=('turnover', 'last'),\n", "\t\t)\n", "\t\tminbars.append(minbar2)\n", "\n", "\t\tbid_quotes = quotes.loc[quotes['side'] == 'BID']\n", "\t\tminbar3 = bid_quotes.resample('1min').agg(\n", "\t\t\tqt_bid_num=('quote_id', 'count'),\n", "\t\t\tqt_bid_px_first=('price', 'first'),\n", "\t\t\tqt_bid_px_max=('price', 'max'),\n", "\t\t\tqt_bid_px_min=('price', 'min'),\n", "\t\t\tqt_bid_px_last=('price', 'last'),\n", "\t\t\tqt_bid_vol_sum=('size', 'sum'),\n", "\t\t\tqt_bid_vol_first=('size', 'first'),\n", "\t\t\tqt_bid_vol_max=('size', 'max'),\n", "\t\t\tqt_bid_vol_min=('size', 'min'),\n", "\t\t\tqt_bid_vol_last=('size', 'last'),\n", "\t\t\tqt_bid_tvr_sum=('turnover', 'sum'),\n", "\t\t\tqt_bid_tvr_first=('turnover', 'first'),\n", "\t\t\tqt_bid_tvr_max=('turnover', 'max'),\n", "\t\t\tqt_bid_tvr_min=('turnover', 'min'),\n", "\t\t\tqt_bid_tvr_last=('turnover', 'last'),\n", "\t\t)\n", "\t\tminbars.append(minbar3)\n", "\n", "\treturn pd.concat(minbars, axis=1)\n", "\n", "\n", "def gen_minbar_1product(product_type: str, product: str, sDate: str, eDate: str):\n", "\tLogger.info(f'processing {product_type} {product}', end='\\n')\n", "\tminbar_saveroot = (\n", "\t\tf'{hfg_root}{product_type}/{data.ticker_maps[product_type][product]}/minbar/'\n", "\t)\n", "\t# if os.path.exists(minbar_saveroot):\n", "\t# \tLogger.info(f'{product_type} {product} already exists, skipping', end='\\n')\n", "\t# \treturn None\n", "\n", "\tdates = pd.date_range(start=sDate, end=eDate).astype(np.str_)\n", "\ttrades, quotes = load_tickdata(dates, hfg_root, product_type, product)\n", "\tif product_type == 'stock_indices': ## stock indices have no quotes\n", "\t\tquotes = {}\n", "\tminbar = generate_1minbar(trades, quotes)\n", "\tos.makedirs(minbar_saveroot, exist_ok=True)\n", "\n", "\tdates = minbar.index.normalize().unique().astype(np.str_)\n", "\tminiters = len(dates) // 50\n", "\tfor date in tqdm(\n", "\t\tdates[:],\n", "\t\tdesc=f'saving {product_type} {product}',\n", "\t\tminiters=miniters,\n", "\t\tposition=0,\n", "\t\tleave=False,\n", "\t):\n", "\t\tif os.path.exists(f'{minbar_saveroot}/{date}.parquet'):\n", "\t\t\tcontinue\n", "\t\tminbar.loc[date].to_parquet(f'{minbar_saveroot}/{date}.parquet')\n", "\n", "sDate, eDate = '2023-01-01', '2025-04-25'\n", "hfg_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/HFG/'\n", "product_types = list(data.ticker_maps.keys())\n", "\n", "for product_type in product_types[:]:\n", "    products = list(data.ticker_maps[product_type].keys())[:]\n", "    for product in products:\n", "        gen_minbar_1product(product_type, product, sDate, eDate)\n", "\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "263196a6fbb2423f84457e4ad3468640", "version_major": 2, "version_minor": 0}, "text/plain": ["loading stock_indices sp500:   0%|          | 0/32 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["(492020, 8)\n"]}], "source": ["# pd.read_parquet('/mnt/sda/NAS/ShareFolder/lishuanglin/HFG/stock_indices/SPX Index/tick/quote/2021-01-04.parquet')\n", "\n", "sDate, eDate = '2021-01-01', '2021-02-01'\n", "dates = pd.date_range(start=sDate, end=eDate).astype(np.str_)\n", "product_type, product = 'stock_indices', 'sp500'\n", "trades, quotes = load_tickdata(dates, hfg_root, product_type, product)\n", "if product_type == 'stock_indices': ## stock indices have no quotes\n", "    quotes = {}\n", "minbar = generate_1minbar(trades, quotes)\n"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["minbar1 = trades.resample('1min').agg(\n", "    trd_num=('trade_id', 'count'),\n", "    security=('security', 'last'),\n", "    exchange_src=('exchange_src', 'last'),\n", "    trd_px_first=('price', 'first'),\n", "    trd_px_max=('price', 'max'),\n", "    trd_px_min=('price', 'min'),\n", "    trd_px_last=('price', 'last'),\n", "    trd_vol_sum=('size', 'sum'),\n", "    trd_vol_first=('size', 'first'),\n", "    trd_vol_max=('size', 'max'),\n", "    trd_vol_min=('size', 'min'),\n", "    trd_vol_last=('size', 'last'),\n", "    trd_tvr_sum=('turnover', 'sum'),\n", "    trd_tvr_first=('turnover', 'first'),\n", "    trd_tvr_max=('turnover', 'max'),\n", "    trd_tvr_min=('turnover', 'min'),\n", "    twap=('price', 'mean'),\n", ")\n", "minbar1['vwap'] = minbar1['trd_tvr_sum'].div(minbar1['trd_vol_sum'])\n"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>security</th>\n", "      <th>trade_id</th>\n", "      <th>trade_type</th>\n", "      <th>trade_datetime</th>\n", "      <th>report_datetime</th>\n", "      <th>price</th>\n", "      <th>size</th>\n", "      <th>exchange_src</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>JYSOC Index</td>\n", "      <td>33</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 00:19:36</td>\n", "      <td>NaT</td>\n", "      <td>-0.039187</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>JYSOC Index</td>\n", "      <td>35</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 00:19:36</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>JYSOC Index</td>\n", "      <td>37</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 00:19:36</td>\n", "      <td>NaT</td>\n", "      <td>-0.040325</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>JYSOC Index</td>\n", "      <td>39</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 00:19:36</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>JYSOC Index</td>\n", "      <td>41</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 00:30:00</td>\n", "      <td>NaT</td>\n", "      <td>-0.040266</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>JYSOC Index</td>\n", "      <td>43</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 00:30:00</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>JYSOC Index</td>\n", "      <td>45</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 00:30:00</td>\n", "      <td>NaT</td>\n", "      <td>-0.040222</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>JYSOC Index</td>\n", "      <td>47</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 00:30:00</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>JYSOC Index</td>\n", "      <td>49</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 00:30:11</td>\n", "      <td>NaT</td>\n", "      <td>-0.040188</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>JYSOC Index</td>\n", "      <td>51</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 00:30:11</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>JYSOC Index</td>\n", "      <td>53</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 00:30:11</td>\n", "      <td>NaT</td>\n", "      <td>-0.040161</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>JYSOC Index</td>\n", "      <td>55</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 00:30:11</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50</th>\n", "      <td>JYSOC Index</td>\n", "      <td>57</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 01:03:26</td>\n", "      <td>NaT</td>\n", "      <td>-0.039489</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51</th>\n", "      <td>JYSOC Index</td>\n", "      <td>59</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 01:03:26</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>JYSOC Index</td>\n", "      <td>61</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 01:03:26</td>\n", "      <td>NaT</td>\n", "      <td>-0.040162</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53</th>\n", "      <td>JYSOC Index</td>\n", "      <td>63</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 01:03:26</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>JYSOC Index</td>\n", "      <td>65</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 01:42:23</td>\n", "      <td>NaT</td>\n", "      <td>-0.040143</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55</th>\n", "      <td>JYSOC Index</td>\n", "      <td>67</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 01:42:23</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>56</th>\n", "      <td>JYSOC Index</td>\n", "      <td>69</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 01:54:08</td>\n", "      <td>NaT</td>\n", "      <td>-0.040127</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57</th>\n", "      <td>JYSOC Index</td>\n", "      <td>71</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 01:54:08</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>58</th>\n", "      <td>JYSOC Index</td>\n", "      <td>73</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 01:59:12</td>\n", "      <td>NaT</td>\n", "      <td>-0.040114</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>59</th>\n", "      <td>JYSOC Index</td>\n", "      <td>75</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 01:59:12</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>60</th>\n", "      <td>JYSOC Index</td>\n", "      <td>77</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 02:00:38</td>\n", "      <td>NaT</td>\n", "      <td>-0.040103</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61</th>\n", "      <td>JYSOC Index</td>\n", "      <td>79</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 02:00:38</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62</th>\n", "      <td>JYSOC Index</td>\n", "      <td>81</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 02:55:13</td>\n", "      <td>NaT</td>\n", "      <td>-0.039629</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63</th>\n", "      <td>JYSOC Index</td>\n", "      <td>83</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 02:55:13</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64</th>\n", "      <td>JYSOC Index</td>\n", "      <td>85</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 05:02:42</td>\n", "      <td>NaT</td>\n", "      <td>-0.039879</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>65</th>\n", "      <td>JYSOC Index</td>\n", "      <td>87</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 05:02:42</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66</th>\n", "      <td>JYSOC Index</td>\n", "      <td>89</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 05:02:42</td>\n", "      <td>NaT</td>\n", "      <td>-0.040106</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67</th>\n", "      <td>JYSOC Index</td>\n", "      <td>91</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 05:02:42</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>68</th>\n", "      <td>JYSOC Index</td>\n", "      <td>93</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 05:31:47</td>\n", "      <td>NaT</td>\n", "      <td>-0.040097</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>69</th>\n", "      <td>JYSOC Index</td>\n", "      <td>95</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 05:31:47</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>70</th>\n", "      <td>JYSOC Index</td>\n", "      <td>97</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 05:31:47</td>\n", "      <td>NaT</td>\n", "      <td>-0.040090</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71</th>\n", "      <td>JYSOC Index</td>\n", "      <td>99</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 05:31:47</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72</th>\n", "      <td>JYSOC Index</td>\n", "      <td>101</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 06:02:05</td>\n", "      <td>NaT</td>\n", "      <td>-0.040083</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73</th>\n", "      <td>JYSOC Index</td>\n", "      <td>103</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 06:02:05</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>74</th>\n", "      <td>JYSOC Index</td>\n", "      <td>105</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 06:56:49</td>\n", "      <td>NaT</td>\n", "      <td>-0.040077</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75</th>\n", "      <td>JYSOC Index</td>\n", "      <td>107</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 06:56:49</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>76</th>\n", "      <td>JYSOC Index</td>\n", "      <td>109</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 06:56:49</td>\n", "      <td>NaT</td>\n", "      <td>-0.039901</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>JYSOC Index</td>\n", "      <td>111</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 06:56:49</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>78</th>\n", "      <td>JYSOC Index</td>\n", "      <td>113</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 07:10:09</td>\n", "      <td>NaT</td>\n", "      <td>-0.039736</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>79</th>\n", "      <td>JYSOC Index</td>\n", "      <td>115</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 07:10:09</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>80</th>\n", "      <td>JYSOC Index</td>\n", "      <td>3</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 22:25:35</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81</th>\n", "      <td>JYSOC Index</td>\n", "      <td>5</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 22:25:35</td>\n", "      <td>NaT</td>\n", "      <td>-0.039917</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82</th>\n", "      <td>JYSOC Index</td>\n", "      <td>7</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 22:25:35</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>83</th>\n", "      <td>JYSOC Index</td>\n", "      <td>9</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 22:27:29</td>\n", "      <td>NaT</td>\n", "      <td>-0.039923</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84</th>\n", "      <td>JYSOC Index</td>\n", "      <td>11</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 22:27:29</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>85</th>\n", "      <td>JYSOC Index</td>\n", "      <td>13</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 22:27:29</td>\n", "      <td>NaT</td>\n", "      <td>-0.039927</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>86</th>\n", "      <td>JYSOC Index</td>\n", "      <td>15</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 22:27:29</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87</th>\n", "      <td>JYSOC Index</td>\n", "      <td>17</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 22:32:39</td>\n", "      <td>NaT</td>\n", "      <td>-0.039939</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>88</th>\n", "      <td>JYSOC Index</td>\n", "      <td>19</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 22:32:39</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>89</th>\n", "      <td>JYSOC Index</td>\n", "      <td>21</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 23:02:55</td>\n", "      <td>NaT</td>\n", "      <td>-0.039942</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90</th>\n", "      <td>JYSOC Index</td>\n", "      <td>23</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 23:02:55</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91</th>\n", "      <td>JYSOC Index</td>\n", "      <td>25</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 23:37:02</td>\n", "      <td>NaT</td>\n", "      <td>-0.040078</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92</th>\n", "      <td>JYSOC Index</td>\n", "      <td>27</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 23:37:02</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93</th>\n", "      <td>JYSOC Index</td>\n", "      <td>29</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 23:37:02</td>\n", "      <td>NaT</td>\n", "      <td>-0.040073</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>94</th>\n", "      <td>JYSOC Index</td>\n", "      <td>31</td>\n", "      <td>NEW</td>\n", "      <td>2021-01-04 23:37:02</td>\n", "      <td>NaT</td>\n", "      <td>-0.040000</td>\n", "      <td>0.0</td>\n", "      <td>nan</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       security  trade_id trade_type      trade_datetime report_datetime  \\\n", "38  JYSOC Index        33        NEW 2021-01-04 00:19:36             NaT   \n", "39  JYSOC Index        35        NEW 2021-01-04 00:19:36             NaT   \n", "40  JYSOC Index        37        NEW 2021-01-04 00:19:36             NaT   \n", "41  JYSOC Index        39        NEW 2021-01-04 00:19:36             NaT   \n", "42  JYSOC Index        41        NEW 2021-01-04 00:30:00             NaT   \n", "43  JYSOC Index        43        NEW 2021-01-04 00:30:00             NaT   \n", "44  JYSOC Index        45        NEW 2021-01-04 00:30:00             NaT   \n", "45  JYSOC Index        47        NEW 2021-01-04 00:30:00             NaT   \n", "46  JYSOC Index        49        NEW 2021-01-04 00:30:11             NaT   \n", "47  JYSOC Index        51        NEW 2021-01-04 00:30:11             NaT   \n", "48  JYSOC Index        53        NEW 2021-01-04 00:30:11             NaT   \n", "49  JYSOC Index        55        NEW 2021-01-04 00:30:11             NaT   \n", "50  JYSOC Index        57        NEW 2021-01-04 01:03:26             NaT   \n", "51  JYSOC Index        59        NEW 2021-01-04 01:03:26             NaT   \n", "52  JYSOC Index        61        NEW 2021-01-04 01:03:26             NaT   \n", "53  JYSOC Index        63        NEW 2021-01-04 01:03:26             NaT   \n", "54  JYSOC Index        65        NEW 2021-01-04 01:42:23             NaT   \n", "55  JYSOC Index        67        NEW 2021-01-04 01:42:23             NaT   \n", "56  JYSOC Index        69        NEW 2021-01-04 01:54:08             NaT   \n", "57  JYSOC Index        71        NEW 2021-01-04 01:54:08             NaT   \n", "58  JYSOC Index        73        NEW 2021-01-04 01:59:12             NaT   \n", "59  JYSOC Index        75        NEW 2021-01-04 01:59:12             NaT   \n", "60  JYSOC Index        77        NEW 2021-01-04 02:00:38             NaT   \n", "61  JYSOC Index        79        NEW 2021-01-04 02:00:38             NaT   \n", "62  JYSOC Index        81        NEW 2021-01-04 02:55:13             NaT   \n", "63  JYSOC Index        83        NEW 2021-01-04 02:55:13             NaT   \n", "64  JYSOC Index        85        NEW 2021-01-04 05:02:42             NaT   \n", "65  JYSOC Index        87        NEW 2021-01-04 05:02:42             NaT   \n", "66  JYSOC Index        89        NEW 2021-01-04 05:02:42             NaT   \n", "67  JYSOC Index        91        NEW 2021-01-04 05:02:42             NaT   \n", "68  JYSOC Index        93        NEW 2021-01-04 05:31:47             NaT   \n", "69  JYSOC Index        95        NEW 2021-01-04 05:31:47             NaT   \n", "70  JYSOC Index        97        NEW 2021-01-04 05:31:47             NaT   \n", "71  JYSOC Index        99        NEW 2021-01-04 05:31:47             NaT   \n", "72  JYSOC Index       101        NEW 2021-01-04 06:02:05             NaT   \n", "73  JYSOC Index       103        NEW 2021-01-04 06:02:05             NaT   \n", "74  JYSOC Index       105        NEW 2021-01-04 06:56:49             NaT   \n", "75  JYSOC Index       107        NEW 2021-01-04 06:56:49             NaT   \n", "76  JYSOC Index       109        NEW 2021-01-04 06:56:49             NaT   \n", "77  JYSOC Index       111        NEW 2021-01-04 06:56:49             NaT   \n", "78  JYSOC Index       113        NEW 2021-01-04 07:10:09             NaT   \n", "79  JYSOC Index       115        NEW 2021-01-04 07:10:09             NaT   \n", "80  JYSOC Index         3        NEW 2021-01-04 22:25:35             NaT   \n", "81  JYSOC Index         5        NEW 2021-01-04 22:25:35             NaT   \n", "82  JYSOC Index         7        NEW 2021-01-04 22:25:35             NaT   \n", "83  JYSOC Index         9        NEW 2021-01-04 22:27:29             NaT   \n", "84  JYSOC Index        11        NEW 2021-01-04 22:27:29             NaT   \n", "85  JYSOC Index        13        NEW 2021-01-04 22:27:29             NaT   \n", "86  JYSOC Index        15        NEW 2021-01-04 22:27:29             NaT   \n", "87  JYSOC Index        17        NEW 2021-01-04 22:32:39             NaT   \n", "88  JYSOC Index        19        NEW 2021-01-04 22:32:39             NaT   \n", "89  JYSOC Index        21        NEW 2021-01-04 23:02:55             NaT   \n", "90  JYSOC Index        23        NEW 2021-01-04 23:02:55             NaT   \n", "91  JYSOC Index        25        NEW 2021-01-04 23:37:02             NaT   \n", "92  JYSOC Index        27        NEW 2021-01-04 23:37:02             NaT   \n", "93  JYSOC Index        29        NEW 2021-01-04 23:37:02             NaT   \n", "94  JYSOC Index        31        NEW 2021-01-04 23:37:02             NaT   \n", "\n", "       price  size exchange_src  \n", "38 -0.039187   0.0          nan  \n", "39 -0.040000   0.0          nan  \n", "40 -0.040325   0.0          nan  \n", "41 -0.040000   0.0          nan  \n", "42 -0.040266   0.0          nan  \n", "43 -0.040000   0.0          nan  \n", "44 -0.040222   0.0          nan  \n", "45 -0.040000   0.0          nan  \n", "46 -0.040188   0.0          nan  \n", "47 -0.040000   0.0          nan  \n", "48 -0.040161   0.0          nan  \n", "49 -0.040000   0.0          nan  \n", "50 -0.039489   0.0          nan  \n", "51 -0.040000   0.0          nan  \n", "52 -0.040162   0.0          nan  \n", "53 -0.040000   0.0          nan  \n", "54 -0.040143   0.0          nan  \n", "55 -0.040000   0.0          nan  \n", "56 -0.040127   0.0          nan  \n", "57 -0.040000   0.0          nan  \n", "58 -0.040114   0.0          nan  \n", "59 -0.040000   0.0          nan  \n", "60 -0.040103   0.0          nan  \n", "61 -0.040000   0.0          nan  \n", "62 -0.039629   0.0          nan  \n", "63 -0.040000   0.0          nan  \n", "64 -0.039879   0.0          nan  \n", "65 -0.040000   0.0          nan  \n", "66 -0.040106   0.0          nan  \n", "67 -0.040000   0.0          nan  \n", "68 -0.040097   0.0          nan  \n", "69 -0.040000   0.0          nan  \n", "70 -0.040090   0.0          nan  \n", "71 -0.040000   0.0          nan  \n", "72 -0.040083   0.0          nan  \n", "73 -0.040000   0.0          nan  \n", "74 -0.040077   0.0          nan  \n", "75 -0.040000   0.0          nan  \n", "76 -0.039901   0.0          nan  \n", "77 -0.040000   0.0          nan  \n", "78 -0.039736   0.0          nan  \n", "79 -0.040000   0.0          nan  \n", "80 -0.040000   0.0          nan  \n", "81 -0.039917   0.0          nan  \n", "82 -0.040000   0.0          nan  \n", "83 -0.039923   0.0          nan  \n", "84 -0.040000   0.0          nan  \n", "85 -0.039927   0.0          nan  \n", "86 -0.040000   0.0          nan  \n", "87 -0.039939   0.0          nan  \n", "88 -0.040000   0.0          nan  \n", "89 -0.039942   0.0          nan  \n", "90 -0.040000   0.0          nan  \n", "91 -0.040078   0.0          nan  \n", "92 -0.040000   0.0          nan  \n", "93 -0.040073   0.0          nan  \n", "94 -0.040000   0.0          nan  "]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.read_parquet('/mnt/sda/NAS/ShareFolder/lishuanglin/HFG/rates/JYSOC/tick/trade/2021-01-04.parquet')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## label"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from yrqtlib.utils import add_methods_to_pandas"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile './gen_label_comd.py'\n", "# coding = utf-8\n", "import os\n", "import data\n", "import numpy as np\n", "import pandas as pd\n", "from tqdm.auto import tqdm\n", "\n", "def load_minbar(minbar_root: str, sDate: str, eDate: str, columns: list[str]) -> pd.DataFrame:\n", "    minbars = []\n", "    dates = sorted(os.listdir(minbar_root))\n", "    for date in tqdm(dates, desc='loading minbar', miniters=len(dates)//50, position=0, leave=False):\n", "        if date < sDate or date > eDate:\n", "            continue\n", "        minbar = pd.read_parquet(f'{minbar_root}/{date}', columns=columns)\n", "        minbars.append(minbar)\n", "    return pd.concat(minbars)\n", "\n", "def cacl_label(minbar: pd.DataFrame) -> pd.DataFrame:\n", "    dflabel = minbar[['vwap', 'twap']].copy()\n", "\n", "    dflabel['vwap_1min'] = dflabel['vwap'].div(dflabel['vwap'].shift(1)).shift(-1)\n", "    dflabel['vwap_2min'] = dflabel['vwap'].div(dflabel['vwap'].shift(2)).shift(-2)\n", "    dflabel['vwap_5min'] = dflabel['vwap'].div(dflabel['vwap'].shift(5)).shift(-5)\n", "    dflabel['vwap_10min'] = dflabel['vwap'].div(dflabel['vwap'].shift(10)).shift(-10)\n", "    dflabel['vwap_15min'] = dflabel['vwap'].div(dflabel['vwap'].shift(15)).shift(-15)\n", "    dflabel['vwap_20min'] = dflabel['vwap'].div(dflabel['vwap'].shift(20)).shift(-20)\n", "    dflabel['vwap_30min'] = dflabel['vwap'].div(dflabel['vwap'].shift(30)).shift(-30)\n", "\n", "    dflabel['twap_1min'] = dflabel['twap'].div(dflabel['twap'].shift(1)).shift(-1)\n", "    dflabel['twap_2min'] = dflabel['twap'].div(dflabel['twap'].shift(2)).shift(-2)\n", "    dflabel['twap_5min'] = dflabel['twap'].div(dflabel['twap'].shift(5)).shift(-5)\n", "    dflabel['twap_10min'] = dflabel['twap'].div(dflabel['twap'].shift(10)).shift(-10)\n", "    dflabel['twap_15min'] = dflabel['twap'].div(dflabel['twap'].shift(15)).shift(-15)\n", "    dflabel['twap_20min'] = dflabel['twap'].div(dflabel['twap'].shift(20)).shift(-20)\n", "    dflabel['twap_30min'] = dflabel['twap'].div(dflabel['twap'].shift(30)).shift(-30)\n", "\n", "    dflabel.iloc[:,2:] = dflabel.iloc[:,2:].sub(1.0) * 100.0 ## in unit of percentage\n", "    dflabel[np.isinf(dflabel)] = 0.0 ## set inf to 0\n", "    dflabel = dflabel.clip(lower=-5.0, upper=5.0) ## clip to [-5%, 5%]\n", "\n", "    return dflabel\n", "\n", "product_type = 'future_commodities'\n", "sDate, eDate = '2021-01-01', '2025-01-01'\n", "\n", "for product in data.ticker_maps[product_type].keys():\n", "    minbar_root = f'/mnt/sda/NAS/ShareFolder/lishuanglin/HFG/{product_type}/{data.ticker_maps[product_type][product]}/minbar/'\n", "    minbar = load_minbar(minbar_root, sDate, eDate, columns=['vwap', 'twap'])\n", "    label = cacl_label(minbar)\n", "\n", "    label_root = f'/mnt/sda/NAS/ShareFolder/lishuanglin/HFG/{product_type}/{data.ticker_maps[product_type][product]}/label/'\n", "    os.makedirs(label_root, exist_ok=True)\n", "\n", "    dates = label.index.normalize().unique().astype(np.str_)\n", "    for date in tqdm(dates, desc=f'saving {product_type} {product}', miniters=len(dates)//50, position=0, leave=False):\n", "        label.loc[date].to_parquet(f'{label_root}/{date}.parquet')\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.read_parquet('/mnt/sda/NAS/ShareFolder/lishuanglin/HFG/future_commodities/GC1/minbar/2021-01-01.parquet')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## backtest"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Writing ./hfg/backtest.py\n"]}], "source": ["%%writefile './hfg/backtest.py'\n", "# coding = utf-8\n", "import os\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from tqdm.auto import tqdm\n", "\n", "\n", "def load_label(\n", "\tlabel_root: str,\n", "\tsDate: str,\n", "\teDate: str,\n", "\tref_price: str = 'vwap',\n", "\tbuyLag: int = 0,\n", "\tretLag: int = 1,\n", "\tfreq: str = '1min',\n", ") -> pd.Series:\n", "\tlabels = []\n", "\tdates = sorted(os.listdir(label_root))\n", "\tfor date in tqdm(\n", "\t\tdates, desc='loading label', miniters=len(dates) // 50, position=0, leave=False\n", "\t):\n", "\t\tif date < sDate or date > eDate:\n", "\t\t\tcontinue\n", "\t\tlabel = pd.read_parquet(f'{label_root}/{date}', columns=[ref_price])\n", "\t\tlabels.append(label)\n", "\tprice_bar = pd.concat(labels)[ref_price]\n", "\tif freq != '1min':\n", "\t\tprice_bar = price_bar.resample(freq).mean()\n", "\tlabel = price_bar.div(price_bar.shift(retLag)).sub(1.0).shift(-buyLag - retLag)\n", "\tlabel[np.isinf(label)] = np.nan  ## set inf to nan\n", "\tmul = int(freq.split('min')[0]) / 1\n", "\tlabel = label.clip(\n", "\t\tlower=-0.02 * mul, upper=0.02 * mul\n", "\t)  ## clip to [-2%, 2%] for each minute\n", "\tlast_smooth_px = price_bar.rolling(1000, min_periods=1).mean()\n", "\tlabel = label.where(price_bar >= 0.1 * last_smooth_px, np.nan)\n", "\treturn label * 100.0  ## in unit of percentage\n", "\n", "\n", "def backtest(\n", "\tx: pd.Series,\n", "\ty: pd.Series,\n", "\tlong_threshold: float,\n", "\tshort_threshold: float,\n", "\tfee: float = 1.0e-3,\n", "\tdesc: str = 'backtesting',\n", ") -> pd.Series:\n", "\t\"\"\"\n", "\tBacktest strategy based on signal threshold\n", "\n", "\tArgs:\n", "\t    x: Input signal series\n", "\t    y: Return series\n", "\t    buy_threshold: Signal threshold for buying\n", "\t    sell_threshold: Signal threshold for selling\n", "\n", "\tReturns:\n", "\t    pd.Series: Position series (1 for long, 0 for flat)\n", "\t\"\"\"\n", "\tposition = np.zeros_like(y)\n", "\tx_ = x.copy().shift(1).values\n", "\ty_ = y.copy().values\n", "\tassert long_threshold > short_threshold, 'long_threshold must > short_threshold'\n", "\n", "\tminiters = min(len(x) // 50, len(x))\n", "\tfor i in tqdm(\n", "\t\trange(1, len(x)), desc=desc, miniters=miniters, position=0, leave=False\n", "\t):\n", "\t\t# Keep previous position by default\n", "\t\tposition[i] = position[i - 1]\n", "\n", "\t\tif x_[i] >= long_threshold:  ## a new long signal\n", "\t\t\tif position[i] == 0:  # open a long position if empty\n", "\t\t\t\tposition[i] = 1\n", "\t\t\t\ty_[i] = y_[i] - fee\n", "\t\telif x_[i] < long_threshold:\n", "\t\t\tif position[i] == 1:  ## close a long position if any\n", "\t\t\t\tposition[i] = 0\n", "\t\t\t\ty_[i] = y_[i] - fee\n", "\t\t\tif x_[i] <= short_threshold:  ## a new short signal\n", "\t\t\t\tif position[i] == 0:  # open a short position if empty\n", "\t\t\t\t\tposition[i] = -1\n", "\t\t\t\t\ty_[i] = y_[i] + fee\n", "\t\t\telif x_[i] > short_threshold:\n", "\t\t\t\tif position[i] == -1:  ## close a short position if any\n", "\t\t\t\t\tposition[i] = 0\n", "\t\t\t\t\ty_[i] = y_[i] + fee\n", "\treturn pd.Series(y_, index=y.index).mul(position), pd.Series(\n", "\t\tposition, index=y.index\n", "\t)\n", "\n", "\n", "def calc_ann_sharpe(ret: pd.Series, period: str = '1min') -> float:\n", "\tret = ret.copy()\n", "\tnum_per_year = 365 * 24 * 60 / int(period.replace('min', ''))\n", "\tann = ret.mean() * num_per_year\n", "\tstd_ann = ret.std() * np.sqrt(num_per_year)\n", "\treturn ann, ann / std_ann\n", "\n", "\n", "def calc_ann_calmar(ret: pd.Series, period: str = '1min'):\n", "\t# calmar\n", "\tcumret = ret.cumsum()\n", "\tnum_per_year = 365 * 24 * 60 / int(period.replace('min', ''))\n", "\tmaxdd = (cumret.expanding(min_periods=1).max() - cumret).max()  ## max drawdown\n", "\tyearRet = ret.mean() * num_per_year\n", "\tcr = yearRet / (maxdd + 0.0001)\n", "\n", "\treturn cr, maxdd\n", "\n", "\n", "def tradesCount(position):\n", "\tpositionChg = ((position != position.shift(1)) & (position != 0)).sum()  # 开仓次数\n", "\tavgHoldtime = (position != 0).sum() / np.maximum(positionChg, 1)  # 平均持仓周期\n", "\treturn (positionChg, avgHoldtime)\n", "\n", "\n", "def calculate_trading_metrics(position, y):\n", "\t\"\"\"\n", "\t参数:\n", "\tposition (array-like): 持仓序列 (1: 多头, -1: 空头, 0: 无持仓)\n", "\ty (array-like): 对应每个时间点的收益率序列\n", "\n", "\t返回:\n", "\ttuple: (平均单笔净利润, 胜率, 盈亏比)\n", "\t\"\"\"\n", "\tposition = np.array(position, dtype=int)\n", "\ty = np.array(y, dtype=float)\n", "\ttransactions = []  # 存储每笔交易的净利润\n", "\tcurrent_position = 0  # 当前持仓状态\n", "\tstart_idx = None  # 开仓索引\n", "\n", "\tfor i, pos in enumerate(position):\n", "\t\tif pos != 0:\n", "\t\t\tif current_position == 0:\n", "\t\t\t\t# 新开仓\n", "\t\t\t\tstart_idx = i\n", "\t\t\t\tcurrent_position = pos\n", "\t\t\telif pos != current_position:\n", "\t\t\t\t# 反向平仓并开新仓\n", "\t\t\t\tend_idx = i - 1\n", "\t\t\t\tif start_idx is not None:\n", "\t\t\t\t\tprofit = np.nansum(y[start_idx : end_idx + 1])\n", "\t\t\t\t\ttransactions.append(profit)\n", "\t\t\t\tstart_idx = i\n", "\t\t\t\tcurrent_position = pos\n", "\t\telse:\n", "\t\t\tif current_position != 0:\n", "\t\t\t\t# 平仓到空仓\n", "\t\t\t\tend_idx = i - 1\n", "\t\t\t\tif start_idx is not None:\n", "\t\t\t\t\tprofit = np.nansum(y[start_idx : end_idx + 1])\n", "\t\t\t\t\ttransactions.append(profit)\n", "\t\t\t\tcurrent_position = 0\n", "\t\t\t\tstart_idx = None\n", "\n", "\t# 处理末尾未平仓持仓\n", "\tif current_position != 0 and start_idx is not None:\n", "\t\tpass  # 忽略未平仓交易\n", "\n", "\tavg_net_profit = 0.0\n", "\twin_rate = 0.0\n", "\tpl_ratio = 0.0\n", "\n", "\tif transactions:\n", "\t\t# 计算平均单笔净利润\n", "\t\tavg_net_profit = np.mean(transactions)\n", "\n", "\t\t# 胜率计算\n", "\t\twinning_count = sum(1 for p in transactions if p > 0)\n", "\t\twin_rate = winning_count / len(transactions)\n", "\n", "\t\t# 盈亏比计算\n", "\t\twinning_profits = [p for p in transactions if p > 0]\n", "\t\tlosing_profits = [p for p in transactions if p < 0]\n", "\t\tif losing_profits:\n", "\t\t\tavg_win = np.mean(winning_profits) if winning_profits else 0.0\n", "\t\t\tavg_loss = abs(np.mean(losing_profits))\n", "\t\t\tpl_ratio = avg_win / avg_loss if avg_loss != 0 else 0.0\n", "\n", "\treturn (win_rate, avg_net_profit, pl_ratio)\n", "\n", "\n", "def get_summary(ret, position, period: str = '1min'):\n", "\tann, sharpe = calc_ann_sharpe(ret, period)\n", "\tcalmar, maxdd = calc_ann_calmar(ret, period)\n", "\ttradeCount, avgHoldtime = tradesCount(position)\n", "\tavg_net_profit, win_rate, pl_ratio = calculate_trading_metrics(position, ret)\n", "\treturn pd.Series(\n", "\t\t[\n", "\t\t\tann,\n", "\t\t\tsharpe,\n", "\t\t\tcalmar,\n", "\t\t\tmaxdd,\n", "\t\t\ttradeCount,\n", "\t\t\tavgHoldtime,\n", "\t\t\tavg_net_profit,\n", "\t\t\twin_rate,\n", "\t\t\tpl_ratio,\n", "\t\t],\n", "\t\tindex=[\n", "\t\t\t'ann',\n", "\t\t\t'sharpe',\n", "\t\t\t'calmar',\n", "\t\t\t'maxdd',\n", "\t\t\t'tradeCount',\n", "\t\t\t'avgHoldtime',\n", "\t\t\t'win_rate',\n", "\t\t\t'profit_per_trade',\n", "\t\t\t'profit_loss_ratio',\n", "\t\t],\n", "\t)\n", "\n", "\n", "def plot_returns(rets: pd.DataFrame, figsize: tuple = (12, 6)):\n", "\tdef plot_1feature(rets: pd.DataFrame, title: str = 'Cummulative Returns'):\n", "\t\trets = rets.resample('1D').sum().cumsum()\n", "\t\tfig = plt.figure(figsize=figsize)\n", "\t\tfor col in rets.columns:\n", "\t\t\tplt.plot(rets[col], label=col)\n", "\t\tplt.title(title)\n", "\t\tplt.legend(loc='best')\n", "\t\tplt.grid()\n", "\t\tplt.show()\n", "\n", "\tif len(rets.index.names) > 1:\n", "\t\tfeature_names = rets.index.get_level_values('feature').unique()\n", "\t\tfor feature_name in feature_names:\n", "\t\t\tplot_1feature(rets.loc[feature_name], title=f'{feature_name}')\n", "\telse:\n", "\t\tplot_1feature(rets, title=rets.index.name)\n", "\n", "\n", "def run_backtest(cfg: dict):\n", "\tfees = cfg['fees']\n", "\tfeature, label = cfg['feature'], cfg['label']\n", "\tlth, sth = cfg['long_threshold'], cfg['short_threshold']\n", "\n", "\tdef run_backtest_series(x, y, title: str = 'Cummulative Returns'):\n", "\t\tic = x.corr(y)\n", "\t\trets, summs = {}, {}\n", "\t\tfor fee in fees:\n", "\t\t\tret, position = backtest(\n", "\t\t\t\tx, y, lth, sth, fee, desc=f'fee={int(fee * 1e4)} bp'\n", "\t\t\t)\n", "\t\t\tret_sum = get_summary(ret, position)\n", "\t\t\trets[f'{int(fee * 1e4)} bp'] = ret\n", "\t\t\tsumms[f'{int(fee * 1e4)} bp'] = ret_sum\n", "\n", "\t\trets = pd.concat(rets, axis=1)\n", "\t\trets.index.name = 'cost'\n", "\t\tsummary = pd.concat(summs, axis=1).T\n", "\t\tsummary.index.name = 'cost'\n", "\t\tsummary['ic'] = ic\n", "\t\t# plot_returns(rets, title)\n", "\t\treturn rets, summary\n", "\n", "\tif isinstance(feature, pd.Series):\n", "\t\tprint(f'backtesting {feature.name}', flush=True)\n", "\t\treturn run_backtest_series(feature, label)\n", "\telif isinstance(feature, pd.DataFrame):\n", "\t\trets, summary = {}, {}\n", "\t\tfor col in feature.columns:\n", "\t\t\tprint(f'backtesting {col}', flush=True)\n", "\t\t\trets[col], summary[col] = run_backtest_series(feature[col], label)\n", "\t\treturn pd.concat(rets, names=['feature']), pd.concat(summary, names=['feature'])\n", "\telse:\n", "\t\traise ValueError(\n", "\t\t\tf'feature must be a pd.Series or pd.DataFrame, but got {type(feature)}'\n", "\t\t)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## backtest demo"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "10e2d1f8c5b54449bd3eb94416fb4893", "version_major": 2, "version_minor": 0}, "text/plain": ["loading minbar:   0%|          | 0/1551 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dbf9d1937bc744b0bedc8ce3eafce38b", "version_major": 2, "version_minor": 0}, "text/plain": ["loading label:   0%|          | 0/1551 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["backtesting mom_1min\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4ab8a2a0f0474ab9934ce7546aa9139a", "version_major": 2, "version_minor": 0}, "text/plain": ["fee=0 bp:   0%|          | 0/2102519 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2e919cb4e44646f6a964c37e281fa89e", "version_major": 2, "version_minor": 0}, "text/plain": ["fee=2 bp:   0%|          | 0/2102519 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bec59ce652964f2b953ce28c733aa72e", "version_major": 2, "version_minor": 0}, "text/plain": ["fee=5 bp:   0%|          | 0/2102519 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["backtesting mom_5min\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e3a34074434f48efadf91a9c7de9f996", "version_major": 2, "version_minor": 0}, "text/plain": ["fee=0 bp:   0%|          | 0/2102519 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "73fd16a0cbc74226bbbc016dbe95cb9b", "version_major": 2, "version_minor": 0}, "text/plain": ["fee=2 bp:   0%|          | 0/2102519 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a0ebc223783f4201a48ed26c2363b953", "version_major": 2, "version_minor": 0}, "text/plain": ["fee=5 bp:   0%|          | 0/2102519 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>ann</th>\n", "      <th>sharpe</th>\n", "      <th>calmar</th>\n", "      <th>maxdd</th>\n", "      <th>tradeCount</th>\n", "      <th>avgHoldtime</th>\n", "      <th>win_rate</th>\n", "      <th>profit_per_trade</th>\n", "      <th>profit_loss_ratio</th>\n", "      <th>ic</th>\n", "    </tr>\n", "    <tr>\n", "      <th>feature</th>\n", "      <th>cost</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"3\" valign=\"top\">mom_1min</th>\n", "      <th>0 bp</th>\n", "      <td>0.066577</td>\n", "      <td>1.840390</td>\n", "      <td>2.262076</td>\n", "      <td>0.029332</td>\n", "      <td>229.0</td>\n", "      <td>385.567686</td>\n", "      <td>0.414847</td>\n", "      <td>0.000352</td>\n", "      <td>1.322776</td>\n", "      <td>-0.001882</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2 bp</th>\n", "      <td>0.047110</td>\n", "      <td>1.301218</td>\n", "      <td>1.307443</td>\n", "      <td>0.035932</td>\n", "      <td>229.0</td>\n", "      <td>385.567686</td>\n", "      <td>0.397380</td>\n", "      <td>0.000249</td>\n", "      <td>1.266883</td>\n", "      <td>-0.001882</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5 bp</th>\n", "      <td>0.017908</td>\n", "      <td>0.491338</td>\n", "      <td>0.389889</td>\n", "      <td>0.045832</td>\n", "      <td>229.0</td>\n", "      <td>385.567686</td>\n", "      <td>0.362445</td>\n", "      <td>0.000095</td>\n", "      <td>1.217354</td>\n", "      <td>-0.001882</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"3\" valign=\"top\">mom_5min</th>\n", "      <th>0 bp</th>\n", "      <td>0.068039</td>\n", "      <td>1.877958</td>\n", "      <td>2.246408</td>\n", "      <td>0.030188</td>\n", "      <td>231.0</td>\n", "      <td>384.316017</td>\n", "      <td>0.415584</td>\n", "      <td>0.000357</td>\n", "      <td>1.347373</td>\n", "      <td>-0.002026</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2 bp</th>\n", "      <td>0.048241</td>\n", "      <td>1.330447</td>\n", "      <td>1.300733</td>\n", "      <td>0.036988</td>\n", "      <td>231.0</td>\n", "      <td>384.316017</td>\n", "      <td>0.389610</td>\n", "      <td>0.000253</td>\n", "      <td>1.343290</td>\n", "      <td>-0.002026</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5 bp</th>\n", "      <td>0.018545</td>\n", "      <td>0.507985</td>\n", "      <td>0.392177</td>\n", "      <td>0.047188</td>\n", "      <td>231.0</td>\n", "      <td>384.316017</td>\n", "      <td>0.359307</td>\n", "      <td>0.000097</td>\n", "      <td>1.251475</td>\n", "      <td>-0.002026</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    ann    sharpe    calmar     maxdd  tradeCount  \\\n", "feature  cost                                                       \n", "mom_1min 0 bp  0.066577  1.840390  2.262076  0.029332       229.0   \n", "         2 bp  0.047110  1.301218  1.307443  0.035932       229.0   \n", "         5 bp  0.017908  0.491338  0.389889  0.045832       229.0   \n", "mom_5min 0 bp  0.068039  1.877958  2.246408  0.030188       231.0   \n", "         2 bp  0.048241  1.330447  1.300733  0.036988       231.0   \n", "         5 bp  0.018545  0.507985  0.392177  0.047188       231.0   \n", "\n", "               avgHoldtime  win_rate  profit_per_trade  profit_loss_ratio  \\\n", "feature  cost                                                               \n", "mom_1min 0 bp   385.567686  0.414847          0.000352           1.322776   \n", "         2 bp   385.567686  0.397380          0.000249           1.266883   \n", "         5 bp   385.567686  0.362445          0.000095           1.217354   \n", "mom_5min 0 bp   384.316017  0.415584          0.000357           1.347373   \n", "         2 bp   384.316017  0.389610          0.000253           1.343290   \n", "         5 bp   384.316017  0.359307          0.000097           1.251475   \n", "\n", "                     ic  \n", "feature  cost            \n", "mom_1min 0 bp -0.001882  \n", "         2 bp -0.001882  \n", "         5 bp -0.001882  \n", "mom_5min 0 bp -0.002026  \n", "         2 bp -0.002026  \n", "         5 bp -0.002026  "]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["# %%writefile './demo.backtest.py'\n", "# coding = utf-8\n", "import os\n", "import numpy as np\n", "import pandas as pd\n", "import hfg.backtest as bt\n", "import hfg.data as data\n", "import matplotlib.pyplot as plt\n", "from tqdm.auto import tqdm\n", "\n", "def preprocess_minbar(\n", "\tminbar: pd.Data<PERSON><PERSON>e,\n", "\tlower: float = -np.inf,\n", "\tupper: float = np.inf,\n", "\tsmooth_window: int = 1000,\n", "\tref_price: str='vwap'\n", ") -> pd.DataFrame:\n", "\tminbar = minbar.copy()\n", "\tminbar = minbar.where(minbar[ref_price] > 0.0, np.nan)\n", "\tminbar = minbar.where(minbar[ref_price] > lower, np.nan)\n", "\tminbar = minbar.where(minbar[ref_price] < upper, np.nan)\n", "\tlast_smoothed= minbar[ref_price].rolling(smooth_window, min_periods=1).mean()\n", "\tminbar = minbar.where(minbar[ref_price] >= 0.1 * last_smoothed, np.nan)\n", "\treturn minbar\n", "\n", "\n", "ref_price = 'vwap'\n", "sDate, eDate = '2021-01-01', '2025-01-01'\n", "minbar_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/HFG/future_commodities/GC1/minbar/'\n", "minbar = data.load_minbar(minbar_root, sDate, eDate, columns=[ref_price])\n", "minbar = preprocess_minbar(minbar, lower=0.0, upper=np.inf, smooth_window=1000, ref_price=ref_price)\n", "\n", "minbar['mom_1min'] = minbar[ref_price].ffill().pct_change(1) * 100.0 # in unit of percentage\n", "minbar['mom_5min'] = minbar[ref_price].ffill().pct_change(5) * 100.0 # in unit of percentage\n", "\n", "feature = minbar[['mom_1min', 'mom_5min']]\n", "feature.values[np.isinf(feature.values)] = np.nan ## set inf to nan\n", "feature = feature.clip(lower=-0.2, upper=0.2) ## clip to [-0.2%, 0.2%]\n", "feature = feature.sub(feature.rolling(1000).mean()).div(feature.rolling(1000).std())\n", "feature = feature.ewm(halflife=120, adjust=False).mean()\n", "\n", "trade_freq = '1min'\n", "feature = feature.resample(trade_freq).last()\n", "label = bt.load_label(minbar_root, sDate, eDate, ref_price=ref_price, freq=trade_freq)*0.01\n", "\n", "\n", "bt_cfg = {\n", "\t'feature': feature,\n", "\t'label': label,\n", "\t'long_threshold': 3.0,\n", "\t'short_threshold': -3.0,\n", "\t'fees': [0.0e-4, 2.0e-4, 5.0e-4], ## typical fees for futures\n", "}\n", "\n", "rets, summary = bt.run_backtest(bt_cfg)\n", "# bt.plot_returns(rets)\n", "summary\n"]}, {"cell_type": "code", "execution_count": 206, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["x = pd.Series(np.random.randn(10000000)).div(100.0).cumsum()\n", "x = x.add(np.abs(x.min()) + 100.0)\n", "\n", "plt.plot(x);\n"]}, {"cell_type": "code", "execution_count": 207, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c9956b0fbed3466094a61b137ec5cda0", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/111 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["acfs = []\n", "for i in tqdm(range(30000, len(x)//20, 30000)):\n", "    acfs.append(x.corr(x.shift(i)))\n", "plt.plot(acfs);"]}, {"cell_type": "code", "execution_count": 208, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.hist(x, bins=100);"]}, {"cell_type": "code", "execution_count": 235, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["y = x.pct_change(30000*20).shift(-30000*20).clip(lower=-10, upper=10)\n", "z = x.pct_change(30000*20).shift(1)\n", "plt.hist(y, bins=100);"]}, {"cell_type": "code", "execution_count": 236, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x7f8b30b27250>]"]}, "execution_count": 236, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(z)"]}, {"cell_type": "code", "execution_count": 237, "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(-0.3038730701939463)"]}, "execution_count": 237, "metadata": {}, "output_type": "execute_result"}], "source": ["x.corr(y)"]}, {"cell_type": "code", "execution_count": 238, "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(0.698710029472388)"]}, "execution_count": 238, "metadata": {}, "output_type": "execute_result"}], "source": ["x.corr(x.shift(-30000*20))"]}, {"cell_type": "code", "execution_count": 239, "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(-0.09155390836286906)"]}, "execution_count": 239, "metadata": {}, "output_type": "execute_result"}], "source": ["x.corr(y) *  (1 - np.abs(x.corr(x.shift(-30000*20))))"]}, {"cell_type": "code", "execution_count": 240, "metadata": {}, "outputs": [], "source": ["# plt.hist(z, bins=100);"]}, {"cell_type": "code", "execution_count": 241, "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(-0.33637562639753504)"]}, "execution_count": 241, "metadata": {}, "output_type": "execute_result"}], "source": ["z.corr(y)"]}, {"cell_type": "code", "execution_count": 242, "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(-0.3363760548287772)"]}, "execution_count": 242, "metadata": {}, "output_type": "execute_result"}], "source": ["z.corr(z.shift(30000*20))"]}, {"cell_type": "code", "execution_count": 243, "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(-0.22322692024937352)"]}, "execution_count": 243, "metadata": {}, "output_type": "execute_result"}], "source": ["z.corr(y) * (1 - np.abs(z.corr(z.shift(30000*20))))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "uvbase", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}