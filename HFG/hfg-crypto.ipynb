%load_ext autoreload
%autoreload 2

from hfg import functions
from yrqtlib.utils import add_methods_to_pandas

import os
import numpy as np
import pandas as pd
from yrqtlib.utils import data
from yrqtlib.utils import tools


def gen_minbar(trade, quote, freq='1min'):
	minbar1 = trade.resample(freq).agg(
		trade_num=('side', 'count'),
		trd_px_first=('price', 'first'),
		trd_px_last=('price', 'last'),
		trd_px_max=('price', 'max'),
		trd_px_min=('price', 'min'),
		twap=('price', 'mean'),
		trd_px_std=('price', 'std'),
		trd_px_skew=('price', 'skew'),
		trd_vol_first=('volume', 'first'),
		trd_vol_last=('volume', 'last'),
		trd_vol_max=('volume', 'max'),
		trd_vol_min=('volume', 'min'),
		trd_vol_sum=('volume', 'sum'),
		trd_vol_std=('volume', 'std'),
		trd_vol_skew=('volume', 'skew'),
		trd_tvr_first=('tvr', 'first'),
		trd_tvr_last=('tvr', 'last'),
		trd_tvr_max=('tvr', 'max'),
		trd_tvr_min=('tvr', 'min'),
		trd_tvr_sum=('tvr', 'sum'),
		trd_tvr_std=('tvr', 'std'),
		trd_tvr_skew=('tvr', 'skew'),
	)
	minbar1['vwap'] = minbar1['trd_tvr_sum'].div(minbar1['trd_vol_sum'])
	minbar1.index = minbar1.index.round('ms')

	minbar2 = quote.resample(freq).agg(
		qt_num=('aqty', 'count'),
		qt_ask_num=('aqty', 'count'),
		qt_bid_num=('bqty', 'count'),
		qt_apx_first=('apx', 'first'),
		qt_apx_last=('apx', 'last'),
		qt_apx_max=('apx', 'max'),
		qt_apx_min=('apx', 'min'),
		qt_apx_std=('apx', 'std'),
		qt_apx_skew=('apx', 'skew'),
		qt_bpx_first=('bpx', 'first'),
		qt_bpx_last=('bpx', 'last'),
		qt_bpx_max=('bpx', 'max'),
		qt_bpx_min=('bpx', 'min'),
		qt_bpx_std=('bpx', 'std'),
		qt_bpx_skew=('bpx', 'skew'),
		qt_aqty_first=('aqty', 'first'),
		qt_aqty_last=('aqty', 'last'),
		qt_aqty_max=('aqty', 'max'),
		qt_aqty_min=('aqty', 'min'),
		qt_aqty_sum=('aqty', 'sum'),
		qt_aqty_std=('aqty', 'std'),
		qt_aqty_skew=('aqty', 'skew'),
		qt_bqty_first=('bqty', 'first'),
		qt_bqty_last=('bqty', 'last'),
		qt_bqty_max=('bqty', 'max'),
		qt_bqty_min=('bqty', 'min'),
		qt_bqty_sum=('bqty', 'sum'),
		qt_bqty_std=('bqty', 'std'),
		qt_bqty_skew=('bqty', 'skew'),
		qt_atv_first=('atv', 'first'),
		qt_atv_last=('atv', 'last'),
		qt_atv_max=('atv', 'max'),
		qt_atv_min=('atv', 'min'),
		qt_atv_sum=('atv', 'sum'),
		qt_atv_std=('atv', 'std'),
		qt_atv_skew=('atv', 'skew'),
		qt_btv_first=('btv', 'first'),
		qt_btv_last=('btv', 'last'),
		qt_btv_max=('btv', 'max'),
		qt_btv_min=('btv', 'min'),
		qt_btv_sum=('btv', 'sum'),
		qt_btv_std=('btv', 'std'),
		qt_btv_skew=('btv', 'skew'),
	)
	minbar2.index = minbar2.index.round('ms')

	return pd.concat([minbar1, minbar2], axis=1)


def process_btc(date, freq='1min'):
	trade = data.load_1sym_tick('gb_bina_future', 'trade', date, 'BTCUSDT.BNF')
	trade['side'] = (1.5 - trade['side']) * 2
	quote = data.load_1sym_tick('gb_bina_future', 'quote', date, 'BTCUSDT.BNF')

	quote.rename(
		columns={
			'ask_amount': 'aqty',
			'ask_price': 'apx',
			'bid_price': 'bpx',
			'bid_amount': 'bqty',
		},
		inplace=True,
	)

	quote = quote.drop(columns=['exchange_time', 'server_recv_time', 'server_recv_id'])
	trade = trade.drop(columns=['exchange_time', 'server_recv_time', 'server_recv_id'])
	trade['tvr'] = trade['price'] * trade['volume']
	quote['atv'] = quote['aqty'] * quote['apx']
	quote['btv'] = quote['bqty'] * quote['bpx']

	quote.to_parquet(f'{saveroot}/tick/quote/{date}.parquet')
	trade.to_parquet(f'{saveroot}/tick/trade/{date}.parquet')

	minbar = gen_minbar(trade, quote, freq)
	minbar.to_parquet(f'{saveroot}/minbar/{date}.parquet')

	print(f'processing for {date} finised!', end='\r')


saveroot = '/mnt/sda/NAS/ShareFolder/lishuanglin/HFG/generic_futures/bitcoin/'
os.makedirs(f'{saveroot}tick/trade/', exist_ok=True)
os.makedirs(f'{saveroot}tick/quote/', exist_ok=True)
os.makedirs(f'{saveroot}minbar/', exist_ok=True)

dates = pd.date_range(start='2021-09-01', end='2025-05-10').astype(np.str_)


tasks = [(date, '1min') for date in dates]
_ = tools.parallel(process_btc, tasks, 60, progress_bar=True)




import numpy as np
import pandas as pd


def calc_label(
	minbar: pd.DataFrame,
	ref_price: str = 'vwap',
	buyLag: int = 0,
	horizons: list[int] = [1, 2, 5, 10, 20, 30],
	logRet: bool = True,
	zscore: bool = True,
	zswindow: int = 1440,
	fee: float = 5.0e-4,
):
	"""Calculate forward-looking price change labels for multiple time horizons.

	Args:
		minbar (pd.DataFrame): DataFrame of 1 minute bar containing price columns vwap or twap
		ref_price (str, optional): Reference price column to use for calculations. Defaults to 'vwap'.
		buyLag (int, optional): Number of periods to delay the label calculation. Defaults to 0.
		horizons (list[int], optional): List of forward-looking horizons (in minutes) to calculate returns.
							Defaults to [1, 2, 5, 10, 20, 30].
		logRet (bool, optional): Whether to calculate logarithmic returns. If False, uses arithmetic returns.
							Defaults to True.
		zscore (bool, optional): Whether to standardize returns using rolling z-score normalization.
							Defaults to True.
		zswindow (int, optional): Rolling window size (in minutes) for z-score normalization.
							Defaults to 1440 (24 hours).

	Returns:
		pd.DataFrame: DataFrame containing calculated labels for each horizon, with columns named
					 'label_{ref_price}_min_{horizon}'. Returns are optionally z-scored.
	"""
	ref_px = minbar[ref_price].copy().bfill()
	ref_px = ref_px.where(ref_px > 0, np.nan)
	labels = []
	for horizon in horizons:
		label = ref_px.div(ref_px.shift(horizon)).shift(-buyLag - horizon)
		if not logRet:
			label = label.sub(1.0)
		else:
			label = np.log(label)
		label = np.sign(label) * label.abs().sub(fee)
		label.name = f'label_{ref_price}_min_{horizon}'
		labels.append(label)
	labels = pd.concat(labels, axis=1)
	if zscore:
		miu_ = labels.rolling(zswindow, min_periods=1).mean()
		std_ = labels.rolling(zswindow, min_periods=1).std()
		labels = labels.sub(miu_).div(std_)
	return labels


def calc_spec_label(
	label: pd.DataFrame, xrisk: pd.DataFrame, update_period: int = 10 * 1440, lookback: int = 20 * 1440
):
	"""Decompose the return from different perspectives
	Args:
		label: pd.DataFrame, label
		xrisk: pd.DataFrame, specific basis
		update_period: the period for updating a risk fitting
		lookbak: the risk fitting window, larger window uses more historical data
	Returns:
		pd.DataFrame, specific return
	"""
	crows = label.index.intersection(xrisk.index)
	x = xrisk.loc[crows].copy().fillna(0.0).sort_index()
	y = label.loc[crows].copy().fillna(0.0).sort_index()

	from tqdm.auto import tqdm
	from sklearn.linear_model import LinearRegression

	results = []
	for i in tqdm(range(lookback, len(x), update_period)):
		xpast, ypast = x.iloc[i - lookback : i], y.iloc[i - lookback : i]
		xnext, ynext = x.iloc[i : i + update_period], y.iloc[i : i + update_period]
		lr = LinearRegression()
		lr.fit(xpast, ypast)
		results.append(ynext.sub(lr.predict(xnext)))
	return pd.concat(results)


import os
import numpy as np
import pandas as pd
from tqdm.auto import tqdm

minbar_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/HFG/generic_futures/bitcoin/minbar/'
label_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/HFG/generic_futures/bitcoin/label/'

horizons = [1, 2, 5, 10, 20, 30, 60, 120]
dates = pd.date_range(start='2021-09-01', end='2025-05-10').astype(np.str_)


# raw_ret = calc_label(minbar, 'vwap', 0, horizons, False, False)
# log_ret = calc_label(minbar, 'vwap', 0, horizons, True, False)
# zs_ret = calc_label(minbar, 'vwap', 0, horizons, True, True, zswindow=1440)



def save_label(label, label_root):
    os.makedirs(label_root, exist_ok=True)
    dates = label.index.normalize().unique().astype(str).tolist()
    for date in tqdm(dates):
        lbl = label.loc[date]
        lbl.to_parquet(f'{label_root}{date}.parquet')
        print(f'saved {date}', end='\r')

# save_label(raw_ret, f'{label_root}raw_ret/')
# save_label(log_ret, f'{label_root}log_ret/')
# save_label(zs_ret, f'{label_root}zscore_1d_ret/')



# from yrqtlib.utils import data

# data.load_1sym_tick('gb_bina_future', 'quote', '2021-09-01', 'BTCUSDT.BNF')

# %%writefile -a './scripts/hfg_crypto/fit.gru.py'
import pandas as pd
import hfg.backtest as bt
from tqdm.auto import tqdm
from yrqtlib.utils import add_methods_to_pandas


def load_minbar(minbar_root: str, start_date: str, end_date: str) -> pd.DataFrame:
	"""Load generic contract data from parquet files for a specified product and date range.

	Args:
	    minbar_root: Root directory containing the data
	    start_date: Start date in 'YYYY-MM-DD' format
	    end_date: End date in 'YYYY-MM-DD' format

	Returns:
	    pd.DataFrame: Concatenated DataFrame with all contract data
	"""
	results = []
	dates = pd.date_range(start_date, end_date).astype(str)
	for date in tqdm(dates, desc='Loading minbar'):
		file_path = f'{minbar_root}/{date}.parquet'
		dt = pd.read_parquet(file_path)
		results.append(dt)

	return pd.concat(results) if results else pd.DataFrame()

def calc_label(
	minbar: pd.DataFrame,
	ref_price: str = 'vwap',
	buyLag: int = 0,
	horizons: list[int] = [1, 2, 5, 10, 20, 30],
	logRet: bool = True,
	zscore: bool = True,
	zswindow: int = 1440,
	fee: float = 5.0e-4,
):
	"""Calculate forward-looking price change labels for multiple time horizons.

	Args:
		minbar (pd.DataFrame): DataFrame of 1 minute bar containing price columns vwap or twap
		ref_price (str, optional): Reference price column to use for calculations. Defaults to 'vwap'.
		buyLag (int, optional): Number of periods to delay the label calculation. Defaults to 0.
		horizons (list[int], optional): List of forward-looking horizons (in minutes) to calculate returns.
							Defaults to [1, 2, 5, 10, 20, 30].
		logRet (bool, optional): Whether to calculate logarithmic returns. If False, uses arithmetic returns.
							Defaults to True.
		zscore (bool, optional): Whether to standardize returns using rolling z-score normalization.
							Defaults to True.
		zswindow (int, optional): Rolling window size (in minutes) for z-score normalization.
							Defaults to 1440 (24 hours).

	Returns:
		pd.DataFrame: DataFrame containing calculated labels for each horizon, with columns named
					 'label_{ref_price}_min_{horizon}'. Returns are optionally z-scored.
	"""
	ref_px = minbar[ref_price].copy().bfill()
	ref_px = ref_px.where(ref_px > 0, np.nan)
	labels = []
	for horizon in horizons:
		label = ref_px.div(ref_px.shift(horizon)).shift(-buyLag - horizon)
		if not logRet:
			label = label.sub(1.0)
		else:
			label = np.log(label)
		label = np.sign(label) * label.abs().sub(fee)
		label.name = f'label_{ref_price}_min_{horizon}'
		labels.append(label)
	labels = pd.concat(labels, axis=1)
	if zscore:
		miu_ = labels.rolling(zswindow, min_periods=1).mean()
		std_ = labels.rolling(zswindow, min_periods=1).std()
		labels = labels.sub(miu_).div(std_)
	return labels


minbar_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/HFG/generic_futures/bitcoin/minbar/'
minbar = load_minbar(minbar_root, '2021-09-01', '2025-05-10')
minbar = minbar.loc[~minbar.index.duplicated()].sort_index()

feature = pd.DataFrame()
feature['td_num'] = minbar['trade_num']
feature['qt_num'] = minbar['qt_num']

feature['td_px_std'] = minbar['trd_px_std']
feature['td_px_skew'] = minbar['trd_px_skew']
feature['td_vol_std'] = minbar['trd_vol_std']
feature['td_vol_skew'] = minbar['trd_vol_skew']
feature['td_tvr_std'] = minbar['trd_tvr_std']
feature['td_tvr_skew'] = minbar['trd_tvr_skew']

feature['td_vol_sum_1min'] = minbar['trd_vol_sum']
feature['td_vol_sum_5min'] = minbar['trd_vol_sum'].rolling(5,min_periods=1).sum()
feature['td_vol_sum_20min'] = minbar['trd_vol_sum'].rolling(20,min_periods=1).sum()

feature['td_tvr_sum_1min'] = minbar['trd_tvr_sum']
feature['td_tvr_sum_5min'] = minbar['trd_tvr_sum'].rolling(5,min_periods=1).sum()
feature['td_tvr_sum_20min'] = minbar['trd_tvr_sum'].rolling(20,min_periods=1).sum()
feature = feature.DIY.asinh()

feature['vwap_mom_1min']  = minbar['vwap'].pct_change(1, fill_method=None)
feature['vwap_mom_5min']  = minbar['vwap'].pct_change(5, fill_method=None)
feature['vwap_mom_20min']  = minbar['vwap'].pct_change(20, fill_method=None)
feature['vwap_mom_60min']  = minbar['vwap'].pct_change(60, fill_method=None)

feature['qt_px_depth'] = minbar['qt_apx_max'] - minbar['qt_bpx_min']
feature['qt_aqty'] = minbar['qt_aqty_last'].DIY.asinh()
feature['qt_bqty'] = minbar['qt_bqty_last'].DIY.asinh()

feature['oiv'] = ((minbar['qt_bqty_sum'] - minbar['qt_aqty_sum'])).shift(1)
feature['rev_5'] = minbar['vwap'].sub(minbar['vwap'].rolling(5, min_periods=1).mean()).shift(1)
feature['rev_20'] = minbar['vwap'].sub(minbar['vwap'].rolling(20, min_periods=1).mean()).shift(1)
feature['rev_60'] = minbar['vwap'].sub(minbar['vwap'].rolling(60, min_periods=1).mean()).shift(1)
feature['rev_120'] = minbar['vwap'].sub(minbar['vwap'].rolling(120, min_periods=1).mean()).shift(1)
feature_mad = functions.winsorize_mad(feature, 60 * 24 * 180, 4)

horizons = [1, 5, 10, 20, 30, 60, 120]
raw_label = calc_label(minbar, 'vwap', logRet=False, zscore=False, horizons=horizons, fee=0.0e-4)
log_label = calc_label(minbar, 'vwap', logRet=True, zscore=False, horizons=horizons, fee=0.0e-4)
zs_label1 = calc_label(minbar, 'vwap', logRet=True, zscore=True, zswindow=1440*1, horizons=horizons, fee=0.0e-4)
zs_label5 = calc_label(minbar, 'vwap', logRet=True, zscore=True, zswindow=1440*5, horizons=horizons, fee=0.0e-4)
zs_label10 = calc_label(minbar, 'vwap', logRet=True, zscore=True, zswindow=1440*10, horizons=horizons, fee=0.0e-4)
zs_label20 = calc_label(minbar, 'vwap', logRet=True, zscore=True, zswindow=1440*20, horizons=horizons, fee=0.0e-4)




from IPython import display
# test = functions.winsorize_mad(feature['vwap_mom_1min'], 60 * 24 *10, 4)
rets, summary = bt.run_backtest(
	{
		'feature': -1.0 * functions.zscore(feature_mad['vwap_mom_1min'], 60 * 24 *10),
		'label': raw_label['label_vwap_min_1'],
		'long_threshold': 2.0,
		'short_threshold': -2.0,
		'fees': [0.0e-4],  ## typical fees for futures
		'period': '1min'
	}
)
display.clear_output()
bt.plot_returns(rets)
summary

summary.sort_values(['Sharpe','PPT(bp)'])[::-1].head(10)\
    .style.format(('{:.4f}'))\
    .highlight_between(subset=['PPT(bp)','Sharpe'], left=0.0)

def highlight_outliers(s:pd.Series, low: float=-0.03, high: float=0.03):
    styles = []
    for v in s:
        if v <= low:
            style = 'background-color: lightgreen; font-weight: bold'
        elif v >= high:
            style = 'background-color: lightcoral; font-weight: bold'
        else:
            style = ''
        styles.append(style)
    return styles

ic = pd.concat([feature_mad, raw_label], axis=1).corr()\
    .iloc[:-len(horizons),-len(horizons):]\
        .sort_values(by='label_vwap_min_1', key=abs, ascending=False)\

ic.style.format('{:.4f}').apply(highlight_outliers, axis=1)

# feature_mad = functions.winsorize_mad(feature, 60 * 24 * 180, 4)
# feature_mad.DIY.hist(bins=100)

# x = feature_mad
x = functions.zscore(feature_mad, 60 * 24 * 180)

y = zs_label10[['label_vwap_min_1']].copy()
# y = np.sign(y) * np.sqrt(np.abs(y))
# y = np.tanh(y / 3.0)
# y = np.arcsinh(y)

y  = functions.winsorize_mad(y, 60 * 24 * 10, 6)
xy = pd.concat([x, y], axis=1).clip(-6.0, 6.0)
xy = xy.dropna(subset=y.columns, how='all').fillna(0.0)

update_period = 10 * 1440
window = 180 * 1440
num_label = y.shape[1]

y.hist(bins=100); 

# xy.DIY.hist(bins=100);

xy.corr().iloc[:-num_label,-num_label:]#.sort_values(ascending=False,key=abs).head(10)

# dates = xy.index.normalize().unique().astype(str).tolist()
# ics = [xy.loc[date].fillna(0.0).corr().iloc[:-1,-1] for date in tqdm(dates)]
# ics = pd.concat(ics, axis=1, keys=dates).T
# ics.mean().sort_values(ascending=False,key=abs).head(10)

def plot_stats(pred: pd.Series, figsize=(12, 5)):
    import matplotlib.pyplot as plt
    fig  = plt.figure(figsize=figsize)
    ax1 = fig.add_subplot(1, 2, 1)
    pred.hist(bins=100, ax=ax1)
    ax1.set_title('Histogram')
    
    ax2 = fig.add_subplot(1, 2, 2)
    pred.plot(ax=ax2, title='value vs time', rot=20)
    plt.show()
    



def winsorize_pct(df: pd.DataFrame, window:int = 1440*1, pct: float=0.05):
    arr = df.copy().values
    for i in tqdm(range(len(arr))):
        if i < window: continue
        max_ = np.nanmax(arr[i-window:i])
        min_ = np.nanmin(arr[i-window:i])
        arr[i] = np.where(arr[i]<=max_*(1+pct), arr[i], np.sign(arr[i])*max_*(1+pct))
        arr[i] = np.where(arr[i]>=min_*(1+pct), arr[i], np.sign(arr[i])*min_*(1-pct))
    return pd.DataFrame(arr, index=df.index, columns=df.columns)




import numpy as np
from sklearn.linear_model import LinearRegression, Ridge


def lr_fit(xy: pd.DataFrame, update_period: int, window: int, num_label: int = 1, fit_method: str = 'lr', **kwargs):
	betas, xpreds = [], []
	for i in tqdm(range(window, len(xy), update_period)):
		x_ = xy.iloc[i - window : i, :-num_label]
		if i + update_period >= len(xy):
			continue
		y_ = xy.iloc[i - window : i, -num_label:]

		if fit_method == 'lr':
			mdl = LinearRegression()
		elif fit_method == 'ridge':
			mdl = Ridge(alpha=kwargs.get('alpha', 0.03))

		mdl.fit(x_, y_)
		coef = mdl.coef_.reshape(1,-1)
		intercept = mdl.intercept_.reshape(-1,1)
		beta = np.hstack([coef, intercept])
		beta = pd.DataFrame(beta, index=[x_.index[-1].strftime('%Y-%m-%d')], columns=xy.columns[:-num_label].tolist() + ['intercept'])
		betas.append(beta)

		xvalid = xy.iloc[i : i + update_period, :-num_label]
		xpred = pd.DataFrame(mdl.predict(xvalid), index=xvalid.index, columns=['pred'])
		xpreds.append(xpred)

	return (pd.concat(xpreds, axis=0), pd.concat(betas, axis=0))

pred_lr, beta_lr = lr_fit(xy, update_period, window, num_label, 'lr', alpha=0.05)

pred_lr = functions.winsorize_mad(pred_lr, 60*24*10, 4)
pd.concat([pred_lr, raw_label['label_vwap_min_1']], axis=1).corr()

pred_ = functions.winsorize_mad(pred_lr, 60*24*10, 4)
pred_ = functions.zscore(pred_, 60 * 24 * 10)
rets, summary = bt.run_backtest(
	{
		'feature': pred_,
		'label': raw_label['label_vwap_min_1'],
		'long_threshold': 2.0,
		'short_threshold': -2.0,
		'fees': [0.0e-4],  ## typical fees for futures，
		'period': '1min'
	}
)
bt.plot_returns(rets)
summary.style.format('{:.4f}')



import torch
import matplotlib.pyplot as plt

from torch import nn
from hfg import functions


class MixedLoss(nn.Module):
    def __init__(self, alpha=0.5, epsilon=1e-8):
        super(MixedLoss, self).__init__()
        self.alpha = alpha
        self.epsilon = epsilon
        
    def mean_squared_error(self, y_true, y_pred):
        """
        Compute the mean squared error (MSE) between y_true and y_pred.

        Args:
            y_true (Tensor): Ground truth values.
            y_pred (Tensor): Predicted values.

        Returns:
            Tensor: The mean squared error.
        """
        return torch.mean(torch.pow(y_true - y_pred, 2))

    def sharpe_ratio_loss(self, y_true, y_pred, epsilon=1e-8):
        """
        Compute the negative Sharpe Ratio as a loss.

        Args:
            y_true (Tensor): Ground truth values.
            y_pred (Tensor): Predicted values.
            epsilon (float): Small value to avoid division by zero.

        Returns:
            Tensor: The negative Sharpe Ratio loss.
        """
        y_pred = torch.sign(y_pred) * torch.abs(y_pred).sub(1.0e-4)
        R_t = y_true * torch.tanh(y_pred / epsilon)
        mean_return = torch.mean(R_t)
        std_return = torch.std(R_t) + epsilon
        return - mean_return / std_return

    def forward(self, y_true, y_pred):
        """
        Compute the combined loss.

        Args:
            y_true (Tensor): Ground truth values.
            y_pred (Tensor): Predicted values.

        Returns:
            Tensor: The combined loss (MSE + alpha * Sharpe Ratio loss).
        """
        mse_loss = self.mean_squared_error(y_true, y_pred)
        sr_loss = self.sharpe_ratio_loss(y_true, y_pred, self.epsilon)
        # print(f'mse_loss: {mse_loss.cpu().item():.4f}, sr_loss: {sr_loss.cpu().item():.4f}')
        mixLoss = mse_loss + self.alpha * sr_loss
        return mixLoss




device = torch.device('cuda:0')


class MlpNet(nn.Module):
	def __init__(
		self,
		input_size: int,
		output_size: int,
		hidden_size: int,
		hidden_layers: int,
		dropout: float,
		activation: str = 'relu',
	):
		super().__init__()
		activatio_map = {
			'relu': nn.ReLU,
			'gelu': nn.GELU,
			'selu': nn.SELU,
			'tanh': nn.Tanh,
			'sigmoid': nn.Sigmoid,
			'leaky_relu': nn.LeakyReLU,
			'elu': nn.ELU,
		}
		self.inlayer = nn.Sequential(
			nn.Linear(input_size, hidden_size),
			nn.BatchNorm1d(hidden_size),
			activatio_map[activation](),
			nn.Dropout(p=dropout),
		)  # linear、batchnorm、relu

		self.layers = nn.ModuleList()
		if hidden_layers >= 1:
			for _ in range(hidden_layers):
				layer = nn.Sequential(
					nn.Linear(hidden_size, hidden_size),
					nn.BatchNorm1d(hidden_size),
					activatio_map[activation](),
					nn.Dropout(p=dropout),
				)
				self.layers.append(layer)

		self.outlayer = nn.Linear(hidden_size, output_size)

	def forward(self, x):
		x = self.inlayer(x)
		for layer in self.layers:
			x = layer(x)
		return self.outlayer(x)


def mlp_fit(xy: pd.DataFrame, update_period: int, window: int, num_label: int = 1, **kwargs):
	xpreds = []
	mdls = []

	losses_all = []

	for i in tqdm(range(window, len(xy), update_period), desc=f'epochs={kwargs["epochs"]}'):
		xtrain = xy.iloc[i - window : i, :-num_label]
		ytrain = xy.iloc[i - window : i, -num_label:]
		xvalid = xy.iloc[i : i + update_period, :-num_label]
		yvalid = xy.iloc[i : i + update_period, -num_label:]

		fit_cfg_ = fit_cfg.copy()
		fit_cfg_['input_size'] = xtrain.shape[1]
		fit_cfg_['output_size'] = ytrain.shape[1]

		mdl = MlpNet(**fit_cfg_).to(device)
		optimizer = torch.optim.Adam(mdl.parameters(), lr=kwargs.get('lr', 0.001))
		loss_fn = nn.MSELoss().to(device)
		# loss_fn = MixedLoss(alpha=1.0).to(device)

		train_losses = []
		xtrain_cuda = torch.from_numpy(xtrain.values).float().to(device)
		ytrain_cuda = torch.from_numpy(ytrain.values).float().to(device)
		for epoch in range(kwargs.get('epochs', 20)):
			pred = mdl(xtrain_cuda)
			loss = loss_fn(pred, ytrain_cuda)
			loss.backward()
			optimizer.step()
			optimizer.zero_grad()
			train_losses.append(loss.cpu().item())
			# print(f'loss [{epoch}]: {loss.cpu().item():.4f}, {xtrain_cuda.shape}', end='\r')

		with torch.inference_mode():
			mdl.eval()
			train_loss = loss_fn(mdl(xtrain_cuda), ytrain_cuda).cpu().item()

			xvalid_cuda = torch.from_numpy(xvalid.values).float().to(device)
			yvalid_cuda = torch.from_numpy(yvalid.values).float().to(device)
			xpred = mdl(xvalid_cuda)
			xpreds.append(pd.DataFrame(xpred.cpu().numpy()[:,0], index=xvalid.index, columns=['pred']))
			valid_loss = loss_fn(xpred, yvalid_cuda).cpu().item()
		losses_all.append([train_loss, valid_loss])
		print(f'loss [{i}]: {train_loss:.4f}, {valid_loss:.4f}', end='\r')

	return pd.concat(xpreds, axis=0), mdls, losses_all


fit_cfg = {
	'hidden_size': 64,
	'hidden_layers': 1,
	'dropout': 0.1,
	'activation': 'relu',
}

losses_all = []
epochs_list = [10, 50, 100, 200]
for epochs in epochs_list:
	pred, mdls, loss_epochs = mlp_fit(
		xy, update_period, window, num_label, **{'lr': 1.0e-2, 'epochs': epochs}
	)
	pred_ = functions.winsorize_mad(pred, 60 * 24 * 10, 4)
	ic = pd.concat([pred_, raw_label['label_vwap_min_1']], axis=1).corr().iloc[0, 1]
	print(f'\nepochs={epochs}, ic={ic:.4f}')
	losses_all.append(pd.DataFrame(loss_epochs, columns=['train', 'valid']).mean())

pd.DataFrame(losses_all, index=epochs_list).plot(marker='o', logy=True);


# pred.hist(bins=100);

# pd.DataFrame(losses_all, index=epochs_list).plot(marker='o', grid=True)

pred_ = functions.winsorize_mad(pred, 60*24*10, 4)
pd.concat([pred_, raw_label['label_vwap_min_1']], axis=1).corr()

pred_ = functions.winsorize_mad(pred_lr, 60*24*10, 4)
pred_ = functions.zscore(pred_, 60 * 24 * 10)
rets, summary = bt.run_backtest(
	{
		'feature': pred_,
		'label': raw_label['label_vwap_min_1'],
		'long_threshold': 2.0,
		'short_threshold': -2.0,
		'fees': [0.0e-4],  ## typical fees for futures，
		'period': '1min'
	}
)
bt.plot_returns(rets)
summary.style.format('{:.4f}')

import copy
import torch
from hfg import functions
from torch import nn

torch.set_num_threads(60)
device = torch.device('cuda:0')

class MlpNet(nn.Module):
	def __init__(
		self,
		input_size: int,
		output_size: int,
		hidden_size: int,
		hidden_layers: int,
		dropout: float,
		activation: str = 'relu',
	):
		super().__init__()
		activatio_map = {
			'relu': nn.ReLU,
			'gelu': nn.GELU,
			'selu': nn.SELU,
			'tanh': nn.Tanh,
			'sigmoid': nn.Sigmoid,
			'leaky_relu': nn.LeakyReLU,
			'elu': nn.ELU,
		}
		self.inlayer = nn.Sequential(
			nn.Linear(input_size, hidden_size),
			nn.BatchNorm1d(hidden_size),
			activatio_map[activation](),
			nn.Dropout(p=dropout),
		)  # linear、batchnorm、relu

		self.layers = nn.ModuleList()
		if hidden_layers >= 1:
			for _ in range(hidden_layers):
				layer = nn.Sequential(
					nn.Linear(hidden_size, hidden_size),
					nn.BatchNorm1d(hidden_size),
					activatio_map[activation](),
					nn.Dropout(p=dropout),
				)
				self.layers.append(layer)

		self.outlayer = nn.Linear(hidden_size, output_size)

	def forward(self, x):
		x = self.inlayer(x)
		for layer in self.layers:
			x = layer(x)
		return self.outlayer(x)


def mlp_fit(xy: pd.DataFrame, update_period: int, window: int, num_label: int = 1, **kwargs):
	xpreds = []
	mdls = []
	losses_all = []
 
	# for i in tqdm(range(window, len(xy), update_period), desc=f'epochs={kwargs.get("epochs", 20)}', leave=False):
	for i in range(window, len(xy), update_period):
		progress = (i - window + 1) // update_period + 1
		total = (len(xy) - window) // update_period + 1
		idx = f'{progress}/{total}'
  
		fit_cfg_ = fit_cfg
		xy_train = xy.iloc[i - window : i,:]
		fit_cfg_['input_size'] = xy_train.shape[1] - num_label
		fit_cfg_['output_size'] = num_label

		mdl = MlpNet(**fit_cfg_).to(device)
		optimizer = torch.optim.Adam(mdl.parameters(), lr=kwargs.get('lr', 0.001))
		loss_fn = nn.MSELoss().to(device)

		losses = []
		dataset_batches = np.array_split(xy_train.values, kwargs.get('num_batches', 10))
		for epoch in range(kwargs.get('epochs', 1)):
			for j, value in enumerate(dataset_batches):
				x_ = torch.from_numpy(value[:,:-num_label]).float().to(device)
				y_ = torch.from_numpy(value[:,-num_label:]).float().to(device)
				pred = mdl(x_)
				loss = loss_fn(pred, y_)
				loss.backward()
				optimizer.step()
				optimizer.zero_grad()
				losses.append(loss.cpu().item())
				print(f'[{idx}] epoch: {epoch+1}, batch: {j}, loss: {loss.cpu().item():.4f}', end='\r')

		with torch.inference_mode():
			mdl.eval()
			xy_train = xy.iloc[i - window : i,:].values
			xtrain_cuda = torch.from_numpy(xy_train[:,:-num_label]).float().to(device)
			ytrain_cuda = torch.from_numpy(xy_train[:,-num_label:]).float().to(device)
			train_loss = loss_fn(mdl(xtrain_cuda), ytrain_cuda).cpu().item()

			xy_valid = xy.iloc[i : i + update_period].values
			xvalid_cuda = torch.from_numpy(xy_valid[:,:-num_label]).float().to(device)
			yvalid_cuda = torch.from_numpy(xy_valid[:,-num_label:]).float().to(device)
			xvalid_pred = mdl(xvalid_cuda)
			xindex = xy.index[i:i+update_period]
			xpreds.append(pd.DataFrame(xvalid_pred.cpu().numpy()[:,0], index=xindex, columns=['pred']))
			valid_loss = loss_fn(xvalid_pred, yvalid_cuda).cpu().item()

		losses_all.append([train_loss, valid_loss])
		# print(f'loss [{i}]: {train_loss:.4f}, {valid_loss:.4f}', end='\r')
		mdls.append(mdl)
	return pd.concat(xpreds, axis=0), mdls, losses_all




fit_cfg = {
	'hidden_size': 64,
	'hidden_layers': 1,
	'dropout': 0.1,
	'activation': 'relu',
}

losses_all = []
epochs_list = [10, 20, 30, 40, 50]
for epochs in tqdm(epochs_list, desc='epochs'):
	pred, mdls, loss_epochs = mlp_fit(
		xy, update_period, window, num_label, **{'lr': 1.0e-2, 'epochs': epochs, 'num_batches': 2}
	)
	pred_ = functions.winsorize_mad(pred, 60 * 24 * 10, 4)
	ic = pd.concat([pred_, raw_label['label_vwap_min_1']], axis=1).corr().iloc[0, 1]
	print(f'epochs: {epochs}, ic: {ic:.4f}, losses: ({loss_epochs[-1][0]:.4f}, {loss_epochs[-1][1]:.4f})')
	losses_all.append(pd.DataFrame(loss_epochs, columns=['train', 'valid']).mean())

pd.DataFrame(losses_all, index=epochs_list).plot(marker='o', logy=True);


pred_ = functions.winsorize_mad(pred, 60*24*10, 4)
pd.concat([pred_, raw_label['label_vwap_min_1']], axis=1).corr()

pred_ = functions.winsorize_mad(pred, 60*24*10, 4)
pred_ = functions.zscore(pred_, 60 * 24 * 10)
rets, summary = bt.run_backtest(
	{
		'feature': pred_,
		'label': raw_label['label_vwap_min_1'],
		'long_threshold': 2.0,
		'short_threshold': -2.0,
		'fees': [0.0e-4],  ## typical fees for futures，
		'period': '1min'
	}
)
bt.plot_returns(rets)
summary.style.format('{:.4f}')



import numpy as np
from numpy.lib.stride_tricks import sliding_window_view
import torch
import torch.nn as nn
import pandas as pd
from tqdm.auto import tqdm

# Define device
device = torch.device("cuda:0")

def stack_time_step(time_step: int, dataset: np.ndarray):
    wdsp = (time_step, dataset.shape[-1])
    return sliding_window_view(dataset, wdsp).squeeze(axis=1)

class GRUNet(nn.Module):
    def __init__(
        self,
        input_size: int,
        output_size: int,
        hidden_size: int,
        hidden_layers: int,
        dropout: float,
        activation: str = 'relu',  # Maintained for interface compatibility
    ):
        super().__init__()
        self.hidden_size = hidden_size
        self.num_layers = hidden_layers
        
        # GRU layer (batch_first=True for [batch, seq, feature] format)
        self.gru = nn.GRU(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=hidden_layers,
            batch_first=True,
            dropout=dropout if hidden_layers > 1 else 0,
        )
        
        # Output layer
        self.outlayer = nn.Linear(hidden_size, output_size)

    def forward(self, x):
        # Initialize hidden state
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
        
        # GRU forward pass
        out, _ = self.gru(x, h0)
        
        # Output predictions for each time step
        return self.outlayer(out)


def gru_fit(xy: pd.DataFrame, update_period: int, window: int, seq_len: int = 10, num_label: int = 1, **kwargs):
    xpreds = []
    mdls = []
    losses_all = []
    
    for i in tqdm(range(window, len(xy), update_period), desc=f'epochs={kwargs.get("epochs", 20)}'):
        trainset = xy.iloc[i-window:i]
        trainset = stack_time_step(seq_len, trainset.values)
        xtrain = trainset[..., :-num_label]   # Features
        ytrain = trainset[..., -num_label:]   # Labels

        validset = xy.iloc[i:i+update_period]
        validset = stack_time_step(seq_len, validset.values)
        xvalid = validset[..., :-num_label]   # Features
        yvalid = validset[..., -num_label:]   # Labels
        
        
        # Initialize model
        fit_cfg_ = fit_cfg.copy()
        fit_cfg_['input_size'] = xtrain.shape[-1]
        fit_cfg_['output_size'] = ytrain.shape[-1]
        
        mdl = GRUNet(**fit_cfg_).to(device)
        optimizer = torch.optim.Adam(mdl.parameters(), lr=kwargs.get('lr', 0.001))
        loss_fn = nn.MSELoss().to(device)

        # Convert to tensors
        xtrain_tensor = torch.tensor(xtrain, dtype=torch.float32).to(device)
        ytrain_tensor = torch.tensor(ytrain, dtype=torch.float32).to(device)
        xvalid_tensor = torch.tensor(xvalid, dtype=torch.float32).to(device)
        yvalid_tensor = torch.tensor(yvalid, dtype=torch.float32).to(device)
        
        # Training loop
        for epoch in range(kwargs.get('epochs', 20)):
            pred = mdl(xtrain_tensor)
            loss = loss_fn(pred, ytrain_tensor)
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

        # Validation
        with torch.no_grad():
            mdl.eval()
            train_pred = mdl(xtrain_tensor)
            train_loss = loss_fn(train_pred, ytrain_tensor).item()

            valid_pred = mdl(xvalid_tensor)
            valid_loss = loss_fn(valid_pred, yvalid_tensor).item()
            
            # Extract last prediction from each sequence
            last_preds = valid_pred[:, -1, :].cpu().numpy()
            
            # Create prediction dataframe
            pred_index = xy.index[i:i+update_period][-len(last_preds):]
            pred_df = pd.DataFrame(last_preds, index=pred_index, 
                                  columns=[f'pred_{i}' for i in range(last_preds.shape[1])])
            xpreds.append(pred_df)
            
        losses_all.append([train_loss, valid_loss])
        print(f'loss [{i}]: {train_loss:.4f}, {valid_loss:.4f}', end='\r')

    return pd.concat(xpreds, axis=0), mdls, losses_all


fit_cfg = {
	'hidden_size': 64,
	'hidden_layers': 1,
	'dropout': 0.1,
	'activation': 'relu',
}

losses_all = []
epochs_list = [10, 50, 100,200,300,400][:]
for epochs in epochs_list:
	pred, mdls, loss_epochs = gru_fit(
		xy, update_period, window, 1, num_label, **{'lr': 1.0e-3, 'epochs': epochs}
	)
	pred_ = functions.winsorize_mad(pred, 60 * 24 * 10, 4)
	ic = pd.concat([pred_, raw_label['label_vwap_min_1']], axis=1).corr().iloc[0, 1]
	print(f'\nepochs={epochs}, ic={ic:.4f}')
	losses_all.append(pd.DataFrame(loss_epochs, columns=['train', 'valid']).mean())

torch.cuda.empty_cache()
pd.DataFrame(losses_all, index=epochs_list).plot(marker='o', logy=True);


pred_ = functions.winsorize_mad(pred, 60*24*10, 4)
pd.concat([pred_, raw_label['label_vwap_min_1']], axis=1).corr()

pred_ = functions.winsorize_mad(pred, 60*24*10, 4)
pred_ = functions.zscore(pred_, 60 * 24 * 10)
rets, summary = bt.run_backtest(
	{
		'feature': pred_,
		'label': raw_label['label_vwap_min_1'],
		'long_threshold': 2.0,
		'short_threshold': -2.0,
		'fees': [0.0e-4],  ## typical fees for futures，
		'period': '1min'
	}
)
bt.plot_returns(rets)
summary.style.format('{:.4f}')



import numpy as np
from numpy.lib.stride_tricks import sliding_window_view
import torch
import torch.nn as nn
import pandas as pd
from tqdm.auto import tqdm

# Define device
device = torch.device('cuda:0')
torch.set_num_threads(120)


def stack_time_step(time_step: int, dataset: np.ndarray):
	wdsp = (time_step, dataset.shape[-1])
	return sliding_window_view(dataset, wdsp).squeeze(axis=1)


class GRUNet(nn.Module):
	def __init__(
		self,
		input_size: int,
		output_size: int,
		hidden_size: int,
		hidden_layers: int,
		dropout: float,
		activation: str = 'relu',  # Maintained for interface compatibility
	):
		super().__init__()
		self.hidden_size = hidden_size
		self.num_layers = hidden_layers

		# GRU layer (batch_first=True for [batch, seq, feature] format)
		self.gru = nn.GRU(
			input_size=input_size,
			hidden_size=hidden_size,
			num_layers=hidden_layers,
			batch_first=True,
			dropout=dropout if hidden_layers > 1 else 0,
		)

		# Output layer: input dim is hidden_size * num_directions if bidirectional
		self.outlayer = nn.Linear(hidden_size, output_size)

	def forward(self, x):
		# Initialize hidden state
		h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)

		# GRU forward pass
		out, _ = self.gru(x, h0)

		# Output predictions for each time step
		return self.outlayer(out)


def gru_fit(
	xy: pd.DataFrame,
	update_period: int,
	window: int,
	seq_len: int = 10,
	num_label: int = 1,
	**kwargs,
):
	xpreds, mdls, losses_all = [], [], []
	datasets = stack_time_step(seq_len, xy.values)
	# print(datasets.shape)

	num_batches = kwargs.get('num_batches', 10)
	tqbar = range(window, len(xy), update_period)
	tqbar = tqdm(tqbar, desc=f'epochs={kwargs.get("epochs", 20)}')
	for i in tqbar:
		progress = (i - window + 1) // update_period + 1
		total = (len(xy) - window) // update_period + 1
		idx = f'{progress}/{total}'

		xytrain = datasets[i - window : i, :, :]
		xyvalid = datasets[i : i + update_period, :, :]

		# Initialize model
		fit_cfg_ = fit_cfg.copy()
		fit_cfg_['input_size'] = xytrain.shape[-1] - num_label
		fit_cfg_['output_size'] = num_label

		mdl = GRUNet(**fit_cfg_).to(device)
		optimizer = torch.optim.Adam(mdl.parameters(), lr=kwargs.get('lr', 0.001))
		loss_fn = nn.MSELoss().to(device)

		# Training loop
		dataset_batches = np.array_split(xytrain, num_batches)
		for epoch in range(kwargs.get('epochs', 20)):
			for j, value in enumerate(dataset_batches):
				x_ = torch.from_numpy(value[:, :, :-num_label]).float().to(device)
				y_ = torch.from_numpy(value[:, :, -num_label:]).float().to(device)
				out = mdl(x_)
				loss = loss_fn(out, y_)
				loss.backward()
				optimizer.step()
				optimizer.zero_grad()
				print(
					f'[{idx}] epoch: {epoch + 1}, batch: {j}, loss: {loss.cpu().item():.4f}',
					end='\r',
				)

		# Validation
		with torch.no_grad():
			mdl.eval()
			xtrain_pred = mdl(
				torch.from_numpy(xytrain[-xyvalid.shape[0] :, :, :-num_label]).float().to(device)
			)
			ytrain = torch.from_numpy(xytrain[-xyvalid.shape[0] :, :, -num_label:]).float().to(device)
			train_loss = loss_fn(xtrain_pred, ytrain).item()

			xvalid = torch.from_numpy(xyvalid[:, :, :-num_label]).float().to(device)
			yvalid = torch.from_numpy(xyvalid[:, :, -num_label:]).float().to(device)
			xvalid_pred = mdl(xvalid)
			valid_loss = loss_fn(xvalid_pred, yvalid).item()

			# Extract last prediction from each sequence
			xvalid_pred_last = xvalid_pred[:, -1, :].cpu().numpy()
			pred_index = xy.index[i + seq_len - 1 : i + seq_len + update_period - 1]
			pred_df = pd.DataFrame(
				xvalid_pred_last,
				index=pred_index,
				columns=[f'pred_{i}' for i in range(num_label)],
			)
			xpreds.append(pred_df)

		losses_all.append([train_loss, valid_loss])
		# print(f'loss [{i}]: {train_loss:.4f}, {valid_loss:.4f}', end='\r')
		mdls.append(mdl)

	return pd.concat(xpreds, axis=0), mdls, losses_all


fit_cfg = {
	'hidden_size': 64,
	'hidden_layers': 2,
	'dropout': 0.2,
	'activation': 'relu',
}

best_ic = -np.inf
best_loss = np.inf
patience = 2
seq_len = 60
losses_all = []
epoch_step = 1
epochs_list = range(epoch_step, 200, epoch_step)
for epochs in epochs_list:
	pred, mdls, loss_epochs = gru_fit(
		xy,
		update_period,
		window,
		seq_len,
		num_label,
		**{'lr': 1.0e-2, 'epochs': epochs, 'num_batches': 20},
	)
	pred_ = functions.winsorize_mad(pred, 60 * 24 * 10, 4)
	ic = pd.concat([pred_, raw_label['label_vwap_min_1']], axis=1).corr().iloc[0, 1]
	print(f'epochs: {epochs}, ic: {ic:.4f}, losses: ({loss_epochs[-1][0]:.4f}, {loss_epochs[-1][1]:.4f})')
	losses_all.append(loss_epochs[-1])
	latest_loss = pd.DataFrame(loss_epochs).iloc[:,-1].mean()
	if latest_loss <= best_loss:
		best_loss = latest_loss
		best_pred = pred
	if latest_loss > best_loss:
		patience -= 1
		if patience == 0:
			break

torch.cuda.empty_cache()
pd.DataFrame(losses_all, index=epochs_list[: len(losses_all)]).plot(marker='o', logy=True);


torch.cuda.empty_cache()

pred_ = functions.winsorize_mad(best_pred, 60*24*10, 4)
# pred_ = functions.winsorize_mad(pred, 60*24*10, 4)
pd.concat([pred_, raw_label['label_vwap_min_1']], axis=1).corr()

# pred_ = functions.winsorize_mad(best_pred, 60*24*10, 4)
pred_ = functions.zscore(pred_, 60 * 24 * 10)
rets, summary = bt.run_backtest(
	{
		'feature': pred_,
		'label': raw_label['label_vwap_min_1'],
		'long_threshold': 2.0,
		'short_threshold': -2.0,
		'fees': [0.0e-4],  ## typical fees for futures，
		'period': '1min'
	}
)
bt.plot_returns(rets)
summary.style.format('{:.4f}')

import numpy as np
from numpy.lib.stride_tricks import sliding_window_view
import torch
import torch.nn as nn
import pandas as pd
from tqdm.auto import tqdm

# Define device
device = torch.device('cuda:0')
torch.set_num_threads(120)


def stack_time_step(time_step: int, dataset: np.ndarray):
	wdsp = (time_step, dataset.shape[-1])
	return sliding_window_view(dataset, wdsp).squeeze(axis=1)


def generate_causal_mask(seq_len, device=None):
	# mask shape: (seq_len, seq_len)
	# True means masked (prevent attention), False means allowed
	# For PyTorch MultiheadAttention, mask is float with -inf for masked, 0 for allowed
	mask = torch.triu(torch.ones(seq_len, seq_len), diagonal=1)
	mask = mask.masked_fill(mask == 1, float('-inf')).masked_fill(mask == 0, float(0.0))
	if device is not None:
		mask = mask.to(device)
	return mask


class SelfAttention(nn.Module):
	def __init__(self, embed_dim, num_heads=1, dropout=0.0):
		super().__init__()
		self.attn = nn.MultiheadAttention(embed_dim, num_heads, dropout=dropout, batch_first=True)

	def forward(self, x):
		# x: [batch, seq, embed_dim]
		seq_len = x.size(1)
		# Causal mask: prevent attention to future tokens
		attn_mask = generate_causal_mask(seq_len, device=x.device)
		attn_output, _ = self.attn(x, x, x, attn_mask=attn_mask)
		return attn_output


class GRUWithAttentionNet(nn.Module):
	def __init__(
		self,
		input_size: int,
		output_size: int,
		hidden_size: int,
		hidden_layers: int,
		dropout: float,
		activation: str = 'relu',  # Maintained for interface compatibility
		num_attention_heads: int = 1,
		attn_dropout: float = 0.0,
	):
		super().__init__()
		self.hidden_size = hidden_size
		self.num_layers = hidden_layers

		# GRU layer (batch_first=True for [batch, seq, feature] format)
		self.gru = nn.GRU(
			input_size=input_size,
			hidden_size=hidden_size,
			num_layers=hidden_layers,
			batch_first=True,
			dropout=dropout if hidden_layers > 1 else 0,
		)

		# Self-attention layer
		self.attn = SelfAttention(hidden_size, num_heads=num_attention_heads, dropout=attn_dropout)

		# Output layer: input dim is hidden_size * num_directions if bidirectional
		self.outlayer = nn.Linear(hidden_size, output_size)

	def forward(self, x):
		# Initialize hidden state
		h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)

		# GRU forward pass
		out, _ = self.gru(x, h0)  # out: [batch, seq, hidden_size]

		# Self-attention with causal mask
		attn_out = self.attn(out)  # [batch, seq, hidden_size]

		# Output predictions for each time step
		return self.outlayer(attn_out)


def gru_fit(
	xy: pd.DataFrame,
	update_period: int,
	window: int,
	seq_len: int = 10,
	num_label: int = 1,
	**kwargs,
):
	xpreds, mdls, losses_all = [], [], []
	datasets = stack_time_step(seq_len, xy.values)
	# print(datasets.shape)

	num_batches = kwargs.get('num_batches', 10)
	tqbar = range(window, len(xy), update_period)
	tqbar = tqdm(tqbar, desc=f'epochs={kwargs.get("epochs", 20)}')
	for i in tqbar:
		progress = (i - window + 1) // update_period + 1
		total = (len(xy) - window) // update_period + 1
		idx = f'{progress}/{total}'

		xytrain = datasets[i - window : i, :, :]
		xyvalid = datasets[i : i + update_period, :, :]

		# Initialize model
		fit_cfg_ = fit_cfg.copy()
		fit_cfg_['input_size'] = xytrain.shape[-1] - num_label
		fit_cfg_['output_size'] = num_label

		# Add attention config if present
		attn_cfg = {}
		if 'num_attention_heads' in kwargs:
			attn_cfg['num_attention_heads'] = kwargs['num_attention_heads']
		if 'attn_dropout' in kwargs:
			attn_cfg['attn_dropout'] = kwargs['attn_dropout']

		mdl = GRUWithAttentionNet(**fit_cfg_, **attn_cfg).to(device)
		optimizer = torch.optim.Adam(mdl.parameters(), lr=kwargs.get('lr', 0.001))
		loss_fn = nn.MSELoss().to(device)

		# Training loop
		dataset_batches = np.array_split(xytrain, num_batches)
		for epoch in range(kwargs.get('epochs', 20)):
			for j, value in enumerate(dataset_batches):
				x_ = torch.from_numpy(value[:, :, :-num_label]).float().to(device)
				y_ = torch.from_numpy(value[:, :, -num_label:]).float().to(device)
				out = mdl(x_)
				loss = loss_fn(out, y_)
				loss.backward()
				optimizer.step()
				optimizer.zero_grad()
				print(
					f'[{idx}] epoch: {epoch + 1}, batch: {j}, loss: {loss.cpu().item():.4f}',
					end='\r',
				)

		# Validation
		with torch.no_grad():
			mdl.eval()
			xtrain_pred = mdl(
				torch.from_numpy(xytrain[-xyvalid.shape[0] :, :, :-num_label]).float().to(device)
			)
			ytrain = torch.from_numpy(xytrain[-xyvalid.shape[0] :, :, -num_label:]).float().to(device)
			train_loss = loss_fn(xtrain_pred, ytrain).item()

			xvalid = torch.from_numpy(xyvalid[:, :, :-num_label]).float().to(device)
			yvalid = torch.from_numpy(xyvalid[:, :, -num_label:]).float().to(device)
			xvalid_pred = mdl(xvalid)
			valid_loss = loss_fn(xvalid_pred, yvalid).item()

			# Extract last prediction from each sequence
			xvalid_pred_last = xvalid_pred[:, -1, :].cpu().numpy()
			pred_index = xy.index[i + seq_len - 1 : i + seq_len + update_period - 1]
			pred_df = pd.DataFrame(
				xvalid_pred_last,
				index=pred_index,
				columns=[f'pred_{i}' for i in range(num_label)],
			)
			xpreds.append(pred_df)

		losses_all.append([train_loss, valid_loss])
		# print(f'loss [{i}]: {train_loss:.4f}, {valid_loss:.4f}', end='\r')
		mdls.append(mdl)

	return pd.concat(xpreds, axis=0), mdls, losses_all


fit_cfg = {
	'hidden_size': 64,
	'hidden_layers': 2,
	'dropout': 0.2,
	'activation': 'relu',
}

best_ic = -np.inf
best_loss = np.inf
patience = 2
seq_len = 30
losses_all = []
epoch_step = 5
epochs_list = range(epoch_step, 200, epoch_step)
for epochs in epochs_list:
	pred, mdls, loss_epochs = gru_fit(
		xy,
		update_period,
		window,
		seq_len,
		num_label,
		**{'lr': 1.0e-2, 'epochs': epochs, 'num_batches': 10, 'num_attention_heads': 4, 'attn_dropout': 0.1},
	)
	pred_ = functions.winsorize_mad(pred, 60 * 24 * 10, 4)
	ic = pd.concat([pred_, raw_label['label_vwap_min_1']], axis=1).corr().iloc[0, 1]
	print(f'epochs: {epochs}, ic: {ic:.4f}, losses: ({loss_epochs[-1][0]:.4f}, {loss_epochs[-1][1]:.4f})')
	losses_all.append(loss_epochs[-1])
	latest_loss = pd.DataFrame(loss_epochs).iloc[:,-1].mean()
	if latest_loss <= best_loss:
		best_loss = latest_loss
		best_pred = pred
	if latest_loss > best_loss:
		patience -= 1
		if patience == 0:
			break

torch.cuda.empty_cache()
pd.DataFrame(losses_all, index=epochs_list[: len(losses_all)]).plot(marker='o', logy=True);


pred_ = functions.winsorize_mad(pred, 60*24*10, 4)
pd.concat([pred_, raw_label['label_vwap_min_1']], axis=1).corr()

pred_ = functions.winsorize_mad(pred, 60*24*10, 4)
pred_ = functions.zscore(pred_, 60 * 24 * 10)
rets, summary = bt.run_backtest(
	{
		'feature': pred_,
		'label': raw_label['label_vwap_min_1'],
		'long_threshold': 2.0,
		'short_threshold': -2.0,
		'fees': [0.0e-4],  ## typical fees for futures，
		'period': '1min'
	}
)
bt.plot_returns(rets)
summary.style.format('{:.4f}')

import numpy as np
from numpy.lib.stride_tricks import sliding_window_view
import torch
import torch.nn as nn
import pandas as pd
from tqdm.auto import tqdm

# Define device
device = torch.device('cuda:0')
torch.set_num_threads(120)


def stack_time_step(time_step: int, dataset: np.ndarray):
	wdsp = (time_step, dataset.shape[-1])
	return sliding_window_view(dataset, wdsp).squeeze(axis=1)


class GRUWithPoolingNet(nn.Module):
	def __init__(
		self,
		input_size: int,
		output_size: int,
		hidden_size: int,
		hidden_layers: int,
		dropout: float,
		activation: str = 'relu',  # Maintained for interface compatibility
		pool_type: str = 'mean',   # 'mean', 'max'
	):
		super().__init__()
		self.hidden_size = hidden_size
		self.num_layers = hidden_layers
		self.pool_type = pool_type

		# GRU layer (batch_first=True for [batch, seq, feature] format)
		self.gru = nn.GRU(
			input_size=input_size,
			hidden_size=hidden_size,
			num_layers=hidden_layers,
			batch_first=True,
			dropout=dropout if hidden_layers > 1 else 0,
		)

		# Output layer: input dim is hidden_size * num_directions if bidirectional
		self.outlayer = nn.Linear(hidden_size, output_size)

	def forward(self, x):
		# Initialize hidden state
		h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)

		# GRU forward pass
		out, _ = self.gru(x, h0)  # out: [batch, seq, hidden_size]

		if self.pool_type == 'mean':
			# Causal mean pooling: for each time step t, mean over [0, t]
			# out: [batch, seq, hidden_size]
			cumsum = out.cumsum(dim=1)
			steps = torch.arange(1, out.size(1) + 1, device=out.device).view(1, -1, 1)
			pooled = cumsum / steps  # [batch, seq, hidden_size]
		elif self.pool_type == 'max':
			# torch.cummax returns a tuple (values, indices), we want only the values
			pooled, _ = out.cummax(dim=1)  # [batch, seq, hidden_size]
		else:
			raise ValueError(f"Unknown pool_type: {self.pool_type}")

		# Output predictions for each time step
		return self.outlayer(pooled)


def gru_fit(
	xy: pd.DataFrame,
	update_period: int,
	window: int,
	seq_len: int = 10,
	num_label: int = 1,
	**kwargs,
):
	xpreds, mdls, losses_all = [], [], []
	datasets = stack_time_step(seq_len, xy.values)
	# print(datasets.shape)

	num_batches = kwargs.get('num_batches', 10)
	tqbar = range(window, len(xy), update_period)
	tqbar = tqdm(tqbar, desc=f'epochs={kwargs.get("epochs", 20)}')
	for i in tqbar:
		progress = (i - window + 1) // update_period + 1
		total = (len(xy) - window) // update_period + 1
		idx = f'{progress}/{total}'

		xytrain = datasets[i - window : i, :, :]
		xyvalid = datasets[i : i + update_period, :, :]

		# Initialize model
		fit_cfg_ = fit_cfg.copy()
		fit_cfg_['input_size'] = xytrain.shape[-1] - num_label
		fit_cfg_['output_size'] = num_label

		# Add pooling config if present
		pool_cfg = {}
		if 'pool_type' in kwargs:
			pool_cfg['pool_type'] = kwargs['pool_type']

		mdl = GRUWithPoolingNet(**fit_cfg_, **pool_cfg).to(device)
		optimizer = torch.optim.Adam(mdl.parameters(), lr=kwargs.get('lr', 0.001))
		loss_fn = nn.MSELoss().to(device)

		# Training loop
		dataset_batches = np.array_split(xytrain, num_batches)
		for epoch in range(kwargs.get('epochs', 20)):
			for j, value in enumerate(dataset_batches):
				x_ = torch.from_numpy(value[:, :, :-num_label]).float().to(device)
				y_ = torch.from_numpy(value[:, :, -num_label:]).float().to(device)
				out = mdl(x_)
				loss = loss_fn(out, y_)
				loss.backward()
				optimizer.step()
				optimizer.zero_grad()
				print(
					f'[{idx}] epoch: {epoch + 1}, batch: {j}, loss: {loss.cpu().item():.4f}',
					end='\r',
				)

		# Validation
		with torch.no_grad():
			mdl.eval()
			xtrain_pred = mdl(
				torch.from_numpy(xytrain[-xyvalid.shape[0] :, :, :-num_label]).float().to(device)
			)
			ytrain = torch.from_numpy(xytrain[-xyvalid.shape[0] :, :, -num_label:]).float().to(device)
			train_loss = loss_fn(xtrain_pred, ytrain).item()

			xvalid = torch.from_numpy(xyvalid[:, :, :-num_label]).float().to(device)
			yvalid = torch.from_numpy(xyvalid[:, :, -num_label:]).float().to(device)
			xvalid_pred = mdl(xvalid)
			valid_loss = loss_fn(xvalid_pred, yvalid).item()

			# Extract last prediction from each sequence
			xvalid_pred_last = xvalid_pred[:, -1, :].cpu().numpy()
			pred_index = xy.index[i + seq_len - 1 : i + seq_len + update_period - 1]
			pred_df = pd.DataFrame(
				xvalid_pred_last,
				index=pred_index,
				columns=[f'pred_{i}' for i in range(num_label)],
			)
			xpreds.append(pred_df)

		losses_all.append([train_loss, valid_loss])
		# print(f'loss [{i}]: {train_loss:.4f}, {valid_loss:.4f}', end='\r')
		mdls.append(mdl)

	return pd.concat(xpreds, axis=0), mdls, losses_all


fit_cfg = {
	'hidden_size': 64,
	'hidden_layers': 2,
	'dropout': 0.2,
	'activation': 'relu',
}

best_ic = -np.inf
best_loss = np.inf
patience = 2
seq_len = 20
losses_all = []
epoch_step = 5
epochs_list = range(epoch_step, 200, epoch_step)
for epochs in epochs_list:
	pred, mdls, loss_epochs = gru_fit(
		xy,
		update_period,
		window,
		seq_len,
		num_label,
		**{'lr': 1.0e-2, 'epochs': epochs, 'num_batches': 5, 'pool_type': 'max'},
	)
	pred_ = functions.winsorize_mad(pred, 60 * 24 * 10, 4)
	ic = pd.concat([pred_, raw_label['label_vwap_min_1']], axis=1).corr().iloc[0, 1]
	print(f'epochs: {epochs}, ic: {ic:.4f}, losses: ({loss_epochs[-1][0]:.4f}, {loss_epochs[-1][1]:.4f})')
	losses_all.append(loss_epochs[-1])
	latest_loss = pd.DataFrame(loss_epochs).iloc[:,-1].mean()
	if latest_loss <= best_loss:
		best_loss = latest_loss
		best_pred = pred
	if latest_loss > best_loss:
		patience -= 1
		if patience == 0:
			break

torch.cuda.empty_cache()
pd.DataFrame(losses_all, index=epochs_list[: len(losses_all)]).plot(marker='o', logy=True);


pred_ = functions.winsorize_mad(pred, 60*24*10, 4)
pd.concat([pred_, raw_label['label_vwap_min_1']], axis=1).corr()

pred_ = functions.winsorize_mad(pred, 60*24*10, 4)
pred_ = functions.zscore(pred_, 60 * 24 * 10)
rets, summary = bt.run_backtest(
	{
		'feature': pred_,
		'label': raw_label['label_vwap_min_1'],
		'long_threshold': 2.0,
		'short_threshold': -2.0,
		'fees': [0.0e-4],  ## typical fees for futures，
		'period': '1min'
	}
)
bt.plot_returns(rets)
summary.style.format('{:.4f}')

import numpy as np
from numpy.lib.stride_tricks import sliding_window_view
import torch
import torch.nn as nn
import pandas as pd
from tqdm.auto import tqdm

# Define device
device = torch.device('cuda:0')
torch.set_num_threads(120)


def stack_time_step(time_step: int, dataset: np.ndarray):
	wdsp = (time_step, dataset.shape[-1])
	return sliding_window_view(dataset, wdsp).squeeze(axis=1)


def generate_causal_mask(seq_len, device=None):
	# mask shape: (seq_len, seq_len)
	# True means masked (prevent attention), False means allowed
	# For PyTorch MultiheadAttention, mask is float with -inf for masked, 0 for allowed
	mask = torch.triu(torch.ones(seq_len, seq_len), diagonal=1)
	mask = mask.masked_fill(mask == 1, float('-inf')).masked_fill(mask == 0, float(0.0))
	if device is not None:
		mask = mask.to(device)
	return mask


class SelfAttention(nn.Module):
	def __init__(self, embed_dim, num_heads=1, dropout=0.0):
		super().__init__()
		self.attn = nn.MultiheadAttention(embed_dim, num_heads, dropout=dropout, batch_first=True)

	def forward(self, x):
		# x: [batch, seq, embed_dim]
		seq_len = x.size(1)
		# Causal mask: prevent attention to future tokens
		attn_mask = generate_causal_mask(seq_len, device=x.device)
		attn_output, _ = self.attn(x, x, x, attn_mask=attn_mask)
		return attn_output


class GRUWithAttentionNet(nn.Module):
	def __init__(
		self,
		input_size: int,
		output_size: int,
		hidden_size: int,
		hidden_layers: int,
		dropout: float,
		activation: str = 'relu',  # Maintained for interface compatibility
		num_attention_heads: int = 1,
		attn_dropout: float = 0.0,
	):
		super().__init__()
		self.hidden_size = hidden_size
		self.num_layers = hidden_layers

		# GRU layer (batch_first=True for [batch, seq, feature] format)
		self.gru = nn.GRU(
			input_size=input_size,
			hidden_size=hidden_size,
			num_layers=hidden_layers,
			batch_first=True,
			dropout=dropout if hidden_layers > 1 else 0,
		)

		# Self-attention layer
		self.attn = SelfAttention(hidden_size, num_heads=num_attention_heads, dropout=attn_dropout)

		# Output layer: input dim is hidden_size * num_directions if bidirectional
		self.outlayer = nn.Linear(hidden_size, output_size)

	def forward(self, x):
		# Initialize hidden state
		h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)

		# GRU forward pass
		out, _ = self.gru(x, h0)  # out: [batch, seq, hidden_size]

		# Self-attention with causal mask
		attn_out = self.attn(out)  # [batch, seq, hidden_size]

		# Output predictions for each time step
		return self.outlayer(attn_out)


def gru_fit(
	xy: pd.DataFrame,
	update_period: int,
	window: int,
	seq_len: int = 10,
	num_label: int = 1,
	**kwargs,
):
	xpreds, mdls, losses_all = [], [], []
	datasets = stack_time_step(seq_len, xy.values)
	# print(datasets.shape)

	num_batches = kwargs.get('num_batches', 10)
	tqbar = range(window, len(xy), update_period)
	tqbar = tqdm(tqbar, desc=f'epochs={kwargs.get("epochs", 20)}')
	for i in tqbar:
		progress = (i - window + 1) // update_period + 1
		total = (len(xy) - window) // update_period + 1
		idx = f'{progress}/{total}'

		xytrain = datasets[i - window : i, :, :]
		xyvalid = datasets[i : i + update_period, :, :]

		# Initialize model
		fit_cfg_ = fit_cfg.copy()
		fit_cfg_['input_size'] = xytrain.shape[-1] - num_label
		fit_cfg_['output_size'] = num_label

		# Add attention config if present
		attn_cfg = {}
		if 'num_attention_heads' in kwargs:
			attn_cfg['num_attention_heads'] = kwargs['num_attention_heads']
		if 'attn_dropout' in kwargs:
			attn_cfg['attn_dropout'] = kwargs['attn_dropout']

		mdl = GRUWithAttentionNet(**fit_cfg_, **attn_cfg).to(device)
		optimizer = torch.optim.Adam(mdl.parameters(), lr=kwargs.get('lr', 0.001))
		loss_fn = nn.MSELoss().to(device)

		# Training loop
		dataset_batches = np.array_split(xytrain, num_batches)
		for epoch in range(kwargs.get('epochs', 20)):
			for j, value in enumerate(dataset_batches):
				x_ = torch.from_numpy(value[:, :, :-num_label]).float().to(device)
				y_ = torch.from_numpy(value[:, :, -num_label:]).float().to(device)
				out = mdl(x_)
				loss = loss_fn(out, y_)
				loss.backward()
				optimizer.step()
				optimizer.zero_grad()
				print(
					f'[{idx}] epoch: {epoch + 1}, batch: {j}, loss: {loss.cpu().item():.4f}',
					end='\r',
				)

		# Validation
		with torch.no_grad():
			mdl.eval()
			xtrain_pred = mdl(
				torch.from_numpy(xytrain[-xyvalid.shape[0] :, :, :-num_label]).float().to(device)
			)
			ytrain = torch.from_numpy(xytrain[-xyvalid.shape[0] :, :, -num_label:]).float().to(device)
			train_loss = loss_fn(xtrain_pred, ytrain).item()

			xvalid = torch.from_numpy(xyvalid[:, :, :-num_label]).float().to(device)
			yvalid = torch.from_numpy(xyvalid[:, :, -num_label:]).float().to(device)
			xvalid_pred = mdl(xvalid)
			valid_loss = loss_fn(xvalid_pred, yvalid).item()

			# Extract last prediction from each sequence
			xvalid_pred_last = xvalid_pred[:, -1, :].cpu().numpy()
			pred_index = xy.index[i + seq_len - 1 : i + seq_len + update_period - 1]
			pred_df = pd.DataFrame(
				xvalid_pred_last,
				index=pred_index,
				columns=[f'pred_{i}' for i in range(num_label)],
			)
			xpreds.append(pred_df)

		losses_all.append([train_loss, valid_loss])
		# print(f'loss [{i}]: {train_loss:.4f}, {valid_loss:.4f}', end='\r')
		mdls.append(mdl)

	return pd.concat(xpreds, axis=0), mdls, losses_all


fit_cfg = {
	'hidden_size': 64,
	'hidden_layers': 2,
	'dropout': 0.2,
	'activation': 'relu',
}

best_ic = -np.inf
best_loss = np.inf
patience = 2
seq_len = 30
losses_all = []
epoch_step = 5
epochs_list = range(epoch_step, 200, epoch_step)
for epochs in epochs_list:
	pred, mdls, loss_epochs = gru_fit(
		xy,
		update_period,
		window,
		seq_len,
		num_label,
		**{'lr': 1.0e-2, 'epochs': epochs, 'num_batches': 10, 'num_attention_heads': 4, 'attn_dropout': 0.1},
	)
	pred_ = functions.winsorize_mad(pred, 60 * 24 * 10, 4)
	ic = pd.concat([pred_, raw_label['label_vwap_min_1']], axis=1).corr().iloc[0, 1]
	print(f'epochs: {epochs}, ic: {ic:.4f}, losses: ({loss_epochs[-1][0]:.4f}, {loss_epochs[-1][1]:.4f})')
	losses_all.append(loss_epochs[-1])
	latest_loss = pd.DataFrame(loss_epochs).iloc[:,-1].mean()
	if latest_loss <= best_loss:
		best_loss = latest_loss
		best_pred = pred
	if latest_loss > best_loss:
		patience -= 1
		if patience == 0:
			break

torch.cuda.empty_cache()
pd.DataFrame(losses_all, index=epochs_list[: len(losses_all)]).plot(marker='o', logy=True);


pred_ = functions.winsorize_mad(pred, 60*24*10, 4)
pd.concat([pred_, raw_label['label_vwap_min_1']], axis=1).corr()

pred_ = functions.winsorize_mad(pred, 60*24*10, 4)
pred_ = functions.zscore(pred_, 60 * 24 * 10)
rets, summary = bt.run_backtest(
	{
		'feature': pred_,
		'label': raw_label['label_vwap_min_1'],
		'long_threshold': 2.0,
		'short_threshold': -2.0,
		'fees': [0.0e-4],  ## typical fees for futures，
		'period': '1min'
	}
)
bt.plot_returns(rets)
summary.style.format('{:.4f}')

plot_stats(pred)

pred_ = functions.winsorize_mad(pred, 60*24*10, 4)
plot_stats(pred_)
pd.concat([pred_, raw_label['label_vwap_min_1']], axis=1).corr()



# pred_ = pred_.ewm(halflife=5, min_periods=1).mean()
pred_ = functions.zscore(pred_, 60 * 24 * 10)
rets, summary = bt.run_backtest(
	{
		'feature': pred_,
		'label': raw_label['label_vwap_min_1'],
		'long_threshold': 2.0,
		'short_threshold': -2.0,
		'fees': [0.0e-4],  ## typical fees for futures，
		'period': '1min'
	}
)
bt.plot_returns(rets)
summary.style.format('{:.4f}')