import numpy as np
import pandas as pd


import os
data_root = '/mnt/sda/NAS/Global/Bloomberg/XSAU/'
os.listdir(data_root)

pd.read_csv('/mnt/sda/NAS/Global/Bloomberg/XSAU/TickMinOHLC/2025/2P AB Equity_ohlc_1_1.csv.gz').columns.tolist()[:18]

from tqdm.auto import tqdm

minbar_root = '/mnt/sda/NAS/Global/Bloomberg/XSAU/TickMinOHLC/'
year = '2023'
syms = [s for s in sorted(os.listdir(f'{minbar_root}{year}')) if s.endswith('.csv.gz')]

minbars = []
for sym in tqdm(syms[:]):
    filepath = os.path.join(minbar_root, year, sym)
    if os.path.exists(filepath):
        minbar = pd.read_csv(filepath, usecols=['TH_BAR_TIME', 'TH_BAR_VWAP', 'TH_BAR_VOLUME', 'SECURITY'])
        minbar['TH_BAR_TIME'] = pd.to_datetime(minbar['TH_BAR_TIME'])
        minbar['turnover'] = minbar['TH_BAR_VOLUME'] * minbar['TH_BAR_VWAP']
        # minbar.set_index('TH_BAR_TIME', inplace=True)
        # minbar.sort_index(inplace=True)
        minbars.append(minbar)
    else:
        print(f'{filepath} does not exist')


minbars = pd.concat(minbars)

minbars['date'] = minbars['TH_BAR_TIME'].dt.date

minbars.groupby(['date', 'SECURITY']).agg({'turnover': 'sum'})\
    .groupby('SECURITY').sum()\
        .sort_values(by='turnover', ascending=False)\
            .head(20)

import os
import pandas as pd
from tqdm.auto import tqdm

sym = 'RJHI AB Equity'
minbar_root = '/mnt/sda/NAS/Global/Bloomberg/XSAU/TickMinOHLC/'

years = [str(yr) for yr in range(2015, 2026)]
cols = [
	'SECURITY',
	'TH_BAR_TIME',
	'TH_BAR_TYPE',
	'TH_BAR_OPEN',
	'TH_BAR_HIGH',
	'TH_BAR_LOW',
	'TH_BAR_CLOSE',
	'TH_BAR_VOLUME',
	'TH_BAR_VWAP',
	'TH_BAR_TICK_COUNT',
	'TH_BAR_OPEN_TIME',
	'TH_BAR_HIGH_TIME',
	'TH_BAR_LOW_TIME',
	'TH_BAR_CLOSE_TIME',
	'TH_BAR_SNAP',
	'TH_BAR_SNAP_TIME',
	'TH_BAR_CONDITION_CODES:1',
	'TH_BAR_CONDITION_CODES:2',
]

minbars = []
for year in tqdm(years):
	filedir = os.path.join(minbar_root, year)
	for file in os.listdir(filedir):
		if file.startswith(sym):
			filepath = os.path.join(filedir, file)
			minbar = pd.read_csv(filepath, usecols=cols, low_memory=False)
			minbar['TH_BAR_TIME'] = pd.to_datetime(minbar['TH_BAR_TIME'])
			minbars.append(minbar)

minbars = pd.concat(minbars)

a = minbars.loc[minbars['TH_BAR_VWAP']>0.0]

a['TH_BAR_TIME'] = pd.to_datetime(a['TH_BAR_TIME'])
a.set_index('TH_BAR_TIME', inplace=True)
a

a['TH_BAR_VWAP'].plot(logy=True, grid=True);



tick_root = '/mnt/sda/NAS/Global/Bloomberg/XSAU/TickTradeQuote/'
yr_mon = '2025-01'

usecols = ['EVT_TRADE_CONDITION_CODE','EVT_TRADE_PRICE','EVT_TRADE_TIME']

trades = {}
for file in tqdm(os.listdir(f'{tick_root}{yr_mon}')):
	if file.endswith('.csv.gz') & ('trades' in file):
		trade = pd.read_csv(f'{tick_root}{yr_mon}/{file}', compression='gzip', usecols=usecols)
		trade.index = trade['EVT_TRADE_TIME'].str[:-1].astype('datetime64[ns]')
		trade['date'] = trade.index.date
		trades[file] = trade


os.listdir('/mnt/sda/NAS/ShareFolder/dataGroup/YRData/Equity/sa_xsau/config/')

pd.read_csv('/mnt/sda/NAS/ShareFolder/dataGroup/YRData/Equity/sa_xsau/config/sa_xsau_symbols.txt')

pd.read_csv('/mnt/sda/NAS/ShareFolder/dataGroup/YRData/Equity/sa_xsau/config/sa_xsau_restoration_factor.csv')

import h5py
import numpy as np

with h5py.File('/mnt/sda/NAS/ShareFolder/dataGroup/YRData/Equity/sa_xsau/config/sa_xsau_restoration_info.h5', 'r') as f:
    print(list(f.keys()))
    syms = f['SYMBOLS'][:].astype(np.str_)
    dates = f['TRADING_DAYS'][:].astype(np.str_)
    adjfactor = pd.DataFrame(f['DATA'][:], columns=syms, index=dates)


adjfactor

def get_adjfactor(trade):
	tmp = trade[['EVT_TRADE_CONDITION_CODE','EVT_TRADE_PRICE','date']]
	tmp['EVT_TRADE_PRICE'] = tmp['EVT_TRADE_PRICE'].astype(float)
	tmp = tmp.loc[tmp['EVT_TRADE_CONDITION_CODE'].isin(['AOC','OC'])]
	tmp = tmp.groupby(['date','EVT_TRADE_CONDITION_CODE']).last()['EVT_TRADE_PRICE'].unstack()
 
	tmp['AOC'] = tmp['AOC'].shift(-1)
	tmp.columns = ['OC','AOC']
	return tmp['AOC'].div(tmp['OC'])

adjfactors = {}
for key, trade in trades.items():
	adjfactors[key] = get_adjfactor(trade)

import pandas as pd
a = pd.read_parquet('/mnt/sda/NAS/ShareFolder/laihui/Bloomberg/Tadawul/TickTrade/2025-01-01.parquet')
a.loc[a['SECURITY'].str.startswith('ARAMCO')]



