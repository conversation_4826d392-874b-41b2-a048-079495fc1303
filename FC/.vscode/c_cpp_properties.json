{"configurations": [{"name": "Linux", "includePath": ["${workspaceFolder}/**", "/mnt/sda/NAS/Release/shennong/stk/**", "/mnt/sda/NAS/Release/shennong/utils/**", "/mnt/sda/NAS/Release/shennong/common/**", "/mnt/sda/NAS/Release/shennong/fc/dev_global.dev/**"], "defines": [], "compilerPath": "/usr/bin/gcc", "cStandard": "c17", "cppStandard": "gnu++14", "compileCommands": ["${workspaceFolder}/cn_ashare_new/orderbook/src/compile_commands.json"], "intelliSenseMode": "linux-gcc-x64"}], "version": 4}