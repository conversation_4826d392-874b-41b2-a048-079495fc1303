{
    "configurations": [
        {
            "name": "auction1Bar",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceRoot}/cn_ashare_new/auction1/bin/featureGenerator",
            "cwd": "${workspaceRoot}/cn_ashare_new/auction1/",
            "args": [
                "-c",
                "config/fg/cn_ashare/auction1Bar.json",
                "-l",
                "log/"
            ],
            "stopAtEntry": false,
            "environment": [
                {
                    "name": "LD_PRELOAD",
                    "value": "./lib/libshennong_fc.so:./lib/libshennong_stk.so:./external_lib/libshennong_fc_toolkit.so"
                },
                {
                    "name": "LD_LIBRARY_PATH",
                    "value": "${workspaceRoot}/cn_ashare_new/auction1/lib:$LD_LIBRARY_PATH"
                },
            ],
            "externalConsole": false,
            "MIMode": "gdb",
            "setupCommands": [
                {
                    "description": "Enable pretty-printing for gdb",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                },
                {
                    "description": "Set Disassembly Flavor to Intel",
                    "text": "-gdb-set disassembly-flavor intel",
                    "ignoreFailures": true
                }
            ],
            "preLaunchTask": "auction1Bar",
            "miDebuggerPath": "/usr/bin/gdb"
        },
        {
            "name": "ob4_v2",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceRoot}/cn_ashare_new/orderbook/bin/featureGenerator",
            "cwd": "${workspaceRoot}/cn_ashare_new/orderbook/",
            "args": [
                "-c",
                "config/fg/cn_ashare/ob4_v2.json",
                "-l",
                "log/"
            ],
            "stopAtEntry": false,
            "environment": [
                {
                    "name": "LD_PRELOAD",
                    "value": "${workspaceRoot}/cn_ashare_new/orderbook/lib/libshennong_fc.so:${workspaceRoot}/cn_ashare_new/orderbook/lib/libshennong_stk.so"
                },
                {
                    "name": "LD_LIBRARY_PATH",
                    "value": "${workspaceRoot}/cn_ashare_new/orderbook/lib:$LD_LIBRARY_PATH"
                },
            ],
            "externalConsole": false,
            "MIMode": "gdb",
            "setupCommands": [
                {
                    "description": "Enable pretty-printing for gdb",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                },
                {
                    "description": "Set Disassembly Flavor to Intel",
                    "text": "-gdb-set disassembly-flavor intel",
                    "ignoreFailures": true
                }
            ],
            "preLaunchTask": "build_ob",
            "miDebuggerPath": "/usr/bin/gdb"
        }
    ],
    "version": "2.0.0"
}