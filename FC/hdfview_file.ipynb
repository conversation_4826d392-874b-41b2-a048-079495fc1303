{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 查看新生成的特征"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import os, h5py, datetime\n", "import pandas as pd, numpy as np\n", "import matplotlib.pyplot as plt\n", "import polars as pl\n", "from yrqtlib.utils import data\n", "bar = data.BarDataManager()\n", "tick = data.TickDataManager()\n", "from yrqtlib.utils import symbol\n", "from yrqtlib.utils import add_methods_to_pandas\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["path = \"/mnt/sda/NAS/ShareFolder/lishuanglin/features/cn_ashare\"\n", "def update_key_config(path: str='/mnt/sda/NAS/ShareFolder/lishuanglin/features/cn_ashare'):\n", "    from shennong.utils import key_group_manager as sn_km\n", "    sn_km.generate_h5_key_group_config(load_root=path)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d54449cca1044e768686359299d994a8", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/23 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from shennong.utils import trading_days\n", "from tqdm.auto import tqdm\n", "from yrqtlib.utils.algo import Algo\n", "\n", "\n", "feature_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/features/'\n", "\n", "# region, product, freq = 'gb', 'bina_future', '3second'\n", "# key_group_name = 'quote3'\n", "# sym_list = ['BTCUSDT.BNF']\n", "\n", "# region, product, freq = 'cn', 'ashare', '1minute'\n", "# key_group_name = 'lsl__fcts_ob2'\n", "region, product, freq = 'cn', 'ashare', '1hour'\n", "key_group_name = 'auction1'\n", "\n", "dates = trading_days.load(region='cn', product='ashare', start_datetime='2024-01-01',end_datetime='2024-02-01')\n", "\n", "\n", "feature_dir = f'{feature_root}{region}_{product}/'\n", "auction1_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/crontab/auction1_raw_best65_panhou/'\n", "date = '2023-08-29'\n", "\n", "corrs = {}\n", "for date in tqdm(dates):\n", "    a = pd.read_pickle(f'{auction1_root}{date}.pkl')\n", "    f = bar.load(feature_dir, freq, key_group_name, date, load_with='h5py', sym_list=None)\n", "\n", "    sym_list = sorted(set(f.SYMBOL.to_numpy()).intersection(set(a.index)))\n", "    f1 = f[0, :, :].to_pandas().T\n", "    a1 = a.loc[sym_list]\n", "\n", "    corr = Algo.vec_corr_2Df(f1, a1, 'column', 'pearson')\n", "    corrs[date] = corr"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 186, "metadata": {}, "outputs": [{"data": {"text/plain": ["((65, 5107), (65, 10))"]}, "execution_count": 186, "metadata": {}, "output_type": "execute_result"}], "source": ["from tqdm.auto import tqdm\n", "from yrqtlib.utils.algo import Algo\n", "from shennong.utils import trading_days\n", "\n", "\n", "feature_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/features/'\n", "\n", "# region, product, freq = 'gb', 'bina_future', '3second'\n", "# key_group_name = 'quote3'\n", "# sym_list = ['BTCUSDT.BNF']\n", "\n", "region, product, freq = 'cn', 'ashare', '1minute'\n", "\n", "\n", "feature_dir = f'{feature_root}{region}_{product}/'\n", "date = '2024-01-02'\n", "\n", "f1 = bar.load(feature_dir, '1minute', 'lsl__fcts_auction1', date, load_with='h5py', sym_list=None)\n", "f1 = f1[0,:,:].to_pandas()\n", "f2 = bar.load(feature_dir, '1minute', 'lsl__fcts_auction1_test', date, load_with='h5py', sym_list=None)\n", "f2 = f2[0,:,:].to_pandas()\n", "# f2 = bar.load(feature_dir, '1hour', 'lsl__fcts_auction1', date, load_with='h5py', sym_list=None)\n", "# f3 = bar.load(feature_dir, '1hour', 'lsl__fcts_auction1_1hour', date, load_with='h5py', sym_list=None)\n", "# f2 = f2[0,:,:].to_pandas()\n", "# f3 = f3[0,:,:].to_pandas()\n", "f1.shape, f2.shape"]}, {"cell_type": "code", "execution_count": 187, "metadata": {}, "outputs": [{"data": {"text/plain": ["(65, 5089)"]}, "execution_count": 187, "metadata": {}, "output_type": "execute_result"}], "source": ["fpy = pd.read_pickle('/mnt/sda/NAS/ShareFolder/lishuanglin/crontab/auction1_raw_best65_panhou/2024-01-02.pkl').T\n", "fpy.shape\n"]}, {"cell_type": "code", "execution_count": 201, "metadata": {}, "outputs": [{"data": {"text/plain": ["000001.SZ   -9.825895e-06\n", "301055.SZ   -3.707056e-06\n", "600220.SH   -2.710881e-06\n", "002198.SZ   -1.103417e-06\n", "002298.SZ   -2.764772e-07\n", "301130.SZ    1.508370e-07\n", "603059.SH    5.435296e-07\n", "601996.SH    5.883901e-07\n", "002105.SZ    1.183367e-06\n", "002210.SZ    2.081543e-06\n", "dtype: float64"]}, "execution_count": 201, "metadata": {}, "output_type": "execute_result"}], "source": ["a, b  = fpy.copy(), f2.copy()\n", "a.index = [f'lsl__fcts_auction1__{s}' for s in a.index]\n", "\n", "rows = a.index.intersection(b.index)\n", "cols = a.columns.intersection(b.columns)\n", "a = a.loc[rows, cols]\n", "b = b.loc[rows, cols]\n", "dif = (a-b).T\n", "dif.T.sum().sort_values()\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 199, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>000001.SZ</th>\n", "      <th>002105.SZ</th>\n", "      <th>002198.SZ</th>\n", "      <th>002210.SZ</th>\n", "      <th>002298.SZ</th>\n", "      <th>301055.SZ</th>\n", "      <th>301130.SZ</th>\n", "      <th>600220.SH</th>\n", "      <th>601996.SH</th>\n", "      <th>603059.SH</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>lsl__fcts_auction1__cnum</th>\n", "      <td>2.802089</td>\n", "      <td>0.301030</td>\n", "      <td>0.301030</td>\n", "      <td>1.000000</td>\n", "      <td>1.361728</td>\n", "      <td>0.301030</td>\n", "      <td>0.000000</td>\n", "      <td>0.698970</td>\n", "      <td>1.414973</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_auction1__cnum_bid_pct</th>\n", "      <td>0.098196</td>\n", "      <td>0.005604</td>\n", "      <td>0.006949</td>\n", "      <td>0.017259</td>\n", "      <td>0.088775</td>\n", "      <td>0.009760</td>\n", "      <td>0.000000</td>\n", "      <td>0.009192</td>\n", "      <td>0.024880</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_auction1__cvol_bid_pct</th>\n", "      <td>0.044782</td>\n", "      <td>0.011482</td>\n", "      <td>0.000630</td>\n", "      <td>0.045899</td>\n", "      <td>0.067768</td>\n", "      <td>0.039749</td>\n", "      <td>0.000000</td>\n", "      <td>0.018840</td>\n", "      <td>0.026170</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_auction1__anum_bid_pct</th>\n", "      <td>0.054869</td>\n", "      <td>0.189056</td>\n", "      <td>0.142107</td>\n", "      <td>0.048099</td>\n", "      <td>0.143562</td>\n", "      <td>0.112422</td>\n", "      <td>0.000000</td>\n", "      <td>0.066615</td>\n", "      <td>0.186215</td>\n", "      <td>0.176091</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_auction1__bcj_amnt</th>\n", "      <td>6.528362</td>\n", "      <td>5.376396</td>\n", "      <td>4.646874</td>\n", "      <td>5.144739</td>\n", "      <td>5.128535</td>\n", "      <td>4.867143</td>\n", "      <td>NaN</td>\n", "      <td>4.656797</td>\n", "      <td>6.355509</td>\n", "      <td>4.130752</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_auction1__jj_mvol_mean</th>\n", "      <td>5.400644</td>\n", "      <td>3.996452</td>\n", "      <td>3.195963</td>\n", "      <td>4.908398</td>\n", "      <td>4.408296</td>\n", "      <td>3.548937</td>\n", "      <td>2.004321</td>\n", "      <td>4.377588</td>\n", "      <td>5.292215</td>\n", "      <td>3.039315</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_auction1__jj_mvol_skew2</th>\n", "      <td>0.483888</td>\n", "      <td>0.113801</td>\n", "      <td>2.986886</td>\n", "      <td>0.394824</td>\n", "      <td>0.760508</td>\n", "      <td>-0.631772</td>\n", "      <td>NaN</td>\n", "      <td>8.898837</td>\n", "      <td>2.463680</td>\n", "      <td>-0.035627</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_auction1__jj_bunmvol_range</th>\n", "      <td>5.604913</td>\n", "      <td>2.778874</td>\n", "      <td>3.653309</td>\n", "      <td>5.795255</td>\n", "      <td>4.408257</td>\n", "      <td>3.505286</td>\n", "      <td>0.000000</td>\n", "      <td>5.196179</td>\n", "      <td>5.651667</td>\n", "      <td>3.301247</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_auction1__jj_bunmvol_mean</th>\n", "      <td>5.252736</td>\n", "      <td>3.602169</td>\n", "      <td>3.402433</td>\n", "      <td>5.786750</td>\n", "      <td>3.863680</td>\n", "      <td>3.313476</td>\n", "      <td>3.699057</td>\n", "      <td>4.303512</td>\n", "      <td>5.250788</td>\n", "      <td>3.292478</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_auction1__jj_aunmvol_range</th>\n", "      <td>5.857273</td>\n", "      <td>3.763503</td>\n", "      <td>0.000000</td>\n", "      <td>5.757473</td>\n", "      <td>4.691974</td>\n", "      <td>3.204391</td>\n", "      <td>0.000000</td>\n", "      <td>5.156249</td>\n", "      <td>5.554006</td>\n", "      <td>4.344412</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>65 rows × 10 columns</p>\n", "</div>"], "text/plain": ["                                      000001.SZ  002105.SZ  002198.SZ  \\\n", "lsl__fcts_auction1__cnum               2.802089   0.301030   0.301030   \n", "lsl__fcts_auction1__cnum_bid_pct       0.098196   0.005604   0.006949   \n", "lsl__fcts_auction1__cvol_bid_pct       0.044782   0.011482   0.000630   \n", "lsl__fcts_auction1__anum_bid_pct       0.054869   0.189056   0.142107   \n", "lsl__fcts_auction1__bcj_amnt           6.528362   5.376396   4.646874   \n", "...                                         ...        ...        ...   \n", "lsl__fcts_auction1__jj_mvol_mean       5.400644   3.996452   3.195963   \n", "lsl__fcts_auction1__jj_mvol_skew2      0.483888   0.113801   2.986886   \n", "lsl__fcts_auction1__jj_bunmvol_range   5.604913   2.778874   3.653309   \n", "lsl__fcts_auction1__jj_bunmvol_mean    5.252736   3.602169   3.402433   \n", "lsl__fcts_auction1__jj_aunmvol_range   5.857273   3.763503   0.000000   \n", "\n", "                                      002210.SZ  002298.SZ  301055.SZ  \\\n", "lsl__fcts_auction1__cnum               1.000000   1.361728   0.301030   \n", "lsl__fcts_auction1__cnum_bid_pct       0.017259   0.088775   0.009760   \n", "lsl__fcts_auction1__cvol_bid_pct       0.045899   0.067768   0.039749   \n", "lsl__fcts_auction1__anum_bid_pct       0.048099   0.143562   0.112422   \n", "lsl__fcts_auction1__bcj_amnt           5.144739   5.128535   4.867143   \n", "...                                         ...        ...        ...   \n", "lsl__fcts_auction1__jj_mvol_mean       4.908398   4.408296   3.548937   \n", "lsl__fcts_auction1__jj_mvol_skew2      0.394824   0.760508  -0.631772   \n", "lsl__fcts_auction1__jj_bunmvol_range   5.795255   4.408257   3.505286   \n", "lsl__fcts_auction1__jj_bunmvol_mean    5.786750   3.863680   3.313476   \n", "lsl__fcts_auction1__jj_aunmvol_range   5.757473   4.691974   3.204391   \n", "\n", "                                      301130.SZ  600220.SH  601996.SH  \\\n", "lsl__fcts_auction1__cnum               0.000000   0.698970   1.414973   \n", "lsl__fcts_auction1__cnum_bid_pct       0.000000   0.009192   0.024880   \n", "lsl__fcts_auction1__cvol_bid_pct       0.000000   0.018840   0.026170   \n", "lsl__fcts_auction1__anum_bid_pct       0.000000   0.066615   0.186215   \n", "lsl__fcts_auction1__bcj_amnt                NaN   4.656797   6.355509   \n", "...                                         ...        ...        ...   \n", "lsl__fcts_auction1__jj_mvol_mean       2.004321   4.377588   5.292215   \n", "lsl__fcts_auction1__jj_mvol_skew2           NaN   8.898837   2.463680   \n", "lsl__fcts_auction1__jj_bunmvol_range   0.000000   5.196179   5.651667   \n", "lsl__fcts_auction1__jj_bunmvol_mean    3.699057   4.303512   5.250788   \n", "lsl__fcts_auction1__jj_aunmvol_range   0.000000   5.156249   5.554006   \n", "\n", "                                      603059.SH  \n", "lsl__fcts_auction1__cnum               0.000000  \n", "lsl__fcts_auction1__cnum_bid_pct       0.000000  \n", "lsl__fcts_auction1__cvol_bid_pct       0.000000  \n", "lsl__fcts_auction1__anum_bid_pct       0.176091  \n", "lsl__fcts_auction1__bcj_amnt           4.130752  \n", "...                                         ...  \n", "lsl__fcts_auction1__jj_mvol_mean       3.039315  \n", "lsl__fcts_auction1__jj_mvol_skew2     -0.035627  \n", "lsl__fcts_auction1__jj_bunmvol_range   3.301247  \n", "lsl__fcts_auction1__jj_bunmvol_mean    3.292478  \n", "lsl__fcts_auction1__jj_aunmvol_range   4.344412  \n", "\n", "[65 rows x 10 columns]"]}, "execution_count": 199, "metadata": {}, "output_type": "execute_result"}], "source": ["a"]}, {"cell_type": "code", "execution_count": 150, "metadata": {}, "outputs": [{"data": {"text/plain": ["KEY\n", "lsl__fcts_auction1__bid_match_num         0.999411\n", "lsl__fcts_auction1__bid_match_num_bpct    0.999668\n", "lsl__fcts_auction1__ask_match_vol_apct    0.999913\n", "lsl__fcts_auction1__ask_match_vol_tpct    0.999941\n", "lsl__fcts_auction1__tgd1_anum_bpct        0.999961\n", "                                            ...   \n", "lsl__fcts_auction1__ask_untd_vol_apct     1.000000\n", "lsl__fcts_auction1__bid_untd_num_apct     1.000000\n", "lsl__fcts_auction1__bid_cvol_pct          1.000000\n", "lsl__fcts_auction1__tgd1_bnum_bpct        1.000000\n", "lsl__fcts_auction1__jj_price_rms          1.000000\n", "Length: 65, dtype: float64"]}, "execution_count": 150, "metadata": {}, "output_type": "execute_result"}], "source": ["from yrqtlib.utils import algo\n", "\n", "algo.Algo.vec_corr_2Df(f1, f2, 'row', 'pearson').sort_values()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 126, "metadata": {}, "outputs": [{"data": {"text/plain": ["SYMBOL\n", "000001.SZ    0.044782\n", "000002.SZ    0.015951\n", "000004.SZ    0.038484\n", "000005.SZ    0.052358\n", "000006.SZ    0.017359\n", "               ...   \n", "688799.SH    0.003977\n", "688800.SH    0.004119\n", "688819.SH    0.012101\n", "688981.SH    0.028529\n", "689009.SH    0.127629\n", "Name: lsl__fcts_auction1__cvol_bid_pct, Length: 5107, dtype: float32"]}, "execution_count": 126, "metadata": {}, "output_type": "execute_result"}], "source": ["f2.loc['lsl__fcts_auction1__cvol_bid_pct']"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["time_ticks = (f1['DATETIME'] + pd.<PERSON>del<PERSON>(hours=8)).dt.time.astype(np.str_)\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>count</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>min</th>\n", "      <th>25%</th>\n", "      <th>50%</th>\n", "      <th>75%</th>\n", "      <th>max</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>lsl__fcts_ob4_v1__bnum_tylv31_tv</th>\n", "      <td>3641.0</td>\n", "      <td>0.304664</td>\n", "      <td>2.947375e-01</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>3.827757e-01</td>\n", "      <td>0.382776</td>\n", "      <td>1.415614</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_ob4_v1__btv_tylv31</th>\n", "      <td>3641.0</td>\n", "      <td>2.691259</td>\n", "      <td>2.292695e+00</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>3.958468e+00</td>\n", "      <td>4.711504</td>\n", "      <td>6.651556</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_ob4_v1__cnum_tylv31_tv</th>\n", "      <td>3641.0</td>\n", "      <td>3.154209</td>\n", "      <td>3.777532e-01</td>\n", "      <td>1.082166</td>\n", "      <td>3.090611</td>\n", "      <td>3.271377e+00</td>\n", "      <td>3.376212</td>\n", "      <td>3.522183</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_ob4_v1__ctv_tylv31</th>\n", "      <td>3641.0</td>\n", "      <td>7.640985</td>\n", "      <td>4.028604e-01</td>\n", "      <td>5.418245</td>\n", "      <td>7.564867</td>\n", "      <td>7.787714e+00</td>\n", "      <td>7.851207</td>\n", "      <td>7.991587</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_ob4_v1__banum_tylv31_tv</th>\n", "      <td>3641.0</td>\n", "      <td>-0.069235</td>\n", "      <td>4.643419e-01</td>\n", "      <td>-1.623495</td>\n", "      <td>-0.382776</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.382776</td>\n", "      <td>1.302112</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_ob4_v1__batv_tylv31</th>\n", "      <td>3641.0</td>\n", "      <td>-0.328491</td>\n", "      <td>4.359200e+00</td>\n", "      <td>-7.057772</td>\n", "      <td>-4.494906</td>\n", "      <td>0.000000e+00</td>\n", "      <td>4.383672</td>\n", "      <td>6.651556</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_ob4_v1__cnum_pct_tylv31_tv</th>\n", "      <td>3641.0</td>\n", "      <td>2.711785</td>\n", "      <td>5.198727e-01</td>\n", "      <td>0.150264</td>\n", "      <td>2.581156</td>\n", "      <td>2.856125e+00</td>\n", "      <td>3.033692</td>\n", "      <td>3.506776</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_ob4_v1__ctv_pct_tylv31</th>\n", "      <td>3641.0</td>\n", "      <td>3.658715</td>\n", "      <td>1.548126e+00</td>\n", "      <td>0.127900</td>\n", "      <td>2.801958</td>\n", "      <td>3.437473e+00</td>\n", "      <td>4.074338</td>\n", "      <td>7.979431</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_ob4_v1__bnum_gtlv31_tv</th>\n", "      <td>3641.0</td>\n", "      <td>0.000105</td>\n", "      <td>6.343574e-03</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>0.382776</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_ob4_v1__btv_gtlv31</th>\n", "      <td>3641.0</td>\n", "      <td>0.001578</td>\n", "      <td>9.524210e-02</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>5.746975</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_ob4_v1__cnum_gtlv31_tv</th>\n", "      <td>3641.0</td>\n", "      <td>0.789741</td>\n", "      <td>1.110376e-16</td>\n", "      <td>0.789741</td>\n", "      <td>0.789741</td>\n", "      <td>7.897413e-01</td>\n", "      <td>0.789741</td>\n", "      <td>0.789741</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_ob4_v1__ctv_gtlv31</th>\n", "      <td>3641.0</td>\n", "      <td>5.973092</td>\n", "      <td>8.883004e-16</td>\n", "      <td>5.973092</td>\n", "      <td>5.973092</td>\n", "      <td>5.973092e+00</td>\n", "      <td>5.973092</td>\n", "      <td>5.973092</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_ob4_v1__banum_gtlv31_tv</th>\n", "      <td>3641.0</td>\n", "      <td>0.000105</td>\n", "      <td>6.343574e-03</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>0.382776</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_ob4_v1__batv_gtlv31</th>\n", "      <td>3641.0</td>\n", "      <td>0.001578</td>\n", "      <td>9.524210e-02</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>5.746975</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_ob4_v1__cnum_pct_gtlv31_tv</th>\n", "      <td>3641.0</td>\n", "      <td>0.789667</td>\n", "      <td>4.488881e-03</td>\n", "      <td>0.518879</td>\n", "      <td>0.789741</td>\n", "      <td>7.897413e-01</td>\n", "      <td>0.789741</td>\n", "      <td>0.789741</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_ob4_v1__ctv_pct_gtlv31</th>\n", "      <td>3641.0</td>\n", "      <td>5.971605</td>\n", "      <td>8.968877e-02</td>\n", "      <td>0.561209</td>\n", "      <td>5.973092</td>\n", "      <td>5.973092e+00</td>\n", "      <td>5.973092</td>\n", "      <td>5.973092</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_ob4_v1__b2alv1_tvpct</th>\n", "      <td>3641.0</td>\n", "      <td>0.000050</td>\n", "      <td>5.326828e-04</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>4.196619e-07</td>\n", "      <td>0.000004</td>\n", "      <td>0.021130</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lsl__fcts_ob4_v1__a2blv1_tvpct</th>\n", "      <td>3641.0</td>\n", "      <td>0.000039</td>\n", "      <td>3.926072e-04</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>6.796157e-07</td>\n", "      <td>0.000006</td>\n", "      <td>0.012285</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                       count      mean           std  \\\n", "lsl__fcts_ob4_v1__bnum_tylv31_tv      3641.0  0.304664  2.947375e-01   \n", "lsl__fcts_ob4_v1__btv_tylv31          3641.0  2.691259  2.292695e+00   \n", "lsl__fcts_ob4_v1__cnum_tylv31_tv      3641.0  3.154209  3.777532e-01   \n", "lsl__fcts_ob4_v1__ctv_tylv31          3641.0  7.640985  4.028604e-01   \n", "lsl__fcts_ob4_v1__banum_tylv31_tv     3641.0 -0.069235  4.643419e-01   \n", "lsl__fcts_ob4_v1__batv_tylv31         3641.0 -0.328491  4.359200e+00   \n", "lsl__fcts_ob4_v1__cnum_pct_tylv31_tv  3641.0  2.711785  5.198727e-01   \n", "lsl__fcts_ob4_v1__ctv_pct_tylv31      3641.0  3.658715  1.548126e+00   \n", "lsl__fcts_ob4_v1__bnum_gtlv31_tv      3641.0  0.000105  6.343574e-03   \n", "lsl__fcts_ob4_v1__btv_gtlv31          3641.0  0.001578  9.524210e-02   \n", "lsl__fcts_ob4_v1__cnum_gtlv31_tv      3641.0  0.789741  1.110376e-16   \n", "lsl__fcts_ob4_v1__ctv_gtlv31          3641.0  5.973092  8.883004e-16   \n", "lsl__fcts_ob4_v1__banum_gtlv31_tv     3641.0  0.000105  6.343574e-03   \n", "lsl__fcts_ob4_v1__batv_gtlv31         3641.0  0.001578  9.524210e-02   \n", "lsl__fcts_ob4_v1__cnum_pct_gtlv31_tv  3641.0  0.789667  4.488881e-03   \n", "lsl__fcts_ob4_v1__ctv_pct_gtlv31      3641.0  5.971605  8.968877e-02   \n", "lsl__fcts_ob4_v1__b2alv1_tvpct        3641.0  0.000050  5.326828e-04   \n", "lsl__fcts_ob4_v1__a2blv1_tvpct        3641.0  0.000039  3.926072e-04   \n", "\n", "                                           min       25%           50%  \\\n", "lsl__fcts_ob4_v1__bnum_tylv31_tv      0.000000  0.000000  3.827757e-01   \n", "lsl__fcts_ob4_v1__btv_tylv31          0.000000  0.000000  3.958468e+00   \n", "lsl__fcts_ob4_v1__cnum_tylv31_tv      1.082166  3.090611  3.271377e+00   \n", "lsl__fcts_ob4_v1__ctv_tylv31          5.418245  7.564867  7.787714e+00   \n", "lsl__fcts_ob4_v1__banum_tylv31_tv    -1.623495 -0.382776  0.000000e+00   \n", "lsl__fcts_ob4_v1__batv_tylv31        -7.057772 -4.494906  0.000000e+00   \n", "lsl__fcts_ob4_v1__cnum_pct_tylv31_tv  0.150264  2.581156  2.856125e+00   \n", "lsl__fcts_ob4_v1__ctv_pct_tylv31      0.127900  2.801958  3.437473e+00   \n", "lsl__fcts_ob4_v1__bnum_gtlv31_tv      0.000000  0.000000  0.000000e+00   \n", "lsl__fcts_ob4_v1__btv_gtlv31          0.000000  0.000000  0.000000e+00   \n", "lsl__fcts_ob4_v1__cnum_gtlv31_tv      0.789741  0.789741  7.897413e-01   \n", "lsl__fcts_ob4_v1__ctv_gtlv31          5.973092  5.973092  5.973092e+00   \n", "lsl__fcts_ob4_v1__banum_gtlv31_tv     0.000000  0.000000  0.000000e+00   \n", "lsl__fcts_ob4_v1__batv_gtlv31         0.000000  0.000000  0.000000e+00   \n", "lsl__fcts_ob4_v1__cnum_pct_gtlv31_tv  0.518879  0.789741  7.897413e-01   \n", "lsl__fcts_ob4_v1__ctv_pct_gtlv31      0.561209  5.973092  5.973092e+00   \n", "lsl__fcts_ob4_v1__b2alv1_tvpct        0.000000  0.000000  4.196619e-07   \n", "lsl__fcts_ob4_v1__a2blv1_tvpct        0.000000  0.000000  6.796157e-07   \n", "\n", "                                           75%       max  \n", "lsl__fcts_ob4_v1__bnum_tylv31_tv      0.382776  1.415614  \n", "lsl__fcts_ob4_v1__btv_tylv31          4.711504  6.651556  \n", "lsl__fcts_ob4_v1__cnum_tylv31_tv      3.376212  3.522183  \n", "lsl__fcts_ob4_v1__ctv_tylv31          7.851207  7.991587  \n", "lsl__fcts_ob4_v1__banum_tylv31_tv     0.382776  1.302112  \n", "lsl__fcts_ob4_v1__batv_tylv31         4.383672  6.651556  \n", "lsl__fcts_ob4_v1__cnum_pct_tylv31_tv  3.033692  3.506776  \n", "lsl__fcts_ob4_v1__ctv_pct_tylv31      4.074338  7.979431  \n", "lsl__fcts_ob4_v1__bnum_gtlv31_tv      0.000000  0.382776  \n", "lsl__fcts_ob4_v1__btv_gtlv31          0.000000  5.746975  \n", "lsl__fcts_ob4_v1__cnum_gtlv31_tv      0.789741  0.789741  \n", "lsl__fcts_ob4_v1__ctv_gtlv31          5.973092  5.973092  \n", "lsl__fcts_ob4_v1__banum_gtlv31_tv     0.000000  0.382776  \n", "lsl__fcts_ob4_v1__batv_gtlv31         0.000000  5.746975  \n", "lsl__fcts_ob4_v1__cnum_pct_gtlv31_tv  0.789741  0.789741  \n", "lsl__fcts_ob4_v1__ctv_pct_gtlv31      5.973092  5.973092  \n", "lsl__fcts_ob4_v1__b2alv1_tvpct        0.000004  0.021130  \n", "lsl__fcts_ob4_v1__a2blv1_tvpct        0.000006  0.012285  "]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["from shennong.stk import stream\n", "\n", "load_config = { \n", "    'start_datetime': date,\n", "    'end_datetime': date,\n", "    'start_minute': '09:30:00',\n", "    'end_minute': '15:00:00',\n", "    'region_product': 'cn_ashare',\n", "    'freq': 'tick',\n", "    'symbol_list': ['600000.SH', '000001.SZ', '000965.SZ'],\n", "    'load_root': '/mnt/sda/NAS/ShareFolder/lishuanglin/features/',\n", "    'verbose': False,\n", "    'key_group_name': 'lsl__fcts_ob4_v1',\n", "    'timezone': 8,\n", "}\n", "\n", "feature = stream.load(**load_config)\n", "\n", "f1 = feature['000965.SZ']\n", "f1.describe().T"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [], "source": ["# f1.DIY.preview()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[2025-05-28 15:58:45.499593] load symbol list from cn_ashare:None\n", "[2025-05-28 15:58:45.505382] load trading days list from cn_ashare:['2017-01-09']\n", "[2025-05-28 15:58:45.505406] load trading days list from cn_ashare:['2017-01-09']\n", "[2025-05-28 15:58:46.048006] /mnt/sda/NAS/ShareFolder/lishuanglin/features/cn_ashare/1minute/lsl__fcts_auction1 2017-01-09 load complete\n"]}, {"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=dark],\n", "body[data-theme=dark],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1F1F1F;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 20px 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: none;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: '►';\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: '▼';\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: '(';\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: ')';\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: ',';\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.DataArray (SYMBOL: 2841, DATETIME: 241, KEY: 65)&gt; Size: 356MB\n", "array([[[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]],\n", "\n", "       [[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]],\n", "\n", "       [[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "...\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]],\n", "\n", "       [[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]],\n", "\n", "       [[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]]])\n", "Coordinates:\n", "  * SYMBOL    (SYMBOL) &lt;U9 102kB &#x27;000001.SZ&#x27; &#x27;000002.SZ&#x27; ... &#x27;603999.SH&#x27;\n", "  * DATETIME  (DATETIME) datetime64[ns] 2kB 2017-01-09T09:25:00 ... 2017-01-0...\n", "  * KEY       (KEY) &lt;U38 10kB &#x27;lsl__fcts_auction1__cnum&#x27; ... &#x27;lsl__fcts_aucti...</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.DataArray</div><div class='xr-array-name'></div><ul class='xr-dim-list'><li><span class='xr-has-index'>SYMBOL</span>: 2841</li><li><span class='xr-has-index'>DATETIME</span>: 241</li><li><span class='xr-has-index'>KEY</span>: 65</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-f6a208e0-8fe1-42cf-9a23-45292ae13002' class='xr-array-in' type='checkbox' checked><label for='section-f6a208e0-8fe1-42cf-9a23-45292ae13002' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>nan nan nan nan nan nan nan nan ... nan nan nan nan nan nan nan nan</span></div><div class='xr-array-data'><pre>array([[[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]],\n", "\n", "       [[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]],\n", "\n", "       [[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "...\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]],\n", "\n", "       [[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]],\n", "\n", "       [[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]]])</pre></div></div></li><li class='xr-section-item'><input id='section-059a6a83-14cd-4c3f-b0b2-8c942471c9d5' class='xr-section-summary-in' type='checkbox'  checked><label for='section-059a6a83-14cd-4c3f-b0b2-8c942471c9d5' class='xr-section-summary' >Coordinates: <span>(3)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>SYMBOL</span></div><div class='xr-var-dims'>(SYMBOL)</div><div class='xr-var-dtype'>&lt;U9</div><div class='xr-var-preview xr-preview'>&#x27;000001.SZ&#x27; ... &#x27;603999.SH&#x27;</div><input id='attrs-8e10b693-58d6-4371-a2a9-36c37527002a' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-8e10b693-58d6-4371-a2a9-36c37527002a' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-eafe1336-4779-4ef3-9cb6-08ef21a1983a' class='xr-var-data-in' type='checkbox'><label for='data-eafe1336-4779-4ef3-9cb6-08ef21a1983a' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([&#x27;000001.SZ&#x27;, &#x27;000002.SZ&#x27;, &#x27;000004.SZ&#x27;, ..., &#x27;603997.SH&#x27;, &#x27;603998.SH&#x27;,\n", "       &#x27;603999.SH&#x27;], dtype=&#x27;&lt;U9&#x27;)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>DATETIME</span></div><div class='xr-var-dims'>(DATETIME)</div><div class='xr-var-dtype'>datetime64[ns]</div><div class='xr-var-preview xr-preview'>2017-01-09T09:25:00 ... 2017-01-...</div><input id='attrs-b3de3d60-99d8-4bcb-9bf7-7e23b81f4068' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-b3de3d60-99d8-4bcb-9bf7-7e23b81f4068' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-4a0c81f1-0b9b-4912-9f61-c300b8432f64' class='xr-var-data-in' type='checkbox'><label for='data-4a0c81f1-0b9b-4912-9f61-c300b8432f64' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([&#x27;2017-01-09T09:25:00.000000000&#x27;, &#x27;2017-01-09T09:31:00.000000000&#x27;,\n", "       &#x27;2017-01-09T09:32:00.000000000&#x27;, ..., &#x27;2017-01-09T14:58:00.000000000&#x27;,\n", "       &#x27;2017-01-09T14:59:00.000000000&#x27;, &#x27;2017-01-09T15:00:00.000000000&#x27;],\n", "      dtype=&#x27;datetime64[ns]&#x27;)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>KEY</span></div><div class='xr-var-dims'>(KEY)</div><div class='xr-var-dtype'>&lt;U38</div><div class='xr-var-preview xr-preview'>&#x27;lsl__fcts_auction1__cnum&#x27; ... &#x27;...</div><input id='attrs-d3bee035-8136-4b56-a478-04af742f19d2' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-d3bee035-8136-4b56-a478-04af742f19d2' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-62da2de0-156b-4e43-9eb0-10203c5cac4f' class='xr-var-data-in' type='checkbox'><label for='data-62da2de0-156b-4e43-9eb0-10203c5cac4f' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([&#x27;lsl__fcts_auction1__cnum&#x27;, &#x27;lsl__fcts_auction1__cnum_bid_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__cvol_bid_pct&#x27;, &#x27;lsl__fcts_auction1__anum_bid_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__bcj_amnt&#x27;, &#x27;lsl__fcts_auction1__acj_num&#x27;,\n", "       &#x27;lsl__fcts_auction1__bcj_num_pct&#x27;, &#x27;lsl__fcts_auction1__cj_amnt_mean&#x27;,\n", "       &#x27;lsl__fcts_auction1__bcj_amnt_mean&#x27;, &#x27;lsl__fcts_auction1__bcj_amnt_std&#x27;,\n", "       &#x27;lsl__fcts_auction1__bcj_amnt_rms&#x27;, &#x27;lsl__fcts_auction1__cj_cret&#x27;,\n", "       &#x27;lsl__fcts_auction1__cj_oret&#x27;, &#x27;lsl__fcts_auction1__cj_vol_ratio&#x27;,\n", "       &#x27;lsl__fcts_auction1__untd_num&#x27;, &#x27;lsl__fcts_auction1__bid_untd_num_apct&#x27;,\n", "       &#x27;lsl__fcts_auction1__bid_untd_vol_apct&#x27;,\n", "       &#x27;lsl__fcts_auction1__ask_untd_num_apct&#x27;,\n", "       &#x27;lsl__fcts_auction1__ask_untd_vol_apct&#x27;,\n", "       &#x27;lsl__fcts_auction1__cd_bid_vol&#x27;, &#x27;lsl__fcts_auction1__cd_bid_num_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__cd_ask_num&#x27;, &#x27;lsl__fcts_auction1__cd_ask_vol&#x27;,\n", "       &#x27;lsl__fcts_auction1__cd_ask_vol_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__tgd1_bnum_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__tgd1_anum_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__tgd1_tvol_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__tgd1_tamnt_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__bid_cnum_pct&#x27;, &#x27;lsl__fcts_auction1__bid_cvol_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__ask_cvol_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__match_num_bratio&#x27;,\n", "       &#x27;lsl__fcts_auction1__bid_match_num&#x27;,\n", "       &#x27;lsl__fcts_auction1__bid_match_vol&#x27;,\n", "       &#x27;lsl__fcts_auction1__bid_match_num_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__bid_match_vol_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__ask_match_num_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__ask_match_vol_tpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__ask_match_vol_apct&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_anum&#x27;, &#x27;lsl__fcts_auction1__gd1_bnum_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_anum_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_banum_ratio&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_bvol_pct&#x27;, &#x27;lsl__fcts_auction1__gd1_avol_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_bavol_ratio&#x27;, &#x27;lsl__fcts_auction1__gd2_num&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd2_bnum&#x27;, &#x27;lsl__fcts_auction1__gd2_anum&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd2_bnum_pct&#x27;, &#x27;lsl__fcts_auction1__gd2_anum_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd2_avol&#x27;, &#x27;lsl__fcts_auction1__gd2_bamnt&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd2_aamnt_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_num_std_3S&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_bnum_mean_3S&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_bamnt_mean&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_price_mean&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_price_skew2&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_price_rms&#x27;, &#x27;lsl__fcts_auction1__jj_mvol_mean&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_mvol_skew2&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_bunmvol_range&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_bunmvol_mean&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_aunmvol_range&#x27;], dtype=&#x27;&lt;U38&#x27;)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-bfe70ca2-9bc6-480b-a1a8-39a07277412b' class='xr-section-summary-in' type='checkbox'  ><label for='section-bfe70ca2-9bc6-480b-a1a8-39a07277412b' class='xr-section-summary' >Indexes: <span>(3)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>SYMBOL</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-2bcd9820-19b4-4a2e-ae97-d4049f12bbd8' class='xr-index-data-in' type='checkbox'/><label for='index-2bcd9820-19b4-4a2e-ae97-d4049f12bbd8' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([&#x27;000001.SZ&#x27;, &#x27;000002.SZ&#x27;, &#x27;000004.SZ&#x27;, &#x27;000005.SZ&#x27;, &#x27;000006.SZ&#x27;,\n", "       &#x27;000007.SZ&#x27;, &#x27;000008.SZ&#x27;, &#x27;000009.SZ&#x27;, &#x27;000010.SZ&#x27;, &#x27;000011.SZ&#x27;,\n", "       ...\n", "       &#x27;603979.SH&#x27;, &#x27;603987.SH&#x27;, &#x27;603988.SH&#x27;, &#x27;603989.SH&#x27;, &#x27;603990.SH&#x27;,\n", "       &#x27;603993.SH&#x27;, &#x27;603996.SH&#x27;, &#x27;603997.SH&#x27;, &#x27;603998.SH&#x27;, &#x27;603999.SH&#x27;],\n", "      dtype=&#x27;object&#x27;, name=&#x27;SYMBOL&#x27;, length=2841))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>DATETIME</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-b9efd5c6-d0f5-4fae-aa0a-d628ef8f02dd' class='xr-index-data-in' type='checkbox'/><label for='index-b9efd5c6-d0f5-4fae-aa0a-d628ef8f02dd' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(DatetimeIndex([&#x27;2017-01-09 09:25:00&#x27;, &#x27;2017-01-09 09:31:00&#x27;,\n", "               &#x27;2017-01-09 09:32:00&#x27;, &#x27;2017-01-09 09:33:00&#x27;,\n", "               &#x27;2017-01-09 09:34:00&#x27;, &#x27;2017-01-09 09:35:00&#x27;,\n", "               &#x27;2017-01-09 09:36:00&#x27;, &#x27;2017-01-09 09:37:00&#x27;,\n", "               &#x27;2017-01-09 09:38:00&#x27;, &#x27;2017-01-09 09:39:00&#x27;,\n", "               ...\n", "               &#x27;2017-01-09 14:51:00&#x27;, &#x27;2017-01-09 14:52:00&#x27;,\n", "               &#x27;2017-01-09 14:53:00&#x27;, &#x27;2017-01-09 14:54:00&#x27;,\n", "               &#x27;2017-01-09 14:55:00&#x27;, &#x27;2017-01-09 14:56:00&#x27;,\n", "               &#x27;2017-01-09 14:57:00&#x27;, &#x27;2017-01-09 14:58:00&#x27;,\n", "               &#x27;2017-01-09 14:59:00&#x27;, &#x27;2017-01-09 15:00:00&#x27;],\n", "              dtype=&#x27;datetime64[ns]&#x27;, name=&#x27;DATETIME&#x27;, length=241, freq=None))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>KEY</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-5703f4d6-309c-4340-bb5b-7528fa724977' class='xr-index-data-in' type='checkbox'/><label for='index-5703f4d6-309c-4340-bb5b-7528fa724977' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([&#x27;lsl__fcts_auction1__cnum&#x27;, &#x27;lsl__fcts_auction1__cnum_bid_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__cvol_bid_pct&#x27;, &#x27;lsl__fcts_auction1__anum_bid_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__bcj_amnt&#x27;, &#x27;lsl__fcts_auction1__acj_num&#x27;,\n", "       &#x27;lsl__fcts_auction1__bcj_num_pct&#x27;, &#x27;lsl__fcts_auction1__cj_amnt_mean&#x27;,\n", "       &#x27;lsl__fcts_auction1__bcj_amnt_mean&#x27;, &#x27;lsl__fcts_auction1__bcj_amnt_std&#x27;,\n", "       &#x27;lsl__fcts_auction1__bcj_amnt_rms&#x27;, &#x27;lsl__fcts_auction1__cj_cret&#x27;,\n", "       &#x27;lsl__fcts_auction1__cj_oret&#x27;, &#x27;lsl__fcts_auction1__cj_vol_ratio&#x27;,\n", "       &#x27;lsl__fcts_auction1__untd_num&#x27;, &#x27;lsl__fcts_auction1__bid_untd_num_apct&#x27;,\n", "       &#x27;lsl__fcts_auction1__bid_untd_vol_apct&#x27;,\n", "       &#x27;lsl__fcts_auction1__ask_untd_num_apct&#x27;,\n", "       &#x27;lsl__fcts_auction1__ask_untd_vol_apct&#x27;,\n", "       &#x27;lsl__fcts_auction1__cd_bid_vol&#x27;, &#x27;lsl__fcts_auction1__cd_bid_num_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__cd_ask_num&#x27;, &#x27;lsl__fcts_auction1__cd_ask_vol&#x27;,\n", "       &#x27;lsl__fcts_auction1__cd_ask_vol_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__tgd1_bnum_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__tgd1_anum_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__tgd1_tvol_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__tgd1_tamnt_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__bid_cnum_pct&#x27;, &#x27;lsl__fcts_auction1__bid_cvol_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__ask_cvol_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__match_num_bratio&#x27;,\n", "       &#x27;lsl__fcts_auction1__bid_match_num&#x27;,\n", "       &#x27;lsl__fcts_auction1__bid_match_vol&#x27;,\n", "       &#x27;lsl__fcts_auction1__bid_match_num_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__bid_match_vol_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__ask_match_num_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__ask_match_vol_tpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__ask_match_vol_apct&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_anum&#x27;, &#x27;lsl__fcts_auction1__gd1_bnum_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_anum_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_banum_ratio&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_bvol_pct&#x27;, &#x27;lsl__fcts_auction1__gd1_avol_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_bavol_ratio&#x27;, &#x27;lsl__fcts_auction1__gd2_num&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd2_bnum&#x27;, &#x27;lsl__fcts_auction1__gd2_anum&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd2_bnum_pct&#x27;, &#x27;lsl__fcts_auction1__gd2_anum_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd2_avol&#x27;, &#x27;lsl__fcts_auction1__gd2_bamnt&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd2_aamnt_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_num_std_3S&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_bnum_mean_3S&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_bamnt_mean&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_price_mean&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_price_skew2&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_price_rms&#x27;, &#x27;lsl__fcts_auction1__jj_mvol_mean&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_mvol_skew2&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_bunmvol_range&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_bunmvol_mean&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_aunmvol_range&#x27;],\n", "      dtype=&#x27;object&#x27;, name=&#x27;KEY&#x27;))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-f9107ddf-fcf0-41d7-9849-8d6496f5c8a9' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-f9107ddf-fcf0-41d7-9849-8d6496f5c8a9' class='xr-section-summary'  title='Expand/collapse section'>Attributes: <span>(0)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.DataArray (SYMBOL: 2841, DATETIME: 241, KEY: 65)> Size: 356MB\n", "array([[[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]],\n", "\n", "       [[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]],\n", "\n", "       [[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "...\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]],\n", "\n", "       [[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]],\n", "\n", "       [[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]]])\n", "Coordinates:\n", "  * SYMBOL    (SYMBOL) <U9 102kB '000001.SZ' '000002.SZ' ... '603999.SH'\n", "  * DATETIME  (DATETIME) datetime64[ns] 2kB 2017-01-09T09:25:00 ... 2017-01-0...\n", "  * KEY       (KEY) <U38 10kB 'lsl__fcts_auction1__cnum' ... 'lsl__fcts_aucti..."]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "feature_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/features/'\n", "region, product = 'cn', 'ashare'\n", "freq = '1minute'\n", "feature_group_name = 'lsl__fcts_auction1'\n", "\n", "feature_dir = f'{feature_root}{region}_{product}/{freq}/{feature_group_name}/'\n", "\n", "dates = sorted([s.split('.')[0] for s in os.listdir(feature_dir) if s.endswith('.h5')])\n", "\n", "# for date in tqdm(dates):\n", "#     try:\n", "#         with h5py.File(f'{feature_dir}{date}.h5','r+') as file:\n", "#             keys = file['KEY'][:].astype(np.str_)\n", "#             del file['KEY']\n", "#             file['KEY'] = np.array(new_keys).astype('S')\n", "#     except Exception as err:\n", "#         print(date, err)\n", "\n", "from shennong.stk import bar\n", "\n", "xrdt = bar.load(\n", "    start_datetime='2017-01-09',\n", "    end_datetime='2017-01-09',\n", "    start_minute='09:15:00',\n", "    end_minute='15:00:00',\n", "    region_product='cn_ashare',\n", "    freq='1minute',\n", "    load_root='/mnt/sda/NAS/ShareFolder/lishuanglin/features/',\n", "    key_list='lsl__fcts_auction1',\n", ")\n", "xrdt"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["8\n"]}], "source": ["import h5py\n", "with h5py.File('/mnt/sda/NAS/ShareFolder/lishuanglin/features/cn_ashare/1minute/lsl__fcts_auction1/2017-01-09.h5','r') as file:\n", "    print(file.attrs['timezone'])"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=dark],\n", "body[data-theme=dark],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1F1F1F;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 20px 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: none;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: '►';\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: '▼';\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: '(';\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: ')';\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: ',';\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.DataArray (SYMBOL: 3486, DATETIME: 241, KEY: 65)&gt; Size: 437MB\n", "array([[[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]],\n", "\n", "       [[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]],\n", "\n", "       [[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "...\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]],\n", "\n", "       [[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]],\n", "\n", "       [[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]]])\n", "Coordinates:\n", "  * SYMBOL    (SYMBOL) &lt;U9 125kB &#x27;000001.SZ&#x27; &#x27;000002.SZ&#x27; ... &#x27;603999.SH&#x27;\n", "  * DATETIME  (DATETIME) datetime64[ns] 2kB 2018-01-09T09:25:00 ... 2018-01-0...\n", "  * KEY       (KEY) &lt;U38 10kB &#x27;lsl__fcts_auction1__cnum&#x27; ... &#x27;lsl__fcts_aucti...</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.DataArray</div><div class='xr-array-name'></div><ul class='xr-dim-list'><li><span class='xr-has-index'>SYMBOL</span>: 3486</li><li><span class='xr-has-index'>DATETIME</span>: 241</li><li><span class='xr-has-index'>KEY</span>: 65</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-216afb2e-6e0b-4d78-8aa7-b4a7055fee17' class='xr-array-in' type='checkbox' checked><label for='section-216afb2e-6e0b-4d78-8aa7-b4a7055fee17' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>nan nan nan nan nan nan nan nan ... nan nan nan nan nan nan nan nan</span></div><div class='xr-array-data'><pre>array([[[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]],\n", "\n", "       [[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]],\n", "\n", "       [[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "...\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]],\n", "\n", "       [[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]],\n", "\n", "       [[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]]])</pre></div></div></li><li class='xr-section-item'><input id='section-4626332d-3161-4b89-a2e4-1dd050bc39a3' class='xr-section-summary-in' type='checkbox'  checked><label for='section-4626332d-3161-4b89-a2e4-1dd050bc39a3' class='xr-section-summary' >Coordinates: <span>(3)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>SYMBOL</span></div><div class='xr-var-dims'>(SYMBOL)</div><div class='xr-var-dtype'>&lt;U9</div><div class='xr-var-preview xr-preview'>&#x27;000001.SZ&#x27; ... &#x27;603999.SH&#x27;</div><input id='attrs-ece50f17-af09-4f2f-a7da-28a00b5b5423' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-ece50f17-af09-4f2f-a7da-28a00b5b5423' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-6450027c-3df9-4779-8569-00329f9f6c64' class='xr-var-data-in' type='checkbox'><label for='data-6450027c-3df9-4779-8569-00329f9f6c64' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([&#x27;000001.SZ&#x27;, &#x27;000002.SZ&#x27;, &#x27;000004.SZ&#x27;, ..., &#x27;603997.SH&#x27;, &#x27;603998.SH&#x27;,\n", "       &#x27;603999.SH&#x27;], dtype=&#x27;&lt;U9&#x27;)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>DATETIME</span></div><div class='xr-var-dims'>(DATETIME)</div><div class='xr-var-dtype'>datetime64[ns]</div><div class='xr-var-preview xr-preview'>2018-01-09T09:25:00 ... 2018-01-...</div><input id='attrs-66b0c901-2345-440e-a36f-92700f7d5174' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-66b0c901-2345-440e-a36f-92700f7d5174' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-191bc1b6-5565-4bc7-ad99-55832e1b2032' class='xr-var-data-in' type='checkbox'><label for='data-191bc1b6-5565-4bc7-ad99-55832e1b2032' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([&#x27;2018-01-09T09:25:00.000000000&#x27;, &#x27;2018-01-09T09:31:00.000000000&#x27;,\n", "       &#x27;2018-01-09T09:32:00.000000000&#x27;, ..., &#x27;2018-01-09T14:58:00.000000000&#x27;,\n", "       &#x27;2018-01-09T14:59:00.000000000&#x27;, &#x27;2018-01-09T15:00:00.000000000&#x27;],\n", "      dtype=&#x27;datetime64[ns]&#x27;)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>KEY</span></div><div class='xr-var-dims'>(KEY)</div><div class='xr-var-dtype'>&lt;U38</div><div class='xr-var-preview xr-preview'>&#x27;lsl__fcts_auction1__cnum&#x27; ... &#x27;...</div><input id='attrs-9dcab851-f749-4b92-a8b6-a702d75adc1a' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-9dcab851-f749-4b92-a8b6-a702d75adc1a' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-e0523872-b7c5-40c7-bdd3-93062eec359a' class='xr-var-data-in' type='checkbox'><label for='data-e0523872-b7c5-40c7-bdd3-93062eec359a' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([&#x27;lsl__fcts_auction1__cnum&#x27;, &#x27;lsl__fcts_auction1__cnum_bid_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__cvol_bid_pct&#x27;, &#x27;lsl__fcts_auction1__anum_bid_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__bcj_amnt&#x27;, &#x27;lsl__fcts_auction1__acj_num&#x27;,\n", "       &#x27;lsl__fcts_auction1__bcj_num_pct&#x27;, &#x27;lsl__fcts_auction1__cj_amnt_mean&#x27;,\n", "       &#x27;lsl__fcts_auction1__bcj_amnt_mean&#x27;, &#x27;lsl__fcts_auction1__bcj_amnt_std&#x27;,\n", "       &#x27;lsl__fcts_auction1__bcj_amnt_rms&#x27;, &#x27;lsl__fcts_auction1__cj_cret&#x27;,\n", "       &#x27;lsl__fcts_auction1__cj_oret&#x27;, &#x27;lsl__fcts_auction1__cj_vol_ratio&#x27;,\n", "       &#x27;lsl__fcts_auction1__untd_num&#x27;, &#x27;lsl__fcts_auction1__bid_untd_num_apct&#x27;,\n", "       &#x27;lsl__fcts_auction1__bid_untd_vol_apct&#x27;,\n", "       &#x27;lsl__fcts_auction1__ask_untd_num_apct&#x27;,\n", "       &#x27;lsl__fcts_auction1__ask_untd_vol_apct&#x27;,\n", "       &#x27;lsl__fcts_auction1__cd_bid_vol&#x27;, &#x27;lsl__fcts_auction1__cd_bid_num_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__cd_ask_num&#x27;, &#x27;lsl__fcts_auction1__cd_ask_vol&#x27;,\n", "       &#x27;lsl__fcts_auction1__cd_ask_vol_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__tgd1_bnum_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__tgd1_anum_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__tgd1_tvol_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__tgd1_tamnt_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__bid_cnum_pct&#x27;, &#x27;lsl__fcts_auction1__bid_cvol_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__ask_cvol_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__match_num_bratio&#x27;,\n", "       &#x27;lsl__fcts_auction1__bid_match_num&#x27;,\n", "       &#x27;lsl__fcts_auction1__bid_match_vol&#x27;,\n", "       &#x27;lsl__fcts_auction1__bid_match_num_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__bid_match_vol_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__ask_match_num_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__ask_match_vol_tpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__ask_match_vol_apct&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_anum&#x27;, &#x27;lsl__fcts_auction1__gd1_bnum_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_anum_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_banum_ratio&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_bvol_pct&#x27;, &#x27;lsl__fcts_auction1__gd1_avol_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_bavol_ratio&#x27;, &#x27;lsl__fcts_auction1__gd2_num&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd2_bnum&#x27;, &#x27;lsl__fcts_auction1__gd2_anum&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd2_bnum_pct&#x27;, &#x27;lsl__fcts_auction1__gd2_anum_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd2_avol&#x27;, &#x27;lsl__fcts_auction1__gd2_bamnt&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd2_aamnt_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_num_std_3S&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_bnum_mean_3S&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_bamnt_mean&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_price_mean&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_price_skew2&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_price_rms&#x27;, &#x27;lsl__fcts_auction1__jj_mvol_mean&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_mvol_skew2&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_bunmvol_range&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_bunmvol_mean&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_aunmvol_range&#x27;], dtype=&#x27;&lt;U38&#x27;)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-03c01949-3a33-48b1-b416-bb4c6a4287f1' class='xr-section-summary-in' type='checkbox'  ><label for='section-03c01949-3a33-48b1-b416-bb4c6a4287f1' class='xr-section-summary' >Indexes: <span>(3)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>SYMBOL</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-844a67b3-d41f-40d4-80a4-51c080538c35' class='xr-index-data-in' type='checkbox'/><label for='index-844a67b3-d41f-40d4-80a4-51c080538c35' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([&#x27;000001.SZ&#x27;, &#x27;000002.SZ&#x27;, &#x27;000004.SZ&#x27;, &#x27;000005.SZ&#x27;, &#x27;000006.SZ&#x27;,\n", "       &#x27;000007.SZ&#x27;, &#x27;000008.SZ&#x27;, &#x27;000009.SZ&#x27;, &#x27;000010.SZ&#x27;, &#x27;000011.SZ&#x27;,\n", "       ...\n", "       &#x27;603987.SH&#x27;, &#x27;603988.SH&#x27;, &#x27;603989.SH&#x27;, &#x27;603990.SH&#x27;, &#x27;603991.SH&#x27;,\n", "       &#x27;603993.SH&#x27;, &#x27;603996.SH&#x27;, &#x27;603997.SH&#x27;, &#x27;603998.SH&#x27;, &#x27;603999.SH&#x27;],\n", "      dtype=&#x27;object&#x27;, name=&#x27;SYMBOL&#x27;, length=3486))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>DATETIME</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-26036eeb-fb1a-4655-8c9d-a6f3023e86a3' class='xr-index-data-in' type='checkbox'/><label for='index-26036eeb-fb1a-4655-8c9d-a6f3023e86a3' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(DatetimeIndex([&#x27;2018-01-09 09:25:00&#x27;, &#x27;2018-01-09 09:31:00&#x27;,\n", "               &#x27;2018-01-09 09:32:00&#x27;, &#x27;2018-01-09 09:33:00&#x27;,\n", "               &#x27;2018-01-09 09:34:00&#x27;, &#x27;2018-01-09 09:35:00&#x27;,\n", "               &#x27;2018-01-09 09:36:00&#x27;, &#x27;2018-01-09 09:37:00&#x27;,\n", "               &#x27;2018-01-09 09:38:00&#x27;, &#x27;2018-01-09 09:39:00&#x27;,\n", "               ...\n", "               &#x27;2018-01-09 14:51:00&#x27;, &#x27;2018-01-09 14:52:00&#x27;,\n", "               &#x27;2018-01-09 14:53:00&#x27;, &#x27;2018-01-09 14:54:00&#x27;,\n", "               &#x27;2018-01-09 14:55:00&#x27;, &#x27;2018-01-09 14:56:00&#x27;,\n", "               &#x27;2018-01-09 14:57:00&#x27;, &#x27;2018-01-09 14:58:00&#x27;,\n", "               &#x27;2018-01-09 14:59:00&#x27;, &#x27;2018-01-09 15:00:00&#x27;],\n", "              dtype=&#x27;datetime64[ns]&#x27;, name=&#x27;DATETIME&#x27;, length=241, freq=None))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>KEY</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-5d434dc1-83d3-4214-b4b6-169a50e09a1f' class='xr-index-data-in' type='checkbox'/><label for='index-5d434dc1-83d3-4214-b4b6-169a50e09a1f' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([&#x27;lsl__fcts_auction1__cnum&#x27;, &#x27;lsl__fcts_auction1__cnum_bid_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__cvol_bid_pct&#x27;, &#x27;lsl__fcts_auction1__anum_bid_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__bcj_amnt&#x27;, &#x27;lsl__fcts_auction1__acj_num&#x27;,\n", "       &#x27;lsl__fcts_auction1__bcj_num_pct&#x27;, &#x27;lsl__fcts_auction1__cj_amnt_mean&#x27;,\n", "       &#x27;lsl__fcts_auction1__bcj_amnt_mean&#x27;, &#x27;lsl__fcts_auction1__bcj_amnt_std&#x27;,\n", "       &#x27;lsl__fcts_auction1__bcj_amnt_rms&#x27;, &#x27;lsl__fcts_auction1__cj_cret&#x27;,\n", "       &#x27;lsl__fcts_auction1__cj_oret&#x27;, &#x27;lsl__fcts_auction1__cj_vol_ratio&#x27;,\n", "       &#x27;lsl__fcts_auction1__untd_num&#x27;, &#x27;lsl__fcts_auction1__bid_untd_num_apct&#x27;,\n", "       &#x27;lsl__fcts_auction1__bid_untd_vol_apct&#x27;,\n", "       &#x27;lsl__fcts_auction1__ask_untd_num_apct&#x27;,\n", "       &#x27;lsl__fcts_auction1__ask_untd_vol_apct&#x27;,\n", "       &#x27;lsl__fcts_auction1__cd_bid_vol&#x27;, &#x27;lsl__fcts_auction1__cd_bid_num_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__cd_ask_num&#x27;, &#x27;lsl__fcts_auction1__cd_ask_vol&#x27;,\n", "       &#x27;lsl__fcts_auction1__cd_ask_vol_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__tgd1_bnum_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__tgd1_anum_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__tgd1_tvol_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__tgd1_tamnt_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__bid_cnum_pct&#x27;, &#x27;lsl__fcts_auction1__bid_cvol_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__ask_cvol_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__match_num_bratio&#x27;,\n", "       &#x27;lsl__fcts_auction1__bid_match_num&#x27;,\n", "       &#x27;lsl__fcts_auction1__bid_match_vol&#x27;,\n", "       &#x27;lsl__fcts_auction1__bid_match_num_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__bid_match_vol_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__ask_match_num_bpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__ask_match_vol_tpct&#x27;,\n", "       &#x27;lsl__fcts_auction1__ask_match_vol_apct&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_anum&#x27;, &#x27;lsl__fcts_auction1__gd1_bnum_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_anum_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_banum_ratio&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_bvol_pct&#x27;, &#x27;lsl__fcts_auction1__gd1_avol_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_bavol_ratio&#x27;, &#x27;lsl__fcts_auction1__gd2_num&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd2_bnum&#x27;, &#x27;lsl__fcts_auction1__gd2_anum&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd2_bnum_pct&#x27;, &#x27;lsl__fcts_auction1__gd2_anum_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd2_avol&#x27;, &#x27;lsl__fcts_auction1__gd2_bamnt&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd2_aamnt_pct&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_num_std_3S&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_bnum_mean_3S&#x27;,\n", "       &#x27;lsl__fcts_auction1__gd1_bamnt_mean&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_price_mean&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_price_skew2&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_price_rms&#x27;, &#x27;lsl__fcts_auction1__jj_mvol_mean&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_mvol_skew2&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_bunmvol_range&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_bunmvol_mean&#x27;,\n", "       &#x27;lsl__fcts_auction1__jj_aunmvol_range&#x27;],\n", "      dtype=&#x27;object&#x27;, name=&#x27;KEY&#x27;))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-10ff00e6-08ab-4b48-94be-ec87d5e03694' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-10ff00e6-08ab-4b48-94be-ec87d5e03694' class='xr-section-summary'  title='Expand/collapse section'>Attributes: <span>(0)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.DataArray (SYMBOL: 3486, DATETIME: 241, KEY: 65)> Size: 437MB\n", "array([[[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]],\n", "\n", "       [[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]],\n", "\n", "       [[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "...\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]],\n", "\n", "       [[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]],\n", "\n", "       [[nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        ...,\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan],\n", "        [nan, nan, nan, ..., nan, nan, nan]]])\n", "Coordinates:\n", "  * SYMBOL    (SYMBOL) <U9 125kB '000001.SZ' '000002.SZ' ... '603999.SH'\n", "  * DATETIME  (DATETIME) datetime64[ns] 2kB 2018-01-09T09:25:00 ... 2018-01-0...\n", "  * KEY       (KEY) <U38 10kB 'lsl__fcts_auction1__cnum' ... 'lsl__fcts_aucti..."]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "xrdt[:,0,:] = np.nan\n", "xrdt"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7605ec7d1a1a4216b9fb94812cd286f3", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/3237 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import h5py\n", "from tqdm.auto import tqdm\n", "\n", "feature_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/features/'\n", "region, product = 'cn', 'ashare'\n", "freq = '1minute'\n", "feature_group_name = 'lsl__fcts_auction1'\n", "\n", "feature_dir = f'{feature_root}{region}_{product}/{freq}/{feature_group_name}/'\n", "\n", "dates = sorted([s.split('.')[0] for s in os.listdir(feature_dir) if s.endswith('.h5')])\n", "\n", "for date in tqdm(dates):\n", "    try:\n", "        with h5py.File(f'{feature_dir}{date}.h5','r') as file:\n", "            timezone = file.attrs['timezone']\n", "            if timezone != 8:\n", "                print(date, timezone)\n", "                # file.attrs['timezone'] = np.int32(8)\n", "    except Exception as err:\n", "        print(date, err)\n", "\n", "# with h5py.File(f'{feature_dir}{date}.h5','r+') as file:\n", "    # print(file['DATA'].shape)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'f1' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/mnt/sda/home/<USER>/FC/hdfview_file.ipynb Cell 23\u001b[0m line \u001b[0;36m1\n\u001b[0;32m----> <a href='vscode-notebook-cell://ssh-remote%2B10.218.224.7/mnt/sda/home/<USER>/FC/hdfview_file.ipynb#X25sdnNjb2RlLXJlbW90ZQ%3D%3D?line=0'>1</a>\u001b[0m time_ticks \u001b[39m=\u001b[39m f1[\u001b[39m'\u001b[39m\u001b[39mDATETIME\u001b[39m\u001b[39m'\u001b[39m]\u001b[39m.\u001b[39mdt\u001b[39m.\u001b[39mtime\u001b[39m.\u001b[39mastype(np\u001b[39m.\u001b[39mstr_)\u001b[39m.\u001b[39mto_numpy()\n", "\u001b[0;31mNameError\u001b[0m: name 'f1' is not defined"]}], "source": ["time_ticks = f1['DATETIME'].dt.time.astype(np.str_).to_numpy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["time_ticks"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["['2013-03-11', '2013-08-20', '2013-08-21', '2017-01-09']"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["import xarray as xr\n", "from shennong.stk import bar\n", "from shennong.utils import trading_days\n", "\n", "freq = '1minute'\n", "python_dir = '/mnt/sda/NAS/ShareFolder/lishuanglin/crontab/auction1_raw_best65_final/'\n", "fc_dir = (\n", "\tf'/mnt/sda/NAS/ShareFolder/lishuanglin/features/cn_ashare/{freq}/lsl__fcts_auction1/'\n", ")\n", "\n", "td_days = trading_days.load(region='cn', product='ashare', start_datetime='2012-01-01',end_datetime='2025-05-27')\n", "\n", "dates1 = sorted([s.split('.')[0] for s in os.listdir(python_dir) if s.endswith('.csv')])\n", "\n", "sorted(set(td_days) - set(dates1))\n", "\n", "# dates2 = sorted([s.split('.')[0] for s in os.listdir(fc_dir) if s.endswith('.h5')])\n", "\n", "# dates = sorted(set(dates1) - set(dates2))\n", "\n", "# with h5py.File(f'{fc_dir}{dates2[0]}.h5', 'r') as file:\n", "# \tkeys = file['KEY'][:].astype(np.str_)\n", "# \tdatetimes = file['DATETIME'][:]\n", "\n", "# # time_ticks = ['09:25:00', '10:30:00', '11:30:00', '14:00:00', '15:00:00']\n", "# # time_ticks = (f1['DATETIME'] + pd.<PERSON><PERSON><PERSON>(hours=8)).dt.time.astype(np.str_)\n", "\n", "# for date in tqdm(dates[:]):\n", "# \tdf = pd.read_pickle(f'{python_dir}{date}.pkl')\n", "# \tdt = df.T.values[np.newaxis, :, :].repeat(241, axis=0)\n", "# \tdatetimes = [f'{date} {t}' for t in time_ticks]\n", "# \tdatetiemes = pd.to_datetime(datetimes)# - pd.Timedel<PERSON>(hours=8)\n", "# \tcorrds = (('DATETIME', datetiemes), ('KEY', keys), ('SYMBOL', df.index))\n", "# \txrdt = xr.<PERSON>(dt, coords=corrds)\n", "# \txrdt[1:,:,:] = np.nan\n", "# \tbar.save(xrdt.transpose('SY<PERSON><PERSON>', 'DATETIME', 'KEY'), 'cn_ashare', freq=freq, key_group_name='lsl__fcts_auction1', save_root='/mnt/sda/NAS/ShareFolder/lishuanglin/features/', timezone=0, verbose=False)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["time_ticks = xrdt['DATETIME'].dt.time.astype(np.str_).to_numpy()\n", "time_ticks"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["# time_ticks = xrdt['DATETIME'].dt.time.astype(np.str_).to_numpy()\n", "# time_ticks"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6563325398534a7ead7e0e47b7cc597f", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/4 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[2025-05-28 15:58:17.845595] Saved h5 file:/mnt/sda/NAS/ShareFolder/lishuanglin/features/cn_ashare/1minute/lsl__fcts_auction1/2013-03-11.h5\n", "generate h5 key stucture end,root=/mnt/sda/NAS/ShareFolder/lishuanglin/features/cn_ashare/1minute/lsl__fcts_auction1\n", "[2025-05-28 15:58:20.845099] Saved h5 file:/mnt/sda/NAS/ShareFolder/lishuanglin/features/cn_ashare/1minute/lsl__fcts_auction1/2013-08-20.h5\n", "generate h5 key stucture end,root=/mnt/sda/NAS/ShareFolder/lishuanglin/features/cn_ashare/1minute/lsl__fcts_auction1\n", "[2025-05-28 15:58:23.799157] Saved h5 file:/mnt/sda/NAS/ShareFolder/lishuanglin/features/cn_ashare/1minute/lsl__fcts_auction1/2013-08-21.h5\n", "generate h5 key stucture end,root=/mnt/sda/NAS/ShareFolder/lishuanglin/features/cn_ashare/1minute/lsl__fcts_auction1\n", "[2025-05-28 15:58:31.703099] Saved h5 file:/mnt/sda/NAS/ShareFolder/lishuanglin/features/cn_ashare/1minute/lsl__fcts_auction1/2017-01-09.h5\n", "generate h5 key stucture end,root=/mnt/sda/NAS/ShareFolder/lishuanglin/features/cn_ashare/1minute/lsl__fcts_auction1\n"]}], "source": ["import pandas as pd\n", "from tqdm.auto import tqdm\n", "special_dates = ['2013-03-11', '2013-08-20', '2013-08-21', '2017-01-09']\n", "\n", "for date in tqdm(special_dates):\n", "\tdatetimes = [f'{date} {t}' for t in time_ticks]\n", "\tdatetiemes = pd.to_datetime(datetimes)\n", "\t# corrds = (('DATETIME', datetiemes), ('KEY', keys), ('SYMBOL', df.index))\n", "\t# xrdt = xr.<PERSON>(dt, coords=corrds)\n", "\t# xrdt[:,0,:] = np.nan\n", "\txrdt['DATETIME'] = datetiemes\n", "\tbar.save(xrdt.transpose('SY<PERSON><PERSON>', 'DATETIM<PERSON>', 'KEY'), 'cn_ashare', freq='1minute', key_group_name='lsl__fcts_auction1', save_root='/mnt/sda/NAS/ShareFolder/lishuanglin/features/', timezone=8, verbose=True)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["# feature_root = '/mnt/sda/NAS/ShareFolder/chenshenghong/cn_ashare/data/'\n", "# region, product, freq = 'cn', 'ashare', '3second'\n", "# key_group_name = 'ohlcvt'\n", "# sym_list = ['000001.SZ']\n", "\n", "# feature_dir = f'{feature_root}{region}_{product}/'\n", "\n", "# date = '2023-08-29'\n", "# f = bar.load(feature_dir, freq, key_group_name, date, load_with='h5py', sym_list=None)\n", "# f = f.sel({'SYMBOL':sym_list[0]}).to_pandas()\n", "# f.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [], "source": ["# for cn_ashare test tick data\n", "region_product, date, sym = 'cn_ashare', date, '000001.SZ'\n", "trade = data.load_1sym_tick(region_product, 'trade', date, sym)\n", "order = data.load_1sym_tick(region_product, 'order', date, sym)\n", "market = data.load_1sym_tick(region_product, 'market', date, sym)\n", "\n", "# for binance future test tick data\n", "# region_product, date, sym = 'gb_bina_future', '2024-01-01', 'BTCUSDT.BNF'\n", "# quote = data.load_1sym_tick(region_product, 'quote', date, sym)\n", "# trade = data.load_1sym_tick(region_product, 'trade', date, sym)\n", "# ob25 = data.load_1sym_tick(region_product, 'orderbook25', date, sym)\n", "\n"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>iturnover</th>\n", "      <th>ivolume</th>\n", "      <th>nactionday</th>\n", "      <th>naskprice__1</th>\n", "      <th>naskprice__2</th>\n", "      <th>naskprice__3</th>\n", "      <th>naskprice__4</th>\n", "      <th>naskprice__5</th>\n", "      <th>naskprice__6</th>\n", "      <th>naskprice__7</th>\n", "      <th>...</th>\n", "      <th>nstatus</th>\n", "      <th>nsyl1</th>\n", "      <th>nsyl2</th>\n", "      <th>ntime</th>\n", "      <th>ntotalaskvol</th>\n", "      <th>ntotalbidvol</th>\n", "      <th>ntradingday</th>\n", "      <th>nweightedavgaskprice</th>\n", "      <th>nweightedavgbidprice</th>\n", "      <th>nyieldtomaturity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2025-04-22 09:15:00</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>20250422.0</td>\n", "      <td>11.02</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>91500000.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>20250422.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-04-22 09:15:09</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>20250422.0</td>\n", "      <td>11.01</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>91509000.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>20250422.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-04-22 09:15:18</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>20250422.0</td>\n", "      <td>11.01</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>91518000.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>20250422.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-04-22 09:15:45</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>20250422.0</td>\n", "      <td>11.01</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>91545000.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>20250422.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-04-22 09:15:54</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>20250422.0</td>\n", "      <td>11.01</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>91554000.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>20250422.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-04-22 14:59:24</th>\n", "      <td>909019565.0</td>\n", "      <td>82483529.0</td>\n", "      <td>20250422.0</td>\n", "      <td>11.04</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>145924000.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>20250422.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-04-22 14:59:33</th>\n", "      <td>909019565.0</td>\n", "      <td>82483529.0</td>\n", "      <td>20250422.0</td>\n", "      <td>11.04</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>145933000.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>20250422.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-04-22 14:59:42</th>\n", "      <td>909019565.0</td>\n", "      <td>82483529.0</td>\n", "      <td>20250422.0</td>\n", "      <td>11.04</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>145942000.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>20250422.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-04-22 14:59:51</th>\n", "      <td>909019565.0</td>\n", "      <td>82483529.0</td>\n", "      <td>20250422.0</td>\n", "      <td>11.04</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>145951000.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>20250422.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-04-22 15:00:00</th>\n", "      <td>915967678.0</td>\n", "      <td>83112887.0</td>\n", "      <td>20250422.0</td>\n", "      <td>11.05</td>\n", "      <td>11.06</td>\n", "      <td>11.07</td>\n", "      <td>11.08</td>\n", "      <td>11.09</td>\n", "      <td>11.1</td>\n", "      <td>11.11</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>150000000.0</td>\n", "      <td>28597766.0</td>\n", "      <td>13836647.0</td>\n", "      <td>20250422.0</td>\n", "      <td>114900.0</td>\n", "      <td>108900.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4819 rows × 66 columns</p>\n", "</div>"], "text/plain": ["                       iturnover     ivolume  nactionday  naskprice__1  \\\n", "2025-04-22 09:15:00          0.0         0.0  20250422.0         11.02   \n", "2025-04-22 09:15:09          0.0         0.0  20250422.0         11.01   \n", "2025-04-22 09:15:18          0.0         0.0  20250422.0         11.01   \n", "2025-04-22 09:15:45          0.0         0.0  20250422.0         11.01   \n", "2025-04-22 09:15:54          0.0         0.0  20250422.0         11.01   \n", "...                          ...         ...         ...           ...   \n", "2025-04-22 14:59:24  909019565.0  82483529.0  20250422.0         11.04   \n", "2025-04-22 14:59:33  909019565.0  82483529.0  20250422.0         11.04   \n", "2025-04-22 14:59:42  909019565.0  82483529.0  20250422.0         11.04   \n", "2025-04-22 14:59:51  909019565.0  82483529.0  20250422.0         11.04   \n", "2025-04-22 15:00:00  915967678.0  83112887.0  20250422.0         11.05   \n", "\n", "                     naskprice__2  naskprice__3  naskprice__4  naskprice__5  \\\n", "2025-04-22 09:15:00          0.00          0.00          0.00          0.00   \n", "2025-04-22 09:15:09          0.00          0.00          0.00          0.00   \n", "2025-04-22 09:15:18          0.00          0.00          0.00          0.00   \n", "2025-04-22 09:15:45          0.00          0.00          0.00          0.00   \n", "2025-04-22 09:15:54          0.00          0.00          0.00          0.00   \n", "...                           ...           ...           ...           ...   \n", "2025-04-22 14:59:24          0.00          0.00          0.00          0.00   \n", "2025-04-22 14:59:33          0.00          0.00          0.00          0.00   \n", "2025-04-22 14:59:42          0.00          0.00          0.00          0.00   \n", "2025-04-22 14:59:51          0.00          0.00          0.00          0.00   \n", "2025-04-22 15:00:00         11.06         11.07         11.08         11.09   \n", "\n", "                     naskprice__6  naskprice__7  ...  nstatus  nsyl1  nsyl2  \\\n", "2025-04-22 09:15:00           0.0          0.00  ...      0.0    0.0    0.0   \n", "2025-04-22 09:15:09           0.0          0.00  ...      0.0    0.0    0.0   \n", "2025-04-22 09:15:18           0.0          0.00  ...      0.0    0.0    0.0   \n", "2025-04-22 09:15:45           0.0          0.00  ...      0.0    0.0    0.0   \n", "2025-04-22 09:15:54           0.0          0.00  ...      0.0    0.0    0.0   \n", "...                           ...           ...  ...      ...    ...    ...   \n", "2025-04-22 14:59:24           0.0          0.00  ...      0.0    0.0    0.0   \n", "2025-04-22 14:59:33           0.0          0.00  ...      0.0    0.0    0.0   \n", "2025-04-22 14:59:42           0.0          0.00  ...      0.0    0.0    0.0   \n", "2025-04-22 14:59:51           0.0          0.00  ...      0.0    0.0    0.0   \n", "2025-04-22 15:00:00          11.1         11.11  ...      0.0    0.0    0.0   \n", "\n", "                           ntime  ntotalaskvol  ntotalbidvol  ntradingday  \\\n", "2025-04-22 09:15:00   91500000.0           0.0           0.0   20250422.0   \n", "2025-04-22 09:15:09   91509000.0           0.0           0.0   20250422.0   \n", "2025-04-22 09:15:18   91518000.0           0.0           0.0   20250422.0   \n", "2025-04-22 09:15:45   91545000.0           0.0           0.0   20250422.0   \n", "2025-04-22 09:15:54   91554000.0           0.0           0.0   20250422.0   \n", "...                          ...           ...           ...          ...   \n", "2025-04-22 14:59:24  145924000.0           0.0           0.0   20250422.0   \n", "2025-04-22 14:59:33  145933000.0           0.0           0.0   20250422.0   \n", "2025-04-22 14:59:42  145942000.0           0.0           0.0   20250422.0   \n", "2025-04-22 14:59:51  145951000.0           0.0           0.0   20250422.0   \n", "2025-04-22 15:00:00  150000000.0    28597766.0    13836647.0   20250422.0   \n", "\n", "                     nweightedavgaskprice  nweightedavgbidprice  \\\n", "2025-04-22 09:15:00                   0.0                   0.0   \n", "2025-04-22 09:15:09                   0.0                   0.0   \n", "2025-04-22 09:15:18                   0.0                   0.0   \n", "2025-04-22 09:15:45                   0.0                   0.0   \n", "2025-04-22 09:15:54                   0.0                   0.0   \n", "...                                   ...                   ...   \n", "2025-04-22 14:59:24                   0.0                   0.0   \n", "2025-04-22 14:59:33                   0.0                   0.0   \n", "2025-04-22 14:59:42                   0.0                   0.0   \n", "2025-04-22 14:59:51                   0.0                   0.0   \n", "2025-04-22 15:00:00              114900.0              108900.0   \n", "\n", "                     nyieldtomaturity  \n", "2025-04-22 09:15:00               0.0  \n", "2025-04-22 09:15:09               0.0  \n", "2025-04-22 09:15:18               0.0  \n", "2025-04-22 09:15:45               0.0  \n", "2025-04-22 09:15:54               0.0  \n", "...                               ...  \n", "2025-04-22 14:59:24               0.0  \n", "2025-04-22 14:59:33               0.0  \n", "2025-04-22 14:59:42               0.0  \n", "2025-04-22 14:59:51               0.0  \n", "2025-04-22 15:00:00               0.0  \n", "\n", "[4819 rows x 66 columns]"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["market"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 214, "metadata": {}, "outputs": [{"data": {"text/plain": ["(189, 10)"]}, "execution_count": 214, "metadata": {}, "output_type": "execute_result"}], "source": ["order.loc[order['chfunctioncode']==66].shape"]}, {"cell_type": "code", "execution_count": 215, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.08421052631578947"]}, "execution_count": 215, "metadata": {}, "output_type": "execute_result"}], "source": ["16/190"]}, {"cell_type": "code", "execution_count": 415, "metadata": {}, "outputs": [], "source": ["from shennong.stk import bar\n", "from shennong.utils import consts\n", "\n", "df = bar.load(\n", "    start_datetime='2023-08-29',\n", "    end_datetime='2023-08-29',\n", "    start_minute=\"09:25\",\n", "    end_minute=\"09:25\",\n", "    region_product=\"cn_ashare\",\n", "    freq=\"1minute\",\n", "    symbol_list=None,\n", "    load_root=\"/mnt/sda/NAS/ShareFolder/chenshenghong/cn_ashare/data\",\n", "    verbose=False,\n", "    key_list=\"hn_auction_tot\",\n", "    remove_all_nan_symbols=True,\n", "    ret=consts.RET_PANDAS,\n", ")"]}, {"cell_type": "code", "execution_count": 439, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 452, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["feature_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/crypto/features/raw/'\n", "f2 = bar.load(feature_root, freq, key_group_name, date)\n", "f2 = f2.sel({'SYMBOL':'BTCUSDT.BNF'}).to_pandas()\n", "f2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from shennong.stk import bar\n", "\n", "date = \"2024-01-01\"\n", "xr_label = bar.load(region_product='gb_bina_future', freq='3second',\n", "                    load_root=\"/mnt/sda/NAS/ShareFolder/folei/\",\n", "                    symbol_list=[\"BTCUSDT.BNF\"],\n", "                    start_datetime=date, end_datetime=date,\n", "                    key_list=\"label_folei_crypto_v6\",\n", "                    restoration=None, auction=False,\n", "                    remove_all_nan_symbols=True,\n", "                    use_multiprocess=False,\n", "                    use_sharememory=False, \n", "                    verbose=False)\n", "\n", "b1 = xr_label.sel({'SYMBOL':'BTCUSDT.BNF'}).to_pandas().DIY.clip_columns('label_folei_crypto_v6_ret_').iloc[:,:8]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from yrqtlib import data\n", "bar2 = data.data_manager('bar')\n", "\n", "\n", "label_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/crypto/labels/'\n", "b = bar2.load(label_root, '3second', 'raw', date, sym_list=['BTCUSDT.BNF'])\n", "b2 = b.sel({'SYMBOL':'BTCUSDT.BNF'}).to_pandas().DIY.clip_columns('mid_last_ret_')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os, h5py\n", "from shennong.utils import trading_days\n", "from tqdm.notebook import tqdm\n", "\n", "td_days = trading_days.load(region='cn', product='ashare', start_datetime='2023-01-01',end_datetime='2024-01-01')\n", "\n", "\n", "def load_1day_facset(date):\n", "    load_root = '/mnt/sda/NAS/ShareFolder/zhengyifan/YRData/Equity/'\n", "    xchgs = ['she', 'shg', 'bjse']\n", "\n", "    dfs = []\n", "    for xchg in xchgs:\n", "        with h5py.File(f'{load_root}cn_{xchg}/1day/{date}.h5', 'r') as file:\n", "            dt = file['DATA'][0,:,:]\n", "            syms = file['SYMBOL'][:].astype(np.str_)\n", "            keys = file['KEY'][:].astype(np.str_)\n", "            dfs.append(pd.DataFrame(dt, index=keys, columns=syms).T)\n", "    return pd.concat(dfs)\n", "\n", "\n", "\n", "results = {date: load_1day_facset(date) for date in tqdm(td_days)}\n", "results = pd.concat(results, names=['date','symbol'])"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["diff_eps = results['ff_eps_basic'].unstack(1).diff()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["diff_eps.replace(0.0,np.nan).iloc[:,0].dropna()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with h5py.File('/mnt/sda/NAS/sample/20241206/data_ts/2024-01-01.h5','r') as file:\n", "    print(file['X'][51,:,:])\n", "    print(file['Y'].shape)"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [], "source": ["syms = pd.read_csv('/mnt/sda/NAS/sample/20241206/metadata/2024-01-01.csv')['0']\n", "btc_idx = syms[syms=='BTCUSDT.BNF']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qt"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import h5py\n", "import pandas as pd\n", "\n", "paths = [\n", "\t'/mnt/sda/NAS/ShareFolder/folei/gb_bina_future/3second/feature_crypto_market_folei_v1/',\n", "\t'/mnt/sda/NAS/ShareFolder/lishuanglin/features/gb_bina_future/3second/trade1/',\n", "\t'/mnt/sda/NAS/ShareFolder/lishuanglin/features/gb_bina_future/3second/trade2/',\n", "]\n", "Dates = pd.date_range(start='2021-09-01', end='2024-10-01', freq='1d').strftime('%Y-%m-%d').tolist()[:200]\n", "print(f'Info: lenth of dates is {len(Dates)}.')\n", "\n", "df = pd.DataFrame(index=Dates, columns=[p.split('/')[-2] for p in paths])\n", "for date in Dates:\n", "\tfor path in paths:\n", "\t\ttry:\n", "\t\t\twith h5py.File(f'{path}/{date}.h5', 'r') as f:\n", "\t\t\t\tdf.loc[date, path.split('/')[-2]] = f['DATA'].shape[-1]\n", "\t\texcept Exception as err:\n", "\t\t\tprint(path, date)\n", "\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["feature_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/features/'\n", "region_product = 'gb_bina_future'\n", "freq = '3second'\n", "feature_group_name = 'trade1'\n", "\n", "feature_dir = f'{feature_root}{region}_{product}/{freq}/{feature_group_name}/'\n", "for file in os.listdir(feature_dir):\n", "    try:\n", "        with h5py.File(f\"{feature_dir}/{file}\",\"r\") as f:\n", "            # print(file,f[\"DATA\"].shape, end='\\r')\n", "            pass\n", "    except:\n", "        print(file) "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_root = \"/mnt/sda/NAS/ShareFolder/folei/gb_bina_future/3second/feature_crypto_market_folei_v1/\"\n", "svdates = sorted([s.split('.')[0] for s in os.listdir(load_root) if s.endswith('.h5')])\n", "sorted(set(dates)-set(svdates))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["log_root = '/mnt/sda/NAS/log/lishuanglin/fg/20241101_120353_raw_crypto_1minbar_test/'\n", "\n", "dirs = [s for s in os.listdir(log_root) if os.path.isdir(os.path.join(log_root, s))]\n", "\n", "for dir in dirs:\n", "    file_path = os.listdir(os.path.join(log_root, dir))[0]\n", "    with open(os.path.join(log_root, dir, file_path),'r') as f:\n", "        for line in f:\n", "            if '248.json' in line:\n", "                print(dir, file_path, line)\n", "            break\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calc_1sym(sym):\n", "    trade = load_1date_1sym_tick(tickdata_root, tick_group_name, date, sym, region=region, product=product)\n", "    if trade is None:\n", "        return None\n", "    f = trade.sort('server_recv_time').group_by_dynamic(index_column='server_recv_time', every='3s').agg(\n", "        trd_num = pl.count('price').add(1.0).log10(),\n", "        trd_vol = pl.sum('volume').add(1.0).log10(),\n", "        trd_amnt = (pl.col('price') * pl.col('volume')).sum().add(1.0).log10(),\n", "        trd_bnum = pl.col('price').filter(pl.col('side')==1).count().add(1.0).log10(),\n", "        trd_bvol = pl.col('volume').filter(pl.col('side')==1).sum().add(1.0).log10(),\n", "        trd_bamnt = (pl.col('price') * pl.col('volume')).filter(pl.col('side')==1).sum().add(1.0).log10(),\n", "        trd_amnt_std = (pl.col('price')*pl.col('volume')).std().add(1.0).log10(),\n", "        trd_bamnt_std = (pl.col('price') * pl.col('volume')).filter(pl.col('side')==1).std().add(1.0).log10(),\n", "    )\n", "    print(' '*50, end='\\r')\n", "    print(f'finised {sym} [{syms.index(sym)+1}/{len(syms)}]', end='\\r')\n", "    return f\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from joblib import Parallel, delayed\n", "date = '2024-03-29'\n", "\n", "syms = pd.read_csv(f'/mnt/sda/NAS/Crypto/AllData_new/config/{date.replace(\"-\",\"\")}_gb_bina_future_symbols.txt',header=None)[0].tolist()\n", "%time x = Parallel(n_jobs=3)(delayed(calc_1sym)(sym) for sym in syms)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from joblib import Parallel, delayed\n", "date = '2023-08-29'\n", "\n", "# syms = pd.read_csv(f'/mnt/sda/NAS/Crypto/AllData_new/config/{date.replace(\"-\",\"\")}_gb_bina_future_symbols.txt',header=None)[0].tolist()\n", "%time x = Parallel(n_jobs=3)(delayed(calc_1sym)(sym) for sym in syms)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tick['amnt'] = tick['price'] * tick['volume']\n", "x = tick.set_index('exchange_time')\n", "num = x.amnt.resample('3s').count()\n", "sum = x.amnt.resample('3s').sum()\n", "sum2 = (x.amnt**2).resample('3s').sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["np.sqrt(sum2/num - (sum/num)**2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(('/mnt/sda/home/<USER>/crypto/'));\n", "from config import params\n", "\n", "from yrqtlib import Crypto\n", "\n", "dm = Crypto.DataManager(**params)\n", "trade = dm.load_onesym_oneday_tick_data('2023-01-05','BTCUSDT.BNF','raw_gb_bina_future_transaction')\n", "trade = trade.to_pandas().set_index('datetime')\n", "# b = np.log10(trade.resample('3s').volume.sum().add((1.0))).shift(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trade.resample('3s').volume.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## 查看原始tick数据"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime, os, time, h5py\n", "from shennong.utils import symbol, trading_days\n", "from yrqtlib.utils import tools, data_loader as data\n", "from tqdm.notebook import tqdm\n", "from joblib import Parallel, delayed\n", "\n", "# date = '2018-01-02'\n", "date = '2024-01-31'\n", "tick_root = '/mnt/sda/NAS/AllData/'\n", "dates = tools.get_trading_days('2023-02-01','2023-10-24')\n", "start_time, end_time = '09:30:00', '14:50:00'\n", "syms = symbol.load(region='cn', product='ashare')\n", "\n", "sym = '000001.SZ'\n", "# sym = '600000.SH'\n", "## 涨停股的封板金额\n", "mkt = data.load_onesym_tickh5(date,start_time,end_time,sym,tick_root,'raw_wangbo_market__complete')\n", "odt = data.load_onesym_tickh5(date,start_time,end_time,sym,tick_root,'raw_wangbo_order__complete')\n", "tdt = data.load_onesym_tickh5(date,start_time,end_time,sym,tick_root,'raw_wangbo_transaction__complete')\n", "mkt.columns = mkt.columns.str[11:]\n", "odt.columns = odt.columns.str[11:]\n", "tdt.columns = tdt.columns.str[11:]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["odt = odt[['chfunctioncode', 'norder', 'nprice', 'nvolume']]\n", "mkt = mkt[['naskvol__1','nbidvol__1']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mgd = odt.join(mkt,how='outer')\n", "mgd[mkt.columns] = mgd[mkt.columns].ffill()\n", "mgd = mgd.loc[odt.index.unique()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bid1 = mgd.loc[(mgd.chfunctioncode==66) & (mgd.nvolume.between(0.1*mgd.naskvol__1,1.0*mgd.naskvol__1))]\n", "bid2 = mgd.loc[(mgd.chfunctioncode==66) & (mgd.nvolume>=0.062*mgd.naskvol__1)]\n", "ask = mgd.loc[(mgd.chfunctioncode==83) & (mgd.nvolume.between(0.1*mgd.nbidvol__1, 1.0*mgd.nbidvol__1))]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bid1.nvolume.mean(), bid2.nvolume.mean()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## 更新配置文件"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from yrqtlib.utils import tools\n", "import pandas as pd\n", "import os, datetime\n", "from shennong.utils import symbol, trading_days\n", "\n", "def update_fc_dates(dates, file_path):\n", "    with open(file_path,'w+') as file:\n", "        for date in dates:\n", "            file.write(f'{date}\\n')\n", "    print(f'{file_path} updated!', end='\\n')\n", "\n", "def update_fc_syms(syms, file_path):\n", "    with open(file_path,'w+') as file:\n", "        for sym in syms:\n", "            file.write(f'{sym}\\n')\n", "    print(f'{file_path} updated!', end='\\n')\n", "\n", "\n", "fc_src_path = '/mnt/sda/home/<USER>/FC/features/'\n", "groups = sorted([s for s in os.listdir((fc_src_path)) if s.startswith('order')])\n", "\n", "syms = symbol.load(region='cn',product='ashare')\n", "dates = tools.get_trading_days('2018-01-02',str(datetime.date.today()))[::-1]\n", "\n", "# ## update \n", "# with open('/mnt/sda/home/<USER>/FC/features/time.txt','w+') as file:\n", "#     for date in dates[::-1]:\n", "#         file.write(f'{date}\\n')\n", "\n", "\n", "for group in groups:\n", "    time_file_path = f'/mnt/sda/home/<USER>/FC/features/{group}/config/time.txt'\n", "    sym_file_path = f'/mnt/sda/home/<USER>/FC/features/{group}/config/symbol.txt'\n", "    update_fc_dates(dates[:], time_file_path)\n", "    update_fc_syms(syms[:], sym_file_path)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "def update_fc_dates(dates, file_path):\n", "    with open(file_path,'w+', encoding='utf-8') as file:\n", "        for date in dates:\n", "            file.write(f'{date}\\n')\n", "    print(f'{file_path} updated!', end='\\n')\n", "\n", "\n", "dates = pd.date_range('2021-09-01','2024-10-25').astype(str)\n", "date_file_path = '/mnt/sda/home/<USER>/FC/gb_bina_future/trade1/config/T/gb_bina_future/time.txt'\n", "\n", "update_fc_dates(dates, date_file_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["file_path = '/mnt/sda/home/<USER>/FC/gb_bina_future/trade1/config/S/gb_bina_future/symbol.txt'\n", "def update_fc_syms(syms, file_path):\n", "    with open(file_path,'w+') as file:\n", "        for sym in syms:\n", "            file.write(f'{sym}\\n')\n", "    print(f'{file_path} updated!', end='\\n')\n", "\n", "\n", "update_fc_syms(syms[:], file_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from yrqtlib.utils import symbol\n", "from yrqtlib.utils import tools\n", "import pandas as pd, os\n", "\n", "sym_parts, date_parts = 10, 100\n", "group_name = 'trade2'\n", "\n", "syms = symbol.load('gb_bina_future')\n", "syms = [s for s in syms if not s.startswith('PERP') and \"_\" not in s and '-' not in s]\n", "symbol_lists = tools.split_list(syms, sym_parts)\n", "\n", "dates = pd.date_range('2021-09-01','2024-11-05').astype(str)\n", "date_lists = tools.split_list(dates, date_parts)\n", "\n", "os.system('rm -r /mnt/sda/home/<USER>/FC/gb_bina_future/config/*')\n", "config_dir = '/mnt/sda/home/<USER>/FC/gb_bina_future/config/'\n", "\n", "\n", "if not os.path.exists(f'{config_dir}symbol/'):\n", "    os.makedirs(f'{config_dir}symbol/')\n", "if not os.path.exists(f'{config_dir}date/'):\n", "    os.makedirs(f'{config_dir}date/')\n", "if not os.path.exists(f'{config_dir}gentBar/{group_name}/'):\n", "    os.makedirs(f'{config_dir}gentBar/{group_name}/')\n", "\n", "\n", "for i in range(len(symbol_lists)):\n", "    with open(f'{config_dir}symbol/part{i}.txt','w+', encoding='utf-8') as file:\n", "        for sym in symbol_lists[i]:\n", "            file.write(f'{sym}\\n')\n", "\n", "\n", "for i in range(len(date_lists)):\n", "    with open(f'{config_dir}date/part{i}.txt','w+', encoding='utf-8') as file:\n", "        for date in date_lists[i]:\n", "            file.write(f'{date}\\n')\n", "\n", "def generate_gentBar_config(symbol_lists, date_lists, config_dir):\n", "    json_file_path = f'/mnt/sda/home/<USER>/FC/gb_bina_future/{group_name}/config/fg/gb_bina_future/gentBar.json'\n", "    import json\n", "    with open(json_file_path, 'r', encoding='utf-8') as f:\n", "        config = json.load(f)\n", "        num_ts_threads = int(config['exchange']['gb_bina_future']['basic_info']['tsFeatureThread'])\n", "        by_part_path = config['exchange']['gb_bina_future']['output_data']['3secbar']['savePath']\n", "        by_part_path += f'by_symbol/{group_name}/'\n", "        config['exchange']['gb_bina_future']['output_data']['3secbar']['savePath'] = by_part_path\n", "\n", "    for i in range(len(symbol_lists)):\n", "        for j in range(len(date_lists)):\n", "            if len(date_lists[j]) >= 1:\n", "                startDate, endDate = date_lists[j][0], date_lists[j][-1]\n", "            else:\n", "                continue\n", "            config['tag'] = f'{group_name}'\n", "            config['startDate'] = startDate\n", "            config['endDate'] = endDate\n", "            config['exchange']['gb_bina_future']['basic_info']['symbolFilePath'] = f'{config_dir}symbol/part{i}.txt'\n", "            config['exchange']['gb_bina_future']['basic_info']['tsStartCore'] = (num_ts_threads+1) * i\n", "            config['exchange']['gb_bina_future']['output_data']['3secbar']['saveName'] = f'part{i}'\n", "            \n", "            # config['exchange']['gb_bina_future']['output_data']['3secbar']['featureFilePath'] = f'{config_dir}gentBar/part{i+1}.json'\n", "            with open(f'{config_dir}gentBar/{group_name}/part_{i*len(date_lists)+j}.json', 'w+', encoding='utf-8') as f:\n", "                json.dump(config, f, indent=4)\n", "\n", "generate_gentBar_config(symbol_lists, date_lists, config_dir)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["scripts_folder = '/mnt/sda/home/<USER>/FC/gb_bina_future/config/scripts/trade2/'\n", "if not os.path.exists(scripts_folder):\n", "    os.makedirs(scripts_folder)\n", "\n", "for i in range(1000):\n", "    with open(f'{scripts_folder}{i}.sh', 'w') as f:\n", "        f.write('#!/bin/bash\\n')\n", "        f.write('cd /mnt/sda/home/<USER>/FC/gb_bina_future/trade2/\\n')\n", "        f.write('export PYTHONPATH=\"/mnt/sda/NAS/Release/shennong/stk/dev_global.dev/20240708-112716-706c3b0f/python/:$PYTHONPATH\"\\n')\n", "        f.write('export LD_PRELOAD=./lib/libshennong_fc.so:./lib/libshennong_stk.so\\n')\n", "        f.write('export LD_LIBRARY_PATH=./lib:$LD_LIBRARY_PATH\\n')\n", "        f.write(f'bin/featureGenerator -c /mnt/sda/home/<USER>/FC/gb_bina_future/config/gentBar/trade2/part_{i}.json -l /mnt/sda/NAS/log/lishuanglin/fg/test_trade2/{i}')\n", "        \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/home/<USER>/FC/gb_bina_future/config/slurm.sh'\n", "#!/bin/bash\n", "#SBATCH -o /mnt/sda/NAS/log/lishuanglin/fg/test_trade2/%A_%a.out\n", "#SBATCH --partition=AMD_COMPUTE\n", "#SBATCH --nodes=1\n", "#SBATCH --ntasks-per-node=1\n", "#SBATCH --cpus-per-task=2\n", "#SBATCH --mem=10G\n", "#SBATCH --array=0-999\n", "\n", "bash /mnt/sda/home/<USER>/FC/gb_bina_future/config/scripts/trade2/$SLURM_ARRAY_TASK_ID.sh"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## 特征文件检验"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os, pandas as pd\n", "from yrqtlib.utils import tools\n", "from tqdm.notebook import tqdm\n", "\n", "group = 'feature_orderbook19'\n", "feature_root = f'/mnt/sda/NAS/ShareFolder/lishuanglin/features/cn_ashare/tick/{group}/'\n", "feature_files = sorted([s for s in os.listdir(feature_root) if s.endswith('.h5')])\n", "dates = [s.split('.')[0] for s in feature_files]\n", "\n", "today = str(datetime.date.today())\n", "dates0 = tools.get_trading_days('2018-01-02',today)\n", "# set(dates0) - set(dates)#/len(dates0)\n", "\n", "file_size = [os.path.getsize(feature_root+s)/(1024**3) for s in tqdm(feature_files)]\n", "import matplotlib.pyplot as plt\n", "sizes = pd.Series(file_size, index=dates)\n", "sizes[sizes<3.0]\n", "sizes.plot(figsize=(15,6))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os, pandas as pd\n", "from yrqtlib.utils import tools\n", "feature_root = '/mnt/sda/NAS/AllData/cn_ashare/tick/raw_wangbo_transaction__complete/'\n", "feature_files = sorted([s for s in os.listdir(feature_root) if s.endswith('.h5')])\n", "dates = [s.split('.')[0] for s in feature_files]\n", "\n", "dates0 = tools.get_trading_days('2018-01-02','2023-10-23')\n", "# set(dates0) - set(dates)#/len(dates0)\n", "\n", "file_size = [os.path.getsize(feature_root+s)/(1024**3) for s in feature_files]\n", "import matplotlib.pyplot as plt\n", "sizes = pd.Series(file_size, index=dates)\n", "sizes[sizes<3.0]\n", "sizes['2023-06':'2023-10-23'].plot(figsize=(15,6),grid=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/home/<USER>/working/3.crontab/py_scripts/fc_update_basic_info.py'\n", "import shutil\n", "from yrqtlib.utils import tools\n", "import datetime,os\n", "\n", "tmp_basic_info_path = '/mnt/sda/NAS/ShareFolder/lishuanglin/tmp_basic_info/'\n", "dates = tools.get_trading_days('2018-01-01',str(datetime.date.today()))\n", "for date in dates:\n", "    if not os.path.exists(f'{tmp_basic_info_path}{date}.csv'):\n", "        yesterday = dates[dates.index(date)-1]\n", "        shutil.copyfile(f'{tmp_basic_info_path}{yesterday}.csv', f'{tmp_basic_info_path}{date}.csv')\n", "        print(f'file for {date} copyed!')\n", "\n", "print('all dates finished!')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 文件合并"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os, hdf5plugin, h5py, time\n", "import numpy as np, pandas as pd\n", "from tqdm.notebook import tqdm\n", "from joblib import Parallel, delayed\n", "from IPython import display\n", "from shennong.utils import symbol\n", "from yrqtlib.utils import tools\n", "\n", "def resave_1day(feature_root, group, date):\n", "\tall_syms = symbol.load(region='cn', product='ashare')\n", "\texists = [os.path.exists(f'{feature_root}{group}_{i}/{date}.h5') for i in range(5)]\n", "\tif np.sum(exists) == 5:  # noqa: PLR1702\n", "\t\ttry:\n", "\t\t\twith h5py.File(f'{feature_root}{group}/{date}.h5', 'w') as file:\n", "\t\t\t\tfor i in range(5):\n", "\t\t\t\t\twith h5py.File(f'{feature_root}{group}_{i}/{date}.h5') as file2:\n", "\t\t\t\t\t\tsyms = list(file2.keys())\n", "\t\t\t\t\t\tfor sym in syms:\n", "\t\t\t\t\t\t\tgp = file.create_group(sym)\n", "\t\t\t\t\t\t\tgp.create_dataset('ROW', data=file2[sym]['ROW'])\n", "\t\t\t\t\t\t\tgp.create_dataset('COLUMN', data=file2[sym]['COLUMN'])\n", "\t\t\t\t\t\t\tgp.create_dataset(\n", "\t\t\t\t\t\t\t\t'DATA', data=file2[sym]['DATA'].astype(np.float32), chunks=True, **hdf5plugin.LZ4()\n", "\t\t\t\t\t\t\t)\n", "\t\t\t\t\t\t\t# print(' ' * 50, end='\\r')\n", "\t\t\t\t\t\t\t# print(date, sym, 'resaved!', end='\\r')\n", "\t\t\t\t\t\t\ttools.log_progress(sym, all_syms, 60, f'{date}')\n", "\t\t\t\tfor i in range(5):\n", "\t\t\t\t\tos.remove(f'{feature_root}{group}_{i}/{date}.h5')\n", "\t\texcept Exception as e:\n", "\t\t\tprint(date, e, end='\\r')\n", "\t\t\ttime.sleep(0.2)\n", "\n", "\n", "feature_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/features/cn_ashare/tick/'\n", "group = 'feature_orderbook12'\n", "\n", "if not os.path.exists(f'{feature_root}{group}/'):\n", "\tos.mkdir(f'{feature_root}{group}/')\n", "\n", "# syms = symbol.load(region='cn', product='ashare')\n", "\n", "# dates = sorted([s.split('.')[0] for s in os.listdir(f'{feature_root}{group}_0/') if s.endswith('.h5')])\n", "while True:\n", "\tdates = sorted([s.split('.')[0] for s in os.listdir(f'{feature_root}{group}_0/') if s.endswith('.h5')])\n", "\tif len(dates) == 0:\n", "\t\tbreak\n", "\t# grids = [(date, sym) for date in dates for sym in syms]\n", "\t# _ = Parallel(n_jobs=10)(delayed(resave_1day)(feature_root, group, date) for date in tqdm(dates))\n", "\tfor date in tqdm(dates):\n", "\t\t_ = resave_1day(feature_root, group, date)\n", "\tdisplay.clear_output(wait=True)\n", "\tfor i in range(1200):\n", "\t\tprint(f'time elapsed: {1200 - i} seconds', end='\\r')\n", "\t\ttime.sleep(1)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## read key json"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["(8, 16)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["import json\n", "home_root = '/mnt/sda/home/<USER>/FC/gb_bina_future/trade/config/K/gb_bina_future/'\n", "key_path = f'{home_root}3second/quote3.json'\n", "with open(key_path, 'r') as file:\n", "    key_json = json.load(file)\n", "\n", "unique_keys = [s['Formula'].replace('__1', '__%d') for s in key_json if '__1' in s['Formula']]\n", "len(unique_keys), len(key_json)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## 其他测试"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp = {}\n", "with h5py.File(f'{feature_root}{group}/2018-01-02.h5','r') as file:\n", "    for key in tqdm(file.keys()):\n", "        rows = file[key]['ROW'][:].astype(np.str_)\n", "        cols = file[key]['COLUMN'][:].astype(np.str_)\n", "        dt = file[key]['DATA'][:].astype(np.float32)\n", "        temp[key] = pd.DataFrame(data=dt, index=rows, columns=cols)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from yrqtlib.utils import tools\n", "tools.get_h5_hirerarchy('/mnt/sda/NAS/ShareFolder/lishuanglin/features/cn_ashare/tick/feature_orderbook15/2018-01-02.h5')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with h5py.File(f'test.h5','w') as file:\n", "    for key in tqdm(temp.keys()):\n", "        gp = file.create_group(key)\n", "        file[key]['ROW'] = temp[key].index.to_numpy().astype('S')\n", "        file[key]['COLUMN'] = temp[key].columns.to_numpy().astype('S')\n", "        dt = temp[key].to_numpy().astype(np.float32)\n", "        file[key].create_dataset('DATA',data=dt,chunks=True,**hdf5plugin.Blosc())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os, pandas as pd, numpy as np\n", "import h5py, sys, time\n", "from tqdm.notebook import tqdm\n", "from joblib import Parallel, delayed\n", "\n", "feature_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/features/cn_ashare/tick/'\n", "group = 'feature_orderbook12'\n", "\n", "files = sorted([s for s in os.listdir(f'{feature_root}{group}/') if s.endswith('.h5')])\n", "\n", "for file in tqdm(files):\n", "    try:\n", "        with h5py.File(f'{feature_root}{group}/{file}') as f:\n", "            # syms = list(f.keys())\n", "            _ = f['600000.SH'].keys()\n", "    except Exception as e:\n", "        print(file, e, end='\\n')\n", "        # os.remove(f'{feature_root}{group}/{file}')\n", "        continue"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["arr = np.random.randn(5000,5000)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# tmp = arr>=1.0e-8"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%writefile 'test.py'\n", "def get_index(arr0, threshold=1.0e-8):\n", "    arr = np.tril(arr0)\n", "    tmp = arr>=threshold\n", "    x = np.array(np.arange(1,arr.shape[0]+1))[np.newaxis,:].repeat(arr.shape[0],axis=0)\n", "    y = np.array(np.arange(1,arr.shape[1]+1))[:,np.newaxis].repeat(arr.shape[1],axis=1)\n", "    x = x*tmp - 1\n", "    cols = x[x>=0]\n", "    y = y*tmp-1\n", "    rows = y[y>=0]\n", "    return rows, cols, arr[tmp]\n", "\n", "\n", "def get_index0(arr, threshold=1.0e-8):\n", "    row, col, value = [], [], []\n", "    for i in range(arr.shape[0]):\n", "        for j in range(i+1):\n", "            if arr[i,j]>threshold:\n", "                row.append(i)\n", "                col.append(j)\n", "                value.append(arr[i,j])\n", "    return np.array(row), np.array(col), np.array(value)\n", "\n", "\n", "from yrqtlib.utils import tools\n", "import numpy as np\n", "arr0 = np.random.randn(5000, 5000)\n", "# print('arr_shape: ', arr.shape)\n", "# tmr = tools.Timer()\n", "# row0, col0, value0 = get_index0(arr, threshold=1.0e-8)\n", "# tmr.add_tick('for loop time')\n", "# row, col, value = get_index(arr, threshold=1.0e-8)\n", "# tmr.add_tick('new method')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tmr = tools.Timer()\n", "threshold = 1.0e-8\n", "arr = np.tril(arr0)\n", "tmr.add_tick('tril')\n", "\n", "tmp = (arr>=threshold)\n", "tmr.add_tick('tmp')\n", "\n", "x = np.array(np.arange(1,arr.shape[0]+1))[np.newaxis,:].repeat(arr.shape[0],axis=0)\n", "tmr.add_tick('x')\n", "\n", "y = np.array(np.arange(1,arr.shape[1]+1))[:,np.newaxis].repeat(arr.shape[1],axis=1)\n", "tmr.add_tick('y')\n", "\n", "# x0 = (x*tmp - 1).reshape(-1)\n", "x0 = (x*tmp)\n", "# cols = x0[x0>=1]\n", "tmr.add_tick('cols')\n", "\n", "# y0 = (y*tmp-1).reshape(-1)\n", "y0 = (y*tmp)\n", "# rows = y0[y0>=1]\n", "tmr.add_tick('rows')\n", "\n", "values = arr[tmp]\n", "tmr.add_tick('values')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "uvbase", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}