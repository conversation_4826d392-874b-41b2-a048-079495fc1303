[2025-05-06 22:58:16.247] [fg] [info] [2492060:2492060] [inputArgPaser] [featureGenerator.cpp:349] configuration_file_path=config/fg/cn_ashare/ob4_v2.json
[2025-05-06 22:58:16.248] [fg] [info] [2492060:2492060] [inputArgPaser] [featureGenerator.cpp:423] exchange_name=cn_ashare
[2025-05-06 22:58:16.248] [fg] [warning] [2492060:2492060] [inputArgPaser] [featureGenerator.cpp:485] Please explicitly set a valid maxFeatureLength value.
[2025-05-06 22:58:16.248] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1024] Start to initialize trading day list
[2025-05-06 22:58:16.248] [fg] [info] [2492060:2492060] [readStrVecFromFile] [Util.h:389] Read fileName=/mnt/sda/NAS/AllData/config/cn_ashare_trading_days.txt
[2025-05-06 22:58:16.252] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1028] Start to initialize each exchange feature calculator
[2025-05-06 22:58:16.252] [fg] [info] [2492060:2492060] [initializeExchangeFeatureCalculator] [featureGenerator.cpp:711] Load Symbol List Path=./config/S/cn_ashare/symbol.txt
[2025-05-06 22:58:16.252] [fg] [info] [2492060:2492060] [readStrVecFromFile] [Util.h:389] Read fileName=./config/S/cn_ashare/symbol.txt
[2025-05-06 22:58:16.254] [fg] [info] [2492060:2492060] [initializeExchangeFeatureCalculator] [featureGenerator.cpp:839] feature_config_size=37
[2025-05-06 22:58:16.254] [fg] [info] [2492060:2492060] [initializeExchangeFeatureCalculator] [featureGenerator.cpp:846] nTradingDay=20180102, tsStartCore=0, tsFeatureThread=1, csStartCore=2, csFeatureThread=1, featureManifestPath=./config/fc/cn_ashare/feature_manifest.json, preLoadDays=0, maxTickLength=1000, maxFeatureLength=1000
[2025-05-06 22:58:16.262] [fg] [info] [2492060:2492060] [initializeExchangeFeatureCalculator] [featureGenerator.cpp:852] Get Symbol Load Finished.
[2025-05-06 22:58:16.262] [fg] [info] [2492060:2492060] [FeatureFactoryImpl] [FeatureFactoryImpl.cpp:47] Loading /mnt/sda/home/<USER>/FC/cn_ashare_new/orderbook/config/fc/cn_ashare/feature_manifest.json as as manifest file
[2025-05-06 22:58:16.455] [fg] [info] [2492060:2492060] [CreateFeatureCalc] [FeatureFactoryImpl.cpp:164] [FeatureCalcInit] There are total 5 meta-features in feature library
[2025-05-06 22:58:16.455] [fg] [info] [2492060:2492060] [CreateFeatureCalc] [FeatureFactoryImpl.cpp:166] TradingDayFilePath=/mnt/sda/NAS/AllData/config/cn_ashare_trading_days.txt, ExchangeGroupInfoFilePath=/mnt/sda/NAS/AllData/config/exchange_groups/cn_ashare.json
[2025-05-06 22:58:16.456] [fg] [info] [2492060:2492060] [GetExchangeGroupInfo] [FeatureFactoryImpl.cpp:140] 0: begin_time=91500000, end_time=92500000, type=A
[2025-05-06 22:58:16.456] [fg] [info] [2492060:2492060] [GetExchangeGroupInfo] [FeatureFactoryImpl.cpp:140] 1: begin_time=92500000, end_time=93000000, type=I
[2025-05-06 22:58:16.456] [fg] [info] [2492060:2492060] [GetExchangeGroupInfo] [FeatureFactoryImpl.cpp:140] 2: begin_time=93000000, end_time=113000000, type=O
[2025-05-06 22:58:16.456] [fg] [info] [2492060:2492060] [GetExchangeGroupInfo] [FeatureFactoryImpl.cpp:140] 3: begin_time=113000000, end_time=130000000, type=I
[2025-05-06 22:58:16.456] [fg] [info] [2492060:2492060] [GetExchangeGroupInfo] [FeatureFactoryImpl.cpp:140] 4: begin_time=130000000, end_time=145700000, type=O
[2025-05-06 22:58:16.456] [fg] [info] [2492060:2492060] [GetExchangeGroupInfo] [FeatureFactoryImpl.cpp:140] 5: begin_time=145700000, end_time=150000000, type=A
[2025-05-06 22:58:16.456] [fg] [info] [2492060:2492060] [GetExchangeGroupInfo] [FeatureFactoryImpl.cpp:141] time_zone=28800
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__bnum_tylv31_tv,ts,60000,lsl__fcts_ob4_v2__bnum_tylv31_tv,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__btv_tylv31,ts,60000,lsl__fcts_ob4_v2__btv_tylv31,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__cnum_tylv31_tv,ts,60000,lsl__fcts_ob4_v2__cnum_tylv31_tv,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__ctv_tylv31,ts,60000,lsl__fcts_ob4_v2__ctv_tylv31,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__banum_tylv31_tv,ts,60000,lsl__fcts_ob4_v2__banum_tylv31_tv,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__batv_tylv31,ts,60000,lsl__fcts_ob4_v2__batv_tylv31,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__cnum_pct_tylv31_tv,ts,60000,lsl__fcts_ob4_v2__cnum_pct_tylv31_tv,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__ctv_pct_tylv31,ts,60000,lsl__fcts_ob4_v2__ctv_pct_tylv31,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__bnum_gtlv31_tv,ts,60000,lsl__fcts_ob4_v2__bnum_gtlv31_tv,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__btv_gtlv31,ts,60000,lsl__fcts_ob4_v2__btv_gtlv31,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__cnum_gtlv31_tv,ts,60000,lsl__fcts_ob4_v2__cnum_gtlv31_tv,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__ctv_gtlv31,ts,60000,lsl__fcts_ob4_v2__ctv_gtlv31,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__banum_gtlv31_tv,ts,60000,lsl__fcts_ob4_v2__banum_gtlv31_tv,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__batv_gtlv31,ts,60000,lsl__fcts_ob4_v2__batv_gtlv31,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv,ts,60000,lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__ctv_pct_gtlv31,ts,60000,lsl__fcts_ob4_v2__ctv_pct_gtlv31,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__b2alv1_tvpct,ts,60000,lsl__fcts_ob4_v2__b2alv1_tvpct,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__a2blv1_tvpct,ts,60000,lsl__fcts_ob4_v2__a2blv1_tvpct,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__bnum_tylv31_tv,ts,3,lsl__fcts_ob4_v2__bnum_tylv31_tv,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__btv_tylv31,ts,3,lsl__fcts_ob4_v2__btv_tylv31,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__cnum_tylv31_tv,ts,3,lsl__fcts_ob4_v2__cnum_tylv31_tv,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__ctv_tylv31,ts,3,lsl__fcts_ob4_v2__ctv_tylv31,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__banum_tylv31_tv,ts,3,lsl__fcts_ob4_v2__banum_tylv31_tv,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__batv_tylv31,ts,3,lsl__fcts_ob4_v2__batv_tylv31,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__cnum_pct_tylv31_tv,ts,3,lsl__fcts_ob4_v2__cnum_pct_tylv31_tv,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__ctv_pct_tylv31,ts,3,lsl__fcts_ob4_v2__ctv_pct_tylv31,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__bnum_gtlv31_tv,ts,3,lsl__fcts_ob4_v2__bnum_gtlv31_tv,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__btv_gtlv31,ts,3,lsl__fcts_ob4_v2__btv_gtlv31,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__cnum_gtlv31_tv,ts,3,lsl__fcts_ob4_v2__cnum_gtlv31_tv,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__ctv_gtlv31,ts,3,lsl__fcts_ob4_v2__ctv_gtlv31,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__banum_gtlv31_tv,ts,3,lsl__fcts_ob4_v2__banum_gtlv31_tv,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__batv_gtlv31,ts,3,lsl__fcts_ob4_v2__batv_gtlv31,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv,ts,3,lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv,internal
[2025-05-06 22:58:16.458] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__ctv_pct_gtlv31,ts,3,lsl__fcts_ob4_v2__ctv_pct_gtlv31,internal
[2025-05-06 22:58:16.459] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__b2alv1_tvpct,ts,3,lsl__fcts_ob4_v2__b2alv1_tvpct,internal
[2025-05-06 22:58:16.459] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__a2blv1_tvpct,ts,3,lsl__fcts_ob4_v2__a2blv1_tvpct,internal
[2025-05-06 22:58:16.459] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature raw_gen_info_bar,ts,60000,raw_gen_info_bar,internal
[2025-05-06 22:58:16.459] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:378] Features created are : lsl__fcts_ob4_v2__bnum_tylv31_tv lsl__fcts_ob4_v2__btv_tylv31 lsl__fcts_ob4_v2__cnum_tylv31_tv lsl__fcts_ob4_v2__ctv_tylv31 lsl__fcts_ob4_v2__banum_tylv31_tv lsl__fcts_ob4_v2__batv_tylv31 lsl__fcts_ob4_v2__cnum_pct_tylv31_tv lsl__fcts_ob4_v2__ctv_pct_tylv31 lsl__fcts_ob4_v2__bnum_gtlv31_tv lsl__fcts_ob4_v2__btv_gtlv31 lsl__fcts_ob4_v2__cnum_gtlv31_tv lsl__fcts_ob4_v2__ctv_gtlv31 lsl__fcts_ob4_v2__banum_gtlv31_tv lsl__fcts_ob4_v2__batv_gtlv31 lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv lsl__fcts_ob4_v2__ctv_pct_gtlv31 lsl__fcts_ob4_v2__b2alv1_tvpct lsl__fcts_ob4_v2__a2blv1_tvpct lsl__fcts_ob4_v2__bnum_tylv31_tv lsl__fcts_ob4_v2__btv_tylv31 lsl__fcts_ob4_v2__cnum_tylv31_tv lsl__fcts_ob4_v2__ctv_tylv31 lsl__fcts_ob4_v2__banum_tylv31_tv lsl__fcts_ob4_v2__batv_tylv31 lsl__fcts_ob4_v2__cnum_pct_tylv31_tv lsl__fcts_ob4_v2__ctv_pct_tylv31 lsl__fcts_ob4_v2__bnum_gtlv31_tv lsl__fcts_ob4_v2__btv_gtlv31 lsl__fcts_ob4_v2__cnum_gtlv31_tv lsl__fcts_ob4_v2__ctv_gtlv31 lsl__fcts_ob4_v2__banum_gtlv31_tv lsl__fcts_ob4_v2__batv_gtlv31 lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv lsl__fcts_ob4_v2__ctv_pct_gtlv31 lsl__fcts_ob4_v2__b2alv1_tvpct lsl__fcts_ob4_v2__a2blv1_tvpct raw_gen_info_bar
[2025-05-06 22:58:16.459] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:379] Calculating topology order for features
[2025-05-06 22:58:16.499] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:450] Successfully calculated topology order is : 0 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24 25 26 27 28 29 30 31 32 33 34 35 36
[2025-05-06 22:58:16.499] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:470] Constructing function list for 37 features
[2025-05-06 22:58:16.499] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:709] Creating FC with Category ts upon Horizon market features=36
[2025-05-06 22:58:16.499] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:717] Category ts upon Horizon market features created are : 0:lsl__fcts_ob4_v2__bnum_tylv31_tv 1:lsl__fcts_ob4_v2__btv_tylv31 2:lsl__fcts_ob4_v2__cnum_tylv31_tv 3:lsl__fcts_ob4_v2__ctv_tylv31 4:lsl__fcts_ob4_v2__banum_tylv31_tv 5:lsl__fcts_ob4_v2__batv_tylv31 6:lsl__fcts_ob4_v2__cnum_pct_tylv31_tv 7:lsl__fcts_ob4_v2__ctv_pct_tylv31 8:lsl__fcts_ob4_v2__bnum_gtlv31_tv 9:lsl__fcts_ob4_v2__btv_gtlv31 10:lsl__fcts_ob4_v2__cnum_gtlv31_tv 11:lsl__fcts_ob4_v2__ctv_gtlv31 12:lsl__fcts_ob4_v2__banum_gtlv31_tv 13:lsl__fcts_ob4_v2__batv_gtlv31 14:lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv 15:lsl__fcts_ob4_v2__ctv_pct_gtlv31 16:lsl__fcts_ob4_v2__b2alv1_tvpct 17:lsl__fcts_ob4_v2__a2blv1_tvpct 18:lsl__fcts_ob4_v2__bnum_tylv31_tv 19:lsl__fcts_ob4_v2__btv_tylv31 20:lsl__fcts_ob4_v2__cnum_tylv31_tv 21:lsl__fcts_ob4_v2__ctv_tylv31 22:lsl__fcts_ob4_v2__banum_tylv31_tv 23:lsl__fcts_ob4_v2__batv_tylv31 24:lsl__fcts_ob4_v2__cnum_pct_tylv31_tv 25:lsl__fcts_ob4_v2__ctv_pct_tylv31 26:lsl__fcts_ob4_v2__bnum_gtlv31_tv 27:lsl__fcts_ob4_v2__btv_gtlv31 28:lsl__fcts_ob4_v2__cnum_gtlv31_tv 29:lsl__fcts_ob4_v2__ctv_gtlv31 30:lsl__fcts_ob4_v2__banum_gtlv31_tv 31:lsl__fcts_ob4_v2__batv_gtlv31 32:lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv 33:lsl__fcts_ob4_v2__ctv_pct_gtlv31 34:lsl__fcts_ob4_v2__b2alv1_tvpct 35:lsl__fcts_ob4_v2__a2blv1_tvpct 
[2025-05-06 22:58:16.499] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:725] The Topology of Category ts Horizon market features created are :  0 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24 25 26 27 28 29 30 31 32 33 34 35
[2025-05-06 22:58:16.499] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:726] Successfully Constructing topology list for Category ts Horizon market features
[2025-05-06 22:58:16.499] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:709] Creating FC with Category ts upon Horizon transaction features=36
[2025-05-06 22:58:16.499] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:717] Category ts upon Horizon transaction features created are : 0:lsl__fcts_ob4_v2__bnum_tylv31_tv 1:lsl__fcts_ob4_v2__btv_tylv31 2:lsl__fcts_ob4_v2__cnum_tylv31_tv 3:lsl__fcts_ob4_v2__ctv_tylv31 4:lsl__fcts_ob4_v2__banum_tylv31_tv 5:lsl__fcts_ob4_v2__batv_tylv31 6:lsl__fcts_ob4_v2__cnum_pct_tylv31_tv 7:lsl__fcts_ob4_v2__ctv_pct_tylv31 8:lsl__fcts_ob4_v2__bnum_gtlv31_tv 9:lsl__fcts_ob4_v2__btv_gtlv31 10:lsl__fcts_ob4_v2__cnum_gtlv31_tv 11:lsl__fcts_ob4_v2__ctv_gtlv31 12:lsl__fcts_ob4_v2__banum_gtlv31_tv 13:lsl__fcts_ob4_v2__batv_gtlv31 14:lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv 15:lsl__fcts_ob4_v2__ctv_pct_gtlv31 16:lsl__fcts_ob4_v2__b2alv1_tvpct 17:lsl__fcts_ob4_v2__a2blv1_tvpct 18:lsl__fcts_ob4_v2__bnum_tylv31_tv 19:lsl__fcts_ob4_v2__btv_tylv31 20:lsl__fcts_ob4_v2__cnum_tylv31_tv 21:lsl__fcts_ob4_v2__ctv_tylv31 22:lsl__fcts_ob4_v2__banum_tylv31_tv 23:lsl__fcts_ob4_v2__batv_tylv31 24:lsl__fcts_ob4_v2__cnum_pct_tylv31_tv 25:lsl__fcts_ob4_v2__ctv_pct_tylv31 26:lsl__fcts_ob4_v2__bnum_gtlv31_tv 27:lsl__fcts_ob4_v2__btv_gtlv31 28:lsl__fcts_ob4_v2__cnum_gtlv31_tv 29:lsl__fcts_ob4_v2__ctv_gtlv31 30:lsl__fcts_ob4_v2__banum_gtlv31_tv 31:lsl__fcts_ob4_v2__batv_gtlv31 32:lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv 33:lsl__fcts_ob4_v2__ctv_pct_gtlv31 34:lsl__fcts_ob4_v2__b2alv1_tvpct 35:lsl__fcts_ob4_v2__a2blv1_tvpct 
[2025-05-06 22:58:16.499] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:725] The Topology of Category ts Horizon transaction features created are :  0 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24 25 26 27 28 29 30 31 32 33 34 35
[2025-05-06 22:58:16.499] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:726] Successfully Constructing topology list for Category ts Horizon transaction features
[2025-05-06 22:58:16.500] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:709] Creating FC with Category ts upon Horizon order features=36
[2025-05-06 22:58:16.500] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:717] Category ts upon Horizon order features created are : 0:lsl__fcts_ob4_v2__bnum_tylv31_tv 1:lsl__fcts_ob4_v2__btv_tylv31 2:lsl__fcts_ob4_v2__cnum_tylv31_tv 3:lsl__fcts_ob4_v2__ctv_tylv31 4:lsl__fcts_ob4_v2__banum_tylv31_tv 5:lsl__fcts_ob4_v2__batv_tylv31 6:lsl__fcts_ob4_v2__cnum_pct_tylv31_tv 7:lsl__fcts_ob4_v2__ctv_pct_tylv31 8:lsl__fcts_ob4_v2__bnum_gtlv31_tv 9:lsl__fcts_ob4_v2__btv_gtlv31 10:lsl__fcts_ob4_v2__cnum_gtlv31_tv 11:lsl__fcts_ob4_v2__ctv_gtlv31 12:lsl__fcts_ob4_v2__banum_gtlv31_tv 13:lsl__fcts_ob4_v2__batv_gtlv31 14:lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv 15:lsl__fcts_ob4_v2__ctv_pct_gtlv31 16:lsl__fcts_ob4_v2__b2alv1_tvpct 17:lsl__fcts_ob4_v2__a2blv1_tvpct 18:lsl__fcts_ob4_v2__bnum_tylv31_tv 19:lsl__fcts_ob4_v2__btv_tylv31 20:lsl__fcts_ob4_v2__cnum_tylv31_tv 21:lsl__fcts_ob4_v2__ctv_tylv31 22:lsl__fcts_ob4_v2__banum_tylv31_tv 23:lsl__fcts_ob4_v2__batv_tylv31 24:lsl__fcts_ob4_v2__cnum_pct_tylv31_tv 25:lsl__fcts_ob4_v2__ctv_pct_tylv31 26:lsl__fcts_ob4_v2__bnum_gtlv31_tv 27:lsl__fcts_ob4_v2__btv_gtlv31 28:lsl__fcts_ob4_v2__cnum_gtlv31_tv 29:lsl__fcts_ob4_v2__ctv_gtlv31 30:lsl__fcts_ob4_v2__banum_gtlv31_tv 31:lsl__fcts_ob4_v2__batv_gtlv31 32:lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv 33:lsl__fcts_ob4_v2__ctv_pct_gtlv31 34:lsl__fcts_ob4_v2__b2alv1_tvpct 35:lsl__fcts_ob4_v2__a2blv1_tvpct 
[2025-05-06 22:58:16.500] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:725] The Topology of Category ts Horizon order features created are :  0 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24 25 26 27 28 29 30 31 32 33 34 35
[2025-05-06 22:58:16.500] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:726] Successfully Constructing topology list for Category ts Horizon order features
[2025-05-06 22:58:16.500] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:709] Creating FC with Category ts upon Horizon quote features=0
[2025-05-06 22:58:16.500] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:709] Creating FC with Category ts upon Horizon depth features=0
[2025-05-06 22:58:16.500] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:709] Creating FC with Category ts upon Horizon derivative features=0
[2025-05-06 22:58:16.500] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:709] Creating FC with Category ts upon Horizon 1minbar features=19
[2025-05-06 22:58:16.500] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:717] Category ts upon Horizon 1minbar features created are : 0:lsl__fcts_ob4_v2__bnum_tylv31_tv 1:lsl__fcts_ob4_v2__btv_tylv31 2:lsl__fcts_ob4_v2__cnum_tylv31_tv 3:lsl__fcts_ob4_v2__ctv_tylv31 4:lsl__fcts_ob4_v2__banum_tylv31_tv 5:lsl__fcts_ob4_v2__batv_tylv31 6:lsl__fcts_ob4_v2__cnum_pct_tylv31_tv 7:lsl__fcts_ob4_v2__ctv_pct_tylv31 8:lsl__fcts_ob4_v2__bnum_gtlv31_tv 9:lsl__fcts_ob4_v2__btv_gtlv31 10:lsl__fcts_ob4_v2__cnum_gtlv31_tv 11:lsl__fcts_ob4_v2__ctv_gtlv31 12:lsl__fcts_ob4_v2__banum_gtlv31_tv 13:lsl__fcts_ob4_v2__batv_gtlv31 14:lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv 15:lsl__fcts_ob4_v2__ctv_pct_gtlv31 16:lsl__fcts_ob4_v2__b2alv1_tvpct 17:lsl__fcts_ob4_v2__a2blv1_tvpct 18:raw_gen_info_bar 
[2025-05-06 22:58:16.500] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:725] The Topology of Category ts Horizon 1minbar features created are :  0 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18
[2025-05-06 22:58:16.500] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:726] Successfully Constructing topology list for Category ts Horizon 1minbar features
[2025-05-06 22:58:16.500] [fg] [info] [2492060:2492060] [CreateFeatureCalc] [FeatureFactoryImpl.cpp:254] [FeatureCalcInit] Created FC with Category ts Horizon market Python Features=0
[2025-05-06 22:58:16.500] [fg] [info] [2492060:2492060] [CreateFeatureCalc] [FeatureFactoryImpl.cpp:254] [FeatureCalcInit] Created FC with Category ts Horizon transaction Python Features=0
[2025-05-06 22:58:16.500] [fg] [info] [2492060:2492060] [CreateFeatureCalc] [FeatureFactoryImpl.cpp:254] [FeatureCalcInit] Created FC with Category ts Horizon order Python Features=0
[2025-05-06 22:58:16.500] [fg] [info] [2492060:2492060] [CreateFeatureCalc] [FeatureFactoryImpl.cpp:254] [FeatureCalcInit] Created FC with Category ts Horizon quote Python Features=0
[2025-05-06 22:58:16.500] [fg] [info] [2492060:2492060] [CreateFeatureCalc] [FeatureFactoryImpl.cpp:254] [FeatureCalcInit] Created FC with Category ts Horizon depth Python Features=0
[2025-05-06 22:58:16.500] [fg] [info] [2492060:2492060] [CreateFeatureCalc] [FeatureFactoryImpl.cpp:254] [FeatureCalcInit] Created FC with Category ts Horizon derivative Python Features=0
[2025-05-06 22:58:16.500] [fg] [info] [2492060:2492060] [CreateFeatureCalc] [FeatureFactoryImpl.cpp:254] [FeatureCalcInit] Created FC with Category ts Horizon 1minbar Python Features=0
[2025-05-06 22:58:16.500] [fg] [info] [2492060:2492060] [FeatureContextImpl] [FeatureContextImpl.cpp:128] oneDaySeconds=15000
[2025-05-06 22:58:16.500] [fg] [info] [2492060:2492060] [FeatureContextImpl] [FeatureContextImpl.cpp:141] date=20180102, preLoadHistoryBarDayLength=0
[2025-05-06 22:58:16.500] [fg] [info] [2492060:2492060] [FeatureContextImpl] [FeatureContextImpl.cpp:143] preloadStartDate=20180102
[2025-05-06 22:58:16.503] [fg] [info] [2492060:2492060] [FeatureContextImpl] [FeatureContextImpl.cpp:187] start to init ts feature
[2025-05-06 22:58:16.512] [fg] [info] [2492060:2492060] [FeatureContextImpl] [FeatureContextImpl.cpp:206] start to init cs feature
[2025-05-06 22:58:16.512] [fg] [info] [2492060:2492060] [FeatureContextImpl] [FeatureContextImpl.cpp:270] Start to create ts threads, ts_start_core=0, ts_threads=1
[2025-05-06 22:58:16.512] [fg] [info] [2492060:2492060] [FeatureContextImpl] [FeatureContextImpl.cpp:321] Start to create cs threads, cs_threads=1
[2025-05-06 22:58:16.512] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1059] Start to iterate all tradingDays
[2025-05-06 22:58:16.512] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1078] Start to handle trading day:2018-01-02
[2025-05-06 22:58:16.512] [fg] [info] [2492060:2492060] [GetProcMemUsage] [featureGenerator.cpp:956] before input create Resident Memory=35MB
[2025-05-06 22:58:16.512] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1103] [DATA LOAD] converrot=ashare, timezone=8
[2025-05-06 22:58:16.512] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1163] [DATA LOAD] dtype=1minbar, state=3298534893328, threading_read=true, streaming_read=true, read_chunk_num=10000, load_file_path=/mnt/sda/NAS/AllData/cn_ashare/1minute/ohlcvt/2018-01-02.h5, symbol_size=3
[2025-05-06 22:58:18.302] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1163] [DATA LOAD] dtype=market, state=3298534893328, threading_read=true, streaming_read=true, read_chunk_num=10000, load_file_path=/mnt/sda/NAS/AllData/cn_ashare/tick/raw_wangbo_market__complete/2018-01-02.h5, symbol_size=3
[2025-05-06 22:58:18.404] [fg] [info] [2492060:2492060] [initGroup] [Hdf5.cpp:838] group num:3250
[2025-05-06 22:58:20.959] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1163] [DATA LOAD] dtype=order, state=3298534893328, threading_read=true, streaming_read=true, read_chunk_num=10000, load_file_path=/mnt/sda/NAS/AllData/cn_ashare/tick/raw_wangbo_order__complete/2018-01-02.h5, symbol_size=3
[2025-05-06 22:58:21.064] [fg] [info] [2492060:2492060] [initGroup] [Hdf5.cpp:838] group num:3250
[2025-05-06 22:58:23.238] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1163] [DATA LOAD] dtype=transaction, state=3298534893328, threading_read=true, streaming_read=true, read_chunk_num=10000, load_file_path=/mnt/sda/NAS/AllData/cn_ashare/tick/raw_wangbo_transaction__complete/2018-01-02.h5, symbol_size=3
[2025-05-06 22:58:23.347] [fg] [info] [2492060:2492060] [initGroup] [Hdf5.cpp:838] group num:3250
[2025-05-06 22:58:25.544] [fg] [info] [2492060:2492060] [GetProcMemUsage] [featureGenerator.cpp:956] after input create Resident Memory=406MB
[2025-05-06 22:58:25.544] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1218] Start to initialize the replay manager
[2025-05-06 22:58:25.546] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1221] Finished initialize the replay manager
[2025-05-06 22:58:25.546] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1223] Start to initialize the result stream
[2025-05-06 22:58:25.546] [fg] [info] [2492060:2492060] [GetProcMemUsage] [featureGenerator.cpp:956] before output create Resident Memory=406MB
[2025-05-06 22:58:25.549] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1329] [DATA SAVE] streaming_dump=false, threading_dump=false, save_file_path=/mnt/sda/NAS/ShareFolder/lishuanglin/features//cn_ashare/1minute/lsl__fcts_ob4_v2/2018-01-02.h5, save_feature_list_size=18, bar_length=241
[2025-05-06 22:58:25.552] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1329] [DATA SAVE] streaming_dump=false, threading_dump=false, save_file_path=/mnt/sda/NAS/ShareFolder/lishuanglin/features//cn_ashare/tick/lsl__fcts_ob4_v2/2018-01-02.h5, save_feature_list_size=18, bar_length=0
[2025-05-06 22:58:25.552] [fg] [info] [2492060:2492060] [GetProcMemUsage] [featureGenerator.cpp:956] after output create Resident Memory=409MB
[2025-05-06 22:58:25.552] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1334] Finished initialize the result bar
[2025-05-06 22:58:25.552] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1336] Start to initialize the FC featureValueArray callback handler
[2025-05-06 22:58:25.552] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1490] Finished initialize the FC featureValueArray callback handler
[2025-05-06 22:58:25.552] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1492] Start to replay all the data, this step may take long time, please wait...
[2025-05-06 22:58:25.552] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1495] anchor_interval=60000
[2025-05-06 22:58:25.567] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 09:15:00
[2025-05-06 22:58:25.575] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 09:25:00
[2025-05-06 22:58:25.586] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 09:30:00
[2025-05-06 22:58:25.660] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 09:40:00
[2025-05-06 22:58:25.758] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 09:50:00
[2025-05-06 22:58:25.893] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 10:00:00
[2025-05-06 22:58:25.974] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 10:10:00
[2025-05-06 22:58:26.030] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 10:20:00
[2025-05-06 22:58:26.094] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 10:30:00
[2025-05-06 22:58:26.143] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 10:40:00
[2025-05-06 22:58:26.205] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 10:50:00
[2025-05-06 22:58:26.255] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 11:00:00
[2025-05-06 22:58:26.288] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 11:10:00
[2025-05-06 22:58:26.326] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 11:20:00
[2025-05-06 22:58:26.359] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 11:30:00
[2025-05-06 22:58:26.359] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 11:40:00
[2025-05-06 22:58:26.360] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 11:50:00
[2025-05-06 22:58:26.361] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 12:00:00
[2025-05-06 22:58:26.361] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 12:10:00
[2025-05-06 22:58:26.362] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 12:20:00
[2025-05-06 22:58:26.363] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 12:30:00
[2025-05-06 22:58:26.363] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 12:40:00
[2025-05-06 22:58:26.364] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 12:50:00
[2025-05-06 22:58:26.380] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 13:00:00
[2025-05-06 22:58:26.417] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 13:10:00
[2025-05-06 22:58:26.449] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 13:20:00
[2025-05-06 22:58:26.475] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 13:30:00
[2025-05-06 22:58:26.524] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 13:40:00
[2025-05-06 22:58:26.561] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 13:50:00
[2025-05-06 22:58:26.604] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 14:00:00
[2025-05-06 22:58:26.638] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 14:10:00
[2025-05-06 22:58:26.674] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 14:20:00
[2025-05-06 22:58:26.708] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 14:30:00
[2025-05-06 22:58:26.742] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 14:40:00
[2025-05-06 22:58:26.787] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 14:50:00
[2025-05-06 22:58:26.819] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-02 14:57:00
[2025-05-06 22:58:26.825] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1695] Already replayed market to timestamp 2018-01-02 23:59:59
[2025-05-06 22:58:26.825] [fg] [info] [2492060:2492060] [GetProcMemUsage] [featureGenerator.cpp:956] before input close Resident Memory=417MB
[2025-05-06 22:58:26.825] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1710] Start to close input files
[2025-05-06 22:58:26.847] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1722] Input files are closed
[2025-05-06 22:58:26.847] [fg] [info] [2492060:2492060] [GetProcMemUsage] [featureGenerator.cpp:956] after input close Resident Memory=82MB
[2025-05-06 22:58:26.847] [fg] [info] [2492060:2492060] [GetProcMemUsage] [featureGenerator.cpp:956] before output close Resident Memory=82MB
[2025-05-06 22:58:26.847] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1737] Start to save result feature 
[2025-05-06 22:58:26.847] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1753] bar length=241
[2025-05-06 22:58:26.850] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1775] Closing output file /mnt/sda/NAS/ShareFolder/lishuanglin/features//cn_ashare/1minute/lsl__fcts_ob4_v2/2018-01-02.h5
[2025-05-06 22:58:26.851] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1779] Result feature saving is finished
[2025-05-06 22:58:26.851] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1737] Start to save result feature 
[2025-05-06 22:58:26.863] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1775] Closing output file /mnt/sda/NAS/ShareFolder/lishuanglin/features//cn_ashare/tick/lsl__fcts_ob4_v2/2018-01-02.h5
[2025-05-06 22:58:26.864] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1779] Result feature saving is finished
[2025-05-06 22:58:26.864] [fg] [info] [2492060:2492060] [GetProcMemUsage] [featureGenerator.cpp:956] after output close Resident Memory=82MB
[2025-05-06 22:58:26.864] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1065] Reinitilization of fc for date 2018-01-03
[2025-05-06 22:58:26.865] [fg] [info] [2492060:2492060] [initializeExchangeFeatureCalculator] [featureGenerator.cpp:711] Load Symbol List Path=./config/S/cn_ashare/symbol.txt
[2025-05-06 22:58:26.865] [fg] [info] [2492060:2492060] [readStrVecFromFile] [Util.h:389] Read fileName=./config/S/cn_ashare/symbol.txt
[2025-05-06 22:58:26.865] [fg] [info] [2492060:2492060] [initializeExchangeFeatureCalculator] [featureGenerator.cpp:846] nTradingDay=20180103, tsStartCore=0, tsFeatureThread=1, csStartCore=2, csFeatureThread=1, featureManifestPath=./config/fc/cn_ashare/feature_manifest.json, preLoadDays=0, maxTickLength=1000, maxFeatureLength=1000
[2025-05-06 22:58:26.874] [fg] [info] [2492060:2492060] [initializeExchangeFeatureCalculator] [featureGenerator.cpp:852] Get Symbol Load Finished.
[2025-05-06 22:58:26.874] [fg] [info] [2492060:2492060] [FeatureFactoryImpl] [FeatureFactoryImpl.cpp:47] Loading /mnt/sda/home/<USER>/FC/cn_ashare_new/orderbook/config/fc/cn_ashare/feature_manifest.json as as manifest file
[2025-05-06 22:58:26.874] [fg] [info] [2492060:2492060] [CreateFeatureCalc] [FeatureFactoryImpl.cpp:164] [FeatureCalcInit] There are total 5 meta-features in feature library
[2025-05-06 22:58:26.874] [fg] [info] [2492060:2492060] [CreateFeatureCalc] [FeatureFactoryImpl.cpp:166] TradingDayFilePath=/mnt/sda/NAS/AllData/config/cn_ashare_trading_days.txt, ExchangeGroupInfoFilePath=/mnt/sda/NAS/AllData/config/exchange_groups/cn_ashare.json
[2025-05-06 22:58:26.876] [fg] [info] [2492060:2492060] [GetExchangeGroupInfo] [FeatureFactoryImpl.cpp:140] 0: begin_time=91500000, end_time=92500000, type=A
[2025-05-06 22:58:26.876] [fg] [info] [2492060:2492060] [GetExchangeGroupInfo] [FeatureFactoryImpl.cpp:140] 1: begin_time=92500000, end_time=93000000, type=I
[2025-05-06 22:58:26.876] [fg] [info] [2492060:2492060] [GetExchangeGroupInfo] [FeatureFactoryImpl.cpp:140] 2: begin_time=93000000, end_time=113000000, type=O
[2025-05-06 22:58:26.876] [fg] [info] [2492060:2492060] [GetExchangeGroupInfo] [FeatureFactoryImpl.cpp:140] 3: begin_time=113000000, end_time=130000000, type=I
[2025-05-06 22:58:26.876] [fg] [info] [2492060:2492060] [GetExchangeGroupInfo] [FeatureFactoryImpl.cpp:140] 4: begin_time=130000000, end_time=145700000, type=O
[2025-05-06 22:58:26.876] [fg] [info] [2492060:2492060] [GetExchangeGroupInfo] [FeatureFactoryImpl.cpp:140] 5: begin_time=145700000, end_time=150000000, type=A
[2025-05-06 22:58:26.876] [fg] [info] [2492060:2492060] [GetExchangeGroupInfo] [FeatureFactoryImpl.cpp:141] time_zone=28800
[2025-05-06 22:58:26.877] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__bnum_tylv31_tv,ts,60000,lsl__fcts_ob4_v2__bnum_tylv31_tv,internal
[2025-05-06 22:58:26.877] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__btv_tylv31,ts,60000,lsl__fcts_ob4_v2__btv_tylv31,internal
[2025-05-06 22:58:26.877] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__cnum_tylv31_tv,ts,60000,lsl__fcts_ob4_v2__cnum_tylv31_tv,internal
[2025-05-06 22:58:26.877] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__ctv_tylv31,ts,60000,lsl__fcts_ob4_v2__ctv_tylv31,internal
[2025-05-06 22:58:26.877] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__banum_tylv31_tv,ts,60000,lsl__fcts_ob4_v2__banum_tylv31_tv,internal
[2025-05-06 22:58:26.877] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__batv_tylv31,ts,60000,lsl__fcts_ob4_v2__batv_tylv31,internal
[2025-05-06 22:58:26.877] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__cnum_pct_tylv31_tv,ts,60000,lsl__fcts_ob4_v2__cnum_pct_tylv31_tv,internal
[2025-05-06 22:58:26.877] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__ctv_pct_tylv31,ts,60000,lsl__fcts_ob4_v2__ctv_pct_tylv31,internal
[2025-05-06 22:58:26.877] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__bnum_gtlv31_tv,ts,60000,lsl__fcts_ob4_v2__bnum_gtlv31_tv,internal
[2025-05-06 22:58:26.877] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__btv_gtlv31,ts,60000,lsl__fcts_ob4_v2__btv_gtlv31,internal
[2025-05-06 22:58:26.877] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__cnum_gtlv31_tv,ts,60000,lsl__fcts_ob4_v2__cnum_gtlv31_tv,internal
[2025-05-06 22:58:26.877] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__ctv_gtlv31,ts,60000,lsl__fcts_ob4_v2__ctv_gtlv31,internal
[2025-05-06 22:58:26.877] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__banum_gtlv31_tv,ts,60000,lsl__fcts_ob4_v2__banum_gtlv31_tv,internal
[2025-05-06 22:58:26.877] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__batv_gtlv31,ts,60000,lsl__fcts_ob4_v2__batv_gtlv31,internal
[2025-05-06 22:58:26.877] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv,ts,60000,lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv,internal
[2025-05-06 22:58:26.877] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__ctv_pct_gtlv31,ts,60000,lsl__fcts_ob4_v2__ctv_pct_gtlv31,internal
[2025-05-06 22:58:26.878] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__b2alv1_tvpct,ts,60000,lsl__fcts_ob4_v2__b2alv1_tvpct,internal
[2025-05-06 22:58:26.878] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__a2blv1_tvpct,ts,60000,lsl__fcts_ob4_v2__a2blv1_tvpct,internal
[2025-05-06 22:58:26.878] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__bnum_tylv31_tv,ts,3,lsl__fcts_ob4_v2__bnum_tylv31_tv,internal
[2025-05-06 22:58:26.878] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__btv_tylv31,ts,3,lsl__fcts_ob4_v2__btv_tylv31,internal
[2025-05-06 22:58:26.878] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__cnum_tylv31_tv,ts,3,lsl__fcts_ob4_v2__cnum_tylv31_tv,internal
[2025-05-06 22:58:26.878] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__ctv_tylv31,ts,3,lsl__fcts_ob4_v2__ctv_tylv31,internal
[2025-05-06 22:58:26.878] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__banum_tylv31_tv,ts,3,lsl__fcts_ob4_v2__banum_tylv31_tv,internal
[2025-05-06 22:58:26.878] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__batv_tylv31,ts,3,lsl__fcts_ob4_v2__batv_tylv31,internal
[2025-05-06 22:58:26.878] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__cnum_pct_tylv31_tv,ts,3,lsl__fcts_ob4_v2__cnum_pct_tylv31_tv,internal
[2025-05-06 22:58:26.878] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__ctv_pct_tylv31,ts,3,lsl__fcts_ob4_v2__ctv_pct_tylv31,internal
[2025-05-06 22:58:26.878] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__bnum_gtlv31_tv,ts,3,lsl__fcts_ob4_v2__bnum_gtlv31_tv,internal
[2025-05-06 22:58:26.878] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__btv_gtlv31,ts,3,lsl__fcts_ob4_v2__btv_gtlv31,internal
[2025-05-06 22:58:26.878] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__cnum_gtlv31_tv,ts,3,lsl__fcts_ob4_v2__cnum_gtlv31_tv,internal
[2025-05-06 22:58:26.878] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__ctv_gtlv31,ts,3,lsl__fcts_ob4_v2__ctv_gtlv31,internal
[2025-05-06 22:58:26.878] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__banum_gtlv31_tv,ts,3,lsl__fcts_ob4_v2__banum_gtlv31_tv,internal
[2025-05-06 22:58:26.878] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__batv_gtlv31,ts,3,lsl__fcts_ob4_v2__batv_gtlv31,internal
[2025-05-06 22:58:26.878] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv,ts,3,lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv,internal
[2025-05-06 22:58:26.878] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__ctv_pct_gtlv31,ts,3,lsl__fcts_ob4_v2__ctv_pct_gtlv31,internal
[2025-05-06 22:58:26.878] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__b2alv1_tvpct,ts,3,lsl__fcts_ob4_v2__b2alv1_tvpct,internal
[2025-05-06 22:58:26.878] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature lsl__fcts_ob4_v2__a2blv1_tvpct,ts,3,lsl__fcts_ob4_v2__a2blv1_tvpct,internal
[2025-05-06 22:58:26.878] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:362] Successfully created feature raw_gen_info_bar,ts,60000,raw_gen_info_bar,internal
[2025-05-06 22:58:26.878] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:378] Features created are : lsl__fcts_ob4_v2__bnum_tylv31_tv lsl__fcts_ob4_v2__btv_tylv31 lsl__fcts_ob4_v2__cnum_tylv31_tv lsl__fcts_ob4_v2__ctv_tylv31 lsl__fcts_ob4_v2__banum_tylv31_tv lsl__fcts_ob4_v2__batv_tylv31 lsl__fcts_ob4_v2__cnum_pct_tylv31_tv lsl__fcts_ob4_v2__ctv_pct_tylv31 lsl__fcts_ob4_v2__bnum_gtlv31_tv lsl__fcts_ob4_v2__btv_gtlv31 lsl__fcts_ob4_v2__cnum_gtlv31_tv lsl__fcts_ob4_v2__ctv_gtlv31 lsl__fcts_ob4_v2__banum_gtlv31_tv lsl__fcts_ob4_v2__batv_gtlv31 lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv lsl__fcts_ob4_v2__ctv_pct_gtlv31 lsl__fcts_ob4_v2__b2alv1_tvpct lsl__fcts_ob4_v2__a2blv1_tvpct lsl__fcts_ob4_v2__bnum_tylv31_tv lsl__fcts_ob4_v2__btv_tylv31 lsl__fcts_ob4_v2__cnum_tylv31_tv lsl__fcts_ob4_v2__ctv_tylv31 lsl__fcts_ob4_v2__banum_tylv31_tv lsl__fcts_ob4_v2__batv_tylv31 lsl__fcts_ob4_v2__cnum_pct_tylv31_tv lsl__fcts_ob4_v2__ctv_pct_tylv31 lsl__fcts_ob4_v2__bnum_gtlv31_tv lsl__fcts_ob4_v2__btv_gtlv31 lsl__fcts_ob4_v2__cnum_gtlv31_tv lsl__fcts_ob4_v2__ctv_gtlv31 lsl__fcts_ob4_v2__banum_gtlv31_tv lsl__fcts_ob4_v2__batv_gtlv31 lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv lsl__fcts_ob4_v2__ctv_pct_gtlv31 lsl__fcts_ob4_v2__b2alv1_tvpct lsl__fcts_ob4_v2__a2blv1_tvpct raw_gen_info_bar
[2025-05-06 22:58:26.878] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:379] Calculating topology order for features
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:450] Successfully calculated topology order is : 0 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24 25 26 27 28 29 30 31 32 33 34 35 36
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:470] Constructing function list for 37 features
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:709] Creating FC with Category ts upon Horizon market features=36
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:717] Category ts upon Horizon market features created are : 0:lsl__fcts_ob4_v2__bnum_tylv31_tv 1:lsl__fcts_ob4_v2__btv_tylv31 2:lsl__fcts_ob4_v2__cnum_tylv31_tv 3:lsl__fcts_ob4_v2__ctv_tylv31 4:lsl__fcts_ob4_v2__banum_tylv31_tv 5:lsl__fcts_ob4_v2__batv_tylv31 6:lsl__fcts_ob4_v2__cnum_pct_tylv31_tv 7:lsl__fcts_ob4_v2__ctv_pct_tylv31 8:lsl__fcts_ob4_v2__bnum_gtlv31_tv 9:lsl__fcts_ob4_v2__btv_gtlv31 10:lsl__fcts_ob4_v2__cnum_gtlv31_tv 11:lsl__fcts_ob4_v2__ctv_gtlv31 12:lsl__fcts_ob4_v2__banum_gtlv31_tv 13:lsl__fcts_ob4_v2__batv_gtlv31 14:lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv 15:lsl__fcts_ob4_v2__ctv_pct_gtlv31 16:lsl__fcts_ob4_v2__b2alv1_tvpct 17:lsl__fcts_ob4_v2__a2blv1_tvpct 18:lsl__fcts_ob4_v2__bnum_tylv31_tv 19:lsl__fcts_ob4_v2__btv_tylv31 20:lsl__fcts_ob4_v2__cnum_tylv31_tv 21:lsl__fcts_ob4_v2__ctv_tylv31 22:lsl__fcts_ob4_v2__banum_tylv31_tv 23:lsl__fcts_ob4_v2__batv_tylv31 24:lsl__fcts_ob4_v2__cnum_pct_tylv31_tv 25:lsl__fcts_ob4_v2__ctv_pct_tylv31 26:lsl__fcts_ob4_v2__bnum_gtlv31_tv 27:lsl__fcts_ob4_v2__btv_gtlv31 28:lsl__fcts_ob4_v2__cnum_gtlv31_tv 29:lsl__fcts_ob4_v2__ctv_gtlv31 30:lsl__fcts_ob4_v2__banum_gtlv31_tv 31:lsl__fcts_ob4_v2__batv_gtlv31 32:lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv 33:lsl__fcts_ob4_v2__ctv_pct_gtlv31 34:lsl__fcts_ob4_v2__b2alv1_tvpct 35:lsl__fcts_ob4_v2__a2blv1_tvpct 
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:725] The Topology of Category ts Horizon market features created are :  0 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24 25 26 27 28 29 30 31 32 33 34 35
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:726] Successfully Constructing topology list for Category ts Horizon market features
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:709] Creating FC with Category ts upon Horizon transaction features=36
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:717] Category ts upon Horizon transaction features created are : 0:lsl__fcts_ob4_v2__bnum_tylv31_tv 1:lsl__fcts_ob4_v2__btv_tylv31 2:lsl__fcts_ob4_v2__cnum_tylv31_tv 3:lsl__fcts_ob4_v2__ctv_tylv31 4:lsl__fcts_ob4_v2__banum_tylv31_tv 5:lsl__fcts_ob4_v2__batv_tylv31 6:lsl__fcts_ob4_v2__cnum_pct_tylv31_tv 7:lsl__fcts_ob4_v2__ctv_pct_tylv31 8:lsl__fcts_ob4_v2__bnum_gtlv31_tv 9:lsl__fcts_ob4_v2__btv_gtlv31 10:lsl__fcts_ob4_v2__cnum_gtlv31_tv 11:lsl__fcts_ob4_v2__ctv_gtlv31 12:lsl__fcts_ob4_v2__banum_gtlv31_tv 13:lsl__fcts_ob4_v2__batv_gtlv31 14:lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv 15:lsl__fcts_ob4_v2__ctv_pct_gtlv31 16:lsl__fcts_ob4_v2__b2alv1_tvpct 17:lsl__fcts_ob4_v2__a2blv1_tvpct 18:lsl__fcts_ob4_v2__bnum_tylv31_tv 19:lsl__fcts_ob4_v2__btv_tylv31 20:lsl__fcts_ob4_v2__cnum_tylv31_tv 21:lsl__fcts_ob4_v2__ctv_tylv31 22:lsl__fcts_ob4_v2__banum_tylv31_tv 23:lsl__fcts_ob4_v2__batv_tylv31 24:lsl__fcts_ob4_v2__cnum_pct_tylv31_tv 25:lsl__fcts_ob4_v2__ctv_pct_tylv31 26:lsl__fcts_ob4_v2__bnum_gtlv31_tv 27:lsl__fcts_ob4_v2__btv_gtlv31 28:lsl__fcts_ob4_v2__cnum_gtlv31_tv 29:lsl__fcts_ob4_v2__ctv_gtlv31 30:lsl__fcts_ob4_v2__banum_gtlv31_tv 31:lsl__fcts_ob4_v2__batv_gtlv31 32:lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv 33:lsl__fcts_ob4_v2__ctv_pct_gtlv31 34:lsl__fcts_ob4_v2__b2alv1_tvpct 35:lsl__fcts_ob4_v2__a2blv1_tvpct 
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:725] The Topology of Category ts Horizon transaction features created are :  0 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24 25 26 27 28 29 30 31 32 33 34 35
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:726] Successfully Constructing topology list for Category ts Horizon transaction features
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:709] Creating FC with Category ts upon Horizon order features=36
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:717] Category ts upon Horizon order features created are : 0:lsl__fcts_ob4_v2__bnum_tylv31_tv 1:lsl__fcts_ob4_v2__btv_tylv31 2:lsl__fcts_ob4_v2__cnum_tylv31_tv 3:lsl__fcts_ob4_v2__ctv_tylv31 4:lsl__fcts_ob4_v2__banum_tylv31_tv 5:lsl__fcts_ob4_v2__batv_tylv31 6:lsl__fcts_ob4_v2__cnum_pct_tylv31_tv 7:lsl__fcts_ob4_v2__ctv_pct_tylv31 8:lsl__fcts_ob4_v2__bnum_gtlv31_tv 9:lsl__fcts_ob4_v2__btv_gtlv31 10:lsl__fcts_ob4_v2__cnum_gtlv31_tv 11:lsl__fcts_ob4_v2__ctv_gtlv31 12:lsl__fcts_ob4_v2__banum_gtlv31_tv 13:lsl__fcts_ob4_v2__batv_gtlv31 14:lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv 15:lsl__fcts_ob4_v2__ctv_pct_gtlv31 16:lsl__fcts_ob4_v2__b2alv1_tvpct 17:lsl__fcts_ob4_v2__a2blv1_tvpct 18:lsl__fcts_ob4_v2__bnum_tylv31_tv 19:lsl__fcts_ob4_v2__btv_tylv31 20:lsl__fcts_ob4_v2__cnum_tylv31_tv 21:lsl__fcts_ob4_v2__ctv_tylv31 22:lsl__fcts_ob4_v2__banum_tylv31_tv 23:lsl__fcts_ob4_v2__batv_tylv31 24:lsl__fcts_ob4_v2__cnum_pct_tylv31_tv 25:lsl__fcts_ob4_v2__ctv_pct_tylv31 26:lsl__fcts_ob4_v2__bnum_gtlv31_tv 27:lsl__fcts_ob4_v2__btv_gtlv31 28:lsl__fcts_ob4_v2__cnum_gtlv31_tv 29:lsl__fcts_ob4_v2__ctv_gtlv31 30:lsl__fcts_ob4_v2__banum_gtlv31_tv 31:lsl__fcts_ob4_v2__batv_gtlv31 32:lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv 33:lsl__fcts_ob4_v2__ctv_pct_gtlv31 34:lsl__fcts_ob4_v2__b2alv1_tvpct 35:lsl__fcts_ob4_v2__a2blv1_tvpct 
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:725] The Topology of Category ts Horizon order features created are :  0 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24 25 26 27 28 29 30 31 32 33 34 35
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:726] Successfully Constructing topology list for Category ts Horizon order features
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:709] Creating FC with Category ts upon Horizon quote features=0
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:709] Creating FC with Category ts upon Horizon depth features=0
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:709] Creating FC with Category ts upon Horizon derivative features=0
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:709] Creating FC with Category ts upon Horizon 1minbar features=19
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:717] Category ts upon Horizon 1minbar features created are : 0:lsl__fcts_ob4_v2__bnum_tylv31_tv 1:lsl__fcts_ob4_v2__btv_tylv31 2:lsl__fcts_ob4_v2__cnum_tylv31_tv 3:lsl__fcts_ob4_v2__ctv_tylv31 4:lsl__fcts_ob4_v2__banum_tylv31_tv 5:lsl__fcts_ob4_v2__batv_tylv31 6:lsl__fcts_ob4_v2__cnum_pct_tylv31_tv 7:lsl__fcts_ob4_v2__ctv_pct_tylv31 8:lsl__fcts_ob4_v2__bnum_gtlv31_tv 9:lsl__fcts_ob4_v2__btv_gtlv31 10:lsl__fcts_ob4_v2__cnum_gtlv31_tv 11:lsl__fcts_ob4_v2__ctv_gtlv31 12:lsl__fcts_ob4_v2__banum_gtlv31_tv 13:lsl__fcts_ob4_v2__batv_gtlv31 14:lsl__fcts_ob4_v2__cnum_pct_gtlv31_tv 15:lsl__fcts_ob4_v2__ctv_pct_gtlv31 16:lsl__fcts_ob4_v2__b2alv1_tvpct 17:lsl__fcts_ob4_v2__a2blv1_tvpct 18:raw_gen_info_bar 
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:725] The Topology of Category ts Horizon 1minbar features created are :  0 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [InitFeatureCalcIndividualCategory] [FeatureFactoryImpl.cpp:726] Successfully Constructing topology list for Category ts Horizon 1minbar features
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [CreateFeatureCalc] [FeatureFactoryImpl.cpp:254] [FeatureCalcInit] Created FC with Category ts Horizon market Python Features=0
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [CreateFeatureCalc] [FeatureFactoryImpl.cpp:254] [FeatureCalcInit] Created FC with Category ts Horizon transaction Python Features=0
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [CreateFeatureCalc] [FeatureFactoryImpl.cpp:254] [FeatureCalcInit] Created FC with Category ts Horizon order Python Features=0
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [CreateFeatureCalc] [FeatureFactoryImpl.cpp:254] [FeatureCalcInit] Created FC with Category ts Horizon quote Python Features=0
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [CreateFeatureCalc] [FeatureFactoryImpl.cpp:254] [FeatureCalcInit] Created FC with Category ts Horizon depth Python Features=0
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [CreateFeatureCalc] [FeatureFactoryImpl.cpp:254] [FeatureCalcInit] Created FC with Category ts Horizon derivative Python Features=0
[2025-05-06 22:58:26.919] [fg] [info] [2492060:2492060] [CreateFeatureCalc] [FeatureFactoryImpl.cpp:254] [FeatureCalcInit] Created FC with Category ts Horizon 1minbar Python Features=0
[2025-05-06 22:58:26.920] [fg] [info] [2492060:2492060] [FeatureContextImpl] [FeatureContextImpl.cpp:128] oneDaySeconds=15000
[2025-05-06 22:58:26.920] [fg] [info] [2492060:2492060] [FeatureContextImpl] [FeatureContextImpl.cpp:141] date=20180103, preLoadHistoryBarDayLength=0
[2025-05-06 22:58:26.920] [fg] [info] [2492060:2492060] [FeatureContextImpl] [FeatureContextImpl.cpp:143] preloadStartDate=20180103
[2025-05-06 22:58:26.923] [fg] [info] [2492060:2492060] [FeatureContextImpl] [FeatureContextImpl.cpp:187] start to init ts feature
[2025-05-06 22:58:26.932] [fg] [info] [2492060:2492060] [FeatureContextImpl] [FeatureContextImpl.cpp:206] start to init cs feature
[2025-05-06 22:58:26.932] [fg] [info] [2492060:2492060] [FeatureContextImpl] [FeatureContextImpl.cpp:270] Start to create ts threads, ts_start_core=0, ts_threads=1
[2025-05-06 22:58:26.932] [fg] [info] [2492060:2492060] [FeatureContextImpl] [FeatureContextImpl.cpp:321] Start to create cs threads, cs_threads=1
[2025-05-06 22:58:26.932] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1078] Start to handle trading day:2018-01-03
[2025-05-06 22:58:26.932] [fg] [info] [2492060:2492060] [GetProcMemUsage] [featureGenerator.cpp:956] before input create Resident Memory=76MB
[2025-05-06 22:58:26.932] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1103] [DATA LOAD] converrot=ashare, timezone=8
[2025-05-06 22:58:26.932] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1163] [DATA LOAD] dtype=1minbar, state=3298534893328, threading_read=true, streaming_read=true, read_chunk_num=10000, load_file_path=/mnt/sda/NAS/AllData/cn_ashare/1minute/ohlcvt/2018-01-03.h5, symbol_size=3
[2025-05-06 22:58:28.061] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1163] [DATA LOAD] dtype=market, state=3298534893328, threading_read=true, streaming_read=true, read_chunk_num=10000, load_file_path=/mnt/sda/NAS/AllData/cn_ashare/tick/raw_wangbo_market__complete/2018-01-03.h5, symbol_size=3
[2025-05-06 22:58:28.167] [fg] [info] [2492060:2492060] [initGroup] [Hdf5.cpp:838] group num:3258
[2025-05-06 22:58:30.329] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1163] [DATA LOAD] dtype=order, state=3298534893328, threading_read=true, streaming_read=true, read_chunk_num=10000, load_file_path=/mnt/sda/NAS/AllData/cn_ashare/tick/raw_wangbo_order__complete/2018-01-03.h5, symbol_size=3
[2025-05-06 22:58:30.436] [fg] [info] [2492060:2492060] [initGroup] [Hdf5.cpp:838] group num:3258
[2025-05-06 22:58:32.651] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1163] [DATA LOAD] dtype=transaction, state=3298534893328, threading_read=true, streaming_read=true, read_chunk_num=10000, load_file_path=/mnt/sda/NAS/AllData/cn_ashare/tick/raw_wangbo_transaction__complete/2018-01-03.h5, symbol_size=3
[2025-05-06 22:58:32.761] [fg] [info] [2492060:2492060] [initGroup] [Hdf5.cpp:838] group num:3258
[2025-05-06 22:58:35.006] [fg] [info] [2492060:2492060] [GetProcMemUsage] [featureGenerator.cpp:956] after input create Resident Memory=442MB
[2025-05-06 22:58:35.006] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1218] Start to initialize the replay manager
[2025-05-06 22:58:35.007] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1221] Finished initialize the replay manager
[2025-05-06 22:58:35.007] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1223] Start to initialize the result stream
[2025-05-06 22:58:35.007] [fg] [info] [2492060:2492060] [GetProcMemUsage] [featureGenerator.cpp:956] before output create Resident Memory=442MB
[2025-05-06 22:58:35.009] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1329] [DATA SAVE] streaming_dump=false, threading_dump=false, save_file_path=/mnt/sda/NAS/ShareFolder/lishuanglin/features//cn_ashare/1minute/lsl__fcts_ob4_v2/2018-01-03.h5, save_feature_list_size=18, bar_length=241
[2025-05-06 22:58:35.012] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1329] [DATA SAVE] streaming_dump=false, threading_dump=false, save_file_path=/mnt/sda/NAS/ShareFolder/lishuanglin/features//cn_ashare/tick/lsl__fcts_ob4_v2/2018-01-03.h5, save_feature_list_size=18, bar_length=0
[2025-05-06 22:58:35.012] [fg] [info] [2492060:2492060] [GetProcMemUsage] [featureGenerator.cpp:956] after output create Resident Memory=444MB
[2025-05-06 22:58:35.012] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1334] Finished initialize the result bar
[2025-05-06 22:58:35.012] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1336] Start to initialize the FC featureValueArray callback handler
[2025-05-06 22:58:35.012] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1490] Finished initialize the FC featureValueArray callback handler
[2025-05-06 22:58:35.012] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1492] Start to replay all the data, this step may take long time, please wait...
[2025-05-06 22:58:35.012] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1495] anchor_interval=60000
[2025-05-06 22:58:35.031] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 09:15:00
[2025-05-06 22:58:35.041] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 09:25:00
[2025-05-06 22:58:35.057] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 09:30:00
[2025-05-06 22:58:35.129] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 09:40:00
[2025-05-06 22:58:35.198] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 09:50:00
[2025-05-06 22:58:35.254] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 10:00:00
[2025-05-06 22:58:35.314] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 10:10:00
[2025-05-06 22:58:35.380] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 10:20:00
[2025-05-06 22:58:35.458] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 10:30:00
[2025-05-06 22:58:35.529] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 10:40:00
[2025-05-06 22:58:35.589] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 10:50:00
[2025-05-06 22:58:35.652] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 11:00:00
[2025-05-06 22:58:35.707] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 11:10:00
[2025-05-06 22:58:35.754] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 11:20:00
[2025-05-06 22:58:35.789] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 11:30:00
[2025-05-06 22:58:35.790] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 11:40:00
[2025-05-06 22:58:35.791] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 11:50:00
[2025-05-06 22:58:35.792] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 12:00:00
[2025-05-06 22:58:35.792] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 12:10:00
[2025-05-06 22:58:35.793] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 12:20:00
[2025-05-06 22:58:35.794] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 12:30:00
[2025-05-06 22:58:35.794] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 12:40:00
[2025-05-06 22:58:35.795] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 12:50:00
[2025-05-06 22:58:35.812] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 13:00:00
[2025-05-06 22:58:35.852] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 13:10:00
[2025-05-06 22:58:35.905] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 13:20:00
[2025-05-06 22:58:35.959] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 13:30:00
[2025-05-06 22:58:36.029] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 13:40:00
[2025-05-06 22:58:36.117] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 13:50:00
[2025-05-06 22:58:36.174] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 14:00:00
[2025-05-06 22:58:36.246] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 14:10:00
[2025-05-06 22:58:36.299] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 14:20:00
[2025-05-06 22:58:36.358] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 14:30:00
[2025-05-06 22:58:36.508] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 14:40:00
[2025-05-06 22:58:36.662] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 14:50:00
[2025-05-06 22:58:36.740] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1665] Already replayed market to timestamp 2018-01-03 14:57:00
[2025-05-06 22:58:36.754] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1695] Already replayed market to timestamp 2018-01-03 23:59:59
[2025-05-06 22:58:36.754] [fg] [info] [2492060:2492060] [GetProcMemUsage] [featureGenerator.cpp:956] before input close Resident Memory=454MB
[2025-05-06 22:58:36.754] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1710] Start to close input files
[2025-05-06 22:58:36.777] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1722] Input files are closed
[2025-05-06 22:58:36.777] [fg] [info] [2492060:2492060] [GetProcMemUsage] [featureGenerator.cpp:956] after input close Resident Memory=167MB
[2025-05-06 22:58:36.777] [fg] [info] [2492060:2492060] [GetProcMemUsage] [featureGenerator.cpp:956] before output close Resident Memory=167MB
[2025-05-06 22:58:36.777] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1737] Start to save result feature 
[2025-05-06 22:58:36.777] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1753] bar length=241
[2025-05-06 22:58:36.779] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1775] Closing output file /mnt/sda/NAS/ShareFolder/lishuanglin/features//cn_ashare/1minute/lsl__fcts_ob4_v2/2018-01-03.h5
[2025-05-06 22:58:36.781] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1779] Result feature saving is finished
[2025-05-06 22:58:36.781] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1737] Start to save result feature 
[2025-05-06 22:58:36.794] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1775] Closing output file /mnt/sda/NAS/ShareFolder/lishuanglin/features//cn_ashare/tick/lsl__fcts_ob4_v2/2018-01-03.h5
[2025-05-06 22:58:36.796] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1779] Result feature saving is finished
[2025-05-06 22:58:36.796] [fg] [info] [2492060:2492060] [GetProcMemUsage] [featureGenerator.cpp:956] after output close Resident Memory=167MB
[2025-05-06 22:58:36.796] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1784] Finished iterate all tradingDays
[2025-05-06 22:58:36.796] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1803] Start to deinitialize FC Calc, FC Context
[2025-05-06 22:58:36.797] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1806] Finished deinitialize FC Calc, FC Context
[2025-05-06 22:58:36.797] [fg] [info] [2492060:2492060] [featGenOffline] [featureGenerator.cpp:1824] Completed.
