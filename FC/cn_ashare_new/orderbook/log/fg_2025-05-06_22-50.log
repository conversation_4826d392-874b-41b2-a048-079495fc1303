[2025-05-06 22:50:36.960] [fg] [info] [2491012:2491012] [inputArgPaser] [featureGenerator.cpp:349] configuration_file_path=config/fg/cn_ashare/ob4_v2.json
[2025-05-06 22:50:36.960] [fg] [info] [2491012:2491012] [inputArgPaser] [featureGenerator.cpp:423] exchange_name=cn_ashare
[2025-05-06 22:50:36.960] [fg] [warning] [2491012:2491012] [inputArgPaser] [featureGenerator.cpp:485] Please explicitly set a valid maxFeatureLength value.
[2025-05-06 22:50:36.960] [fg] [info] [2491012:2491012] [featGenOffline] [featureGenerator.cpp:1024] Start to initialize trading day list
[2025-05-06 22:50:36.960] [fg] [info] [2491012:2491012] [readStrVecFromFile] [Util.h:389] Read fileName=/mnt/sda/NAS/AllData/config/cn_ashare_trading_days.txt
[2025-05-06 22:50:36.964] [fg] [info] [2491012:2491012] [featGenOffline] [featureGenerator.cpp:1028] Start to initialize each exchange feature calculator
[2025-05-06 22:50:36.964] [fg] [info] [2491012:2491012] [initializeExchangeFeatureCalculator] [featureGenerator.cpp:711] Load Symbol List Path=./config/S/cn_ashare/symbol.txt
[2025-05-06 22:50:36.964] [fg] [info] [2491012:2491012] [readStrVecFromFile] [Util.h:389] Read fileName=./config/S/cn_ashare/symbol.txt
[2025-05-06 22:50:36.965] [fg] [info] [2491012:2491012] [initializeExchangeFeatureCalculator] [featureGenerator.cpp:839] feature_config_size=37
[2025-05-06 22:50:36.965] [fg] [info] [2491012:2491012] [initializeExchangeFeatureCalculator] [featureGenerator.cpp:846] nTradingDay=20180102, tsStartCore=0, tsFeatureThread=1, csStartCore=2, csFeatureThread=1, featureManifestPath=./config/fc/cn_ashare/feature_manifest.json, preLoadDays=0, maxTickLength=1000, maxFeatureLength=1000
[2025-05-06 22:50:36.978] [fg] [info] [2491012:2491012] [initializeExchangeFeatureCalculator] [featureGenerator.cpp:852] Get Symbol Load Finished.
[2025-05-06 22:50:36.978] [fg] [info] [2491012:2491012] [FeatureFactoryImpl] [FeatureFactoryImpl.cpp:47] Loading /mnt/sda/home/<USER>/FC/cn_ashare_new/orderbook/config/fc/cn_ashare/feature_manifest.json as as manifest file
[2025-05-06 22:50:37.210] [fg] [error] [2491012:2491012] [initializeExchangeFeatureCalculator] [featureGenerator.cpp:865] Unable to locate symbol OB4V2BarDef: ./external_lib/libshennong_feature_hfa_ob4_v2.so: undefined symbol: OB4V2BarDef
