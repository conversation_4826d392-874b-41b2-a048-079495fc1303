[2025-05-06 22:55:40.709] [fg] [info] [2491492:2491492] [inputArgPaser] [featureGenerator.cpp:349] configuration_file_path=config/fg/cn_ashare/ob4_v2.json
[2025-05-06 22:55:40.709] [fg] [info] [2491492:2491492] [inputArgPaser] [featureGenerator.cpp:423] exchange_name=cn_ashare
[2025-05-06 22:55:40.709] [fg] [warning] [2491492:2491492] [inputArgPaser] [featureGenerator.cpp:485] Please explicitly set a valid maxFeatureLength value.
[2025-05-06 22:55:40.710] [fg] [info] [2491492:2491492] [featGenOffline] [featureGenerator.cpp:1024] Start to initialize trading day list
[2025-05-06 22:55:40.710] [fg] [info] [2491492:2491492] [readStrVecFromFile] [Util.h:389] Read fileName=/mnt/sda/NAS/AllData/config/cn_ashare_trading_days.txt
[2025-05-06 22:55:40.713] [fg] [info] [2491492:2491492] [featGenOffline] [featureGenerator.cpp:1028] Start to initialize each exchange feature calculator
[2025-05-06 22:55:40.713] [fg] [info] [2491492:2491492] [initializeExchangeFeatureCalculator] [featureGenerator.cpp:711] Load Symbol List Path=./config/S/cn_ashare/symbol.txt
[2025-05-06 22:55:40.713] [fg] [info] [2491492:2491492] [readStrVecFromFile] [Util.h:389] Read fileName=./config/S/cn_ashare/symbol.txt
[2025-05-06 22:55:40.713] [fg] [info] [2491492:2491492] [initializeExchangeFeatureCalculator] [featureGenerator.cpp:839] feature_config_size=37
[2025-05-06 22:55:40.713] [fg] [info] [2491492:2491492] [initializeExchangeFeatureCalculator] [featureGenerator.cpp:846] nTradingDay=20180102, tsStartCore=0, tsFeatureThread=1, csStartCore=2, csFeatureThread=1, featureManifestPath=./config/fc/cn_ashare/feature_manifest.json, preLoadDays=0, maxTickLength=1000, maxFeatureLength=1000
[2025-05-06 22:55:40.722] [fg] [info] [2491492:2491492] [initializeExchangeFeatureCalculator] [featureGenerator.cpp:852] Get Symbol Load Finished.
[2025-05-06 22:55:40.722] [fg] [info] [2491492:2491492] [FeatureFactoryImpl] [FeatureFactoryImpl.cpp:47] Loading /mnt/sda/home/<USER>/FC/cn_ashare_new/orderbook/config/fc/cn_ashare/feature_manifest.json as as manifest file
[2025-05-06 22:55:40.930] [fg] [info] [2491492:2491492] [CreateFeatureCalc] [FeatureFactoryImpl.cpp:164] [FeatureCalcInit] There are total 5 meta-features in feature library
[2025-05-06 22:55:40.930] [fg] [info] [2491492:2491492] [CreateFeatureCalc] [FeatureFactoryImpl.cpp:166] TradingDayFilePath=/mnt/sda/NAS/AllData/config/cn_ashare_trading_days.txt, ExchangeGroupInfoFilePath=/mnt/sda/NAS/AllData/config/exchange_groups/cn_ashare.json
[2025-05-06 22:55:40.935] [fg] [info] [2491492:2491492] [GetExchangeGroupInfo] [FeatureFactoryImpl.cpp:140] 0: begin_time=91500000, end_time=92500000, type=A
[2025-05-06 22:55:40.935] [fg] [info] [2491492:2491492] [GetExchangeGroupInfo] [FeatureFactoryImpl.cpp:140] 1: begin_time=92500000, end_time=93000000, type=I
[2025-05-06 22:55:40.935] [fg] [info] [2491492:2491492] [GetExchangeGroupInfo] [FeatureFactoryImpl.cpp:140] 2: begin_time=93000000, end_time=113000000, type=O
[2025-05-06 22:55:40.935] [fg] [info] [2491492:2491492] [GetExchangeGroupInfo] [FeatureFactoryImpl.cpp:140] 3: begin_time=113000000, end_time=130000000, type=I
[2025-05-06 22:55:40.935] [fg] [info] [2491492:2491492] [GetExchangeGroupInfo] [FeatureFactoryImpl.cpp:140] 4: begin_time=130000000, end_time=145700000, type=O
[2025-05-06 22:55:40.935] [fg] [info] [2491492:2491492] [GetExchangeGroupInfo] [FeatureFactoryImpl.cpp:140] 5: begin_time=145700000, end_time=150000000, type=A
[2025-05-06 22:55:40.935] [fg] [info] [2491492:2491492] [GetExchangeGroupInfo] [FeatureFactoryImpl.cpp:141] time_zone=28800
[2025-05-06 22:55:40.936] [fg] [error] [2491492:2491492] [InitFeatureCalcTopology] [FeatureFactoryImpl.cpp:366] Fail to resolve feature lsl__fcts_ob4_v2__bnum_tylv31_tv,ts,60000,lsl__fcts_ob4_v2__bnum_tylv31_tv,internal with last_code=0x524289
