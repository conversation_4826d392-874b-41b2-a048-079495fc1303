cmake_minimum_required(VERSION 3.5)
project(shennong_feature_orderbook)

set(CMAKE_EXPORT_COMPILE_COMMANDS 1)
set(CMAKE_CXX_STANDARD 14)
set(CMAKE_C_COMPILER_VERSION 5.4)
set(CMAKE_CXX_COMPILER_VERSION 5.4)

set(CMAKE_BUILD_TYPE "DEBUG")
set(CMAKE_CXX_FLAGS_DEBUG "$ENV{CXXFLAGS} -O0 -Wall -g -ggdb")

set(CMAKE_EXE_LINKER_FLAGS "-Wl,--no-as-needed")
set(CMAKE_SHARED_LINKER_FLAGS "-Wl,--no-as-needed")
set(CMAKE_STATIC_LINKER_FLAGS "-Wl,--no-as-needed")

set(BOOST_ALL_DYN_LINK ON)

set(BOOST_ALL_DYN_LINK ON)
set(BOOST_LOG_DYN_LINK ON)
set(Boost_USE_MULTITHREADED ON)
set(Boost_USE_STATIC_LIBS OFF)
set(Boost_USE_STATIC_RUNTIME OFF)

add_definitions(-DBOOST_ALL_NO_LIB)
add_definitions(-DBOOST_ALL_DYN_LINK)
add_definitions(-DBOOST_LOG_DYN_LINK)

set(spdlog /mnt/sda/NAS/Release/external/spdlog/)
set(shennong_utils /mnt/sda/NAS/Release/shennong/utils/develop/20180409-184943-e8469650)
set(shennong_common /mnt/sda/NAS/Release/shennong/common/feature/latest)
set(shennong_stk /mnt/sda/NAS/Release/shennong/stk/dev_feature/latest)
set(shennong_fc /mnt/sda/NAS/Release/shennong/fc/dev_global.dev/latest)

add_definitions(-DBOOST_ALL_DYN_LINK)
add_definitions(-DBOOST_LOG_DYN_LINK)

include_directories(
    /mnt/sda/NAS/Release/external/legacy/PT_QuantBaseApi_Cpp/include
    /mnt/sda/NAS/Release/external/legacy/boost_1_64_0
    /mnt/sda/NAS/Release/external/legacy/jsoncpp/include
    ${spdlog}/include
    ${shennong_utils}/include
    ${shennong_common}/include
    ${shennong_stk}/include
    ${shennong_fc}/include
)

link_directories(
    /mnt/sda/NAS/Release/external/legacy/boost_1_64_0/lib
    /mnt/sda/NAS/Release/external/legacy/jsoncpp/lib
    ${shennong_utils}/lib
    ${shennong_common}/lib
    ${shennong_stk}/lib
    ${shennong_fc}/lib
)

add_library(shennong_feature_hfa_ob3_v4 SHARED
        cpp/functions.cpp
        cpp/hfa_ob3_v4.cpp
        )

target_link_libraries(shennong_feature_hfa_ob3_v4
        shennong_fc
        )

add_library(shennong_feature_hfa_ob3_v3 SHARED
        cpp/functions.cpp
        cpp/hfa_ob3_v3.cpp
        )

target_link_libraries(shennong_feature_hfa_ob3_v3
        shennong_fc
        )

set (LIBRARY_OUTPUT_PATH ../lib)
