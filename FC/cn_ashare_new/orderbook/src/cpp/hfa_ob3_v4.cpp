#include <cstdint>
#include <cstdio>
#include <limits>
#include <queue>
#include <string>
#include <regex>

#include "functions.h"
#include <shennong/fc.h>
#include <shennong/utility/logger.h>
#include <shennong/stk/stream.h>
#include <shennong/toolkit/DayBarLoader.h>
#include <shennong/toolkit/RestorationLoader.h>

using namespace std;
using namespace shennong::fc;
using namespace shennong::data;

#pragma GCC diagnostic ignored "-Wunused-variable"
#pragma GCC diagnostic ignored "-Wsign-compare"
#pragma GCC diagnostic ignored "-Wreturn-type"
#pragma GCC diagnostic ignored "-Wcomment"
#pragma GCC diagnostic ignored "-Wunknown-pragmas"

int FEATURE_COUNT = 0;

struct OB3V4Argument : public FeatureArgument
{
    int64_t index;

    OB3V4Argument(int _index)
    {
        index = _index;
    }
};

struct OB3V4State : public FeatureState
{
    double bnum{0.0}, btv{0.0}, anum{0.0}, atv{0.0}, btv_lv1{0.0}, atv_lv1{0.0};
    double cnum_tylv62{0.0}, ctv_tylv62{0.0}, cnum_gtlv62{0.0}, ctv_gtlv62{0.0};
    double bnum_tylv62{0.0}, btv_tylv62{0.0}, anum_tylv62{0.0}, atv_tylv62{0.0};
    double bnum_gtlv62{0.0}, btv_gtlv62{0.0}, anum_gtlv62{0.0}, atv_gtlv62{0.0};
    double b2alv1_tvpct{0.0}, a2blv1_tvpct{0.0};

    void reset(double value)
    {
        bnum = btv = anum = atv = value;
        bnum_tylv62 = btv_tylv62 = anum_tylv62 = atv_tylv62 = value;
        bnum_gtlv62 = btv_gtlv62 = anum_gtlv62 = atv_gtlv62 = value;
        b2alv1_tvpct = a2blv1_tvpct = value;
    }
};

static int OB3V4InitBar(Feature *pFeature)
{
    char const *pFeatureName = pFeature->featureConfig.formula.c_str();
    std::cout << "pFeatureName: " << pFeatureName << endl;
    bool matchSuccess = false;
    int index = 0;

    bool has_parameter = pFeature->featureConfig.parameter.isObject();
    bool has_findex = has_parameter && pFeature->featureConfig.parameter.isMember("findex");
    bool has_version = has_parameter && pFeature->featureConfig.parameter.isMember("version");
    int version = has_version ? pFeature->featureConfig.parameter["version"].asInt() : 0;   
    int horizon = pFeature->featureConfig.horizon;
    if (has_parameter && has_findex && has_version && version == 4 && horizon > 20)
    {
        int findex = pFeature->featureConfig.parameter["findex"].asInt();
        index = findex;
        if (index >= FEATURE_COUNT)
        {
            FEATURE_COUNT += 1;
        }
        matchSuccess = true;
    }

    if (matchSuccess == false)
        return FC_ARGSERR;

    pFeature->pFeatureArgument = new OB3V4Argument(index);

    return FC_SUCCESS;
}

static int OB3V4InitMarket(Feature *pFeature)
{
    char const *pFeatureName = pFeature->featureConfig.formula.c_str();
    std::cout << "pFeatureName: " << pFeatureName << endl;
    bool matchSuccess = false;
    int index = 0;

    bool has_parameter = pFeature->featureConfig.parameter.isObject();
    bool has_findex = has_parameter && pFeature->featureConfig.parameter.isMember("findex");
    bool has_version = has_parameter && pFeature->featureConfig.parameter.isMember("version");
    int version = has_version ? pFeature->featureConfig.parameter["version"].asInt() : 0;   
    int horizon = pFeature->featureConfig.horizon;
    if (has_parameter && has_findex && has_version && version == 4 && horizon < 20)
    {
        int findex = pFeature->featureConfig.parameter["findex"].asInt();
        index = findex;
        if (index >= FEATURE_COUNT)
        {
            FEATURE_COUNT += 1;
        }
        matchSuccess = true;
    }

    if (matchSuccess == false)
        return FC_ARGSERR;

    pFeature->pFeatureArgument = new OB3V4Argument(index);

    return FC_SUCCESS;
}

static int OB3V4Free(Feature *pFeature)
{
    delete pFeature->pFeatureArgument; // 释放特征参数
    delete pFeature;                   // 释放特征
    return FC_SUCCESS;
}

static int OB3V4StateInit(FeatureState **ppFeatureState, Feature const *pFeature, SymbolState const *pSymbolState)
{
    auto pFS = new OB3V4State();

    *ppFeatureState = pFS;
    pFS->reset(std::numeric_limits<double>::quiet_NaN());
    return FC_SUCCESS;
}

static int OB3V4StateFree(FeatureState *pFeatureState)
{
    delete pFeatureState; // 释放特征状态
    return FC_SUCCESS;
}

static int OB3V4OnTransaction(FeatureState *pFeatureState0, YR_TRANSACTION const *pTransaction)
{
    auto pFS = (OB3V4State *)pFeatureState0;                // 创建特征状态，转换为OrderState类型
    auto pFeature = pFS->pFeature;                        // 创建特征，用创建的特征状态赋值
    auto pFA = (OB3V4Argument *)pFeature->pFeatureArgument; // 创建特征参数，

    return FC_SUCCESS;
}

static int OB3V4OnOrder(FeatureState *pFeatureState0, YR_ORDER const *pOrder)
{
    auto pFS = (OB3V4State *)pFeatureState0;
    auto pFeature = pFS->pFeature;
    auto pFA = (OB3V4Argument *)pFeature->pFeatureArgument;

    auto order_tv = pOrder->volume.get_double() * pOrder->price.get_double();

    if (pOrder->side == Side::kBuy) // bid order
    {
        pFS->bnum = reset_nan(pFS->bnum) + double(1.0);
        pFS->btv = reset_nan(pFS->btv) + order_tv;

        if (between(order_tv, 0.0, 0.062 * pFS->atv_lv1)) // tiny order with order volume less than 0.062 * lv1
        {
            pFS->bnum_tylv62 = reset_nan(pFS->bnum_tylv62) + double(1.0);
            pFS->btv_tylv62 = reset_nan(pFS->btv_tylv62) + order_tv;

            if (pOrder->order_action == OrderAction::kDelete)
            {
                pFS->cnum_tylv62 = reset_nan(pFS->cnum_tylv62) + double(1.0);
                pFS->ctv_tylv62 = reset_nan(pFS->ctv_tylv62) + order_tv;
            }
        }
        else if (between(order_tv, 0.062 * pFS->atv_lv1, std::numeric_limits<double>::max())) // tiny order with order volume greater than 0.062 * lv1
        {
            pFS->bnum_gtlv62 = reset_nan(pFS->bnum_gtlv62) + double(1.0);
            pFS->btv_gtlv62 = reset_nan(pFS->btv_gtlv62) + order_tv;

            if (pOrder->order_action == OrderAction::kDelete)
            {
                pFS->cnum_gtlv62 = reset_nan(pFS->cnum_gtlv62) + double(1.0);
                pFS->ctv_gtlv62 = reset_nan(pFS->ctv_gtlv62) + order_tv;
            }
        }
    }
    else if (pOrder->side == Side::kSell) // ask order
    {
        pFS->anum = reset_nan(pFS->anum) + double(1.0);
        pFS->atv = reset_nan(pFS->atv) + order_tv;

        if (between(order_tv, 0.0, 0.062 * pFS->btv_lv1)) // tiny order with order volume less than 0.062 * lv
        {
            pFS->anum_tylv62 = reset_nan(pFS->anum_tylv62) + double(1.0);
            pFS->atv_tylv62 = reset_nan(pFS->atv_tylv62) + order_tv;

            if (pOrder->order_action == OrderAction::kDelete)
            {
                pFS->cnum_tylv62 = reset_nan(pFS->cnum_tylv62) + double(1.0);
                pFS->ctv_tylv62 = reset_nan(pFS->ctv_tylv62) + order_tv;
            }
        }
        else if (between(order_tv, 0.062 * pFS->btv_lv1, std::numeric_limits<double>::max())) // tiny order with order volume greater than 0.062 * lv1
        {
            pFS->anum_gtlv62 = reset_nan(pFS->anum_gtlv62) + double(1.0);
            pFS->atv_gtlv62 = reset_nan(pFS->atv_gtlv62) + order_tv;

            if (pOrder->order_action == OrderAction::kDelete)
            {
                pFS->cnum_gtlv62 = reset_nan(pFS->cnum_gtlv62) + double(1.0);
                pFS->ctv_gtlv62 = reset_nan(pFS->ctv_gtlv62) + order_tv;
            }
        }
    }

    return FC_SUCCESS;
}

static int OB3V4OnMarket(FeatureState *pFeatureState0, YR_MARKET const *pMarket, double *pValues)
{
    auto pFS = (OB3V4State *)pFeatureState0;                // 创建特征状态，转换为OrderState类型
    auto pFeature = pFS->pFeature;                        // 创建特征，用创建的特征状态赋值
    auto pFA = (OB3V4Argument *)pFeature->pFeatureArgument; // 创建特征参数，用创建的特征赋值

    pFS->btv_lv1 = pMarket->bid_volume[0].get_double() * pMarket->bid_price[0].get_double(); // update accumulated bid turnover at level 1
    pFS->atv_lv1 = pMarket->ask_volume[0].get_double() * pMarket->ask_price[0].get_double(); // update accumulated ask turnover at level 1
    auto pFeatureValue = pValues + pFeature->index;

    double features[FEATURE_COUNT] = {0.0};
    switch (pFA->index)
    {
    case 0: // bid tiny order number
    {
        features[0] = clip(asinh10(pFS->bnum_tylv62), 0.0, 10.0);
        break;
    }
    case 1: // bid tiny order volume
    {
        features[1] = clip(asinh10(pFS->btv_tylv62), 0.0, 10.0);
        break;
    }
    case 2: // tiny order cancel number
    {
        features[2] = clip(asinh10(pFS->cnum_tylv62), 0.0, 10.0);
        break;
    }
    case 3: // tiny order cancel volume
    {
        features[3] = clip(asinh10(pFS->ctv_tylv62), 0.0, 10.0);
        break;
    }
    case 4: // bid tiny order number - ask tiny order number
    {
        features[4] = clip(asinh10(pFS->bnum_tylv62 - pFS->anum_tylv62), -10.0, 10.0);
        break;
    }
    case 5: // bid tiny order volume - ask tiny order volume
    {
        features[5] = clip(asinh10(pFS->btv_tylv62 - pFS->atv_tylv62), -10.0, 10.0);
        break;
    }
    case 6: // tiny order cancel number / (bid tiny order number + ask tiny order number)
    {
        double denominator = pFS->bnum_tylv62 + pFS->anum_tylv62;
        features[6] = clip(asinh10(offset_divide(pFS->cnum_tylv62, denominator, 1.0)), 0.0, 10.0);
        break;
    }
    case 7: // tiny order cancel volume / (bid tiny order volume + ask tiny order volume)
    {
        double denominator = pFS->btv_tylv62 + pFS->atv_tylv62;
        features[7] = clip(asinh10(offset_divide(pFS->ctv_tylv62, denominator, 1.0)), 0.0, 10.0);
        break;
    }
    case 8: // bid large order number
    {
        features[8] = clip(asinh10(pFS->bnum_gtlv62), 0.0, 10.0);
        break;
    }
    case 9: // bid large order volume
    {
        features[9] = clip(asinh10(pFS->btv_gtlv62), 0.0, 10.0);
        break;
    }
    case 10: // large order cancel number
    {
        features[10] = clip(asinh10(pFS->cnum_gtlv62), 0.0, 10.0);
        break;
    }
    case 11: // large order cancel volume
    {
        features[11] = clip(asinh10(pFS->ctv_gtlv62), 0.0, 10.0);
        break;
    }
    case 12: // bid large order number - ask large order number
    {
        features[12] = clip(asinh10(pFS->bnum_gtlv62 - pFS->anum_gtlv62), -10.0, 10.0);
        break;
    }
    case 13: // bid large order volume - ask large order volume
    {
        features[13] = clip(asinh10(pFS->btv_gtlv62 - pFS->atv_gtlv62), -10.0, 10.0);
        break;
    }
    case 14: // large order cancel number / (bid large order number + ask large order number)
    {
        double denominator = pFS->bnum_gtlv62 + pFS->anum_gtlv62;
        features[14] = clip(asinh10(offset_divide(pFS->cnum_gtlv62, denominator, 1.0)), 0.0, 10.0);
        break;
    }
    case 15: // large order cancel volume / (bid large order volume + ask large order volume)
    {
        double denominator = pFS->btv_gtlv62 + pFS->atv_gtlv62;
        features[15] = clip(asinh10(offset_divide(pFS->ctv_gtlv62, denominator, 1.0)), 0.0, 10.0);
        break;
    }
    case 16: // bid order volume / last ask order volume at level 1
    {
        features[16] = clip(asinh10(offset_divide(pFS->btv, pFS->atv_lv1, 1.0)), 0.0, 10.0);
        break;
    }
    case 17: // ask order volume / last bid order volume at level 1
    {
        features[17] = clip(asinh10(offset_divide(pFS->atv, pFS->btv_lv1, 1.0)), 0.0, 10.0);
        break;
    }
    default:
    {
        break;
    }
    }

    *pFeatureValue = features[pFA->index];

    // pFS->reset(0.0);
    return FC_SUCCESS;
}

static int OB3V4OnMarket_calc(FeatureState *pFeatureState0, YR_MARKET const *pMarket)
{
    auto pFS = (OB3V4State *)pFeatureState0;                // 创建特征状态，转换为OrderState类型
    auto pFeature = pFS->pFeature;                        // 创建特征，用创建的特征状态赋值
    auto pFA = (OB3V4Argument *)pFeature->pFeatureArgument; // 创建特征参数，用创建的特征赋值

    pFS->btv_lv1 = pMarket->bid_volume[0].get_double() * pMarket->bid_price[0].get_double(); // update accumulated bid turnover at level 1
    pFS->atv_lv1 = pMarket->ask_volume[0].get_double() * pMarket->ask_price[0].get_double(); // update accumulated ask turnover at level 1
    return FC_SUCCESS;
}

static int OB3V4OnBar(FeatureState *pFeatureState0, YR_BAR const *pBar, double *pValues)
{
    auto pFS = (OB3V4State *)pFeatureState0;
    auto pFeature = pFS->pFeature;
    auto pFA = (OB3V4Argument *)pFeature->pFeatureArgument;
    auto pFeatureValue = pValues + pFeature->index;

    double features[FEATURE_COUNT] = {0.0};

    switch (pFA->index)
    {
    case 0: // bid tiny order number
    {
        features[0] = clip(asinh10(pFS->bnum_tylv62), 0.0, 10.0);
        break;
    }
    case 1: // bid tiny order volume
    {
        features[1] = clip(asinh10(pFS->btv_tylv62), 0.0, 10.0);
        break;
    }
    case 2: // tiny order cancel number
    {
        features[2] = clip(asinh10(pFS->cnum_tylv62), 0.0, 10.0);
        break;
    }
    case 3: // tiny order cancel volume
    {
        features[3] = clip(asinh10(pFS->ctv_tylv62), 0.0, 10.0);
        break;
    }
    case 4: // bid tiny order number - ask tiny order number
    {
        features[4] = clip(asinh10(pFS->bnum_tylv62 - pFS->anum_tylv62), -10.0, 10.0);
        break;
    }
    case 5: // bid tiny order volume - ask tiny order volume
    {
        features[5] = clip(asinh10(pFS->btv_tylv62 - pFS->atv_tylv62), -10.0, 10.0);
        break;
    }
    case 6: // tiny order cancel number / (bid tiny order number + ask tiny order number)
    {
        double denominator = pFS->bnum_tylv62 + pFS->anum_tylv62;
        features[6] = clip(asinh10(offset_divide(pFS->cnum_tylv62, denominator, 1.0)), 0.0, 10.0);
        break;
    }
    case 7: // tiny order cancel volume / (bid tiny order volume + ask tiny order volume)
    {
        double denominator = pFS->btv_tylv62 + pFS->atv_tylv62;
        features[7] = clip(asinh10(offset_divide(pFS->ctv_tylv62, denominator, 1.0)), 0.0, 10.0);
        break;
    }
    case 8: // bid large order number
    {
        features[8] = clip(asinh10(pFS->bnum_gtlv62), 0.0, 10.0);
        break;
    }
    case 9: // bid large order volume
    {
        features[9] = clip(asinh10(pFS->btv_gtlv62), 0.0, 10.0);
        break;
    }
    case 10: // large order cancel number
    {
        features[10] = clip(asinh10(pFS->cnum_gtlv62), 0.0, 10.0);
        break;
    }
    case 11: // large order cancel volume
    {
        features[11] = clip(asinh10(pFS->ctv_gtlv62), 0.0, 10.0);
        break;
    }
    case 12: // bid large order number - ask large order number
    {
        features[12] = clip(asinh10(pFS->bnum_gtlv62 - pFS->anum_gtlv62), -10.0, 10.0);
        break;
    }
    case 13: // bid large order volume - ask large order volume
    {
        features[13] = clip(asinh10(pFS->btv_gtlv62 - pFS->atv_gtlv62), -10.0, 10.0);
        break;
    }
    case 14: // large order cancel number / (bid large order number + ask large order number)
    {
        double denominator = pFS->bnum_gtlv62 + pFS->anum_gtlv62;
        features[14] = clip(asinh10(offset_divide(pFS->cnum_gtlv62, denominator, 1.0)), 0.0, 10.0);
        break;
    }
    case 15: // large order cancel volume / (bid large order volume + ask large order volume)
    {
        double denominator = pFS->btv_gtlv62 + pFS->atv_gtlv62;
        features[15] = clip(asinh10(offset_divide(pFS->ctv_gtlv62, denominator, 1.0)), 0.0, 10.0);
        break;
    }
    case 16: // bid order volume / last ask order volume at level 1
    {
        features[16] = clip(asinh10(offset_divide(pFS->btv, pFS->atv_lv1, 1.0)), 0.0, 10.0);
        break;
    }
    case 17: // ask order volume / last bid order volume at level 1
    {
        features[17] = clip(asinh10(offset_divide(pFS->atv, pFS->btv_lv1, 1.0)), 0.0, 10.0);
        break;
    }
    default:
    {
        break;
    }
    }

    *pFeatureValue = features[pFA->index];
    // pFS->reset(0.0);
    return FC_SUCCESS;
}

FeatureDefStruct ob3V4BarDef = {

    .pFeatureInitFunc = OB3V4InitBar,
    .pFeatureFreeFunc = OB3V4Free,

    .pFeatureStateInitFunc = OB3V4StateInit,
    .pFeatureStateFreeFunc = OB3V4StateFree,

    .pCrossSectionalFeatureStateInitFunc = nullptr,
    .pCrossSectionalFeatureStateFreeFunc = nullptr,

    .pFeatureOnMarketFunc = nullptr,
    .pCalcOnMarketFunc = OB3V4OnMarket_calc,

    .pFeatureOnTransactionFunc = nullptr,
    .pCalcOnTransactionFunc = OB3V4OnTransaction,

    .pFeatureOnOrderFunc = nullptr,
    .pCalcOnOrderFunc = OB3V4OnOrder,

    .pFeatureOnDepthFunc = nullptr,
    .pCalcOnDepthFunc = nullptr,

    .pFeatureOnQuoteFunc = nullptr,
    .pCalcOnQuoteFunc = nullptr,

    .pFeatureOnDerivativeFunc = nullptr,
    .pCalcOnDerivativeFunc = nullptr,

    .pCalcOnTimerFunc = nullptr,

    .pFeatureOnBarFunc = OB3V4OnBar,
    .pCalcOnBarFunc = nullptr,

    .pFeatureOnCrossSectionalBarFunc = nullptr,
    .pCalcOnCrossSectionalBarFunc = nullptr,
};

FeatureDefStruct ob3V4MarketDef = {

    .pFeatureInitFunc = OB3V4InitMarket,
    .pFeatureFreeFunc = OB3V4Free,

    .pFeatureStateInitFunc = OB3V4StateInit,
    .pFeatureStateFreeFunc = OB3V4StateFree,

    .pCrossSectionalFeatureStateInitFunc = nullptr,
    .pCrossSectionalFeatureStateFreeFunc = nullptr,

    .pFeatureOnMarketFunc = OB3V4OnMarket,
    .pCalcOnMarketFunc = nullptr,

    .pFeatureOnTransactionFunc = nullptr,
    .pCalcOnTransactionFunc = OB3V4OnTransaction,

    .pFeatureOnOrderFunc = nullptr,
    .pCalcOnOrderFunc = OB3V4OnOrder,

    .pFeatureOnDepthFunc = nullptr,
    .pCalcOnDepthFunc = nullptr,

    .pFeatureOnQuoteFunc = nullptr,
    .pCalcOnQuoteFunc = nullptr,

    .pFeatureOnDerivativeFunc = nullptr,
    .pCalcOnDerivativeFunc = nullptr,

    .pCalcOnTimerFunc = nullptr,

    .pFeatureOnBarFunc = nullptr,
    .pCalcOnBarFunc = nullptr,

    .pFeatureOnCrossSectionalBarFunc = nullptr,
    .pCalcOnCrossSectionalBarFunc = nullptr,
};

FeatureDef OB3V4BarDef = {ob3V4BarDef};
FeatureDef OB3V4MarketDef = {ob3V4MarketDef};
