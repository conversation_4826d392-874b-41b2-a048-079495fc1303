#include <cstdint>
#include <cstdio>
#include <limits>
#include <queue>
#include <string>
#include <regex>

#include "functions.h"
#include <shennong/fc.h>
#include <shennong/utility/logger.h>
#include <shennong/stk/stream.h>
#include <shennong/toolkit/DayBarLoader.h>
#include <shennong/toolkit/RestorationLoader.h>

using namespace std;
using namespace shennong::fc;
using namespace shennong::data;

#pragma GCC diagnostic ignored "-Wunused-variable"
#pragma GCC diagnostic ignored "-Wsign-compare"
#pragma GCC diagnostic ignored "-Wreturn-type"
#pragma GCC diagnostic ignored "-Wcomment"
#pragma GCC diagnostic ignored "-Wunknown-pragmas"

int FEATURE_COUNT = 0;

struct OB4V1Argument : public FeatureArgument
{
    int64_t index;

    OB4V2Argument(int _index)
    {
        index = _index;
    }
};

struct OB4V1State : public FeatureState
{
    double bnum{0.0}, btv{0.0}, anum{0.0}, atv{0.0}, btv_lv5{0.0}, atv_lv5{0.0};
    double cnum_tylv31{0.0}, ctv_tylv31{0.0}, cnum_gtlv31{0.0}, ctv_gtlv31{0.0};
    double bnum_tylv31{0.0}, btv_tylv31{0.0}, anum_tylv31{0.0}, atv_tylv31{0.0};
    double bnum_gtlv31{0.0}, btv_gtlv31{0.0}, anum_gtlv31{0.0}, atv_gtlv31{0.0};
    double b2alv1_tvpct{0.0}, a2blv1_tvpct{0.0};

    void reset(double value)
    {
        bnum = btv = anum = atv = value;
        bnum_tylv31 = btv_tylv31 = anum_tylv31 = atv_tylv31 = value;
        bnum_gtlv31 = btv_gtlv31 = anum_gtlv31 = atv_gtlv31 = value;
        b2alv1_tvpct = a2blv1_tvpct = value;
    }
};

static int OB4V1InitBar(Feature *pFeature)
{
    char const *pFeatureName = pFeature->featureConfig.formula.c_str();
    // std::cout << "pFeatureName: " << pFeatureName << endl;
    bool matchSuccess = false;
    int index = 0;

    bool has_parameter = pFeature->featureConfig.parameter.isObject();
    bool has_findex = has_parameter && pFeature->featureConfig.parameter.isMember("findex");
    bool has_version = has_parameter && pFeature->featureConfig.parameter.isMember("version");
    int version = has_version ? pFeature->featureConfig.parameter["version"].asInt() : 0;   
    int horizon = pFeature->featureConfig.horizon;
    if (has_parameter && has_findex && has_version && version == 1 && horizon > 20)
    {
        int findex = pFeature->featureConfig.parameter["findex"].asInt();
        index = findex;
        if (index >= FEATURE_COUNT)
        {
            FEATURE_COUNT += 1;
        }
        matchSuccess = true;
    }

    if (matchSuccess == false)
        return FC_ARGSERR;

    pFeature->pFeatureArgument = new OB4V1Argument(index);

    return FC_SUCCESS;
}

static int OB4V1InitMarket(Feature *pFeature)
{
    char const *pFeatureName = pFeature->featureConfig.formula.c_str();
    // std::cout << "pFeatureName: " << pFeatureName << endl;
    bool matchSuccess = false;
    int index = 0;

    bool has_parameter = pFeature->featureConfig.parameter.isObject();
    bool has_findex = has_parameter && pFeature->featureConfig.parameter.isMember("findex");
    bool has_version = has_parameter && pFeature->featureConfig.parameter.isMember("version");
    int version = has_version ? pFeature->featureConfig.parameter["version"].asInt() : 0;   
    int horizon = pFeature->featureConfig.horizon;
    if (has_parameter && has_findex && has_version && version == 1 && horizon < 20)
    {
        int findex = pFeature->featureConfig.parameter["findex"].asInt();
        index = findex;
        if (index >= FEATURE_COUNT)
        {
            FEATURE_COUNT += 1;
        }
        matchSuccess = true;
    }

    if (matchSuccess == false)
        return FC_ARGSERR;

    pFeature->pFeatureArgument = new OB4V1Argument(index);

    return FC_SUCCESS;
}

static int OB4V1Free(Feature *pFeature)
{
    delete pFeature->pFeatureArgument; // 释放特征参数
    delete pFeature;                   // 释放特征
    return FC_SUCCESS;
}

static int OB4V1StateInit(FeatureState **ppFeatureState, Feature const *pFeature, SymbolState const *pSymbolState)
{
    auto pFS = new OB4V1State();

    *ppFeatureState = pFS;
    pFS->reset(std::numeric_limits<double>::quiet_NaN());
    return FC_SUCCESS;
}

static int OB4V1StateFree(FeatureState *pFeatureState)
{
    delete pFeatureState; // 释放特征状态
    return FC_SUCCESS;
}

static int OB4V1OnTransaction(FeatureState *pFeatureState0, YR_TRANSACTION const *pTransaction)
{
    auto pFS = (OB4V1State *)pFeatureState0;                // 创建特征状态，转换为OrderState类型
    auto pFeature = pFS->pFeature;                        // 创建特征，用创建的特征状态赋值
    auto pFA = (OB4V1Argument *)pFeature->pFeatureArgument; // 创建特征参数，用创建的特征赋值

    return FC_SUCCESS;
}

static int OB4V1OnOrder(FeatureState *pFeatureState0, YR_ORDER const *pOrder)
{
    auto pFS = (OB4V1State *)pFeatureState0;
    auto pFeature = pFS->pFeature;
    auto pFA = (OB4V1Argument *)pFeature->pFeatureArgument;

    auto order_tv = pOrder->volume.get_double() * pOrder->price.get_double();

    if (pOrder->side == Side::kBuy) // bid order
    {
        pFS->bnum = reset_nan(pFS->bnum) + double(1.0);
        pFS->btv = reset_nan(pFS->btv) + order_tv;

        if (between(order_tv, 0.0, 0.031 * pFS->atv_lv5)) // tiny order with order volume less than 0.031 * lv5
        {
            pFS->bnum_tylv31 = reset_nan(pFS->bnum_tylv31) + double(1.0);
            pFS->btv_tylv31 = reset_nan(pFS->btv_tylv31) + order_tv;

            if (pOrder->order_action == OrderAction::kDelete)
            {
                pFS->cnum_tylv31 = reset_nan(pFS->cnum_tylv31) + double(1.0);
                pFS->ctv_tylv31 = reset_nan(pFS->ctv_tylv31) + order_tv;
            }
        }
        else if (order_tv > 0.031 * pFS->atv_lv5) // tiny order with order volume greater than 0.031 * lv5
        {
            pFS->bnum_gtlv31 = reset_nan(pFS->bnum_gtlv31) + double(1.0);
            pFS->btv_gtlv31 = reset_nan(pFS->btv_gtlv31) + order_tv;

            if (pOrder->order_action == OrderAction::kDelete)
            {
                pFS->cnum_gtlv31 = reset_nan(pFS->cnum_gtlv31) + double(1.0);
                pFS->ctv_gtlv31 = reset_nan(pFS->ctv_gtlv31) + order_tv;
            }
        }
    }
    else if (pOrder->side == Side::kSell) // ask order
    {
        pFS->anum = reset_nan(pFS->anum) + double(1.0);
        pFS->atv = reset_nan(pFS->atv) + order_tv;

        if (between(order_tv, 0.0, 0.031 * pFS->btv_lv5)) // tiny order with order volume less than 0.031 * lv5
        {
            pFS->anum_tylv31 = reset_nan(pFS->anum_tylv31) + double(1.0);
            pFS->atv_tylv31 = reset_nan(pFS->atv_tylv31) + order_tv;

            if (pOrder->order_action == OrderAction::kDelete)
            {
                pFS->cnum_tylv31 = reset_nan(pFS->cnum_tylv31) + double(1.0);
                pFS->ctv_tylv31 = reset_nan(pFS->ctv_tylv31) + order_tv;
            }
        }
        else if (order_tv > 0.031 * pFS->btv_lv5) // tiny order with order volume greater than 0.031 * lv5
        {
            pFS->anum_gtlv31 = reset_nan(pFS->anum_gtlv31) + double(1.0);
            pFS->atv_gtlv31 = reset_nan(pFS->atv_gtlv31) + order_tv;

            if (pOrder->order_action == OrderAction::kDelete)
            {
                pFS->cnum_gtlv31 = reset_nan(pFS->cnum_gtlv31) + double(1.0);
                pFS->ctv_gtlv31 = reset_nan(pFS->ctv_gtlv31) + order_tv;
            }
        }
    }

    return FC_SUCCESS;
}

static int OB4V1OnMarket(FeatureState *pFeatureState0, YR_MARKET const *pMarket, double *pValues)
{
    auto pFS = (OB4V1State *)pFeatureState0;                // 创建特征状态，转换为OrderState类型
    auto pFeature = pFS->pFeature;                        // 创建特征，用创建的特征状态赋值
    auto pFA = (OB4V1Argument *)pFeature->pFeatureArgument; // 创建特征参数，用创建的特征赋值

    for (int i = 0; i < 5; i++)
    {
        pFS->btv_lv5 += pMarket->bid_volume[i].get_double() * pMarket->bid_price[i].get_double();
        pFS->atv_lv5 += pMarket->ask_volume[i].get_double() * pMarket->ask_price[i].get_double();
    }

    auto pFeatureValue = pValues + pFeature->index;

    double features[FEATURE_COUNT] = {0.0};
    switch (pFA->index)
    {
    case 0: // bid tiny order number
    {
        features[0] = clip(asinh10(pFS->bnum_tylv31), 0.0, 10.0);
        break;
    }
    case 1: // bid tiny order volume
    {
        features[1] = clip(asinh10(pFS->btv_tylv31), 0.0, 10.0);
        break;
    }
    case 2: // tiny order cancel number
    {
        features[2] = clip(asinh10(pFS->cnum_tylv31), 0.0, 10.0);
        break;
    }
    case 3: // tiny order cancel volume
    {
        features[3] = clip(asinh10(pFS->ctv_tylv31), 0.0, 10.0);
        break;
    }
    case 4: // bid tiny order number - ask tiny order number
    {
        features[4] = clip(asinh10(pFS->bnum_tylv31 - pFS->anum_tylv31), -10.0, 10.0);
        break;
    }
    case 5: // bid tiny order volume - ask tiny order volume
    {
        features[5] = clip(asinh10(pFS->btv_tylv31 - pFS->atv_tylv31), -10.0, 10.0);
        break;
    }
    case 6: // tiny order cancel number / (bid tiny order number + ask tiny order number)
    {
        double denominator = pFS->bnum_tylv31 + pFS->anum_tylv31;
        features[6] = clip(asinh10(offset_divide(pFS->cnum_tylv31, denominator, 1.0)), 0.0, 10.0);
        break;
    }
    case 7: // tiny order cancel volume / (bid tiny order volume + ask tiny order volume)
    {
        double denominator = pFS->btv_tylv31 + pFS->atv_tylv31;
        features[7] = clip(asinh10(offset_divide(pFS->ctv_tylv31, denominator, 1.0)), 0.0, 10.0);
        break;
    }
    case 8: // bid large order number
    {
        features[8] = clip(asinh10(pFS->bnum_gtlv31), 0.0, 10.0);
        break;
    }
    case 9: // bid large order volume
    {
        features[9] = clip(asinh10(pFS->btv_gtlv31), 0.0, 10.0);
        break;
    }
    case 10: // large order cancel number
    {
        features[10] = clip(asinh10(pFS->cnum_gtlv31), 0.0, 10.0);
        break;
    }
    case 11: // large order cancel volume
    {
        features[11] = clip(asinh10(pFS->ctv_gtlv31), 0.0, 10.0);
        break;
    }
    case 12: // bid large order number - ask large order number
    {
        features[12] = clip(asinh10(pFS->bnum_gtlv31 - pFS->anum_gtlv31), -10.0, 10.0);
        break;
    }
    case 13: // bid large order volume - ask large order volume
    {
        features[13] = clip(asinh10(pFS->btv_gtlv31 - pFS->atv_gtlv31), -10.0, 10.0);
        break;
    }
    case 14: // large order cancel number / (bid large order number + ask large order number)
    {
        double denominator = pFS->bnum_gtlv31 + pFS->anum_gtlv31;
        features[14] = clip(asinh10(offset_divide(pFS->cnum_gtlv31, denominator, 1.0)), 0.0, 10.0);
        break;
    }
    case 15: // large order cancel volume / (bid large order volume + ask large order volume)
    {
        double denominator = pFS->btv_gtlv31 + pFS->atv_gtlv31;
        features[15] = clip(asinh10(offset_divide(pFS->ctv_gtlv31, denominator, 1.0)), 0.0, 10.0);
        break;
    }
    case 16: // bid order volume / last ask order volume at level 5
    {
        features[16] = clip(asinh10(offset_divide(pFS->btv, pFS->atv_lv5, 1.0)), 0.0, 10.0);
        break;
    }
    case 17: // ask order volume / last bid order volume at level 5
    {
        features[17] = clip(asinh10(offset_divide(pFS->atv, pFS->btv_lv5, 1.0)), 0.0, 10.0);
        break;
    }
    default:
    {
        break;
    }
    }

    *pFeatureValue = features[pFA->index];

    pFS->reset(0.0);
    return FC_SUCCESS;
}

static int OB4V1OnMarket_calc(FeatureState *pFeatureState0, YR_MARKET const *pMarket)
{
    auto pFS = (OB4V1State *)pFeatureState0;                // 创建特征状态，转换为OrderState类型
    auto pFeature = pFS->pFeature;                        // 创建特征，用创建的特征状态赋值
    auto pFA = (OB4V1Argument *)pFeature->pFeatureArgument; // 创建特征参数，用创建的特征赋值

    for (int i = 0; i < 5; i++)
    {
        pFS->btv_lv5 += pMarket->bid_volume[i].get_double() * pMarket->bid_price[i].get_double();
        pFS->atv_lv5 += pMarket->ask_volume[i].get_double() * pMarket->ask_price[i].get_double();
    }
    return FC_SUCCESS;
}

static int OB4V1OnBar(FeatureState *pFeatureState0, YR_BAR const *pBar, double *pValues)
{
    auto pFS = (OB4V1State *)pFeatureState0;
    auto pFeature = pFS->pFeature;
    auto pFA = (OB4V1Argument *)pFeature->pFeatureArgument;
    auto pFeatureValue = pValues + pFeature->index;

    double features[FEATURE_COUNT] = {0.0};

    switch (pFA->index)
    {
    case 0: // bid tiny order number
    {
        features[0] = clip(asinh10(pFS->bnum_tylv31), 0.0, 10.0);
        break;
    }
    case 1: // bid tiny order volume
    {
        features[1] = clip(asinh10(pFS->btv_tylv31), 0.0, 10.0);
        break;
    }
    case 2: // tiny order cancel number
    {
        features[2] = clip(asinh10(pFS->cnum_tylv31), 0.0, 10.0);
        break;
    }
    case 3: // tiny order cancel volume
    {
        features[3] = clip(asinh10(pFS->ctv_tylv31), 0.0, 10.0);
        break;
    }
    case 4: // bid tiny order number - ask tiny order number
    {
        features[4] = clip(asinh10(pFS->bnum_tylv31 - pFS->anum_tylv31), -10.0, 10.0);
        break;
    }
    case 5: // bid tiny order volume - ask tiny order volume
    {
        features[5] = clip(asinh10(pFS->btv_tylv31 - pFS->atv_tylv31), -10.0, 10.0);
        break;
    }
    case 6: // tiny order cancel number / (bid tiny order number + ask tiny order number)
    {
        double denominator = pFS->bnum_tylv31 + pFS->anum_tylv31;
        features[6] = clip(asinh10(offset_divide(pFS->cnum_tylv31, denominator, 1.0)), 0.0, 10.0);
        break;
    }
    case 7: // tiny order cancel volume / (bid tiny order volume + ask tiny order volume)
    {
        double denominator = pFS->btv_tylv31 + pFS->atv_tylv31;
        features[7] = clip(asinh10(offset_divide(pFS->ctv_tylv31, denominator, 1.0)), 0.0, 10.0);
        break;
    }
    case 8: // bid large order number
    {
        features[8] = clip(asinh10(pFS->bnum_gtlv31), 0.0, 10.0);
        break;
    }
    case 9: // bid large order volume
    {
        features[9] = clip(asinh10(pFS->btv_gtlv31), 0.0, 10.0);
        break;
    }
    case 10: // large order cancel number
    {
        features[10] = clip(asinh10(pFS->cnum_gtlv31), 0.0, 10.0);
        break;
    }
    case 11: // large order cancel volume
    {
        features[11] = clip(asinh10(pFS->ctv_gtlv31), 0.0, 10.0);
        break;
    }
    case 12: // bid large order number - ask large order number
    {
        features[12] = clip(asinh10(pFS->bnum_gtlv31 - pFS->anum_gtlv31), -10.0, 10.0);
        break;
    }
    case 13: // bid large order volume - ask large order volume
    {
        features[13] = clip(asinh10(pFS->btv_gtlv31 - pFS->atv_gtlv31), -10.0, 10.0);
        break;
    }
    case 14: // large order cancel number / (bid large order number + ask large order number)
    {
        double denominator = pFS->bnum_gtlv31 + pFS->anum_gtlv31;
        features[14] = clip(asinh10(offset_divide(pFS->cnum_gtlv31, denominator, 1.0)), 0.0, 10.0);
        break;
    }
    case 15: // large order cancel volume / (bid large order volume + ask large order volume)
    {
        double denominator = pFS->btv_gtlv31 + pFS->atv_gtlv31;
        features[15] = clip(asinh10(offset_divide(pFS->ctv_gtlv31, denominator, 1.0)), 0.0, 10.0);
        break;
    }
    case 16: // bid order volume / last ask order volume at level 5
    {
        features[16] = clip(asinh10(offset_divide(pFS->btv, pFS->atv_lv5, 1.0)), 0.0, 10.0);
        break;
    }
    case 17: // ask order volume / last bid order volume at level 5
    {
        features[17] = clip(asinh10(offset_divide(pFS->atv, pFS->btv_lv5, 1.0)), 0.0, 10.0);
        break;
    }
    default:
    {
        break;
    }
    }

    *pFeatureValue = features[pFA->index];
    pFS->reset(0.0);
    return FC_SUCCESS;
}

FeatureDefStruct ob4V1BarDef = {

    .pFeatureInitFunc = OB4V1InitBar,
    .pFeatureFreeFunc = OB4V1Free,

    .pFeatureStateInitFunc = OB4V1StateInit,
    .pFeatureStateFreeFunc = OB4V1StateFree,

    .pCrossSectionalFeatureStateInitFunc = nullptr,
    .pCrossSectionalFeatureStateFreeFunc = nullptr,

    .pFeatureOnMarketFunc = nullptr,
    .pCalcOnMarketFunc = OB4V1OnMarket_calc,

    .pFeatureOnTransactionFunc = nullptr,
    .pCalcOnTransactionFunc = OB4V1OnTransaction,

    .pFeatureOnOrderFunc = nullptr,
    .pCalcOnOrderFunc = OB4V1OnOrder,

    .pFeatureOnDepthFunc = nullptr,
    .pCalcOnDepthFunc = nullptr,

    .pFeatureOnQuoteFunc = nullptr,
    .pCalcOnQuoteFunc = nullptr,

    .pFeatureOnDerivativeFunc = nullptr,
    .pCalcOnDerivativeFunc = nullptr,

    .pCalcOnTimerFunc = nullptr,

    .pFeatureOnBarFunc = OB4V1OnBar,
    .pCalcOnBarFunc = nullptr,

    .pFeatureOnCrossSectionalBarFunc = nullptr,
    .pCalcOnCrossSectionalBarFunc = nullptr,
};

FeatureDefStruct ob4V1MarketDef = {

    .pFeatureInitFunc = OB4V1InitMarket,
    .pFeatureFreeFunc = OB4V1Free,

    .pFeatureStateInitFunc = OB4V1StateInit,
    .pFeatureStateFreeFunc = OB4V1StateFree,

    .pCrossSectionalFeatureStateInitFunc = nullptr,
    .pCrossSectionalFeatureStateFreeFunc = nullptr,

    .pFeatureOnMarketFunc = OB4V1OnMarket,
    .pCalcOnMarketFunc = nullptr,

    .pFeatureOnTransactionFunc = nullptr,
    .pCalcOnTransactionFunc = OB4V1OnTransaction,

    .pFeatureOnOrderFunc = nullptr,
    .pCalcOnOrderFunc = OB4V1OnOrder,

    .pFeatureOnDepthFunc = nullptr,
    .pCalcOnDepthFunc = nullptr,

    .pFeatureOnQuoteFunc = nullptr,
    .pCalcOnQuoteFunc = nullptr,

    .pFeatureOnDerivativeFunc = nullptr,
    .pCalcOnDerivativeFunc = nullptr,

    .pCalcOnTimerFunc = nullptr,

    .pFeatureOnBarFunc = nullptr,
    .pCalcOnBarFunc = nullptr,

    .pFeatureOnCrossSectionalBarFunc = nullptr,
    .pCalcOnCrossSectionalBarFunc = nullptr,
};

FeatureDef OB4V1BarDef = {ob4V1BarDef};
FeatureDef OB4V1MarketDef = {ob4V1MarketDef};
