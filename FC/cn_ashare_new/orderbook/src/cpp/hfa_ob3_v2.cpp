#include <cstdint>
#include <cstdio>
#include <limits>
#include <queue>
#include <string>
#include <regex>

#include "functions.h"
#include <shennong/fc.h>
#include <shennong/utility/logger.h>
#include <shennong/stk/stream.h>
#include <shennong/toolkit/DayBarLoader.h>
#include <shennong/toolkit/RestorationLoader.h>

using namespace std;
using namespace shennong::fc;
using namespace shennong::data;

#pragma GCC diagnostic ignored "-Wunused-variable"
#pragma GCC diagnostic ignored "-Wsign-compare"
#pragma GCC diagnostic ignored "-Wreturn-type"
#pragma GCC diagnostic ignored "-Wcomment"
#pragma GCC diagnostic ignored "-Wunknown-pragmas"

int FEATURE_COUNT = 0;

struct OB3Argument : public FeatureArgument
{
    int64_t index;

    OB3Argument(int _index)
    {
        index = _index;
    }
};

struct OB3State : public FeatureState
{
    double bnum{0.0}, bvol{0.0}, anum{0.0}, avol{0.0}, bvol_lv1{0.0}, avol_lv1{0.0};
    double cnum_tylv62{0.0}, cvol_tylv62{0.0}, cnum_gtlv62{0.0}, cvol_gtlv62{0.0};
    double bnum_tylv62{0.0}, bvol_tylv62{0.0}, anum_tylv62{0.0}, avol_tylv62{0.0};
    double bnum_gtlv62{0.0}, bvol_gtlv62{0.0}, anum_gtlv62{0.0}, avol_gtlv62{0.0};

    void reset(double value)
    {
        bnum = bvol = anum = avol = value;
        bnum_tylv62 = bvol_tylv62 = anum_tylv62 = avol_tylv62 = value;
        bnum_gtlv62 = bvol_gtlv62 = anum_gtlv62 = avol_gtlv62 = value;
    }
};

static int OB3InitBar(Feature *pFeature)
{
    char const *pFeatureName = pFeature->featureConfig.formula.c_str();
    std::cout << "pFeatureName: " << pFeatureName << endl;
    bool matchSuccess = false;
    int index = 0;

    bool has_parameter = pFeature->featureConfig.parameter.isObject();
    bool has_findex = has_parameter && pFeature->featureConfig.parameter.isMember("findex");
    bool has_version = has_parameter && pFeature->featureConfig.parameter.isMember("version");
    int version = has_version ? pFeature->featureConfig.parameter["version"].asInt() : 0;   
    if (has_parameter && has_findex && has_version && version == 2)
    {
        int findex = pFeature->featureConfig.parameter["findex"].asInt();
        index = findex;
        if (index >= FEATURE_COUNT)
        {
            FEATURE_COUNT += 1;
        }
        matchSuccess = true;
    }

    if (matchSuccess == false)
        return FC_ARGSERR;

    pFeature->pFeatureArgument = new OB3Argument(index);

    return FC_SUCCESS;
}

static int OB3InitMarket(Feature *pFeature)
{
    char const *pFeatureName = pFeature->featureConfig.formula.c_str();
    std::cout << "pFeatureName: " << pFeatureName << endl;
    bool matchSuccess = false;
    int index = 0;

    bool has_parameter = pFeature->featureConfig.parameter.isObject();
    bool has_findex = has_parameter && pFeature->featureConfig.parameter.isMember("findex");
    bool has_version = has_parameter && pFeature->featureConfig.parameter.isMember("version");
    int version = has_version ? pFeature->featureConfig.parameter["version"].asInt() : 0;   
    if (has_parameter && has_findex && has_version && version == 2)
    {
        int findex = pFeature->featureConfig.parameter["findex"].asInt();
        index = findex;
        if (index >= FEATURE_COUNT)
        {
            FEATURE_COUNT += 1;
        }
        matchSuccess = true;
    }

    if (matchSuccess == false)
        return FC_ARGSERR;

    pFeature->pFeatureArgument = new OB3Argument(index);

    return FC_SUCCESS;
}

static int OB3Free(Feature *pFeature)
{
    delete pFeature->pFeatureArgument; // 释放特征参数
    delete pFeature;                   // 释放特征
    return FC_SUCCESS;
}

static int OB3StateInit(FeatureState **ppFeatureState, Feature const *pFeature, SymbolState const *pSymbolState)
{
    auto pFS = new OB3State();

    *ppFeatureState = pFS;
    pFS->reset(std::numeric_limits<double>::quiet_NaN());
    return FC_SUCCESS;
}

static int OB3StateFree(FeatureState *pFeatureState)
{
    delete pFeatureState; // 释放特征状态
    return FC_SUCCESS;
}

static int OB3OnTransaction(FeatureState *pFeatureState0, YR_TRANSACTION const *pTransaction)
{
    auto pFS = (OB3State *)pFeatureState0;                // 创建特征状态，转换为OrderState类型
    auto pFeature = pFS->pFeature;                        // 创建特征，用创建的特征状态赋值
    auto pFA = (OB3Argument *)pFeature->pFeatureArgument; // 创建特征参数，

    return FC_SUCCESS;
}

static int OB3OnOrder(FeatureState *pFeatureState0, YR_ORDER const *pOrder)
{
    auto pFS = (OB3State *)pFeatureState0;
    auto pFeature = pFS->pFeature;
    auto pFA = (OB3Argument *)pFeature->pFeatureArgument;

    auto order_vol = pOrder->volume.get_double();

    if (pOrder->side == Side::kBuy) // bid order
    {
        pFS->bnum = reset_nan(pFS->bnum) + double(1.0);
        pFS->bvol = reset_nan(pFS->bvol) + order_vol;

        if (between(order_vol, 0.0, 0.062 * pFS->avol_lv1)) // tiny order with order volume less than 0.062 * lv1
        {
            pFS->bnum_tylv62 = reset_nan(pFS->bnum_tylv62) + double(1.0);
            pFS->bvol_tylv62 = reset_nan(pFS->bvol_tylv62) + order_vol;

            if (pOrder->order_action == OrderAction::kDelete)
            {
                pFS->cnum_tylv62 = reset_nan(pFS->cnum_tylv62) + double(1.0);
                pFS->cvol_tylv62 = reset_nan(pFS->cvol_tylv62) + order_vol;
            }
        }
        else // tiny order with order volume greater than 0.062 * lv1
        {
            pFS->bnum_gtlv62 = reset_nan(pFS->bnum_gtlv62) + double(1.0);
            pFS->bvol_gtlv62 = reset_nan(pFS->bvol_gtlv62) + order_vol;

            if (pOrder->order_action == OrderAction::kDelete)
            {
                pFS->cnum_gtlv62 = reset_nan(pFS->cnum_gtlv62) + double(1.0);
                pFS->cvol_gtlv62 = reset_nan(pFS->cvol_gtlv62) + order_vol;
            }
        }
    }
    else if (pOrder->side == Side::kSell) // ask order
    {
        pFS->anum = reset_nan(pFS->anum) + double(1.0);
        pFS->avol = reset_nan(pFS->avol) + order_vol;

        if (between(order_vol, 0.0, 0.062 * pFS->bvol_lv1)) // tiny order with order volume less than 0.062 * lv
        {
            pFS->anum_tylv62 = reset_nan(pFS->anum_tylv62) + double(1.0);
            pFS->avol_tylv62 = reset_nan(pFS->avol_tylv62) + order_vol;

            if (pOrder->order_action == OrderAction::kDelete)
            {
                pFS->cnum_tylv62 = reset_nan(pFS->cnum_tylv62) + double(1.0);
                pFS->cvol_tylv62 = reset_nan(pFS->cvol_tylv62) + order_vol;
            }
        }
        else // tiny order with order volume greater than 0.062 * lv1
        {
            pFS->anum_gtlv62 = reset_nan(pFS->anum_gtlv62) + double(1.0);
            pFS->avol_gtlv62 = reset_nan(pFS->avol_gtlv62) + order_vol;

            if (pOrder->order_action == OrderAction::kDelete)
            {
                pFS->cnum_gtlv62 = reset_nan(pFS->cnum_gtlv62) + double(1.0);
                pFS->cvol_gtlv62 = reset_nan(pFS->cvol_gtlv62) + order_vol;
            }
        }
    }

    return FC_SUCCESS;
}

static int OB3OnMarket(FeatureState *pFeatureState0, YR_MARKET const *pMarket, double *pValues)
{
    auto pFS = (OB3State *)pFeatureState0;                // 创建特征状态，转换为OrderState类型
    auto pFeature = pFS->pFeature;                        // 创建特征，用创建的特征状态赋值
    auto pFA = (OB3Argument *)pFeature->pFeatureArgument; // 创建特征参数，用创建的特征赋值

    pFS->bvol_lv1 = pMarket->bid_volume[0]; // update accumulated bid volume at level 1
    pFS->avol_lv1 = pMarket->ask_volume[0]; // update accumulated ask volume at level 1
    auto pFeatureValue = pValues + pFeature->index;

    double features[FEATURE_COUNT] = {0.0};
    switch (pFA->index)
    {
    case 0: // bid tiny order number
    {
        features[0] = clip(asinh10(pFS->bnum_tylv62), 0.0, 10.0);
        break;
    }
    case 1: // bid tiny order volume
    {
        features[1] = clip(asinh10(pFS->bvol_tylv62), 0.0, 10.0);
        break;
    }
    case 2: // tiny order cancel number
    {
        features[2] = clip(asinh10(pFS->cnum_tylv62), 0.0, 10.0);
        break;
    }
    case 3: // tiny order cancel volume
    {
        features[3] = clip(asinh10(pFS->cvol_tylv62), 0.0, 10.0);
        break;
    }
    case 4: // bid tiny order number - ask tiny order number
    {
        features[4] = clip(asinh10(pFS->bnum_tylv62 - pFS->anum_tylv62), -10.0, 10.0);
        break;
    }
    case 5: // bid tiny order volume - ask tiny order volume
    {
        features[5] = clip(asinh10(pFS->bvol_tylv62 - pFS->avol_tylv62), -10.0, 10.0);
        break;
    }
    case 6: // tiny order cancel number / (bid tiny order number + ask tiny order number)
    {
        double denominator = pFS->bnum_tylv62 + pFS->anum_tylv62;
        features[6] = clip(asinh10(offset_divide(pFS->cnum_tylv62, denominator, 1.0)), 0.0, 10.0);
        break;
    }
    case 7: // tiny order cancel volume / (bid tiny order volume + ask tiny order volume)
    {
        double denominator = pFS->bvol_tylv62 + pFS->avol_tylv62;
        features[7] = clip(asinh10(offset_divide(pFS->cvol_tylv62, denominator, 1.0)), 0.0, 10.0);
        break;
    }
    case 8: // bid large order number
    {
        features[8] = clip(asinh10(pFS->bnum_gtlv62), 0.0, 10.0);
        break;
    }
    case 9: // bid large order volume
    {
        features[9] = clip(asinh10(pFS->bvol_gtlv62), 0.0, 10.0);
        break;
    }
    case 10: // large order cancel number
    {
        features[10] = clip(asinh10(pFS->cnum_gtlv62), 0.0, 10.0);
        break;
    }
    case 11: // large order cancel volume
    {
        features[11] = clip(asinh10(pFS->cvol_gtlv62), 0.0, 10.0);
        break;
    }
    case 12: // bid large order number - ask large order number
    {
        features[12] = clip(asinh10(pFS->bnum_gtlv62 - pFS->anum_gtlv62), -10.0, 10.0);
        break;
    }
    case 13: // bid large order volume - ask large order volume
    {
        features[13] = clip(asinh10(pFS->bvol_gtlv62 - pFS->avol_gtlv62), -10.0, 10.0);
        break;
    }
    case 14: // large order cancel number / (bid large order number + ask large order number)
    {
        double denominator = pFS->bnum_gtlv62 + pFS->anum_gtlv62;
        features[14] = clip(asinh10(offset_divide(pFS->cnum_gtlv62, denominator, 1.0)), 0.0, 10.0);
        break;
    }
    case 15: // large order cancel volume / (bid large order volume + ask large order volume)
    {
        double denominator = pFS->bvol_gtlv62 + pFS->avol_gtlv62;
        features[15] = clip(asinh10(offset_divide(pFS->cvol_gtlv62, denominator, 1.0)), 0.0, 10.0);
        break;
    }
    case 16: // bid order volume / last ask order volume at level 1
    {
        features[16] = clip(asinh10(offset_divide(pFS->bvol, pFS->avol_lv1, 1.0)), 0.0, 10.0);
        break;
    }
    case 17: // ask order volume / last bid order volume at level 1
    {
        features[17] = clip(asinh10(offset_divide(pFS->avol, pFS->bvol_lv1, 1.0)), 0.0, 10.0);
        break;
    }
    default:
    {
        break;
    }
    }

    *pFeatureValue = features[pFA->index];

    // pFS->reset(0.0);
    return FC_SUCCESS;
}

static int OB3OnMarket_calc(FeatureState *pFeatureState0, YR_MARKET const *pMarket)
{
    auto pFS = (OB3State *)pFeatureState0;                // 创建特征状态，转换为OrderState类型
    auto pFeature = pFS->pFeature;                        // 创建特征，用创建的特征状态赋值
    auto pFA = (OB3Argument *)pFeature->pFeatureArgument; // 创建特征参数，用创建的特征赋值

    pFS->bvol_lv1 = pMarket->bid_volume[0]; // update accumulated bid volume at level 1
    pFS->avol_lv1 = pMarket->ask_volume[0]; // update accumulated ask volume at level 1
    return FC_SUCCESS;
}

static int OB3OnBar(FeatureState *pFeatureState0, YR_BAR const *pBar, double *pValues)
{
    auto pFS = (OB3State *)pFeatureState0;
    auto pFeature = pFS->pFeature;
    auto pFA = (OB3Argument *)pFeature->pFeatureArgument;
    auto pFeatureValue = pValues + pFeature->index;

    double features[FEATURE_COUNT] = {0.0};

    switch (pFA->index)
    {
    case 0: // bid tiny order number
    {
        features[0] = clip(asinh10(pFS->bnum_tylv62), 0.0, 10.0);
        break;
    }
    case 1: // bid tiny order volume
    {
        features[1] = clip(asinh10(pFS->bvol_tylv62), 0.0, 10.0);
        break;
    }
    case 2: // tiny order cancel number
    {
        features[2] = clip(asinh10(pFS->cnum_tylv62), 0.0, 10.0);
        break;
    }
    case 3: // tiny order cancel volume
    {
        features[3] = clip(asinh10(pFS->cvol_tylv62), 0.0, 10.0);
        break;
    }
    case 4: // bid tiny order number - ask tiny order number
    {
        features[4] = clip(asinh10(pFS->bnum_tylv62 - pFS->anum_tylv62), -10.0, 10.0);
        break;
    }
    case 5: // bid tiny order volume - ask tiny order volume
    {
        features[5] = clip(asinh10(pFS->bvol_tylv62 - pFS->avol_tylv62), -10.0, 10.0);
        break;
    }
    case 6: // tiny order cancel number / (bid tiny order number + ask tiny order number)
    {
        double denominator = pFS->bnum_tylv62 + pFS->anum_tylv62;
        features[6] = clip(asinh10(offset_divide(pFS->cnum_tylv62, denominator, 1.0)), 0.0, 10.0);
        break;
    }
    case 7: // tiny order cancel volume / (bid tiny order volume + ask tiny order volume)
    {
        double denominator = pFS->bvol_tylv62 + pFS->avol_tylv62;
        features[7] = clip(asinh10(offset_divide(pFS->cvol_tylv62, denominator, 1.0)), 0.0, 10.0);
        break;
    }
    case 8: // bid large order number
    {
        features[8] = clip(asinh10(pFS->bnum_gtlv62), 0.0, 10.0);
        break;
    }
    case 9: // bid large order volume
    {
        features[9] = clip(asinh10(pFS->bvol_gtlv62), 0.0, 10.0);
        break;
    }
    case 10: // large order cancel number
    {
        features[10] = clip(asinh10(pFS->cnum_gtlv62), 0.0, 10.0);
        break;
    }
    case 11: // large order cancel volume
    {
        features[11] = clip(asinh10(pFS->cvol_gtlv62), 0.0, 10.0);
        break;
    }
    case 12: // bid large order number - ask large order number
    {
        features[12] = clip(asinh10(pFS->bnum_gtlv62 - pFS->anum_gtlv62), -10.0, 10.0);
        break;
    }
    case 13: // bid large order volume - ask large order volume
    {
        features[13] = clip(asinh10(pFS->bvol_gtlv62 - pFS->avol_gtlv62), -10.0, 10.0);
        break;
    }
    case 14: // large order cancel number / (bid large order number + ask large order number)
    {
        double denominator = pFS->bnum_gtlv62 + pFS->anum_gtlv62;
        features[14] = clip(asinh10(offset_divide(pFS->cnum_gtlv62, denominator, 1.0)), 0.0, 10.0);
        break;
    }
    case 15: // large order cancel volume / (bid large order volume + ask large order volume)
    {
        double denominator = pFS->bvol_gtlv62 + pFS->avol_gtlv62;
        features[15] = clip(asinh10(offset_divide(pFS->cvol_gtlv62, denominator, 1.0)), 0.0, 10.0);
        break;
    }
    case 16: // bid order volume / last ask order volume at level 1
    {
        features[16] = clip(asinh10(offset_divide(pFS->bvol, pFS->avol_lv1, 1.0)), 0.0, 10.0);
        break;
    }
    case 17: // ask order volume / last bid order volume at level 1
    {
        features[17] = clip(asinh10(offset_divide(pFS->avol, pFS->bvol_lv1, 1.0)), 0.0, 10.0);
        break;
    }
    default:
    {
        break;
    }
    }

    *pFeatureValue = features[pFA->index];
    // pFS->reset(0.0);
    return FC_SUCCESS;
}

FeatureDefStruct ob3BarDef = {

    .pFeatureInitFunc = OB3InitBar,
    .pFeatureFreeFunc = OB3Free,

    .pFeatureStateInitFunc = OB3StateInit,
    .pFeatureStateFreeFunc = OB3StateFree,

    .pCrossSectionalFeatureStateInitFunc = nullptr,
    .pCrossSectionalFeatureStateFreeFunc = nullptr,

    .pFeatureOnMarketFunc = nullptr,
    .pCalcOnMarketFunc = OB3OnMarket_calc,

    .pFeatureOnTransactionFunc = nullptr,
    .pCalcOnTransactionFunc = OB3OnTransaction,

    .pFeatureOnOrderFunc = nullptr,
    .pCalcOnOrderFunc = OB3OnOrder,

    .pFeatureOnDepthFunc = nullptr,
    .pCalcOnDepthFunc = nullptr,

    .pFeatureOnQuoteFunc = nullptr,
    .pCalcOnQuoteFunc = nullptr,

    .pFeatureOnDerivativeFunc = nullptr,
    .pCalcOnDerivativeFunc = nullptr,

    .pCalcOnTimerFunc = nullptr,

    .pFeatureOnBarFunc = OB3OnBar,
    .pCalcOnBarFunc = nullptr,

    .pFeatureOnCrossSectionalBarFunc = nullptr,
    .pCalcOnCrossSectionalBarFunc = nullptr,
};

FeatureDefStruct ob3MarketDef = {

    .pFeatureInitFunc = OB3InitMarket,
    .pFeatureFreeFunc = OB3Free,

    .pFeatureStateInitFunc = OB3StateInit,
    .pFeatureStateFreeFunc = OB3StateFree,

    .pCrossSectionalFeatureStateInitFunc = nullptr,
    .pCrossSectionalFeatureStateFreeFunc = nullptr,

    .pFeatureOnMarketFunc = OB3OnMarket,
    .pCalcOnMarketFunc = nullptr,

    .pFeatureOnTransactionFunc = nullptr,
    .pCalcOnTransactionFunc = OB3OnTransaction,

    .pFeatureOnOrderFunc = nullptr,
    .pCalcOnOrderFunc = OB3OnOrder,

    .pFeatureOnDepthFunc = nullptr,
    .pCalcOnDepthFunc = nullptr,

    .pFeatureOnQuoteFunc = nullptr,
    .pCalcOnQuoteFunc = nullptr,

    .pFeatureOnDerivativeFunc = nullptr,
    .pCalcOnDerivativeFunc = nullptr,

    .pCalcOnTimerFunc = nullptr,

    .pFeatureOnBarFunc = nullptr,
    .pCalcOnBarFunc = nullptr,

    .pFeatureOnCrossSectionalBarFunc = nullptr,
    .pCalcOnCrossSectionalBarFunc = nullptr,
};

FeatureDef OB3V2BarDef = {ob3BarDef};
FeatureDef OB3V2MarketDef = {ob3MarketDef};
