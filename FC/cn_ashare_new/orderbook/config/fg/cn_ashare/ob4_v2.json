{"tag": "fcts_ob4_v2", "startDate": "2018-01-02", "endDate": "2025-04-25", "logSavePath": ".", "logDisplayLevel": ".", "logTimePoint": 10, "mainProcessCore": 0, "streamingDump": false, "streamingRead": true, "threadingRead": true, "dailyRestart": true, "readChunkNum": 10000, "maxCacheLength": 102400, "exchange": {"cn_ashare": {"basic_info": {"region": "cn", "product": "ashare", "exchange_code": [".SZ", ".SH"], "convertor": "ashare", "symbolFilePath": "./config/S/cn_ashare/symbol.txt", "tradingDayFilePath": "/mnt/sda/NAS/AllData/config/cn_ashare_trading_days.txt", "preloadDays": 0, "maxTickLength": 1000, "featureManifestPath": "./config/fc/cn_ashare/feature_manifest.json", "exchangeGroupInfoFilePath": "/mnt/sda/NAS/AllData/config/exchange_groups/cn_ashare.json", "tsStartCore": 0, "tsFeatureThread": 1, "csStartCore": 2, "csFeatureThread": 1, "cs_join": false, "timeZone": 8}, "input_data": {"market": {"loadPath": "/mnt/sda/NAS/AllData/cn_ashare/tick/raw_wangbo_market__complete"}, "transaction": {"loadPath": "/mnt/sda/NAS/AllData/cn_ashare/tick/raw_wangbo_transaction__complete"}, "order": {"loadPath": "/mnt/sda/NAS/AllData/cn_ashare/tick/raw_wangbo_order__complete"}, "1minbar": {"loadPath": "/mnt/sda/NAS/AllData/cn_ashare/1minute/ohlcvt"}}, "output_data": {"market": {"savePath": "/mnt/sda/NAS/ShareFolder/lishuanglin/features/", "saveName": "lsl__fcts_ob4_v2", "featureFilePath": "./config/K/cn_ashare/tick/ob4_v2.json"}, "1minbar": {"savePath": "/mnt/sda/NAS/ShareFolder/lishuanglin/features/", "saveName": "lsl__fcts_ob4_v2", "featureFilePath": "./config/K/cn_ashare/1minute/ob4_v2.json"}}}}}