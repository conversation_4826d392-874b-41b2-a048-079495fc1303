import argparse
import json
import pandas as pd
import numpy as np
import xarray as xr
import itertools
import os
from multiprocessing import Pool, cpu_count
import shennong
from shennong.stk import bar, stream
from shennong.utils import key_group_manager as sn_km
import logging
import datetime
import uuid
from fg import generate_trading_day_list, getSTKHorizon, getSymbolsFromFile
import h5py


logging_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
logging.basicConfig(level = logging.INFO, format = logging_format)
logger = logging.getLogger(__name__)


if __name__ == '__main__':
    logger.info(f"shennong ver. {shennong.__version__}")
    logger.info("Start Merge...")
    parser = argparse.ArgumentParser(
                    prog='FeatureMerge',
                    formatter_class=argparse.RawDescriptionHelpFormatter,
                    description='Merge daliy symbol features h5 files into one h5 file')

    parser.add_argument(
        '--c',
        dest='fm_config_path',
        type=str,
        default = None,
        help='fm json config path, if not given, use default: None') 
    
    parser.add_argument(
        '--sgs',
        dest='symbol_group_size',
        type=int,
        default=-1,
        help='symbols count per task, if not given, use default: -1')

    parser.add_argument(
        '--l',
        dest='log_path',
        type=str,
        default = None,
        help='log path, if not given, use default: None') 
    
    parser.add_argument(
        '--b',
        dest='bruteforce',
        type=str,
        default = 'n',
        help='overwrite existing data, if not given, use default: no')
    
    parser.add_argument(
        '--dtype',
        dest='dtype',
        type=str,
        default = 'float64',
        help='data type of merged h5 file, if not given, use default: np.float64')
    
    parser.add_argument(
        '--attr',
        dest='attribute_config_tag',
        type=str,
        default = '',
        help='config tag of merged h5 file, if not given, use default: ')

    args = parser.parse_args()

    with open(args.fm_config_path) as fin:
        fm_config = json.load(fin)

    trading_day_list = generate_trading_day_list(fm_config)

    task_list = []

    for exchange in fm_config['exchange']:
        for dt in fm_config['exchange'][exchange]['output_data']:
            if 'saveName' in fm_config['exchange'][exchange]['output_data'][dt] and dt[0] != '#':
                merge_root_file_path = fm_config['exchange'][exchange]['output_data'][dt]['savePath']
                output_feature_list_path = fm_config['exchange'][exchange]['output_data'][dt]['featureFilePath']
                output_feature_list = [
                    name for name in pd.read_json(output_feature_list_path)['Name'].tolist()
                    if 'raw_gen' not in name
                ]
                task = (exchange, dt, merge_root_file_path, fm_config['exchange'][exchange]['output_data'][dt]['saveName'], output_feature_list)
                task_list.append(task)

    logger.info("")
    
    for trading_day in trading_day_list:
        for task in task_list:
            data_type = task[1]
            if "_cs" in data_type:
                continue
            data_type = data_type.split("_")[0]
            exchange = task[0]
            symbolFilePath = fm_config['exchange'][exchange]['basic_info']['symbolFilePath']
            if os.path.isfile(symbolFilePath):
                symbol_list = pd.read_csv(fm_config['exchange'][exchange]['basic_info']['symbolFilePath'],index_col=0,header=None).index.tolist()
            elif os.path.isdir(symbolFilePath):
                prefix = trading_day.replace("-", "")
                matching_files = [f for f in os.listdir(symbolFilePath) if f.startswith(prefix) and os.path.isfile(os.path.join(symbolFilePath, f))]
                if len(matching_files) > 0:
                    symbol_list = pd.read_csv(os.path.join(symbolFilePath, matching_files[0]),index_col=0,header=None).index.tolist()
                else:
                    print(f"Cannot find symbol file from {symbolFilePath}")
                    raise
            else:
                print(f"Unknown symbolFilePath {symbolFilePath}")
                raise
            
            region = exchange[0:2]
            product = exchange[3:]
            root_folder_path = task[2]
            save_group_name = task[3]
            stk_horizon = getSTKHorizon(data_type)
            
            source_root_folder_path = root_folder_path
            # source_root_folder_path = os.path.dirname(__file__) + "/../tmp/data" # airflow patch
            
            if args.symbol_group_size == 1:
                source_h5_files = [os.path.join(source_root_folder_path, 'symbol/' + symbol_name, exchange, stk_horizon, save_group_name, f"{trading_day}.h5") for symbol_name in symbol_list]
            else:
                symbol_groups = [symbol_list[i:i + args.symbol_group_size] for i in range(0, len(symbol_list), args.symbol_group_size)]
                source_h5_files = [os.path.join(source_root_folder_path, "symbol", exchange, stk_horizon, save_group_name, f"{trading_day}_{symbol_group_id}.h5") for symbol_group_id, _ in enumerate(symbol_groups)]
            
            target_h5_file = os.path.join(root_folder_path, exchange, stk_horizon, save_group_name, f"{trading_day}.h5")
            
            folder_path = os.path.join(root_folder_path, exchange, stk_horizon, save_group_name)
            if not os.path.exists(folder_path):
                os.makedirs(folder_path, exist_ok=True)
                
            if os.path.exists(target_h5_file):
                if args.bruteforce == "n":
                    # 跳过已生成的文件
                    logger.info(f"H5 File Already exists. Skip {target_h5_file}")
                    continue
                else:
                    # 覆盖已生成的文件
                    os.system('rm -rf ' + target_h5_file)

            logger.info(f"Start to merge {exchange}/{stk_horizon}/{save_group_name}/{trading_day} ...")
            if stk_horizon == 'tick':
                stream.merge_h5_files(source_h5_files, target_h5_file)
            else:
                cnts = 4
                if len(source_h5_files) > 1000:
                    cnts = 100
                if args.dtype == "float64":
                    bar.merge_h5_files(source_h5_files, target_h5_file, cnts=cnts)
                elif args.dtype == "float32":
                    bar.merge_h5_files(source_h5_files, target_h5_file, dtype=np.float32, cnts=cnts)
            
            if args.attribute_config_tag:
                with h5py.File(target_h5_file,'a') as f:
                    f.attrs['.config_tag'] = args.attribute_config_tag
                    logger.info(f"Overwite config to {args.attribute_config_tag}")
            logger.info(f"Merge completed. {exchange}/{stk_horizon}/{save_group_name}/{trading_day} to {target_h5_file}")
            
        logger.info(trading_day + ' end of the saving')
    
    logger.info("Finished Merge.")

