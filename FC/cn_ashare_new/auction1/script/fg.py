import os
import json
import pandas as pd
import random
import argparse
from multiprocessing import Pool, cpu_count, Lock
import logging
import copy
import datetime
import h5py
import getpass
import subprocess
import shutil
import sys
from pathlib import Path


logging_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
logging.basicConfig(level = logging.INFO, format = logging_format)
logger = logging.getLogger(__name__)
lock = Lock()


def getSTKHorizon(dt):
    if dt == 'market' or dt == 'transaction' or dt == 'order' or dt == 'derivatives' or dt == 'quote' or dt == 'depth':
        return 'tick'
    t = dt.split('msbar')
    if len(t) > 1:
        return str(t[0]) + 'msecond'
    t = dt.split('secbar')
    if len(t) > 1:
        return str(t[0]) + 'second'
    t = dt.split('minbar')
    if len(t) > 1:
        return str(t[0])  + 'minute'
    t = dt.split('hourbar')
    if len(t) > 1:
        return str(t[0]) + 'hour'


def generate_cn_ashare_trading_day_list(startDate, endDate):
    f_trading_day_list = set(d.strftime('%Y-%m-%d') for d in pd.date_range(start=startDate, end=endDate))
    
    trading_day_list_exchange = set(pd.read_csv("/mnt/sda/NAS/AllData/config/cn_ashare_trading_days.txt", header=None, index_col=None)[0])

    return sorted(f_trading_day_list & trading_day_list_exchange)


def getSymbolsFromFile(h5_file_path):
    with h5py.File(h5_file_path, "r") as f:
        if "SYMBOL" in f.keys():
            return [str(symbol,'utf-8') for symbol in f["SYMBOL"]]
        else:
            return [symbol for symbol in f.keys()]


def validate_h5_file(save_file_path):
    if os.path.exists(save_file_path):
        try:
            with h5py.File(save_file_path, 'r') as f:
                return len(f.keys()) > 0
        except:
            return False
    else:
        return False


def getIndivSymbolFilePath(tag, exchange, symbol):
    file_path_dir = f'./tmp/symbol/{tag}/{exchange}'
    file_path = f"{file_path_dir}/{symbol}.csv"
    with lock:
        if os.path.exists(file_path_dir) == False:
            os.mkdir(file_path_dir)
        if os.path.exists(file_path) == False:
            df = pd.DataFrame(index=[symbol])
            df.to_csv(file_path, header=None)
    return file_path


def genSymbolGroupFilePath(tag, exchange, trading_start_date, trading_end_date, group_id, trading_day, symbols):
    file_path_dir = f'./tmp/symbol/{tag}/{exchange}/{trading_start_date}-{trading_end_date}/{group_id}'
    with lock:
        if os.path.exists(file_path_dir) == False:
            os.makedirs(file_path_dir)
        file_path = f'{file_path_dir}/{trading_day}.csv'
        if os.path.exists(file_path) == False:
            df = pd.DataFrame(index=[symbols])
            df.to_csv(file_path, header=None)
    return file_path_dir


def getSymbolList(exchange_name, symbol_file_path, start_date, end_date, all_symbol_list_flag):
    symbol_list_dict = getSymbolListDict(exchange_name, symbol_file_path, start_date, end_date, all_symbol_list_flag)
    unique_symbols = list(set(symbol for symbols in symbol_list_dict.values() for symbol in symbols))
    return sorted(unique_symbols)


def getSymbolListDict(exchange_name, symbol_file_path, start_date, end_date, all_symbol_list_flag):
    if exchange_name == "cn_ashare":
        f_trading_day_list = generate_cn_ashare_trading_day_list(start_date, end_date)
    elif exchange_name == "gb_bina_future":
        f_trading_day_list = [d.strftime('%Y-%m-%d') for d in pd.date_range(start=start_date, end=end_date)]
    else:
        print(f"Unknown exchange {exchange_name}")
        raise
    
    # trading_day -> symbol_list
    symbol_list_dict = {}
    if all_symbol_list_flag == 'n':
        symbols = pd.read_csv(symbol_file_path, index_col=0, header=None).index.tolist()
        for trading_day in f_trading_day_list:
            symbol_list_dict[trading_day] = symbols
    else:
        if exchange_name == "cn_ashare":
            for trading_day in f_trading_day_list:
                symbols = pd.read_csv(os.path.join('/mnt/sda/NAS/YRData/Equity/cn_ashare/config/symbols', ''.join(trading_day.split('-'))+ '_cn_ashare_symbols.txt'),index_col=0,header=None).index.tolist()
                # etf_symbols = pd.read_csv('/mnt/sda/NAS/AllData/config/cn_fund_n_symbols.txt',index_col=0,header=None).index.tolist()
                # symbols = list(set(symbols) | set(etf_symbols))
                symbol_list_dict[trading_day] = sorted(symbols)
        elif exchange_name == "gb_bina_future":
            for trading_day in f_trading_day_list:
                symbols = pd.read_csv(os.path.join('/mnt/sda/NAS/YRData/Future/gb_bina_future/config/symbols', ''.join(trading_day.split('-'))+ '_gb_bina_future_symbols.txt'),index_col=0,header=None).index.tolist()
                symbol_list = set(symbols) - set(["PERPETUALS.BNF"])
                symbol_list_dict[trading_day] = sorted(symbol_list)
        else:
            print(f"Unknown exchange {exchange_name}")
            raise
    return symbol_list_dict


def define_arg_parser():
    parser = argparse.ArgumentParser(
            formatter_class=argparse.RawDescriptionHelpFormatter,
            description=\
            '''
        fg_slurm_job_array.py 

                (manually update the fg.slurm.job.array.config.json)

            python fg_slurm_job_array.py --c --g --cpu --mem --array --env
            ''')

    # Step params

    parser.add_argument(
        '--c',
        dest='fg_json_config_path',
        type=str,
        default = None,
        help='fg json config path, if not given, use default: None') 

    parser.add_argument(
        '--env',
        dest='setenv_sh_path',
        type=str,
        default = None,
        help='setenv_sh_path, if not given, use default: None') 

    parser.add_argument(
        '--g',
        dest='fg_command',
        type=str,
        default = None,
        help='fg_commnad, if not given, use default: None')

    parser.add_argument(
        '--n',
        dest='nodes',
        type=int,
        default = 1,
        help='number_of_nodes, if not given, use default: 1')

    parser.add_argument(
        '--t',
        dest='ntasks_per_node',
        type=int,
        default=1,
        help='taks running on each node, if not given, use default: 1')

    parser.add_argument(
        '--cpt',
        dest='cpus_per_task',
        type=int,
        default=1,
        help='cpus required by per task, if not given, use default: 1')

    parser.add_argument(
        '--mpn',
        dest='mem_per_node',
        type=str,
        default="0G",
        help='mem required by per task, if not given, use default: None')

    parser.add_argument(
        '--mpc',
        dest='mem_per_cpu',
        type=str,
        default="0G",
        help='mem required by per cpu, if not given, use default: None') 

    parser.add_argument(
        '--gpu',
        dest='gpu',
        type=str,
        default="",
        help='gpu required by per job, if not given, use default: None')

    parser.add_argument(
        '--cts',
        dest='cts',
        type=int,
        default=1,
        help='consecutive trading days per job if not given, use default: 1')

    parser.add_argument(
        '--all',
        dest='all_symbol_list',
        type=str,
        default="n",
        help='all_symbol_list, if not given, use default: false')
    
    parser.add_argument(
        '--sgs',
        dest='symbol_group_size',
        type=int,
        default=-1,
        help='symbols count per task, if not given, use default: -1')

    parser.add_argument(
        '--m',
        dest='model',
        type=str,
        default = 'stand-alone',
        help='running model, if not given, use default: stand-alone')

    parser.add_argument(
        '--b',
        dest='bruteforce',
        type=str,
        default = 'n',
        help='overwrite existing data, if not given, use default: no')

    parser.add_argument(
        '--p',
        dest='partition',
        type=str,
        default = '',
        help='slurm partition, if not given, use default: ')

    parser.add_argument(
        '--ctn',
        dest='calc_task_number',
        type=str,
        default = 'y',
        help='calc the no. of taks, if not given, use default: -1')

    parser.add_argument(
        '--exclude',
        dest='exclude',
        type=str,
        default = '',
        help='exclude nodes, if not given, use default: -1')

    parser.add_argument(
        '--mt',
        dest='max_task',
        type=str,
        default = '',
        help='the number of max tasks, if not given, use default: -1')
    
    parser.add_argument(
        '--conda',
        dest='conda',
        type=str,
        default = 'base',
        help='conda environment, if not given, use default: base')
    
    parser.add_argument(
        '--postaction',
        dest='postaction',
        type=str,
        default = '',
        help='postaction after previous job fininshed, if not given, use default: -1')
    
    parser.add_argument(
        '--notify',
        dest='notify',
        type=str,
        default = '',
        help='feishu notification after job fininshed, if not given, use default: -1')
    
    parser.add_argument(
        '--dtype',
        dest='dtype',
        type=str,
        default = 'float64',
        help='data type of merged h5 file, if not given, use default: np.float64')
    
    parser.add_argument(
        '--attr',
        dest='attribute_config_tag',
        type=str,
        default = '',
        help='config tag of merged h5 file, if not given, use default: ')

    return parser


def generate_input_args():

    parser = define_arg_parser()

    command_line_args = parser.parse_args()

    if not command_line_args.fg_json_config_path:
        logger.info('No command line arg: fg_json_config_path, will exit')
        exit(-1)

    args_dict = {}

    with open(command_line_args.fg_json_config_path) as f:
        args_dict = json.load(f)
        # class <dict> type
        # print('args_dict type:', type(args_dict))

    command_line_args_dict = vars(command_line_args)
    for key, value in command_line_args_dict.items():
        # Only update those not None value
        if value is not None:
            logger.info('Update args key:{}, value:{} from command line'.format(key, value))
            args_dict[key] = value
        else:
            logger.info(f"Skipped key: {key} because value is None")

    # Generate the args object
    # Use json.loads to recursively generate the object
    args = json.loads(
        json.dumps(args_dict))
        #object_hook=lambda d: namedtuple('X', d.keys())(*d.values()))

    args['tag'] = datetime.datetime.now().strftime("%Y%m%d_%H%M%S") + '_' + args_dict['tag']
    
    if args['fg_command'] == 'script/fm.py':
        args["tag"] += "_merge"
        
    print (args)
 
    return args


def _worker(prod):
    index = prod[0]
    trading_start_date = prod[1][0]
    trading_end_date   = prod[1][1]
    trading_day_list   = prod[1][2]
    exchange_symbol_path_list = prod[2]
    args = prod[3]

    fg_config_json = copy.deepcopy(args['fg_config_json'])
    save_fg_config_json = copy.deepcopy(fg_config_json)

    save_fg_config_json['startDate'] = trading_start_date
    save_fg_config_json['endDate'] = trading_end_date

    for exchange_symbol_path in exchange_symbol_path_list:
        exchange= exchange_symbol_path[0]
        symbol_group_id = exchange_symbol_path[1]
        save_fg_config_json['exchange'][exchange]['basic_info']['symbolFilePath'] = exchange_symbol_path[2]
        if symbol_group_id != 'portfolio':
            for dt in fg_config_json['exchange'][exchange]['output_data']:
                if 'saveName' in fg_config_json['exchange'][exchange]['output_data'][dt]:
                    if args['symbol_group_size'] == 1:
                        save_fg_config_json['exchange'][exchange]['output_data'][dt]['savePath'] = os.path.join(fg_config_json['exchange'][exchange]['output_data'][dt]['savePath'], 'symbol/' + symbol_group_id)
                    else:
                        save_fg_config_json['exchange'][exchange]['output_data'][dt]['savePath'] = os.path.join(fg_config_json['exchange'][exchange]['output_data'][dt]['savePath'])
                        save_fg_config_json['exchange'][exchange]['output_data'][dt]['symbolGroupID'] = symbol_group_id
    
    # 更新起始日期
    first_valid_trading_date = "1900-01-01"
    symbol_group_path = save_fg_config_json['exchange'][exchange]['basic_info']['symbolFilePath']
    if os.path.isdir(symbol_group_path) and args['symbol_group_size'] > 1 and args['fg_command'] != 'script/fm.py':
        symbol_files = sorted(Path(symbol_group_path).glob("*.csv"))
        first_valid_trading_date = symbol_files[0].stem
        if first_valid_trading_date != trading_start_date:
            save_fg_config_json['startDate'] = first_valid_trading_date
    ############################################################################################################################                                     
    fg_flag = False # 是否需要运行
    
    if args['bruteforce'] == 'n' and args['fg_command'] == 'bin/featureGenerator':
        # overwrite existing data
        for trading_day in trading_day_list:
            if trading_day < first_valid_trading_date:
                continue
            for exchange in save_fg_config_json['exchange']:
                for dt in save_fg_config_json['exchange'][exchange]['output_data']:
                    if dt[0] == '#': 
                        continue
                    save_group_path = save_fg_config_json['exchange'][exchange]['output_data'][dt]['savePath']
                    save_group_name = save_fg_config_json['exchange'][exchange]['output_data'][dt]['saveName']
                    if args['symbol_group_size'] <= 1:
                        # ind = y
                        save_file_path = os.path.join(save_group_path, exchange, getSTKHorizon(dt.split('_')[0]), save_group_name, f"{trading_day}.h5")
                    else:
                        symbol_group_id = save_fg_config_json['exchange'][exchange]['output_data'][dt]['symbolGroupID']
                        save_file_path = os.path.join(save_group_path, "symbol", exchange, getSTKHorizon(dt.split('_')[0]), save_group_name, f"{trading_day}_{symbol_group_id}.h5")
                    # 检查前一次运行保存的文件
                    if not validate_h5_file(save_file_path):
                        fg_flag = True
                        save_fg_config_json['startDate'] = trading_day
                        break       # output_data_list
                if fg_flag == True:
                    break           # exchange_list
            if fg_flag == True:
                break               # trading_day_list
    elif args['fg_command'] == 'script/fm.py':
        fg_flag = True
    else:
        fg_flag = True
    ############################################################################################################################
    if fg_flag:
        save_fg_config_file = f'./tmp/fgConfig/{args["tag"]}/{index}.json'                
        save_fg_config_json["configTag"] = args['tag']        
        with open(save_fg_config_file, 'w') as f:
            json.dump(save_fg_config_json,f,indent=4)
        return index

    return -1


def _get_daily_symbol_list(prod):
    # prod -> [(start_date, end_date), args, fg_config_json]
    trading_start_date = prod[0][0]
    trading_end_date = prod[0][1]
    args = prod[1]
    fg_config_json = prod[2]

    # exchange_symbol_path_dict -> { {start_date, end_date}: {exchange_name: {symbol: symbol_file_path} }}
    # exchange_symbol_path_dict -> { {start_date, end_date}: {exchange_name: {group_id: symbol_file_path} }}
    exchange_symbol_path_dict ={}

    for exchange in args['exchange']:
        if (trading_start_date, trading_end_date) not in exchange_symbol_path_dict:
            exchange_symbol_path_dict[(trading_start_date, trading_end_date)] = {}
        if exchange not in exchange_symbol_path_dict[(trading_start_date, trading_end_date)]:
            exchange_symbol_path_dict[(trading_start_date, trading_end_date)][exchange] = {}
            
        if args['symbol_group_size'] > 0 and args['fg_command'] != 'script/fm.py':
            if args['symbol_group_size'] == 1:
                # ind = yes
                # 单票运行，生成 trading_start_date 至 trading_end_date 内的每日所有 symbol 的合集
                symbol_list = getSymbolList(exchange, fg_config_json['exchange'][exchange]['basic_info']['symbolFilePath'], trading_start_date, trading_end_date, args['all_symbol_list'])
                # 对每一支票生成 symbol_file_path, 每个文件只有一只票
                for symbol in symbol_list:
                    exchange_symbol_path_dict[(trading_start_date, trading_end_date)][exchange][symbol] = getIndivSymbolFilePath(args["tag"], exchange, symbol)
            else:
                # symbol 分组运行，生成每个交易日到 symbol_list 的字典
                symbol_list_dict = getSymbolListDict(exchange, fg_config_json['exchange'][exchange]['basic_info']['symbolFilePath'], trading_start_date, trading_end_date, args['all_symbol_list'])
                for trading_day, symbol_list in symbol_list_dict.items():
                    # 对每天的 symbol list 按照 sgs 分组
                    symbol_groups = [symbol_list[i:i + args['symbol_group_size']] for i in range(0, len(symbol_list), args['symbol_group_size'])]
                    # 生成每天每组的 symbol_file_path
                    for idx, symbols in enumerate(symbol_groups):
                        exchange_symbol_path_dict[(trading_start_date, trading_end_date)][exchange][idx] = genSymbolGroupFilePath(args["tag"], exchange, trading_start_date, trading_end_date, idx, trading_day, symbols)        
        else:
            # ind = no/fm.py
            # symbol_group_size<=0, 全标的一起运行, 设置 portfolio
            # 全票运行
            if args['all_symbol_list'] == 'n':
                # 使用 fg 里配置的 symbolFilePath
                symbol_path = fg_config_json['exchange'][exchange]['basic_info']['symbolFilePath']
            else:
                # 使用全局 config 里的 symbolFilePath
                if exchange == "gb_bina_future":
                    symbol_path = '/mnt/sda/NAS/YRData/Future/gb_bina_future/config/symbols'
                elif exchange == "cn_ashare":
                    symbol_path = '/mnt/sda/NAS/YRData/Equity/cn_ashare/config/symbols'
                else:
                    print(f"Unknown exchange {exchange}")
                    raise
            exchange_symbol_path_dict[(trading_start_date, trading_end_date)][exchange]['portfolio'] = symbol_path

    return exchange_symbol_path_dict


def generate_trading_day_list(args):
    # 生成从 startDate 到 endDate 的日期列表
    f_trading_day_list = set(d.strftime('%Y-%m-%d') for d in pd.date_range(start=args['startDate'], end=args['endDate']))
    
    # 读取每个交易所的交易日列表，并使用集合进行交集计算
    trading_day_list = None

    for exchange, info in args['exchange'].items():
        trading_day_file = info['basic_info']['tradingDayFilePath']
        # 读取交易日文件，并转换为集合
        trading_day_list_exchange = set(pd.read_csv(trading_day_file, header=None, index_col=None)[0])

        # 初始化交易日列表或取交集
        if trading_day_list is None:
            trading_day_list = trading_day_list_exchange
        else:
            trading_day_list &= trading_day_list_exchange

    # 最后与日期范围的集合进行交集
    if trading_day_list is not None:
        trading_day_list &= f_trading_day_list
    else:
        # 如果交易日列表是空的，直接使用日期范围
        trading_day_list = f_trading_day_list

    # 转换为列表并排序
    return sorted(trading_day_list)


def mkdir_tmp_tag(args):
    tag = args["tag"]
    directories = [
        './tmp',
        './tmp/tradingDay',
        f'./tmp/symbol/{tag}',
        f'./tmp/fgConfig/{tag}',
        f'./tmp/fcConfig/{tag}',
        f'./tmp/script/{tag}',
        './log',
        f'./log/{tag}' if args['model'] == 'stand-alone' else None
    ]
    for directory in directories:
        if directory:
            Path(directory).mkdir(parents=True, exist_ok=True)


def generate_fg_config(args):
    ############################################################################################################################
    with open(args['fg_json_config_path'], 'r') as f:
        fg_config_json = json.load(f)
    
    mkdir_tmp_tag(args)

    args['fg_config_json'] = fg_config_json
    ############################################################################################################################
    trading_day_list = generate_trading_day_list(args)

    # 根据交易日列表和步长 `cts` 生成交易日的分段字典
    # 交易日字典，键为 (start_date, end_date)，值为对应的交易日子列表
    trading_day_tuple_dict = {
        (trading_day_list[i], trading_day_list[min(i + args['cts'], len(trading_day_list)) - 1]):
        trading_day_list[i:i + args['cts']]
        for i in range(0, len(trading_day_list), args['cts'])
    }
    
    trading_dates_prod = [(key, args, fg_config_json) for key in trading_day_tuple_dict.keys()]
    with Pool(processes=cpu_count()) as pool:
        exchange_symbol_path_dict = pool.map(_get_daily_symbol_list, trading_dates_prod)
    ############################################################################################################################
    # prod -> [prod[i]]
    # prod[i] -> ()
    # prod[i][0] -> (start_date, end_date, [start_date, ..., end_date])
    # prod[i][1] -> [(exchange_name, symbol_name, symbol_file_path)]
    # prod[i][2] -> args
    prod = []

    for result in exchange_symbol_path_dict:
        for td_tup, exchange_data in result.items():
            symbol_prod_list = []
            if args['symbol_group_size'] < 1 and args['fg_command'] != 'script/fm.py':
                # args['individual'] == 'n'
                symbol_prod_list = [
                    [(exchange, 'portfolio', exchange_info['portfolio'])]
                    for exchange, exchange_info in exchange_data.items()
                ]
            else:
                symbol_prod_list = [
                    [(exchange, symbol_group_id, group_data)]
                    for exchange, exchange_info in exchange_data.items()
                    for symbol_group_id, group_data in exchange_info.items()
                ]

            prod.extend([
                (td_tup + (trading_day_tuple_dict[td_tup],), symbol_prod, args)
                for symbol_prod in symbol_prod_list
            ])
            
    prod = [(i, *item) for i, item in enumerate(prod)]
    ############################################################################################################################
    print ('before filter, expected task ->', len(prod))
        
    with Pool(processes=cpu_count()) as pool:
        result_list = pool.map(_worker, prod)

    res_list = [i for i in result_list if i != -1]

    print ('number of waiting task ->', len(res_list))

    return res_list


def generate_slurm_script(args, task_list):
    if len(task_list) == 0:
        print ('no days needs to be generated')
        return

    task_list = [x for x in task_list if x is not None]

    if len(task_list) == 0:
        print ('all days are already generated')
        return None

    print ('task_list_cnt ->', len(task_list))

    fgcommand = args['fg_command']
    setenv_sh_file = args['setenv_sh_path']
    tag = args['tag']
    log_path = f"/mnt/sda/NAS/log/{getpass.getuser()}/fg"
    
    with open(setenv_sh_file, 'r') as setenv_file:
        setenv_content = setenv_file.read()
        
    max_sleep_time = min(120, len(task_list))
    for i, fgConfigId in enumerate(task_list):
        with open(f"./tmp/script/{tag}/{i}.sh",'w') as shell_file:
            shell_file.write('#!/bin/bash\n')
            shell_file.write(f'sleep $((RANDOM % {max_sleep_time}))\n')
            if fgcommand == 'bin/featureGenerator':
                shell_file.write(setenv_content)
                shell_file.write(f"{fgcommand} -c ./tmp/fgConfig/{tag}/{fgConfigId}.json -l {log_path}/{args['tag']}/{i}\n")
            elif fgcommand == 'script/fm.py':
                shell_file.write("BASE_CONDA_PREFIX=$(conda info --base)\n")
                shell_file.write("source $BASE_CONDA_PREFIX/etc/profile.d/conda.sh\n")
                shell_file.write(f'conda activate {args["conda"]}\n')
                if args['attribute_config_tag']:
                    shell_file.write(f"python {fgcommand} --sgs {args['symbol_group_size']} --c ./tmp/fgConfig/{tag}/{fgConfigId}.json --b {args['bruteforce']} --dtype {args['dtype']}  --attr {args['attribute_config_tag']} --l {log_path}/{args['tag']}/{i}\n")
                else:
                    shell_file.write(f"python {fgcommand} --sgs {args['symbol_group_size']} --c ./tmp/fgConfig/{tag}/{fgConfigId}.json --b {args['bruteforce']} --dtype {args['dtype']} --l {log_path}/{args['tag']}/{i}\n")

    script_file_path = f"./tmp/script/{tag}.sh"
    with open(script_file_path, "w") as script_file:
        if args["model"] == "slurm":
            script_file.write("#!/bin/bash\n")
            script_file.write(f"#SBATCH --job-name={tag[16:]}\n")
            script_file.write(f"#SBATCH -o {log_path}/{tag}/%A_%a.out\n")
            script_file.write(f'#SBATCH --partition={args["partition"]}\n')
            script_file.write(f'#SBATCH --nodes={args["nodes"]}\n')
            script_file.write(f'#SBATCH --ntasks-per-node={args["ntasks_per_node"]}\n')
            script_file.write(f'#SBATCH --cpus-per-task={args["cpus_per_task"]}\n')
            if args["gpu"] != "":
                script_file.write(f'#SBATCH --gpus={args["gpu"]}\n')
            if args["mem_per_cpu"] != "0G":
                script_file.write(f'#SBATCH --mem-per-cpu={args["mem_per_cpu"]}\n')
            if args["mem_per_node"] != "0G":
                script_file.write(f'#SBATCH --mem={args["mem_per_node"]}\n')
            if "exclude" in args and args["exclude"] != "":
                script_file.write(f'#SBATCH --exclude={args["exclude"]}\n')
            if "max_task" in args and args["max_task"] != "":
                script_file.write(f'#SBATCH --array=0-{len(task_list)-1}%{args["max_task"]}\n')
            else:
                script_file.write(f"#SBATCH --array=0-{len(task_list)-1}\n")
            script_file.write(f"bash ./tmp/script/{tag}/$SLURM_ARRAY_TASK_ID.sh")
        elif args["model"] == "stand-alone":
            script_file.write(f"seq 0 {len(task_list) - 1} | parallel --result ./log/{tag} bash ./tmp/script/{tag}/{{}}.sh")

    return script_file_path


def generate_notify_script(jobid_array, partition):
    script_content = (
        "#!/bin/bash\n"
        f"#SBATCH --job-name=FeishuBot-{jobid_array}\n"
        f"#SBATCH --output=./log/feishu_notify_task_{jobid_array}.out\n"
        f"#SBATCH --error=./log/feishu_notify_task_{jobid_array}.err\n"
        "#SBATCH --ntasks=1\n"
        "#SBATCH --cpus-per-task=1\n"
        "#SBATCH --mem=128M\n"
        f"#SBATCH --partition={partition}\n"
        f'python script/feishu_notification.py -j "{jobid_array}"\n'
    )
    script_file = f"./tmp/script/JobNotifyBot-{jobid_array}.sh"
    with open(script_file, "w") as f:
        f.write(script_content)
    return script_file


def sbatch(script_path, dependency=None, extra_params=None, env_vars=None):
    """
    Submits a SLURM job script using `sbatch` and returns the JobID.

    Parameters:
    - script_path (str): Path to the SLURM script to submit.
    - dependency (str): SLURM dependency string, e.g., "afterok:12345".
    - extra_params (list): Additional sbatch arguments, e.g., ["--time=01:00:00"].
    - env_vars (dict): Environment variables to pass to the script.

    Returns:
    - str: The JobID of the submitted job.

    Raises:
    - subprocess.CalledProcessError: If sbatch fails.
    """
    cmd = ["sbatch"]

    # Add dependencies if provided
    if dependency:
        cmd.append(f"--dependency={dependency}")

    # Add extra sbatch parameters if provided
    if extra_params:
        cmd.extend(extra_params)

    # Append the script path
    if script_path:
        cmd.append(script_path)

    # Setup environment variables
    env = None
    if env_vars:
        env = {**env_vars, **os.environ}  # Combine provided vars with current environment

    # Execute sbatch and capture output
    try:
        output = subprocess.check_output(cmd, universal_newlines=True, env=env)
        print(f"sbatch output: {output}")
        # Extract JobID from output (SLURM format: "Submitted batch job <JobID>")
        job_id = output.strip().split()[-1]
        return job_id
    except subprocess.CalledProcessError as e:
        print(f"Error submitting job with sbatch: {e.output}")
        raise


def post_action(args, dependency_job_id):
    args['fg_command'] = args['postaction']
    args["tag"] += "_postaction"
    task_list = generate_fg_config(args)
    if len(task_list) > 0:
        script_file_path = generate_slurm_script(args, task_list)
        return sbatch(script_file_path, dependency=f'afterok:{dependency_job_id}')
    else:
        return dependency_job_id


def copy_file_with_structure(src_file, dst_dir):
    """
    拷贝文件到目标文件夹，并保持目录架构
    :param src_file: 源文件路径
    :param dst_dir: 目标文件夹路径
    """
    # 获取源文件的相对路径（相对于其所在目录）
    src_dir = os.path.dirname(".")
    relative_path = os.path.relpath(src_file, src_dir)

    # 构建目标文件路径
    dst_file = os.path.join(dst_dir, relative_path)

    # 创建目标文件的目录结构
    os.makedirs(os.path.dirname(dst_file), exist_ok=True)

    # 拷贝文件
    shutil.copy2(src_file, dst_file)
    # print(f"Copied: {src_file} -> {dst_file}")


def configArchive(args):
    target_directory = f"/mnt/sda/NAS/log/{getpass.getuser()}/ArchivedFgConfig"
    if os.path.exists(target_directory) == False:
        os.mkdir(target_directory)
    os.mkdir(os.path.join(target_directory, args["tag"]))
    archived_path = os.path.join(target_directory, args["tag"])

    command_str = "python " + " ".join(sys.argv)
    with open(os.path.join(archived_path, "command.sh"), 'w') as f2:
        f2.write(command_str+"\n")
    
    copy_file_with_structure(args["fg_json_config_path"], archived_path)
    for exchange, info in args["exchange"].items():
        copy_file_with_structure(info['basic_info']['featureManifestPath'], archived_path)
        for horizon, output_info in info['output_data'].items():
            copy_file_with_structure(output_info["featureFilePath"], archived_path)


if __name__ == '__main__':

    args = generate_input_args()

    task_list = generate_fg_config(args)

    if args['calc_task_number'] == 'y':
        print ('just calc the number of tasks')
    else:
        if len(task_list) > 0:
            script_file_path = generate_slurm_script(args, task_list)
            if script_file_path != None:
                if args['model'] == 'slurm':
                    configArchive(args)
                    jobid_array = sbatch(script_file_path)
                    if args.get("notify") == 'y':
                        notify_script_path = generate_notify_script(jobid_array, args["partition"])
                        sbatch(notify_script_path, dependency=f'afterany:{jobid_array}')

                    if args.get("postaction"):
                        jobid_array = post_action(args, jobid_array)
                        if args.get("notify") == 'y':
                            notify_script_path = generate_notify_script(jobid_array, args["partition"])
                            sbatch(notify_script_path, dependency=f'afterany:{jobid_array}')

                elif args['model'] == 'stand-alone':
                    os.system('bash ' + script_file_path)
        else:
            print ('nothing to be generated!')
