import requests
import json
import configparser
import os
import argparse
import subprocess
from datetime import datetime


# 读取配置文件
def read_config():
    # 获取配置文件路径
    config_path = os.path.join(os.path.dirname(__file__), "config.ini")
    
    # 创建 ConfigParser 对象
    config = configparser.ConfigParser()
    config.read(config_path)
    
    # 读取 webhook_url
    webhook_url = config.get("Feishu", "webhook_url")
    return webhook_url


# 发送消息的函数
def send_feishu_message(message):
    webhook_url = read_config()  # 从配置文件中读取 webhook_url
    headers = {
        "Content-Type": "application/json"
    }
    data = {
        "msg_type": "text",
        "content": {
            "text": message
        }
    }
    response = requests.post(webhook_url, headers=headers, data=json.dumps(data))
    if response.status_code == 200:
        print("消息发送成功！")
    else:
        print(f"消息发送失败，状态码：{response.status_code}, 响应内容：{response.text}")


def get_job_efficiency(jobid):
    # 调用 seff 命令获取作业信息
    # cmd = ["seff", jobid]
    ssh_command = f"ssh master 'seff {jobid}'"
    try:
        result = subprocess.run(ssh_command, shell=True, capture_output=True, text=True, check=True)
        seff_output = result.stdout
    except subprocess.CalledProcessError as e:
        return f"Failed to get seff information for job {jobid}. Error: {e.stderr}"

    # 解析 seff 输出并生成消息
    message = f"Some of Job Efficiency Information:\n"
    for line in seff_output.splitlines():
        message += f"{line}\n"
        
    return message


def get_job_info(jobid):
    # 调用 scontrol 获取作业的详细信息
    try:
        scontrol_result = subprocess.run(
            ["scontrol", "show", "job", jobid],
            capture_output=True, text=True, check=True
        )
        job_info = scontrol_result.stdout
    except subprocess.CalledProcessError as e:
        return f"Failed to get job info for job {jobid}. Error: {e.stderr}"

    # 初始化变量
    first_submit_time = None
    last_end_time = None
    job_states = []
    job_name = None

    # 解析 SubmitTime, EndTime 和 JobState
    for line in job_info.splitlines():
        if "SubmitTime=" in line:
            submit_time = datetime.strptime(line.split("SubmitTime=")[1].split()[0], "%Y-%m-%dT%H:%M:%S")
            if not first_submit_time:
                first_submit_time = submit_time
            if submit_time < first_submit_time:
                first_submit_time = submit_time
        if "EndTime=" in line:
            end_time = datetime.strptime(line.split("EndTime=")[1].split()[0], "%Y-%m-%dT%H:%M:%S")
            if not last_end_time:
                last_end_time = end_time
            if last_end_time < end_time:
                last_end_time = end_time
        if "JobState=" in line:
            job_state = line.split("JobState=")[1].split()[0]
            job_states.append(job_state)
        if "JobName=" in line:
            job_name = line.split("JobName=")[1].split()[0]
        

    return first_submit_time, last_end_time, job_states, job_name

def check_job_completion(jobid):
    # 初始化状态统计
    state_counts = {}

    # 获取主任务的信息
    submit_time, end_time, job_states, job_name = get_job_info(jobid)

    # 统计主任务的状态
    for job_state in job_states:
        if job_state not in state_counts:
            state_counts[job_state] = 0
        state_counts[job_state] += 1

    # 计算总运行时间
    total_time = (end_time - submit_time).total_seconds()
    hours = int(total_time // 3600)
    minutes = int((total_time % 3600) // 60)
    seconds = int(total_time % 60)

    message = f"Job Name: {job_name}\nTotal runtime: {hours}h {minutes}m {seconds}s.\n"
    # 判断是否所有子任务都完成
    if all(state == "COMPLETED" for state in state_counts.keys()):
        message += f"All sub-jobs for job {jobid} are COMPLETED."
    else:
        # 生成状态统计信息
        status_message = ", ".join([f"{count} {state}" for state, count in state_counts.items()])
        message += f"Sub-jobs for job {jobid} have the following statuses: {status_message}."
    return message


def get_message_from_job(jobid):
    message = f"Summary for Job {jobid}\n"
    message += "\n"
    message += check_job_completion(jobid)
    message += "\n\n"
    message += get_job_efficiency(jobid)
    return message


# 发送消息
if __name__ == "__main__":
    # Parse arguments
    parser = argparse.ArgumentParser(description="Send Feishu notification for SLURM job.")
    parser.add_argument("--jobid", "-j", required=True, help="SLURM Jobid")
    args = parser.parse_args()
    message = get_message_from_job(args.jobid)
    send_feishu_message(message)
    