cmake_minimum_required(VERSION 3.16)

set(CMAKE_TOOLCHAIN_FILE "/mnt/sda/NAS/Release/shennong/fc/dev_global.dev/latest/toolchain.cmake")
include(${CMAKE_TOOLCHAIN_FILE})

project(shennong_feature_auction1)
set(CMAKE_BUILD_TYPE "DEBUG")
set(CMAKE_CXX_FLAGS_DEBUG "$ENV{CXXFLAGS} -O0 -Wall -g -ggdb")


# module: shennong_feature_auction1
add_library(shennong_feature_auction1 SHARED
    src/cpp/functions.cpp
    src/cpp/auction1.cpp
    )

target_include_directories(shennong_feature_auction1 PRIVATE
    ${spdlog}/include
    ${jsoncpp}/include
    ${shennong_utils}/include
    ${shennong_common}/include
    ${shennong_stk}/include
    ${shennong_fc}/include
)

target_link_libraries(shennong_feature_auction1 PRIVATE
    ${shennong_fc}/lib/libshennong_fc.so
)

