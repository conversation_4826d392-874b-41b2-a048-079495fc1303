#!/bin/bash
echo "parallele runing ${1} ${2} for multidays"
project_dir=/mnt/sda/home/<USER>/FC/cn_ashare_new/${1}/
cd ${project_dir} # 切换到执行目录下
export LD_LIBRARY_PATH=${project_dir}external_lib:${project_dir}lib
echo "The lib dir is: ${LD_LIBRARY_PATH}"

# echo "num of nodes: ${3}, mode of computation: ${4}"
# python script/fg_slurm_job_array.py --c config/fg.tick2tick.config.json --mem 300G --pjs ${3} --cpu 70 --env script/setenv.sh --g bin/fg_tick_ashare --m ${4}

path=$(dirname $(pwd))
echo -e "params: prject: ${1}, config_file: ${2}, conda_env: ${3}, cpu_per_node: ${4}, mem_per_node: ${5}, mode: ${6}"

source /mnt/sda/home/<USER>/miniconda3/etc/profile.d/conda.sh
conda activate ${3}
echo -e "conda env ${3} activated"

if [[ "${7}" == "fg" ]]; then
    echo -e "runing fg.py using ${6}"
    python script/fg.py \
    --c config/fg/cn_ashare/${2}.json \
    --env script/setenv.sh \
    --g bin/featureGenerator \
    --all y \
    --sgs 50 \
    --n 1 \
    --t 1 \
    --cpt ${4} \
    --mpn ${5} \
    --m ${6} \
    --b n \
    --cts 30 \
    --p AMD_R --ctn n
else
    echo -e "runing fm.py using ${6}"
    python script/fg.py \
    --c config/fg/cn_ashare/${2}.json \
    --env script/setenv.sh \
    --g script/fm.py \
    --all y \
    --sgs 50 \
    --n 1 \
    --t 1 \
    --cpt ${4} \
    --mpn ${5} \
    --m ${6} \
    --b n \
    --cts 1 \
    --p AMD_R --ctn n \
    --conda rawbase
fi

## params: project, config_file, conda_env, cpu_per_node, mem_per_node, partition
# bash runSlurm.sh trade quote3 rawbase 2 10G slurm fg
# bash runSlurm.sh trade quote3 rawbase 2 64G slurm fm

# bash runSlurm.sh auction1 auction1Bar rawbase 2 20G slurm fg
# bash runSlurm.sh auction1 auction1Bar rawbase 2 64G slurm fm

# bash runSlurm.sh orderbook ob4_v2 rawbase 2 20G slurm fg
# bash runSlurm.sh orderbook ob4_v2 rawbase 2 64G slurm fm

# bash runSlurm.sh auction1 auction1Bar rawbase 2 20G slurm fg
# bash runSlurm.sh auction1 auction1Bar rawbase 2 64G slurm fm