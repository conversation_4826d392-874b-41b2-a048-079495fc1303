import re
import seaborn as sns
import matplotlib.pyplot as plt
import polars as pl, pandas as pd, numpy as np

from . import tools
from .algo import Algo
from pandas.api import extensions


###-------------------------------------> Accessor definition
class UniversalAccessor:
	def __init__(self, pandas_obj):
		self._obj = pandas_obj


@extensions.register_dataframe_accessor('DIY')
class DataFrameAccessor(UniversalAccessor):
	def __init__(self, pandas_obj):
		super().__init__(pandas_obj)


@extensions.register_series_accessor('DIY')
class SeriesAccessor(UniversalAccessor):
	def __init__(self, pandas_obj):
		super().__init__(pandas_obj)


@extensions.register_dataframe_accessor('Wrapper')
class DataFrameAccessor(UniversalAccessor):
	def __init__(self, df):
		super().__init__(df)
		self.shape = df.shape
		self.index = df.index
		self.columns = df.columns
		self.arr = df.to_numpy()
		self.dims = df.index.names

	## mean over a specified index
	def groupby_math(self, agg='mean', dim='feature'):
		if isinstance(dim, str):  ## directly specify the dim name
			dims = self.dims.copy()
			dims.remove(dim)
			return self._obj.groupby(level=dims, sort=False).agg(agg)
		elif isinstance(dim, int):  ## specify the index of the dim
			dims = list(range(self.index.levels))
			dims.remove(dim)
			return self._obj.groupby(level=dims, sort=False).agg(agg)
		elif isinstance(dim, list):
			assert len(dim) > 0, 'dim should not be empty'
			if isinstance(dim[0], int):  ## specify the indexs of dim names
				dims = list(range(len(self.index.levels)))
				for d in dim:
					dims.remove(d)
				return self._obj.groupby(level=dims, sort=False).agg(agg)
			elif isinstance(dim[0], str):  ## directly specify the dim names
				dims = self.dims.copy()
				for d in dim:
					dims.remove(d)
				return self._obj.groupby(level=dims, sort=False).agg(agg)

	def filter(self, dim: dict):
		"""
		Args: value of dict should be regex
		"""
		idxs, singles = [], []
		for i in range(len(self.index.names)):
			idx_name = self.index.names[i]
			idx = self.index.get_level_values(i).unique().tolist()
			if i in dim.keys():
				idx = [s for s in idx if re.search(dim[i], s)]
				# print(i,idx)
			elif idx_name in dim.keys():
				idx = [s for s in idx if re.search(dim[idx_name], s)]
				# print(idx_name, idx)
			if len(idx) == 1:
				singles.append(i)
			idxs.append(idx)
		sel = pd.MultiIndex.from_product(idxs)
		return self._obj.loc[self.index.isin(sel)].droplevel(singles)

	def filter2(self, dim: dict):
		"""
		Args: value of dict should be list
		"""
		df = self._obj.copy()
		for dim_name, dim_values in dim.items():
			idx = list(df.index.names).index(dim_name)
			sel = [True if s[idx] in dim_values else False for s in df.index]
			df = df.loc[sel]
		return df


###-------------------------------------> functions definition
## count nan numbers for each column
def _nan_count(df):
	df = df._obj.copy()
	return np.isnan(df).sum()


## get nan ratio for each column
def _nan_ratio(df):
	df = df._obj.copy()
	return np.isnan(df).sum() / df.shape[0]


## count inf numbers for each column
def _inf_count(df, axis=0):
	df = df._obj.copy()
	return np.isinf(df).sum(axis=axis)


## fill inf wil None in pl.DataFrame
def _fill_inf(df: pd.DataFrame, value=None):
	df = df._obj.copy()
	rows, cols = df.index, df.columns
	df = pl.DataFrame(df.to_numpy(), schema=df.columns.tolist())  # .fill_nan(None)
	df = df.with_columns(
		pl.when(pl.exclude('datetime').is_infinite()).then(value).otherwise(pl.exclude('datetime')).name.keep()
	)
	return pd.DataFrame(df.to_numpy(), index=rows, columns=cols)


## log for each column, add 1.0 to avoid log(0) exception, default base=10
def _log(df, base=10):
	df = df._obj.copy()
	assert base > 0, 'base should be non-negatitive'
	if base == 2:
		return np.log2(df.add(1.0))
	elif base == 10:
		return np.log10(df.add(1.0))
	elif base == np.e:
		return np.log(df.add(1.0))
	else:
		return np.log(df.add(1.0)) / np.log(base)


def _log_with_sign(df, base=10):
	df = df._obj.copy()
	assert base > 0, 'base should be non-negatitive'

	sign = np.sign(df)
	return sign * df.abs().DIY.log(base)


def _asinh(df: pd.DataFrame, cols: list[int | str] = []):
	df = df._obj.copy()

	def _asinh_(df):
		return np.arcsinh(df) / np.arcsinh(10.0)

	if len(cols) == 0:
		return _asinh_(df)
	for col in cols:
		if isinstance(col, int):
			col = df.columns[col]
		df[col] = _asinh_(df[col])
	return df


## exp for each column
def _exp(df):
	df = df._obj.copy()
	return np.exp(df)


## square root for each column
def _sqrt(df):
	df = df._obj.copy()
	return np.sqrt(df)


def _square(df):
	df = df._obj.copy()
	return np.square(df)


def _square_sum(df):
	df = df._obj.copy()
	return np.square(df).sum()


## convert to polars dataframe
def _to_polars(df, keep_index=True):
	df = df._obj.copy()
	if keep_index:
		return pl.from_pandas(df.reset_index())
	else:
		return pl.from_pandas(df)


## overload of df.head to view head n rows and head m columns
def _head(df, nrows: int = 5, ncols: int = 5):
	df = df._obj.copy()
	return df.iloc[:nrows, :ncols]


## overload of df.tail to view tail n rows and tail m columns
def _tail(df, nrows: int = 5, ncols: int = 5):
	df = df._obj.copy()
	return df.iloc[-nrows:, -ncols:]


## overload of df.dropna
def _dropna(df, how='all', axis=0):
	df = df._obj.copy()
	if axis in {0, 1, 'row', 'column'}:
		return df.dropna(how=how, axis=axis)
	elif axis == 'both':
		return df.dropna(how=how).dropna(how=how, axis=1)


## zscore a series
def _zscore_(s: pd.Series, method: str = 'median_mad'):
	s = s._obj
	s = s.replace([np.inf, -np.inf], np.nan)
	if method == 'mean_std':
		miu, sigma = s.mean(), s.std()
	elif method == 'median_mad':
		miu, sigma = s.median(), (s - s.median()).abs().median()
	s = s.where(s <= miu + 3.0 * sigma, np.nan).where(s >= miu - 3.0 * sigma, np.nan)
	s = (s - miu) / sigma
	return s


## zscore for each row or column
def _zscore(df: pd.DataFrame, axis=0, method: str = 'median_mad'):
	df = df._obj.copy()
	assert axis in {0, 1, 'row', 'column'}, 'axis should be one of [0, 1, "row", "column"]'
	df = df.replace([np.inf, -np.inf], np.nan)
	if axis in {0, 'row'}:
		df = df.T

	if method == 'mean_std':
		miu, sigma = df.mean(), df.std()
	elif method == 'median_mad':
		miu, sigma = df.median(), (df - df.median()).abs().median()
	df = df.where(df <= miu + 3.0 * sigma, np.nan).where(df >= miu - 3.0 * sigma, np.nan)
	df = (df - miu) / sigma
	return df.T if axis in {0, 'row'} else df


def _demean(df: pd.DataFrame):
	df = df._obj.copy()
	return df - df.mean()


## scale each column or row to a limited range
def _scale(
	df: pd.DataFrame,
	lower: float = -1.0,
	upper: float = 1.0,
):
	df = df._obj.copy()
	assert lower < upper, 'lower should be smaller than upper'
	return (df - df.min()) * (upper - lower) / (df.max() - df.min()) + lower


## histogram
def _hist(df: pd.DataFrame, bins: int = 100):
	df = df._obj.copy()
	num_cols = df.shape[-1]
	fig_cols = int(min(num_cols, 3))  ## each fig row has at most 3 columns
	fig_rows = int(np.ceil(num_cols / fig_cols))
	figsize = (fig_cols * 4, fig_rows * 2.4)
	df.hist(bins=bins, figsize=figsize, layout=(fig_rows, fig_cols))


## pearson correlation using matrix mulplication
def _corr(df: pd.DataFrame, k=None, device='cpu'):
	import gc

	## copy to avoid modification to df
	df = df._obj.copy()
	if k is None:
		fnames, lnames = df.columns, df.columns
		a = df.to_numpy().copy()
		b = df.to_numpy().copy()
	elif k is not None:
		if k < 0:
			k = df.shape[-1] + k
		fnames, lnames = df.columns[:k], df.columns[k:]
		a = df.to_numpy()[:, :k].copy()
		b = df.to_numpy()[:, k:].copy()

	if ('gpu' in device) | ('cuda' in device):
		import torch

		a = torch.from_numpy(a).pin_memory().to(device, non_blocking=True).float()
		b = torch.from_numpy(b).pin_memory().to(device, non_blocking=True).float()
		amask, bmask = torch.isnan(a), torch.isnan(b)
		a[amask], b[bmask] = 0.0, 0.0

		amask = (~amask).float()
		bmask = (~bmask).float()

		cnt = torch.matmul(amask.T, bmask)
		ex = (torch.matmul(a.T, bmask) / cnt).cpu().numpy()
		ey = (torch.matmul(amask.T, b) / cnt).cpu().numpy()
		exy = (torch.matmul(a.T, b) / cnt).cpu().numpy()
		ex2 = (torch.matmul((a**2).T, bmask) / cnt).cpu().numpy()
		ey2 = (torch.matmul(amask.T, (b**2)) / cnt).cpu().numpy()
		cnt = cnt.cpu().numpy()
		del a, b, amask, bmask
		gc.collect()
		torch.cuda.empty_cache()

	else:
		## mask nan as zeros for summation
		amask, bmask = np.isnan(a), np.isnan(b)
		a[amask], b[bmask] = 0.0, 0.0

		## get valid element number between columns
		amask = (~amask).astype(np.float64)
		bmask = (~bmask).astype(np.float64)
		cnt = np.dot(amask.T, bmask)
		## calculate covariance and avarage between columns
		ex = (a.T @ bmask) / cnt
		ey = (amask.T @ b) / cnt
		exy = (a.T @ b) / cnt

		## calculate std for matrix a and b
		ex2 = ((a**2).T @ bmask) / cnt
		ey2 = (amask.T @ (b**2)) / cnt
		del a, b, amask, bmask
		gc.collect()

	## calculate the final correlation coefficients
	r2 = np.true_divide(exy - ex * ey, np.sqrt(ex2 - ex**2) * np.sqrt(ey2 - ey**2))

	cnt = pd.DataFrame(cnt, index=fnames, columns=lnames)
	ex = pd.DataFrame(ex, index=fnames, columns=lnames)
	ey = pd.DataFrame(ey, index=fnames, columns=lnames)
	exy = pd.DataFrame(exy, index=fnames, columns=lnames)
	ex2 = pd.DataFrame(ex2, index=fnames, columns=lnames)
	ey2 = pd.DataFrame(ey2, index=fnames, columns=lnames)
	r2 = pd.DataFrame(r2, index=fnames, columns=lnames)

	return pd.concat({'cnt': cnt, 'ex': ex, 'ey': ey, 'exy': exy, 'ex2': ex2, 'ey2': ey2, 'r2': r2})


## clip column names that are too long
def _clip_columns(df: pd.DataFrame, s: str):
	df = df._obj.copy()
	df.columns = [col.replace(s, '') for col in df.columns]
	return df


## clip column names that are too long
def _drop_duplicates(df: pd.DataFrame, on_index: bool = True, keep: str = 'first'):
	df = df._obj.copy()
	if on_index:
		df = df.loc[~df.index.duplicated(keep=keep)]
	else:
		df = df.drop_duplicates(keep=keep)
	return df


## rename column names
def _rename_columns(df: pd.DataFrame, s1: str, s2: str):
	df = df._obj.copy()
	df.columns = [col.replace(s1, s2) for col in df.columns]
	return df


## get IR for IC and RETURN dataframe
def _IR(df: pd.DataFrame):
	df = df._obj.copy()
	df = df.replace([np.inf, -np.inf], np.nan)
	return np.true_divide(df.mean(), df.std())


## re match rows of pandas dataframe
def _filter(df: pd.DataFrame, restr: str):
	df = df._obj.copy()
	idx = df.index.tolist()
	matched_rows = [v for v in idx if re.search(restr, v)]
	return df.loc[matched_rows, :]


## re match columns of pandas dataframe
def _select(df: pd.DataFrame, restr: str):
	df = df._obj.copy()
	idx = df.columns.tolist()
	matched_cols = [v for v in idx if re.search(restr, v)]
	return df.loc[:, matched_cols]


## re match columns of panas dataframe, get left
def _select_not(df: pd.DataFrame, restr: str):
	df = df._obj.copy()
	idx = df.columns.tolist()
	matched_cols = [v for v in idx if not re.search(restr, v)]
	return df.loc[:, matched_cols]


## add prefix to df.index or df.columns
def _add_fix(df: pd.DataFrame, fixstr: str, method='suffix', axis='columns'):
	df = df._obj.copy()
	if method == 'prefix':
		if axis == 'index':
			df.index = [f'{fixstr}{s}' for s in df.index]
		if axis == 'columns':
			df.columns = [f'{fixstr}{s}' for s in df.columns]
	elif method == 'suffix':
		if axis == 'index':
			df.index = [f'{s}{fixstr}' for s in df.index]
		if axis == 'columns':
			df.columns = [f'{s}{fixstr}' for s in df.columns]
	return df


def _heatmap(df: pd.DataFrame, figsize: tuple = (10, 5), low=-0.03, high=0.03):
	df = df._obj.copy()
	df.columns = [f'col{i}' for i in range(len(df.columns))]
	fig, ax = plt.subplots(figsize=(12, 0.3 * len(df)))
	sns.heatmap(df, annot=True, cmap='coolwarm', cbar=False, fmt='.3f', ax=ax, vmin=low, vmax=high)


def _cast_layer(df: pd.DataFrame, n_layers: int = 10):
	df = df._obj.copy()
	LEN = len(df.dropna())
	num_larger = LEN % n_layers
	len_small = LEN // n_layers
	sizes = [0] + [len_small] * (n_layers - num_larger) + [len_small + 1] * num_larger
	bins = np.cumsum(sizes)
	casted = np.digitize(df, bins, right=True).astype(np.float64)
	casted[casted > n_layers] = np.nan
	if isinstance(df, pd.Series):
		return pd.Series(casted, index=df.index)
	else:
		return pd.DataFrame(casted, index=df.index, columns=df.columns)


def _mad(df: pd.DataFrame):
	df = df._obj.copy()
	return (df - df.median()).abs().median()


## used for feature engineering, checking feature dist and abnormal values
def _preview(df: pd.DataFrame):
	df = df._obj.copy()
	df.corr().DIY.heatmap(low=-1, high=1)
	df.DIY.hist(bins=100)
	a = df.DIY.inf_count().to_frame('inf_count')
	b = df.DIY.nan_count().to_frame('nan_count')
	c = df.DIY.nan_ratio().to_frame('nan_ratio')
	print(df.shape)
	return pd.concat([a, b, c], axis=1).sort_values('nan_ratio', ascending=False)


def _mask_diag(df: pd.DataFrame, value: float = np.nan):
	ic = df._obj.copy()
	row, col = np.diag_indices_from(ic)
	ic.values[row, col] = value
	return ic


###-------------------------------------> functions registration
UniversalAccessor.nan_count = _nan_count
UniversalAccessor.nan_ratio = _nan_ratio
UniversalAccessor.inf_count = _inf_count
UniversalAccessor.fill_inf = _fill_inf
UniversalAccessor.log = _log
UniversalAccessor.log_with_sign = _log_with_sign
UniversalAccessor.asinh = _asinh
UniversalAccessor.exp = _exp
UniversalAccessor.sqrt = _sqrt
UniversalAccessor.square = _square
UniversalAccessor.square_sum = _square_sum
UniversalAccessor.to_polars = _to_polars
UniversalAccessor.head = _head
UniversalAccessor.tail = _tail
UniversalAccessor.dropna = _dropna
UniversalAccessor.zscore = _zscore
UniversalAccessor.demean = _demean
UniversalAccessor.scale = _scale
UniversalAccessor.corr = _corr
UniversalAccessor.hist = _hist
UniversalAccessor.clip_columns = _clip_columns
UniversalAccessor.drop_duplicates = _drop_duplicates
UniversalAccessor.rename_columns = _rename_columns
UniversalAccessor.IR = _IR
UniversalAccessor.filter = _filter
UniversalAccessor.select = _select
UniversalAccessor.select_not = _select_not
UniversalAccessor.add_fix = _add_fix
UniversalAccessor.heatmap = _heatmap
UniversalAccessor.cast_layer = _cast_layer
UniversalAccessor.mad = _mad
UniversalAccessor.preview = _preview
UniversalAccessor.mask_diag = _mask_diag
