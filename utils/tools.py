# coding = utf-8
import os
import re
import sys
import h5py
import time
import psutil
import warnings
import datetime
import numpy as np
import polars as pl
import pandas as pd
import xarray as xr

from tqdm.auto import tqdm
from shennong.utils import trading_days, symbol
from sklearn.linear_model import LinearRegression
from joblib import Parallel, delayed
warnings.filterwarnings('ignore')


## base class for timing code snippets
class Timer:
	def __init__(self, name: str = ''):
		self.name = name
		self.start()

	def now(self):
		"""
		Returns the current date and time in the format 'YYYY-MM-DD HH:MM:SS'.
		"""
		return datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

	def start(self):
		"""
		Starts the timer by initializing tick and time lists.
		Prints a message indicating timer has started.
		"""
		print(f'[{self.now()}] starts to timing!')
		self.ticks = ['start']
		self.times = [time.time()]

	def time_elapsed(self, sTick: int, eTick: int):
		"""
		Calculates the elapsed time between two ticks and sets appropriate time units.

		Args:
			sTick (int): Index of the start tick in self.times
			eTick (int): Index of the end tick in self.times

		"""
		delta = self.times[eTick] - self.times[sTick]
		suffix = 'seconds' if delta < 300 else 'minutes'
		if delta > 600:
			delta /= 60
		self.delta, self.suffix = delta, suffix

	def add_tick(self, tick_desc: str = ''):
		"""
		Adds a new ticker to the timer and updates the elapsed time.

		Args:
			tick_desc (str): Description of the tick event
		"""
		self.ticks.append(tick_desc)
		self.times.append(time.time())
		self.time_elapsed(-2, -1)
		print(
			f'[{self.now()}] time elapsed [{tick_desc}]: {self.delta: .4f} {self.suffix}!'
		)

	def stop(self, tick_desc: str = ''):
		"""
		Stops the timer and prints the final elapsed time.

		Args:
			tick_desc (str): Description of the stop event
		"""
		self.times.append(time.time())
		self.ticks.append('stop')
		self.time_elapsed(0, -1)
		print(
			f'[{self.now()}] time elapsed [{tick_desc}]: {self.delta: .4f} {self.suffix}'
		)

	@classmethod
	def timing(cls, func: callable, args: tuple, tick_desc: str = '', **kwargs):
		"""
		Args:
			func (callable): Function to be executed
			args (tuple): Arguments passed to func
			tick_desc (str): Description of the timing event
			**kwargs: Additional keyword arguments to pass to func

		Returns:
			The result of calling func(*args, **kwargs)
		"""
		inst = cls()
		result = func(*args, **kwargs)
		inst.stop(tick_desc)
		return result


def split_list(arr: list, n: int):
	"""evenly split long list into n short list
	Args:
		arr (list): List to be split
		n (int): Number of sublists to split into

	Returns:
		list: List of n sublists with elements from arr distributed as evenly as possible
	"""
	if n > len(arr):
		return split_list(arr, len(arr))
	if n == 1:
		return list(arr)
	num_per_child = (len(arr) // n) * np.ones(n, dtype=int)
	num_per_child[: len(arr) % n] += 1
	ticks = [0] + list(np.cumsum(num_per_child))
	return [list(arr[ticks[i] : ticks[i + 1]]) for i in range(len(ticks) - 1)]


# cast series into layer index
def cast_layer(se: pd.Series, n_layers: int = 10):
	"""cast series into layer index, can be used for layer pre-calculation
	Args:
		se (pd.Series): Input series to be cast into layers
		n_layers (int): Number of layers to split the data into, defaults to 10

	Returns:
		np.ndarray: Array of layer indices with same shape as input, values from 1 to n_layers,
				  with NaN for values outside the range
	"""
	LEN = len(se.dropna())
	num_larger = LEN % n_layers
	len_small = LEN // n_layers
	sizes = [0] + [len_small] * (n_layers - num_larger) + [len_small + 1] * num_larger
	bins = np.cumsum(sizes)
	casted = np.digitize(se, bins, right=True).astype(np.float64)
	casted[casted > n_layers] = np.nan
	return casted


def get_memory_usage():
	"""
	Returns the current memory usage in GB
	"""
	return psutil.virtual_memory().used / 1024**3


def get_roll_nodes(start: str, end: str, freq: str = 'YS', skip_end: int = 0):
	"""split date range into sub-dates with a given frequency to roll training
	Args:
		start (str): Start date in YYYY-MM-DD format
		end (str): End date in YYYY-MM-DD format
		freq (str, optional): Frequency for date range. Defaults to 'YS'.
			YS for start of year
			MS for start of month
			W-MON for start of week
		skip_end (int, optional): Number of nodes to skip from end. Defaults to 0.

	Returns:
		list: List of dates as strings in YYYY-MM-DD format representing roll nodes
	"""
	first_dates = pd.date_range(start, end, freq=freq).astype('str').to_list()
	dates = pd.to_datetime(get_trading_days(start, end))
	roll_nodes = [dates[dates >= date][0].strftime('%Y-%m-%d') for date in first_dates]
	if roll_nodes[0] != start:
		roll_nodes.insert(0, start)
	roll_nodes.append(dates[-1 - skip_end].strftime('%Y-%m-%d'))
	return pd.Series(roll_nodes).drop_duplicates().to_list()


def get_trading_days(start_date: str, end_date: str):
	"""get ashare trading days, better API defined in calendar.py
	Args:
		start_date (str): Start date in YYYY-MM-DD format
		end_date (str): End date in YYYY-MM-DD format

	Returns:
		list: List of trading days between start_date and end_date (inclusive)
	"""
	td_days = pd.Series(trading_days.load(region='cn', product='ashare'))
	dates = sorted(td_days[(td_days >= start_date) & (td_days <= end_date)])
	return dates


def rgrep(restr, values, exclude=False):
	"""match some pattern in a list of strings using re.search
	Args:
		restr: Regular expression pattern to match
		values: List of strings to search through
		exclude (bool, optional): If True, return non-matching strings. Defaults to False.

	Returns:
		list: List of strings that match (or don't match if exclude=True) the pattern
	"""
	if not exclude:
		return [v for v in values if re.search(restr, v)]
	else:
		return [v for v in values if not re.search(restr, v)]


def recursive_make_dirs(base_root: str, dir_names: list):
	"""recursively make directories, better API defined as data.FileOperator.auto_mkdirs
	Args:
		base_root (str): Base directory path to make directories under
		dir_names (list): List of directory names to make recursively. Each element is a list of directory names at that depth level.
	"""

	def make_dir(base_dir, dir_name):
		if not os.path.exists(f'{base_dir}{dir_name}/'):
			os.makedirs(f'{base_dir}{dir_name}/', exist_ok=True)

	depth = len(dir_names)
	if depth == 1:
		for dir in dir_names[0]:
			make_dir(base_root, dir)
	else:
		for dir in dir_names[0]:
			recursive_make_dirs(f'{base_root}{dir}/', dir_names[1:])


def alignSym(rawdf: pl.DataFrame) -> xr.DataArray:
	"""align symbol to form a 3d-xrarray for large dataframe, missing values are filled with NaN
	Args:
		rawdf (pl.DataFrame): Input Polars DataFrame containing SYMBOL, date and feature columns

	Returns:
		xr.DataArray: 3D array with dimensions (DATETIME, SYMBOL, FEATURE) containing aligned data
	"""
	syms = rawdf['SYMBOL'].unique().sort()
	dates = rawdf['date'].unique().sort()
	fnames = rawdf.columns[2:]
	dfdata = pd.DataFrame(
		np.empty(shape=(len(dates) * len(syms), len(fnames)), dtype=np.float32),
		columns=fnames,
	)
	dfdata.loc[:, :] = np.nan

	a = dates.to_numpy().repeat(len(syms)).reshape(-1, 1)
	b = syms.to_numpy()[:, np.newaxis].repeat(len(dates), axis=1).T.reshape(-1, 1)
	mstr = pd.Series((a + b).squeeze())
	mstr_ = (rawdf['date'] + rawdf['SYMBOL']).to_pandas()
	sel = mstr.isin(mstr_)
	dfdata.loc[sel] = rawdf[:, 2:].to_numpy()

	coords = (
		('DATETIME', dates.to_numpy()),
		('SYMBOL', syms.to_numpy()),
		('FEATURE', fnames),
	)
	return xr.DataArray(
		dfdata.values.reshape(len(dates), len(syms), len(fnames)), coords=coords
	)


def dict2xr(data: dict):
	"""align features with date as key in a dict, return xr.DataArray
	Args:
		data (dict): Dictionary with dates as keys and DataFrames as values. Each DataFrame should have
					symbols as index and feature columns.

	Returns:
		xr.DataArray: 3D array with dimensions (DATETIME, SYMBOL, FEATURE) containing aligned data.
					 Missing values are filled with NaN.
	"""
	raw_arr = np.vstack(list(data.values()))
	dates = sorted(data.keys())
	fnames = data[dates[0]].columns.tolist()
	syms = pd.Series(np.concatenate([s.index for s in data.values()]))
	syms = pd.Series(sorted(syms.unique()))
	mstr_ = pd.Series([date + s for date in dates for s in data[date].index])
	mstr = pd.Series([date + s for date in dates for s in syms])
	new_arr = np.empty(shape=(len(dates) * len(syms), len(fnames)), dtype=np.float32)
	new_arr[:, :] = np.nan
	sel = mstr.isin(mstr_)
	new_arr[sel] = raw_arr
	coords = (('DATETIME', dates), ('SYMBOL', syms), ('FEATURE', fnames))
	return xr.DataArray(
		new_arr.reshape(len(dates), len(syms), len(fnames)), coords=coords
	)


def concat_xrs(xrs: list[xr.DataArray]):
	"""concat xrfeatures align FEATURE dimension with high performance using pre-allocated memory, avoid using xr.concat
	Args:
		xrs (list[xr.DataArray]): List of xr.DataArray objects to concatenate

	Returns:
		xr.DataArray: Concatenated xr.DataArray with dimensions (DATETIME, SYMBOL, FEATURE)
	"""
	dates = pd.Series(
		np.unique(np.concatenate([s['DATETIME'].to_numpy() for s in xrs]))
	)
	syms = pd.Series(np.unique(np.concatenate([s['SYMBOL'].to_numpy() for s in xrs])))
	fnames = pd.Series(np.concatenate([s['FEATURE'].to_numpy() for s in xrs]))
	mgd = np.empty(shape=(len(dates) * len(syms), len(fnames)), dtype=np.float32)
	mgd[:, :] = np.nan

	ticks = [0] + list(np.cumsum([s.shape[-1] for s in xrs]))
	mstrx = pd.Series([date + s for date in dates for s in syms])

	for i in tqdm(range(len(xrs)), desc='merging'):
		item = xrs[i]
		mstrx0 = pd.Series(
			[
				date + s
				for date in item['DATETIME'].to_numpy()
				for s in item['SYMBOL'].to_numpy()
			]
		)
		idx = mstrx.isin(mstrx0)
		mgd[idx, ticks[i] : ticks[i + 1]] = item.to_numpy().reshape(-1, item.shape[-1])

	coords = (('DATETIME', dates), ('SYMBOL', syms), ('FEATURE', fnames))
	return xr.DataArray(mgd.reshape(len(dates), len(syms), len(fnames)), coords=coords)


def corr_sift(df_corr: pd.DataFrame, threshold: float = 0.98):
	"""filter out highly correlated features which can be formed by linear combination of other features above some threshold
	Args:
		df_corr (pd.DataFrame): Correlation matrix DataFrame
		threshold (float, optional): Correlation threshold above which features will be removed. Defaults to 0.98.

	Returns:
		pd.DataFrame: Filtered correlation matrix with highly correlated features removed
	"""
	assert len(df_corr.shape) == 2 and df_corr.shape[0] == df_corr.shape[-1], (
		'input not a correlation matrix!'
	)
	row, col = np.diag_indices_from(df_corr)
	df_corr.values[row, col] = np.nan
	fnames = df_corr.index  # [df_corr.index.str.startswith('feature')].tolist()
	df_corr = df_corr.loc[fnames, fnames]
	n = df_corr.shape[0]

	i = 0
	while i < n * (n - 1) / 2:  ## at most n*(n-1)/2 loops can be applied
		tmp = df_corr.abs().max(axis=1)
		max_corr, max_idx = tmp.max(), tmp.idxmax()
		if max_corr >= threshold:
			df_corr = df_corr.drop(columns=[max_idx], index=[max_idx])
		else:
			break
		i += 1

	nan_ratio = df_corr.isna().sum() / df_corr.shape[0]
	selected = nan_ratio[nan_ratio < 0.5].index.tolist()
	return df_corr.loc[selected, selected]


def corr_sift2(fitness: list, df_corr: pd.DataFrame, threshold: float):
	"""start from highest fitness, filter out features with high correlation with already selected features above some threshold
	Args:
		fitness: List of fitness scores for features
		df_corr: Correlation matrix DataFrame, should be a symmetric matrix
		threshold: Correlation threshold for filtering features

	Returns:
		List of indices for selected features after correlation sifting
	"""
	fitness = list(enumerate(fitness))
	fitness.sort(key=lambda x: x[1], reverse=True)

	sifted = [fitness[0][0]]
	for i in range(1, len(fitness)):
		left = [item[0] for item in fitness if item[0] not in sifted]
		a = df_corr.loc[left, sifted].abs().max(axis=1)
		sel = a[a <= threshold].index.tolist()
		fitness = [item for item in fitness if item[0] in sel]
		fitness.sort(key=lambda x: x[1], reverse=True)
		if len(fitness) == 0:
			break
		sifted.append(fitness[0][0])

	return sifted


def zscore(dffeature: pd.DataFrame, axis=0, method: str = 'median_mad'):
	"""standardize features with outliers beyond 3 sigma replaced with NaN
	Args:
		dffeature: Input DataFrame to standardize
		axis: Axis along which to standardize. Can be 0/'row' or 1/'column'
		method: Method for standardization. Either 'mean_std' or 'median_mad'

	Returns:
		DataFrame with standardized values, with outliers beyond 3 sigma replaced with NaN
	"""
	assert axis in {0, 1, 'row', 'column'}, (
		'axis should be one of [0, 1, "row", "column"]'
	)
	dffeature = dffeature.replace([np.inf, -np.inf], np.nan)
	if axis in {0, 'row'}:
		dffeature = dffeature.T

	if method == 'mean_std':
		miu, sigma = dffeature.mean(), dffeature.std()
	elif method == 'median_mad':
		miu = dffeature.median()
		sigma = (dffeature - miu).abs().median()
	dffeature = dffeature.where(dffeature <= miu + 3.0 * sigma, np.nan).where(
		dffeature >= miu - 3.0 * sigma, np.nan
	)
	dffeature = (dffeature - miu) / sigma
	return dffeature.T if axis in {0, 'row'} else dffeature


def scale(
	df: pd.DataFrame,
	axis=0,
	lower: float = -1.0,
	upper: float = 1.0,
):
	"""scale each column(row) to a designated range
	Args:
		df: Input DataFrame to scale
		axis: Axis along which to scale. Can be 0/'row' or 1/'column'
		lower: Lower bound of scaled range
		upper: Upper bound of scaled range

	Returns:
		DataFrame with values scaled to range [lower, upper]
	"""
	assert lower < upper, 'lower should be smaller than upper'
	assert axis in {0, 1, 'row', 'column'}, 'axis should be one of [0,1,row,column]'
	return (df - df.min()) * (upper - lower) / (df.max() - df.min()) + lower


def get_zdting_matrix(start: str, end: str, ref_time: str = '09:40:00'):
	"""get zdt matrix for given date range at a given reference time
	Args:
		start: Start date in YYYY-MM-DD format
		end: End date in YYYY-MM-DD format
		ref_time: Reference time for price limits, default '09:40:00'

	Returns:
		pd.DataFrame: Matrix with dates as index and symbols as columns, where:
			1.0 = up limit
			-1.0 = down limit
			0.0 = active trading
			NaN = not trading
	"""
	zt = pd.read_pickle(
		'/mnt/sda/NAS/ShareFolder/WangXiang/to_stk/cases_deal_price/cn_ashare/1day/complete/up_limit %s.pkl'
		% ref_time
	)
	dt = pd.read_pickle(
		'/mnt/sda/NAS/ShareFolder/WangXiang/to_stk/cases_deal_price/cn_ashare/1day/complete/down_limit %s.pkl'
		% ref_time
	)
	alive = pd.read_pickle(
		'/mnt/sda/NAS/ShareFolder/WangXiang/to_stk/cases_deal_price/cn_ashare/1day/complete/存在.pkl'
	)

	dates = get_trading_days(start, end)
	syms = symbol.load(region='cn', product='ashare')
	zdt = pd.DataFrame(
		np.full((len(dates), len(syms)), fill_value=np.nan), columns=syms, index=dates
	)
	for date in tqdm(dates):
		zdt.loc[date, alive[date].to_list()] = 0.0
		zdt.loc[date, zt[date].to_list()] = 1.0
		zdt.loc[date, dt[date].to_list()] = -1.0
	return zdt


def xr2df(xrdata: xr.DataArray):
	"""convert 3D xarray DataArray to 2D DataFrame with MultiIndex
	Args:
		xrdata: 3D xarray DataArray to convert

	Returns:
		pd.DataFrame: 2D DataFrame with MultiIndex
	"""
	a, b, c = xrdata.shape
	data = xrdata.to_numpy()
	dims = xrdata.dims
	keys1 = xrdata[dims[0]].to_numpy()
	keys2 = xrdata[dims[1]].to_numpy()
	keys3 = xrdata[dims[2]].to_numpy()
	index = pd.MultiIndex.from_product([keys1, keys2], names=[dims[0], dims[1]])
	columns = pd.Index(keys3, name=dims[2])
	return pd.DataFrame(data.reshape(a*b, c), index=index, columns=columns)


def get_fitness(arr: np.ndarray | pd.Series):
	"""calculate fitness of a feature, which is the ratio of the maximum drawup to the maximum drawdown
	Args:
		arr: Input array or Series to calculate fitness

	Returns:
		float: Fitness value
	"""
	if len(arr[~np.isnan(arr)]) < 2:
		return np.nan
	data = np.nancumsum(arr)  ## 单利累计
	inverse = 1 if np.nansum(arr) >= 0 else -1
	data = inverse * data + 1.0
	zhang = data - np.fmin.accumulate(data)  # 波段累计上涨
	die = np.fmax.accumulate(data) - data  # 波段累计回撤
	fitness = np.clip(zhang.max() / max(die.max(), 1.0e-10), 0, 100)
	return inverse * fitness


def get_fitness2(value: np.ndarray | pd.Series):
	"""calculate fitness of a feature, which is the ratio of sum of postive values to the sum of negative values
	Args:
		value: Input array or Series to calculate fitness

	Returns:
		float: Fitness value
	"""
	plus = np.abs(np.nansum(value[value >= 0]))
	minus = np.abs(np.nansum(value[value < 0]))
	sign = np.sign(np.nansum(value))
	return np.clip(sign * max(plus, minus) / max(1.0e-10, min(plus, minus)), -100, 100)


def get_group_layer_fitness(group_file_root: str, label_name: str):
	"""batch get fitness of a group of features
	Args:
		group_file_root: Root directory containing group folders with pickle files
		label_name: Name of the label column to extract from pickle files

	Returns:
		pd.DataFrame: DataFrame containing fitness values for each group and layer ID
	"""
	groups = os.listdir(group_file_root)
	results = []
	for group in tqdm(groups):
		files = [
			s for s in os.listdir(f'{group_file_root}{group}') if s.endswith('pkl')
		]
		dts = [
			pd.read_pickle(f'{group_file_root}{group}/{file}').loc[:, label_name]
			for file in files
		]
		a = pd.concat(dts, axis=1).T.drop(columns=['lin'])
		a.index = [s.split('.')[0] for s in files]
		a = a.sort_index()

		result = {}
		for id in a.columns:
			fit1, fit2 = get_fitness(a.iloc[:, id - 1], False)
			result[str(id)] = [fit1, fit2]
		result = pd.DataFrame(result, index=['fit1', 'fit2']).T
		results.append(result['fit1'])
	results = pd.concat(results, axis=1)
	results.columns = groups
	results.index.name = 'id'
	return results


def get_group_layer_score(
	layer_fitness: pd.DataFrame,
	first_nlayers: int | None = None,
	last_nlayers: int | None = None,
):
	"""evaluate layer performance based on fitness values
	Args:
		layer_fitness: DataFrame containing fitness values for each group and layer ID
		first_nlayers: Number of top layers to consider
		last_nlayers: Number of bottom layers to consider
	"""
	if first_nlayers is not None:
		layer_fitness = layer_fitness.iloc[:first_nlayers, :]
	if last_nlayers is not None:
		layer_fitness = layer_fitness.iloc[-last_nlayers:, :]
	n = layer_fitness.shape[0]
	y = [
		(layer_fitness >= layer_fitness.shift(-i))
		.replace({True: 1, False: -1})[:-i]
		.sum()
		for i in range(1, layer_fitness.shape[0])
	]
	y = pd.concat(y, axis=1).sum(axis=1).sort_values(ascending=False)
	return y * 2 / (n * (n - 1))


## 对增益曲线线性拟合，输出预测值与信噪比
def get_gain_linear_pred(x, y):
	"""x: 2D array; y: 1D array"""
	lr = LinearRegression()
	lr.fit(x, y)
	a, b = lr.coef_.item(), lr.intercept_
	error = lr.predict(x) - y
	return a, 250 * a / error.std(), lr.predict(x)


def tree_h5(file_path: str, n_groups: int = 10):
	"""get hierarchy of a hdf5 file
	Args:
		file_path (str): Path to the HDF5 file
		n_groups (int, optional): Maximum number of groups to traverse at each level. Defaults to 10.
	"""
	rcd, dsets = [], []

	def get_allkeys(obj):
		keys = (obj.name,)
		i = 0
		if isinstance(obj, h5py.Group):
			for key, value in obj.items():
				if list(obj.keys()) not in rcd:
					rcd.append(list(obj.keys()))
				if isinstance(value, h5py.Group):
					i += 1
					if i >= n_groups:
						break
					keys += get_allkeys(value)
				else:
					if key not in dsets:
						dsets.append(key)
					keys += (value.name,)
		return keys

	with h5py.File(file_path, 'r') as h5r:
		allkeys = get_allkeys(h5r)

	for idx, value in enumerate(rcd):
		tmp_dsets = [s for s in value if s in dsets]
		tmp_gps = [s for s in value if s not in dsets]
		print(f'layer{idx}: [{len(tmp_gps)} groups] {tmp_gps[:8]}')
		print(f'layer{idx}: [{len(tmp_dsets)}, datasets] {tmp_dsets[:8]}')


def plot_setting(figsize=(10, 5)):
	"""set default plot parameters
	Args:
		figsize (tuple, optional): Figure size. Defaults to (10, 5).
	"""
	import matplotlib.pyplot as plt

	plt.rcParams['figure.figsize'] = figsize
	plt.rcParams['axes.grid'] = True
	plt.rcParams['legend.loc'] = 'upper left'
	plt.rcParams['hist.bins'] = 100
	plt.rcParams['grid.alpha'] = 0.5
	plt.rcParams['grid.linestyle'] = '--'
	plt.rcParams['lines.markersize'] = 3.0


def highlight_diagonal(df: pd.DataFrame):
	"""highlight diagonal cells
	Args:
		df (pd.DataFrame): DataFrame to highlight

	Returns:
		Styler: Styler object with diagonal cells highlighted
	"""
	return df.style.apply(
		lambda row: [
			'background-color: yellow' if row.name == df.index[i] else ''
			for i in range(len(df.columns))
		],
		axis=1,
	)


def add_zero_clock_tick(df: pl.DataFrame | pd.DataFrame, keep_last_date: bool = True):
	"""align start and end tick to the start and end of the trading day
	Args:
		df (pl.DataFrame | pd.DataFrame): DataFrame to align
		keep_last_date (bool, optional): Whether to keep only the last date. Defaults to True.

	Returns:
		pl.DataFrame | pd.DataFrame: Aligned DataFrame
	"""
	if isinstance(df, pl.DataFrame):
		index = pd.DatetimeIndex(df['datetime'])
	elif isinstance(df, pd.DataFrame):
		index = pd.DatetimeIndex(df.index)

	dates = sorted(index.normalize().unique().astype(str).tolist())
	time_unit = 'ms' if 'ms' in str(index.dtype) else 'ns'
	start_date, end_date = dates[0], dates[-1]
	tmp_start = pd.DataFrame([np.nan] * df.shape[-1], index=df.columns[:]).T
	tmp_start['datetime'] = pd.to_datetime(
		f'{start_date} 00:00:00.000', unit='ns'
	).as_unit(time_unit)
	tmp_end = pd.DataFrame([np.nan] * df.shape[-1], index=df.columns[:]).T
	tmp_end['datetime'] = pd.to_datetime(f'{end_date} 23:59:59.999', unit='ns').as_unit(
		time_unit
	)
	last_date_tick = pd.to_datetime(f'{end_date} 00:00:00.000', unit='ns').as_unit(
		time_unit
	)

	if isinstance(df, pl.DataFrame):
		df = pl.concat([pl.from_pandas(tmp_start), df, pl.from_pandas(tmp_end)])
		if keep_last_date:
			df = df.filter(pl.col('datetime') >= last_date_tick)
	elif isinstance(df, pd.DataFrame):
		tmp_start = tmp_start.set_index('datetime')
		tmp_end = tmp_end.set_index('datetime')
		df = pd.concat([tmp_start, df, tmp_end])
		if keep_last_date:
			df = df.loc[df.index >= last_date_tick]
	return df


def rename_tickdata(tickdata: pd.DataFrame, data_type: str = 'quote'):
	"""rename tick data for simplification and filter out meanless columns
	Args:
		tickdata (pd.DataFrame): Input tick data DataFrame
		data_type (str, optional): Type of tick data - 'quote' or 'trade'. Defaults to 'quote'.

	Returns:
		pd.DataFrame: Renamed and filtered DataFrame with simplified column names
	"""
	td_name_map = {'price': 'px', 'volume': 'qty'}
	qt_name_map = {
		'ask_price': 'apx',
		'ask_amount': 'aqty',
		'bid_price': 'bpx',
		'bid_amount': 'bqty',
	}

	if data_type == 'quote':
		renamed = tickdata.rename(columns=qt_name_map).loc[
			:, ['apx', 'aqty', 'bpx', 'bqty']
		]
		renamed['atv'] = renamed['apx'] * renamed['aqty']
		renamed['btv'] = renamed['bpx'] * renamed['bqty']

	elif data_type == 'trade':
		renamed = tickdata.rename(columns=td_name_map).loc[:, ['side', 'px', 'qty']]
		renamed['side'] = 2 * renamed.side - 3.0
		renamed['tv'] = renamed['px'] * renamed['qty']

	return renamed


def fold_sublists(flat_list: list, n_sublists: int):
	"""fold a flat list into a list of sublists
	Args:
		flat_list (list): A flat list to be folded
		n_sublists (int): Number of sublists to fold into

	Returns:
		list: A list of sublists
	"""
	if n_sublists == 1:
		return [flat_list]
	if n_sublists > len(flat_list):
		n_sublists = len(flat_list)
	return np.array_split(flat_list, n_sublists)


def unfold_sublists(folded_list: list[list]) -> list:
	"""unfold a list of sublists into a single list with unique elements
	Args:
		folded_list (list[list]): A list containing sublists to be unfolded

	Returns:
		list: A flattened list with unique elements from all sublists
	"""
	return list(set.union(*map(set, folded_list)))


def parallel(
	func: callable,
	list_args: list[tuple], 
	njobs: int = 10,
	backend: str = 'multiprocessing',
	progress_bar: bool = False,
	desc: str = '',
):
	"""Execute a function in parallel across multiple arguments.

	Args:
		func (callable): The function to execute in parallel
		list_args (list[tuple]): List of argument tuples to pass to the function
		njobs (int, optional): Number of parallel jobs to run. Defaults to 10.
		backend (str, optional): Parallelization backend - 'multiprocessing', 'threading' or 'loky'. Defaults to 'multiprocessing'.
		progress_bar (bool, optional): Whether to show a progress bar. Defaults to False.
		desc (str, optional): Description for the progress bar. Defaults to ''.

	Returns:
		list: Results from parallel execution of the function across all arguments
	"""
	if progress_bar:
		from tqdm.auto import tqdm
		list_args = tqdm(list_args, desc=desc)

	with Parallel(n_jobs=njobs, backend=backend) as para:
		return para(delayed(func)(*arg) for arg in list_args)


def update_key_config(path: str='/mnt/sda/NAS/ShareFolder/lishuanglin/features/cn_ashare'):
	print('updating FC key config: ', path)
	from shennong.utils import key_group_manager as sn_km
	sn_km.generate_h5_key_group_config(load_root=path)


def is_notebook():
	"""Check if the code is running in a Jupyter notebook.
	Returns:
		bool: True if running in a Jupyter notebook, False otherwise.
	"""
	return 'ipykernel' in sys.modules


