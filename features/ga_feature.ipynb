{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## autoreload"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## fc2_script.py"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile './fc2_script.py'\n", "# coding = utf-8\n", "import os\n", "import xarray as xr\n", "import pandas as pd\n", "import numpy as np\n", "from joblib import Parallel, delayed\n", "\n", "from yrqtlib.utils import tools, data\n", "from yrqtlib.utils.logger import Logger\n", "\n", "bar = data.BarDataManager()\n", "overwrite = tools.is_notebook()\n", "\n", "\n", "def add_oi1_cols(quote: pd.DataFrame):\n", "\t## absolute bid-ask spread change by turnover\n", "\tquote['sprd_dtv'] = (quote['btv'] - quote['atv']).diff()\n", "\n", "\t## relative bid-ask spread pct_change by turnover, cliped to 1.0 times\n", "\tquote['sprd_ptv'] = (quote['btv'] - quote['atv']).pct_change().clip(-1.0e2, 1.0e2)\n", "\n", "\t## real order imbalance by price\n", "\tquote['oip'] = quote['apx'] - quote['bpx']\n", "\n", "\t## real order imbalance by volume\n", "\tquote['oiv_raw'] = quote['aqty'] - quote['bqty']\n", "\n", "\t## absolute real order imbalance by volume\n", "\tquote['oiv_raw_abs'] = (quote['aqty'] - quote['bqty']).abs()\n", "\n", "\t## relative order imbalance by volume\n", "\tquote['oiv_pct'] = ((quote['aqty'] - quote['bqty']) / (quote['aqty'] + quote['bqty'])).clip(-1, 1)\n", "\n", "\t## real order imbalance by turnover\n", "\tquote['oitv_raw'] = quote['oip'] * quote['oiv_raw']\n", "\n", "\t## absolute order imbalance by turnover\n", "\tquote['oitv_raw_abs'] = (quote['oip'] * quote['oiv_raw']).abs()\n", "\n", "\t## change of turnover imblance imply traded or canceled orders\n", "\tquote['intv_net'] = quote['oitv_raw'].diff()\n", "\n", "\t## change of turnover imblance imply traded or canceled orders\n", "\tquote['intv_abs'] = quote['oitv_raw_abs'].diff()\n", "\treturn quote\n", "\n", "\n", "def trade2(freq: str, date: str, sym: str):\n", "\tfrom yrqtlib.utils import add_methods_to_pandas\n", "\n", "\tquote = data.load_1sym_tick('gb_bina_future', 'quote', date, sym)\n", "\tif quote is None:\n", "\t\treturn None\n", "\n", "\tquote = tools.rename_tickdata(quote, type='quote')\n", "\tquote = tools.add_zero_clock_tick(quote).sort_index()\n", "\tquote['qty'] = quote['aqty'] + quote['bqty']\n", "\tquote['tv'] = quote['atv'] + quote['btv']\n", "\tquote['dbqty'] = quote['bqty'].diff(1).replace(0.0, np.nan)\n", "\tquote['dbtv'] = quote['btv'].diff(1).replace(0.0, np.nan)\n", "\tquote['daqty'] = quote['aqty'].diff(1).replace(0.0, np.nan)\n", "\tquote['datv'] = quote['atv'].diff(1).replace(0.0, np.nan)\n", "\n", "\tevery = freq[:-5]\n", "\tfeature = quote.resample(every).agg(\n", "\t\tqt_bvol=('bqty', 'sum'),\n", "\t\tqt_bvol_last=('bqty', 'last'),\n", "\t\tqt_bvol_max=('bqty', 'max'),\n", "\t\tqt_btv_last=('btv', 'last'),\n", "\t\tqt_avol=('aqty', 'sum'),\n", "\t\tqt_avol_last=('aqty', 'last'),\n", "\t\tqt_avol_max=('aqty', 'max'),\n", "\t\tqt_atv_last=('atv', 'last'),\n", "\t\tqt_new_bnum=('dbqty', 'count'),\n", "\t\tqt_new_bvol=('dbqty', 'sum'),\n", "\t\tqt_new_btv=('dbtv', 'sum'),\n", "\t\tqt_new_anum=('daqty', 'count'),\n", "\t\tqt_new_avol=('daqty', 'sum'),\n", "\t\tqt_new_atv=('datv', 'sum'),\n", "\t)\n", "\n", "\tfeature['qt_net_bvol'] = feature['qt_bvol'] - feature['qt_avol']\n", "\tfeature['qt_net_new_bvol'] = feature['qt_new_bvol'] - feature['qt_new_avol']\n", "\tfeature['qt_net_new_btv'] = feature['qt_new_btv'] - feature['qt_new_atv']\n", "\tfeature[np.isinf(feature)] = np.nan\n", "\tfeature = feature.DIY.asinh().clip(-10.0, 10.0)\n", "\treturn feature\n", "\n", "\n", "def trade3(freq: str, date: str, sym: str):\n", "\tfrom yrqtlib.utils import add_methods_to_pandas\n", "\n", "\tquote = data.load_1sym_tick('gb_bina_future', 'quote', date, sym)\n", "\tif quote is None:\n", "\t\treturn None\n", "\tquote = tools.add_zero_clock_tick(quote)\n", "\tquote = tools.rename_tickdata(quote, type='quote')\n", "\tquote = add_oi1_cols(quote).sort_index()\n", "\n", "\tevery = freq[:-5]\n", "\tfeature = quote.resample(every).agg(\n", "\t\tsprd_dtv_last=('sprd_dtv', 'last'),\n", "\t\tsprd_dtv_max=('sprd_dtv', 'max'),\n", "\t\tsprd_dtv_min=('sprd_dtv', 'min'),\n", "\t\tsprd_dtv_mean=('sprd_dtv', 'mean'),\n", "\t\toiv_raw_last=('oiv_raw', 'last'),\n", "\t\toiv_raw_max=('oiv_raw', 'max'),\n", "\t\toiv_raw_min=('oiv_raw', 'min'),\n", "\t\toiv_raw_mean=('oiv_raw', 'mean'),\n", "\t\toitv_raw_last=('oitv_raw', 'last'),\n", "\t\toitv_raw_max=('oitv_raw', 'max'),\n", "\t\toitv_raw_min=('oitv_raw', 'min'),\n", "\t\toitv_raw_mean=('oitv_raw', 'mean'),\n", "\t\tintv_net_sum=('intv_net', 'sum'),\n", "\t)\n", "\n", "\tfeature[np.isinf(feature)] = np.nan\n", "\tskip_cols = [5, 11, 18, 23]\n", "\tasinh_cols = [i for i in range(feature.shape[-1]) if i not in skip_cols]\n", "\tfeature = feature.DIY.asinh(cols=asinh_cols).clip(-10.0, 10.0)\n", "\treturn feature\n", "\n", "\n", "def raw1(freq: str, date: str, sym: str):\n", "\tfrom yrqtlib.utils import add_methods_to_pandas\n", "\n", "\ttrade = data.load_1sym_tick('gb_bina_future', 'trade', date, sym)\n", "\tif trade is None:\n", "\t\treturn None\n", "\ttrade = tools.rename_tickdata(trade, type='trade')\n", "\ttrade = tools.add_zero_clock_tick(trade).sort_index()\n", "\tbtrade = tools.add_zero_clock_tick(trade.loc[trade.side == 1.0])\n", "\tstrade = tools.add_zero_clock_tick(trade.loc[trade.side == -1.0])\n", "\n", "\tevery = freq[:-5]\n", "\ttmp1 = trade.resample(every).agg(\n", "\t\ttd_num=('px', 'count'),\n", "\t\ttd_qty_sum=('qty', 'sum'),\n", "\t\ttd_tv_sum=('tv', 'sum'),\n", "\t)\n", "\ttmp2 = btrade.resample(every).agg(\n", "\t\ttd_bnum=('px', 'count'),\n", "\t\ttd_bqty_sum=('qty', 'sum'),\n", "\t\ttd_btv_max=('tv', 'max'),\n", "\t\ttd_btv_sum=('tv', 'sum'),\n", "\t)\n", "\ttmp3 = strade.resample(every).agg(\n", "\t\ttd_snum=('px', 'count'),\n", "\t\ttd_sqty_sum=('qty', 'sum'),\n", "\t\ttd_stv_sum=('tv', 'sum'),\n", "\t)\n", "\tfeature = pd.concat([tmp1, tmp2, tmp3], axis=1)\n", "\tfeature['td_net_bnum'] = feature['td_bnum'] - feature['td_snum']\n", "\tfeature['td_net_bqty'] = feature['td_bqty_sum'] - feature['td_sqty_sum']\n", "\tfeature['td_net_btv'] = feature['td_btv_sum'] - feature['td_stv_sum']\n", "\tfeature['td_net_bnum_pct'] = ((feature['td_bnum'] - feature['td_snum']) / feature['td_num'].add(1.0)).clip(\n", "\t\t-1.0, 1.0\n", "\t)\n", "\tfeature['td_net_bqty_pct'] = (\n", "\t\t(feature['td_bqty_sum'] - feature['td_sqty_sum']) / feature['td_qty_sum'].add(1.0)\n", "\t).clip(-1.0, 1.0)\n", "\tfeature['td_net_btv_pct'] = ((feature['td_btv_sum'] - feature['td_stv_sum']) / feature['td_tv_sum'].add(1.0)).clip(\n", "\t\t-1.0, 1.0\n", "\t)\n", "\n", "\tfeature = feature.drop(columns=tmp1.columns)\n", "\n", "\tfeature[np.isinf(feature)] = np.nan\n", "\tasinh_cols = feature.columns[:-3]\n", "\tfeature = feature.DIY.asinh(cols=asinh_cols).clip(-10.0, 10.0)\n", "\treturn feature\n", "\n", "\n", "def ga001(freq: str, date: str, sym: str):"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## ga_applier.py"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%writefile '/mnt/sda/home/<USER>/yrqtlib/ga/ga_applier.py'\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "\n", "from tqdm.auto import tqdm\n", "from ..utils import data\n", "from ..utils import tools\n", "from ..utils import add_methods_to_pandas\n", "from ..crypto import often\n", "from . import functions\n", "\n", "def load1day(loadroot: str, date: str, xsrcs: str):\n", "    xs = []\n", "    for xsrc in xsrcs:\n", "        if not os.path.exists(f'{loadroot}{xsrc}/{date}.parquet'): continue\n", "        x = pd.read_parquet(f'{loadroot}{xsrc}/{date}.parquet')\n", "        xs.append(x)\n", "    if len(xs) == 0: return None\n", "\n", "    x = pd.concat(xs, axis=1)\n", "    y = pd.read_parquet(f'{loadroot}y/{date}.parquet')\n", "    return pd.concat([x,y],axis=1).astype(np.float32)\n", "\n", "\n", "def loaddata(loadroot: str, dates: list[str], xsrcs: str, desc: str=''):\n", "    tasks = [(loadroot, date, xsrcs) for date in dates]\n", "    datas = often.parallel(load1day, tasks, 10, 'loky', progress_bar=True, desc=desc)\n", "    datas = {date: data for date, data in zip(dates, datas) if data is not None}\n", "    return pd.concat(datas)\n", "\n", "function_set = list(functions._function_map.keys())\n", "ga_cfg = {\n", "    'generations': 5,\n", "\t'population_size': 1000000,\n", "\t'num_threads': 20,\n", "\t'metric': 'pearson',\n", "\t'init_depth': (1, 3),\n", "\t'function_set': function_set,\n", "\t'random_state': 42,\n", "\t'verbose': 2,\n", "\t'threshold': 0.03,\n", "    'corr_threshold': 0.8,\n", "\t'sift': <PERSON><PERSON><PERSON>,\n", "\t'parsimony_coefficient': 0.000,\n", "\t'const_range': None,\n", "    'p_crossover': 0.9,\n", "    'p_subtree_mutation': 0.01,\n", "\t'p_hoist_mutation': 0.01,\n", "\t'p_point_mutation': 0.01,\n", "\t'p_point_replace': 0.05,\n", "\t'max_samples': 1.0,\n", "\t'class_weight': None,\n", "\t'feature_names': None,\n", "\t'warm_start': <PERSON><PERSON><PERSON>,\n", "\t'low_memory': <PERSON><PERSON><PERSON>,\n", "    'init_method': 'half and half',\n", "\t'progress_bar': True,\n", "\t'cache_root': '/mnt/sda/NAS/ShareFolder/lishuanglin/crypto/ga/fit_cache/',\n", "}\n", "\n", "\n", "if __name__ == '__main__': ## just for demo\n", "    date, sym = '2024-01-01', 'BTCUSDT.BNF'\n", "    dates = pd.date_range('2021-09-02','2023-04-30').astype(str).tolist()\n", "    train_dates = [s for s in dates if s< '2023-01-01']\n", "    valid_dates = [s for s in dates if s>= '2023-01-01']\n", "\n", "    train_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/crypto/ga/train/'\n", "    valid_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/crypto/ga/valid/'\n", "    len(train_dates), len(valid_dates)\n", "    bar = data.BarDataManager()\n", "    label_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/crypto/labels_10syms/'\n", "\n", "\n", "    xsrcs = [f'x{i}' for i in range(1,12)]\n", "    trainset = loaddata(train_root, train_dates[:], xsrcs, desc='loading train')\n", "    validset = loaddata(valid_root, valid_dates[:], xsrcs, desc='loading valid')\n", "\n", "    xtrain, ytrain =  trainset.iloc[:,:-8], trainset.iloc[:,-8:]\n", "    xvalid, yvalid =  validset.iloc[:,:-8], validset.iloc[:,-8:]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ga_base"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## data"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "import numpy as np\n", "\n", "from tqdm.auto import tqdm\n", "from yrqtlib.utils import data\n", "from yrqtlib.utils import tools\n", "from yrqtlib.crypto import often\n", "from yrqtlib.utils import add_methods_to_pandas\n", "from yrqtlib.ga import ga_applier as gapp\n", "\n", "date, sym = '2024-01-01', 'BTCUSDT.BNF'\n", "dates = pd.date_range('2021-09-02','2023-04-30').astype(str).tolist()\n", "train_dates = [s for s in dates if s< '2023-01-01']\n", "valid_dates = [s for s in dates if s>= '2023-01-01']\n", "\n", "train_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/crypto/ga/train/'\n", "valid_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/crypto/ga/valid/'\n", "len(train_dates), len(valid_dates)\n", "bar = data.BarDataManager()\n", "label_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/crypto/labels_10syms/'\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "db41c67e2cd64812b444cd6bd0d63a9d", "version_major": 2, "version_minor": 0}, "text/plain": ["loading train:   0%|          | 0/486 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "82c605a55b664ee89f7b8fe3c40aab85", "version_major": 2, "version_minor": 0}, "text/plain": ["loading valid:   0%|          | 0/120 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["xsrcs = [f'x{i}' for i in range(1,17)]\n", "trainset = gapp.loaddata(train_root, train_dates[:], xsrcs, desc='loading train').iloc[:,:-8]\n", "validset = gapp.loaddata(valid_root, valid_dates[:], xsrcs, desc='loading valid').iloc[:,:-8]\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["((13996800, 309), (3456000, 309))"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["num_label = 8\n", "xtrain, ytrain =  trainset.iloc[:,:-num_label], trainset.iloc[:,-num_label:]\n", "xvalid, yvalid =  validset.iloc[:,:-num_label], validset.iloc[:,-num_label:]\n", "trainset.shape, validset.shape"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [], "source": ["xtrain = xtrain.fillna(0.0)\n", "ytrain = ytrain.fillna(0.0)\n", "xvalid = xvalid.fillna(0.0)\n", "yvalid = yvalid.fillna(0.0)\n"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [], "source": ["xtx = xtrain.T @ xtrain\n", "xty = xtrain.T @ ytrain"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["beta = pd.DataFrame(np.linalg.lstsq(xtx, xty)[0], index=xtrain.columns, columns=ytrain.columns)\n", "beta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ypred = xvalid @ beta\n", "ic1 = pd.concat([ypred, yvalid], axis=1).DIY.corr(k=-8, device='cpu').loc['r2']"]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [], "source": ["error = (xtrain @ beta).sub(ytrain)\n", "sigma = (error.T @ error).div(error.shape[0]-1)\n"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [], "source": ["# ytrain_ = ytrain.sub(ytrain.mean(axis=0), axis=1).div(ytrain.std(axis=0), axis=1)\n", "# sigma = (ytrain_.T @ ytrain_).div(ytrain_.shape[0]-1)\n", "\n", "beta_se = np.linalg.lstsq(xtx, xty @ np.linalg.inv(sigma))[0]\n", "beta_se = pd.DataFrame(beta_se, index=xtrain.columns, columns=ytrain.columns)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xtx.shape, xty.shape, sigma.shape, beta_se.shape"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [], "source": ["ypred_se = xvalid @ beta_se\n", "ic2 = pd.concat([ypred_se, yvalid], axis=1).DIY.corr(k=-8, device='cpu').loc['r2']\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ic1.DIY.clip_columns('mid_px_')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ic2.DIY.clip_columns('mid_px_')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## ga_corr_sift"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import joblib\n", "import numpy as np\n", "import pandas as pd\n", "import yrqtlib.ga.genetic as ga\n", "import yrqtlib.ga.ga_applier as gapp\n", "\n", "from yrqtlib.utils.logger import Logger\n", "from yrqtlib.utils import tools\n", "\n", "cfg = {\n", "    'generations': 3,\n", "\t'population_size': 10000,\n", "\t'num_threads': 50,\n", "\t'threshold': 0.02,\n", "    'corr_threshold': 0.5,\n", "\t'savetag': 'x1_x11',\n", "}\n", "\n", "ga_cfg = gapp.ga_cfg\n", "ga_cfg.update(cfg)\n", "gp = ga.SymbolicTransformer(ga_cfg)\n", "\n", "tmr = tools.Timer()\n", "temp = joblib.load('/mnt/sda/NAS/ShareFolder/lishuanglin/crypto/ga/fit_cache/x1_x11_gen0.ga')\n", "all_programs = tools.unfold_sublists(temp['all_programs'])\n", "tmr.add_tick('loading fitted programs')\n", "\n", "\n", "gp.good_programs = [s for s in all_programs if np.abs(s.raw_fitness_)>=0.03]\n", "good_programs = list(enumerate(gp.good_programs))\n", "good_programs.sort(key=lambda x: abs(x[1].raw_fitness_), reverse=True)\n", "Logger.info(f'num_programs: {len(good_programs)}, {len(all_programs)}')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import joblib\n", "a = joblib.load('/mnt/sda/NAS/ShareFolder/lishuanglin/crypto/ga/fit_cache/x1_x11_gen0.ga')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(a['all_programs'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.linear_model import LinearRegression\n", "\n", "def test(gpa, gpb):\n", "    a1 = gpa.execute(xtrain).fillna(0.0).to_frame('a')\n", "    a2 = gpa.execute(xvalid).fillna(0.0).to_frame('a')\n", "    b1 = gpb.execute(xtrain).fillna(0.0).to_frame('b')\n", "    b2 = gpb.execute(xvalid).fillna(0.0).to_frame('b')\n", "\n", "    lr = LinearRegression()\n", "    lr.fit(a1, b1)\n", "    \n", "    res1 = b1.sub(lr.predict(a1))\n", "    res2 = b2.sub(lr.predict(a2))\n", "\n", "    x1, y1, z1 = a1.iloc[:,0], b1.iloc[:,0], res1.iloc[:,0]\n", "    x2, y2, z2 = a2.iloc[:,0], b2.iloc[:,0], res2.iloc[:,0]\n", "\n", "    label1 = ytrain.iloc[:,0]\n", "    label2 = yvalid.iloc[:,0]\n", "\n", "    ic1 = x1.corr(label1), y1.corr(label1), z1.corr(label1)\n", "    ic2 = x2.corr(label2), y2.corr(label2), z2.corr(label2)\n", "    \n", "    ic = pd.DataFrame([ic1, ic2],columns=['gpa', 'gpb', 'residual'],index=['train', 'valid'])\n", "    corr1 = a1.iloc[:,0].corr(b1.iloc[:,0])\n", "    corr2 = a2.iloc[:,0].corr(b2.iloc[:,0])\n", "    ic['corr'] = [corr1, corr2]\n", "    return ic\n", "\n", "test(good_programs[0][1], good_programs[1][1])\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tasks = [(good_programs[0][1], good_programs[i][1]) for i in range(1,len(good_programs))]\n", "results = often.parallel(test, tasks[:], 20, backend='threading', progress_bar=True, desc='ic-sifting')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["res = pd.concat({i: res for i, res in enumerate(results)},names=['id','dataset'])\n", "a = res.xs('train', level=1)#['residual'].abs().sort_values(ascending=False)#.head(100).index.tolist()\n", "a[['residual', 'corr']].plot.scatter(x='residual', y='corr')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## scripts-ga_fit"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Writing /mnt/sda/home/<USER>/crypto/scripts/ga/ga_fit_x1_x16.py\n"]}], "source": ["%%writefile '/mnt/sda/home/<USER>/crypto/scripts/ga/ga_fit_x1_x16.py'\n", "\n", "import os\n", "os.environ['PYDEVD_DISABLE_FILE_VALIDATION'] = '1'\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from yrqtlib.utils import data\n", "from yrqtlib.utils import tools\n", "from yrqtlib.crypto import often\n", "import yrqtlib.ga.genetic as ga\n", "from yrqtlib.ga import functions\n", "from yrqtlib.ga import ga_applier as gapp\n", "\n", "# from . import functions\n", "# from . import genetic as ga\n", "# from . import ga_applier as gapp\n", "\n", "bar = data.BarDataManager()\n", "date, sym = '2024-01-01', 'BTCUSDT.BNF'\n", "dates = pd.date_range('2021-09-02', '2023-04-30').astype(str).tolist()\n", "train_dates = [s for s in dates if s < '2023-01-01']\n", "valid_dates = [s for s in dates if s >= '2023-01-01']\n", "\n", "train_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/crypto/ga/train/'\n", "valid_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/crypto/ga/valid/'\n", "label_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/crypto/labels_10syms/'\n", "\n", "function_set = list(functions._function_map.keys())\n", "\n", "cfg = {\n", "    'generations': 5,\n", "\t'population_size': 2000000,\n", "\t'num_threads': 30,\n", "\t'threshold': 0.03,\n", "    'sift':True,\n", "    'corr_threshold': 0.5,\n", "\t'savetag': 'x1_x16',\n", "    'progress_bar': <PERSON><PERSON><PERSON>,\n", "    'warm_start': True\n", "}\n", "ga_cfg = gapp.ga_cfg\n", "ga_cfg.update(cfg)\n", "\n", "xsrcs = [f'x{i}' for i in range(1,17)]\n", "trainset = gapp.loaddata(train_root, train_dates[:], xsrcs, desc='loading trainset')\n", "# label: first 8 columns are return related, last 8 columns are implied volatility related\n", "trainset = trainset[~np.isnan(trainset.iloc[:, :-16]).all(axis=1)].fillna(0.0)\n", "xtrain, ytrain = trainset.iloc[:, :-16], trainset.iloc[:, -16:-8]\n", "\n", "\n", "gp = ga.SymbolicTransformer(ga_cfg)\n", "gp.fit(xtrain, ytrain)\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>KEY</th>\n", "      <th>mid_px_ret_30_sec</th>\n", "      <th>mid_px_ret_60_sec</th>\n", "      <th>mid_px_ret_120_sec</th>\n", "      <th>mid_px_ret_300_sec</th>\n", "      <th>mid_px_ret_600_sec</th>\n", "      <th>mid_px_ret_900_sec</th>\n", "      <th>mid_px_ret_1200_sec</th>\n", "      <th>mid_px_ret_1800_sec</th>\n", "      <th>mid_px_iv_30</th>\n", "      <th>mid_px_iv_60</th>\n", "      <th>mid_px_iv_120</th>\n", "      <th>mid_px_iv_300</th>\n", "      <th>mid_px_iv_600</th>\n", "      <th>mid_px_iv_900</th>\n", "      <th>mid_px_iv_1200</th>\n", "      <th>mid_px_iv_1800</th>\n", "    </tr>\n", "    <tr>\n", "      <th>DATETIME</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-09-02 00:00:00</th>\n", "      <td>-103.257751</td>\n", "      <td>613.970089</td>\n", "      <td>407.266617</td>\n", "      <td>-184.191027</td>\n", "      <td>59.279222</td>\n", "      <td>51.399136</td>\n", "      <td>51.653938</td>\n", "      <td>-5.211968</td>\n", "      <td>0.582651</td>\n", "      <td>0.604018</td>\n", "      <td>0.595857</td>\n", "      <td>0.579736</td>\n", "      <td>0.582529</td>\n", "      <td>0.553368</td>\n", "      <td>0.532615</td>\n", "      <td>0.515713</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-09-02 00:00:03</th>\n", "      <td>-32.268047</td>\n", "      <td>699.934673</td>\n", "      <td>522.147131</td>\n", "      <td>-149.542036</td>\n", "      <td>61.077461</td>\n", "      <td>42.898750</td>\n", "      <td>54.282374</td>\n", "      <td>-8.031507</td>\n", "      <td>0.592077</td>\n", "      <td>0.601874</td>\n", "      <td>0.596628</td>\n", "      <td>0.584176</td>\n", "      <td>0.582368</td>\n", "      <td>0.553203</td>\n", "      <td>0.532584</td>\n", "      <td>0.515614</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-09-02 00:00:06</th>\n", "      <td>192.856407</td>\n", "      <td>771.362972</td>\n", "      <td>622.898626</td>\n", "      <td>-135.751362</td>\n", "      <td>56.158934</td>\n", "      <td>46.687374</td>\n", "      <td>55.137634</td>\n", "      <td>-6.345005</td>\n", "      <td>0.588062</td>\n", "      <td>0.602162</td>\n", "      <td>0.596554</td>\n", "      <td>0.583894</td>\n", "      <td>0.582347</td>\n", "      <td>0.552990</td>\n", "      <td>0.532430</td>\n", "      <td>0.515524</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-09-02 00:00:09</th>\n", "      <td>405.762863</td>\n", "      <td>801.375389</td>\n", "      <td>557.046747</td>\n", "      <td>-190.475464</td>\n", "      <td>47.668991</td>\n", "      <td>42.460155</td>\n", "      <td>58.235993</td>\n", "      <td>-6.849389</td>\n", "      <td>0.621726</td>\n", "      <td>0.607021</td>\n", "      <td>0.597893</td>\n", "      <td>0.583789</td>\n", "      <td>0.583149</td>\n", "      <td>0.553550</td>\n", "      <td>0.532276</td>\n", "      <td>0.515692</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-09-02 00:00:12</th>\n", "      <td>232.204628</td>\n", "      <td>674.621487</td>\n", "      <td>591.695738</td>\n", "      <td>-209.980402</td>\n", "      <td>48.239164</td>\n", "      <td>40.191994</td>\n", "      <td>57.026725</td>\n", "      <td>-6.821194</td>\n", "      <td>0.615765</td>\n", "      <td>0.603917</td>\n", "      <td>0.596359</td>\n", "      <td>0.588029</td>\n", "      <td>0.582814</td>\n", "      <td>0.553383</td>\n", "      <td>0.532123</td>\n", "      <td>0.516264</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-09-02 23:59:45</th>\n", "      <td>-418.732738</td>\n", "      <td>-153.382874</td>\n", "      <td>139.786434</td>\n", "      <td>96.728954</td>\n", "      <td>-16.478634</td>\n", "      <td>64.928741</td>\n", "      <td>14.063230</td>\n", "      <td>1.760645</td>\n", "      <td>0.310529</td>\n", "      <td>0.311242</td>\n", "      <td>0.356158</td>\n", "      <td>0.419307</td>\n", "      <td>0.428782</td>\n", "      <td>0.425053</td>\n", "      <td>0.419328</td>\n", "      <td>0.426293</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-09-02 23:59:48</th>\n", "      <td>-387.843132</td>\n", "      <td>-170.770025</td>\n", "      <td>88.063574</td>\n", "      <td>103.658752</td>\n", "      <td>-13.872128</td>\n", "      <td>66.800079</td>\n", "      <td>21.347036</td>\n", "      <td>2.126141</td>\n", "      <td>0.309980</td>\n", "      <td>0.309449</td>\n", "      <td>0.360749</td>\n", "      <td>0.418939</td>\n", "      <td>0.428694</td>\n", "      <td>0.425040</td>\n", "      <td>0.419247</td>\n", "      <td>0.426211</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-09-02 23:59:51</th>\n", "      <td>-455.136108</td>\n", "      <td>-204.322529</td>\n", "      <td>156.296396</td>\n", "      <td>103.608627</td>\n", "      <td>-0.767541</td>\n", "      <td>76.160946</td>\n", "      <td>19.661579</td>\n", "      <td>7.859201</td>\n", "      <td>0.306419</td>\n", "      <td>0.307667</td>\n", "      <td>0.360524</td>\n", "      <td>0.418505</td>\n", "      <td>0.429345</td>\n", "      <td>0.424968</td>\n", "      <td>0.419126</td>\n", "      <td>0.426129</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-09-02 23:59:54</th>\n", "      <td>-584.396267</td>\n", "      <td>-222.774839</td>\n", "      <td>216.947794</td>\n", "      <td>93.257790</td>\n", "      <td>0.989971</td>\n", "      <td>69.707336</td>\n", "      <td>16.791916</td>\n", "      <td>7.243080</td>\n", "      <td>0.302956</td>\n", "      <td>0.305895</td>\n", "      <td>0.383481</td>\n", "      <td>0.418021</td>\n", "      <td>0.429210</td>\n", "      <td>0.424804</td>\n", "      <td>0.419024</td>\n", "      <td>0.426096</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-09-02 23:59:57</th>\n", "      <td>-478.318977</td>\n", "      <td>-169.704866</td>\n", "      <td>267.323542</td>\n", "      <td>99.561024</td>\n", "      <td>19.548798</td>\n", "      <td>77.505970</td>\n", "      <td>19.448547</td>\n", "      <td>9.014168</td>\n", "      <td>0.307054</td>\n", "      <td>0.304511</td>\n", "      <td>0.383416</td>\n", "      <td>0.417545</td>\n", "      <td>0.429104</td>\n", "      <td>0.424742</td>\n", "      <td>0.418906</td>\n", "      <td>0.426264</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>28800 rows × 16 columns</p>\n", "</div>"], "text/plain": ["KEY                  mid_px_ret_30_sec  mid_px_ret_60_sec  mid_px_ret_120_sec  \\\n", "DATETIME                                                                        \n", "2021-09-02 00:00:00        -103.257751         613.970089          407.266617   \n", "2021-09-02 00:00:03         -32.268047         699.934673          522.147131   \n", "2021-09-02 00:00:06         192.856407         771.362972          622.898626   \n", "2021-09-02 00:00:09         405.762863         801.375389          557.046747   \n", "2021-09-02 00:00:12         232.204628         674.621487          591.695738   \n", "...                                ...                ...                 ...   \n", "2021-09-02 23:59:45        -418.732738        -153.382874          139.786434   \n", "2021-09-02 23:59:48        -387.843132        -170.770025           88.063574   \n", "2021-09-02 23:59:51        -455.136108        -204.322529          156.296396   \n", "2021-09-02 23:59:54        -584.396267        -222.774839          216.947794   \n", "2021-09-02 23:59:57        -478.318977        -169.704866          267.323542   \n", "\n", "KEY                  mid_px_ret_300_sec  mid_px_ret_600_sec  \\\n", "DATETIME                                                      \n", "2021-09-02 00:00:00         -184.191027           59.279222   \n", "2021-09-02 00:00:03         -149.542036           61.077461   \n", "2021-09-02 00:00:06         -135.751362           56.158934   \n", "2021-09-02 00:00:09         -190.475464           47.668991   \n", "2021-09-02 00:00:12         -209.980402           48.239164   \n", "...                                 ...                 ...   \n", "2021-09-02 23:59:45           96.728954          -16.478634   \n", "2021-09-02 23:59:48          103.658752          -13.872128   \n", "2021-09-02 23:59:51          103.608627           -0.767541   \n", "2021-09-02 23:59:54           93.257790            0.989971   \n", "2021-09-02 23:59:57           99.561024           19.548798   \n", "\n", "KEY                  mid_px_ret_900_sec  mid_px_ret_1200_sec  \\\n", "DATETIME                                                       \n", "2021-09-02 00:00:00           51.399136            51.653938   \n", "2021-09-02 00:00:03           42.898750            54.282374   \n", "2021-09-02 00:00:06           46.687374            55.137634   \n", "2021-09-02 00:00:09           42.460155            58.235993   \n", "2021-09-02 00:00:12           40.191994            57.026725   \n", "...                                 ...                  ...   \n", "2021-09-02 23:59:45           64.928741            14.063230   \n", "2021-09-02 23:59:48           66.800079            21.347036   \n", "2021-09-02 23:59:51           76.160946            19.661579   \n", "2021-09-02 23:59:54           69.707336            16.791916   \n", "2021-09-02 23:59:57           77.505970            19.448547   \n", "\n", "KEY                  mid_px_ret_1800_sec  mid_px_iv_30  mid_px_iv_60  \\\n", "DATETIME                                                               \n", "2021-09-02 00:00:00            -5.211968      0.582651      0.604018   \n", "2021-09-02 00:00:03            -8.031507      0.592077      0.601874   \n", "2021-09-02 00:00:06            -6.345005      0.588062      0.602162   \n", "2021-09-02 00:00:09            -6.849389      0.621726      0.607021   \n", "2021-09-02 00:00:12            -6.821194      0.615765      0.603917   \n", "...                                  ...           ...           ...   \n", "2021-09-02 23:59:45             1.760645      0.310529      0.311242   \n", "2021-09-02 23:59:48             2.126141      0.309980      0.309449   \n", "2021-09-02 23:59:51             7.859201      0.306419      0.307667   \n", "2021-09-02 23:59:54             7.243080      0.302956      0.305895   \n", "2021-09-02 23:59:57             9.014168      0.307054      0.304511   \n", "\n", "KEY                  mid_px_iv_120  mid_px_iv_300  mid_px_iv_600  \\\n", "DATETIME                                                           \n", "2021-09-02 00:00:00       0.595857       0.579736       0.582529   \n", "2021-09-02 00:00:03       0.596628       0.584176       0.582368   \n", "2021-09-02 00:00:06       0.596554       0.583894       0.582347   \n", "2021-09-02 00:00:09       0.597893       0.583789       0.583149   \n", "2021-09-02 00:00:12       0.596359       0.588029       0.582814   \n", "...                            ...            ...            ...   \n", "2021-09-02 23:59:45       0.356158       0.419307       0.428782   \n", "2021-09-02 23:59:48       0.360749       0.418939       0.428694   \n", "2021-09-02 23:59:51       0.360524       0.418505       0.429345   \n", "2021-09-02 23:59:54       0.383481       0.418021       0.429210   \n", "2021-09-02 23:59:57       0.383416       0.417545       0.429104   \n", "\n", "KEY                  mid_px_iv_900  mid_px_iv_1200  mid_px_iv_1800  \n", "DATETIME                                                            \n", "2021-09-02 00:00:00       0.553368        0.532615        0.515713  \n", "2021-09-02 00:00:03       0.553203        0.532584        0.515614  \n", "2021-09-02 00:00:06       0.552990        0.532430        0.515524  \n", "2021-09-02 00:00:09       0.553550        0.532276        0.515692  \n", "2021-09-02 00:00:12       0.553383        0.532123        0.516264  \n", "...                            ...             ...             ...  \n", "2021-09-02 23:59:45       0.425053        0.419328        0.426293  \n", "2021-09-02 23:59:48       0.425040        0.419247        0.426211  \n", "2021-09-02 23:59:51       0.424968        0.419126        0.426129  \n", "2021-09-02 23:59:54       0.424804        0.419024        0.426096  \n", "2021-09-02 23:59:57       0.424742        0.418906        0.426264  \n", "\n", "[28800 rows x 16 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["np"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["all_programs = np.load('/mnt/sda/NAS/ShareFolder/lishuanglin/crypto/ga/fit_cache/x1_x15.all.npy',allow_pickle=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import joblib\n", "a = joblib.load('/mnt/sda/NAS/ShareFolder/lishuanglin/crypto/ga/fit_cache/x1_x11.ga')['all_programs']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["np.save('/mnt/sda/NAS/ShareFolder/lishuanglin/crypto/ga/fit_cache/x1_x11.all.npy',np.array(a),allow_pickle=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b = np.load('/mnt/sda/NAS/ShareFolder/lishuanglin/crypto/ga/fit_cache/x1_x11.ga.npy',allow_pickle=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%timeit a[0][0] in all_programs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_programs_str = set([p.__str__() for p in a[0]])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t = a[0][0].__str__()\n", "%timeit t in all_programs_str"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["s1 = xtrain.iloc[:,0].fillna(0.0)\n", "s2 = xtrain.iloc[:,1].fillna(0.0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def ma(s1: pd.Series, wd: int = 5):\n", "\tif wd <= 1: return None\n", "\treturn s1.rolling(wd, min_periods=1).mean()\n", "\n", "%timeit ma(s1, wd=5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## check ga-fitted"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import joblib\n", "gp = joblib.load('/mnt/sda/home/<USER>/crypto/features/ga_test2.ga')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# sifted = gp.good_programs\n", "\n", "res = []\n", "for program in tqdm(sifted):\n", "    pred_train = program.execute(xtrain)\n", "    ic_train = program.metric(ytrain, pred_train)\n", "    pred_valid = program.execute(xvalid)\n", "    ic_valid = program.metric(yvalid, pred_valid)\n", "    depth = program.depth_\n", "    res.append([ic_train, ic_valid, depth, program.__str__()])\n", "\n", "ic = pd.DataFrame(res,columns=['ic_train', 'ic_valid', 'depth', 'ga_name'])\n", "ic.sort_values('ic_train')[::-1].head(10).style.format({'ic_train': '{:.4f}', 'ic_valid': '{:.4f}'})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "pattern = r'[X]+\\d+'\n", "\n", "def replace_(match, cols):\n", "\tindex = int(match.group()[1:])\n", "\tif index < len(cols):\n", "\t\treturn cols[index]\n", "\treturn match.group()\n", "\n", "\n", "def replacex(text, cols):\n", "\tpattern = r'[X]\\d+'\n", "\treturn re.sub(pattern, lambda match: replace_(match, cols), text)\n", "\n", "\n", "ic['ganame2'] = ic.ga_name.apply(lambda text: replacex(text, xtrain.columns))\n", "\n", "\n", "def peek_ga_result(ic, offset: int = 0, num_feature: int = 20):\n", "\tic = ic.sort_values('ic_valid')[::-1]\n", "\treturn ic.iloc[offset : offset + num_feature].style.format({'ic_train': '{:.4f}', 'ic_valid': '{:.4f}'})\n", "\n", "\n", "peek_ga_result(ic, 0, 20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## cv-mlp"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import torch\n", "import random\n", "from torch import nn\n", "from yrqtlib.crypto import fitting\n", "pd.set_option('display.float_format', '{:.4f}'.format)\n", "\n", "mlp_cfg = {\n", "    'learning_rate': 1.0e-3,\n", "    'weight_decay': 1.0e-3,\n", "    'num_epochs': 5,\n", "    'loss': 'mse',\n", "    'optimizer': 'adam',\n", "    'device':'cuda:0',\n", "    'activation': 'selu',\n", "    'hidden_size': 50,\n", "    'hidden_layers': 0,\n", "    'num_threads': 20,\n", "    'verbose': 128,\n", "    'dropout':0.1,\n", "    'num_label': ytrain.shape[1],\n", "    'random_state': None,\n", "    'num_rounds': 5\n", "}\n", "\n", "def mlp_check(trainset: pd.DataFrame, validset: pd.DataFrame, mlp_cfg: dict, tag: str=''):\n", "    num_label = mlp_cfg['num_label']\n", "\n", "    trainset = trainset.copy().fillna(0.0).to_numpy()\n", "    validset = validset.copy().fillna(0.0).to_numpy()\n", "\n", "    mlp_cfg['input_size'] = trainset.shape[-1] - num_label\n", "    mlp_cfg['output_size'] = num_label\n", "    mlp_cfg['batch_size'] = 1024 * 10\n", "\n", "    xvalid = validset[:, :-num_label]\n", "    yvalid = validset[:, -num_label:]\n", "\n", "    ics = []\n", "    for i in range(mlp_cfg['num_rounds']):\n", "        mdl = fitting.MLPRegressor(**mlp_cfg)\n", "        mlptrainset = mdl.make_dataset(trainset)\n", "        mdl.fit(mlptrainset, desc=f'round_{i+1}')\n", "        mgd = pd.DataFrame(np.hstack([mdl.predict(xvalid), yvalid]))\n", "        ic = mgd.DIY.corr(k=-num_label, device='cuda:0').loc['r2'] * 100.0\n", "        ic = pd.Series(np.diag(ic),index=ic.columns)\n", "        ics.append(ic)\n", "        torch.cuda.empty_cache()\n", "    \n", "    return pd.concat(ics,axis=1).mean(axis=1).to_frame(tag).T\n", "\n", "class ReduceSifter:\n", "    def __init__(self, **cfg: dict):\n", "        self.__dict__.update(cfg)\n", "        self.mlp_cfg = cfg\n", "\n", "    def fit(self,trainset: pd.DataFrame):\n", "        self.mlp_cfg['input_size'] = trainset.shape[-1] - self.num_label\n", "        self.mlp_cfg['output_size'] = self.num_label\n", "        self.mlp_cfg['batch_size'] = 1024 * 10\n", "        \n", "        self.mdl = fitting.MLPRegressor(**self.mlp_cfg)\n", "        self.trainset = trainset.copy().fillna(0.0)\n", "        mlptrainset = self.mdl.make_dataset(self.trainset.to_numpy())\n", "        self.mdl.fit(mlptrainset)\n", "        # xvalid, yvalid = validset[:,:-self.num_label], validset[:,-self.num_label:]\n", "        self.xtrain = self.trainset.iloc[:,:-self.num_label].to_numpy()\n", "        self.ytrain = self.trainset.iloc[:,-self.num_label:]\n", "        y0 = self.mdl.predict(self.xtrain)[:,0]\n", "        self.ic_all = pd.Series(y0, index=self.ytrain.index).corr(self.ytrain.iloc[:,0])\n", "\n", "        # mgd = pd.DataFrame(np.hstack([self.mdl.predict(xtrain), ytrain]))\n", "        # ic = mgd.DIY.corr(k=-self.num_label, device='cuda:0').loc['r2'] * 100.0\n", "        # self.ic_all = pd.Series(np.diag(ic),index=ic.columns)\n", "        torch.cuda.empty_cache()\n", "\n", "    def reduce_sift(self):\n", "        ics = []\n", "        for i in tqdm(range(self.xtrain.shape[1]), desc='reduce_sifting'):\n", "            tmpx = self.xtrain.copy()\n", "            tmpx[:,i] = 0.0\n", "            ypred_train = self.mdl.predict(tmpx)\n", "            ic = pd.Series(ypred_train[:,0],index=self.ytrain.index).corr(self.ytrain.iloc[:,0])\n", "            ics.append(ic)\n", "        self.ic_reduced = pd.Series(ics, index=range(self.xtrain.shape[1]))\n", "        torch.cuda.empty_cache()\n", "        gain = self.ic_reduced.sub(self.ic_all)\n", "        self.sifted_idx = gain[gain<0].index.tolist()\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "\n", "\n", "class MLPAttentionNet(nn.Module):\n", "\tdef __init__(\n", "\t\tself, input_size, embed_size, hidden_size, output_size, num_heads=8, dropout=0.1\n", "\t):\n", "\t\t\"\"\"\n", "\t\tMLP with attention mechanism (using projection)\n", "\t\tshould be used with torch.nn.MultiheadAttention or torch.nn.MultiHeadAttention\n", "\n", "\t\tArgs:\n", "\t\t\tinput_size (int): num_features.\n", "\t\t\tembed_size (int): embed_size, projected for attention mechanism.\n", "\t\t\thidden_size (int): hidden_size.\n", "\t\t\toutput_size (int): output_size.\n", "\t\t\tnum_heads (int): num_heads.\n", "\t\t\tdropout (float): dropout.\n", "\t\t\"\"\"\n", "\t\tsuper().__init__()\n", "\n", "\t\tself.input_size = input_size\n", "\t\tself.embed_size = embed_size\n", "\t\tself.num_heads = num_heads\n", "\n", "\t\tself.input_projection = nn.Linear(input_size, embed_size)\n", "\n", "\t\t# 检查是否存在 MultiheadAttention (lowercase h)\n", "\t\tif not hasattr(nn, 'MultiheadAttention'):\n", "\t\t\traise ImportError(\n", "\t\t\t\t\"nn.MultiheadAttention (lowercase 'h') not found. Environment issue persists.\"\n", "\t\t\t)\n", "\t\tMHA = (\n", "\t\t\tnn.MultiHeadAttention\n", "\t\t\tif hasattr(nn, 'MultiHeadAttention')\n", "\t\t\telse nn.MultiheadAttention\n", "\t\t)\n", "\t\tself.attention = MHA(\n", "\t\t\tembed_dim=embed_size, num_heads=num_heads, dropout=dropout, batch_first=True\n", "\t\t)\n", "\n", "\t\tself.layer_norm1 = nn.LayerNorm(embed_size)\n", "\t\tself.dropout1 = nn.Dropout(dropout)\n", "\n", "\t\tself.fc = nn.Sequential(\n", "\t\t\tnn.Linear(embed_size, hidden_size),\n", "\t\t\tnn.SELU(),\n", "\t\t\tnn.Dropout(dropout),\n", "\t\t\tnn.Linear(hidden_size, output_size),\n", "\t\t)\n", "\n", "\tdef forward(self, x):\n", "\t\t\"\"\"\n", "\t\tArgs:\n", "\t\t\tx (torch.Tensor): input tensor, shape (batch_size, input_size).\n", "\n", "\t\tReturns:\n", "\t\t\ttorch.Tensor: output tensor, shape (batch_size, output_size).\n", "\t\t\"\"\"\n", "\n", "\t\tx_projected = self.input_projection(x)\n", "\t\tx_reshaped = x_projected.unsqueeze(1) # (batch_size, 1, embed_size) for multi-head attention\n", "\n", "\t\tattn_output, attn_weights = self.attention(\n", "\t\t\tquery=x_reshaped, key=x_reshaped, value=x_reshaped\n", "\t\t)\n", "\n", "\t\tx_attn = x_projected + self.dropout1(attn_output.squeeze(1))\n", "\t\tx_norm = self.layer_norm1(x_attn)\n", "\n", "\t\treturn self.fc(x_norm)\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["from yrqtlib.crypto.fitting import *\n", "from torch.utils.data import Dataset\n", "\n", "\n", "\n", "class MLPRegressor:\n", "\tdef __init__(self, **cfg: dict):\n", "\t\tself.__dict__.update(cfg)\n", "\t\tinput_size = cfg['input_size']\n", "\t\toutput_size = cfg['output_size']\n", "\t\thidden_size = cfg['hidden_size']\n", "\t\thidden_layers = cfg['hidden_layers']\n", "\t\tdropout = cfg['dropout']\n", "\t\tactivation = cfg['activation']\n", "\t\ttorch.set_num_threads(self.num_threads)\n", "\t\tself.model_name = 'MLP'\n", "\t\trandom_state = cfg.get('random_state', None)\n", "\t\tif random_state is not None:\n", "\t\t\tself.set_random_state(cfg['random_state'])\n", "\n", "\t\t# self.model = MlpNet(input_size, output_size, hidden_size, hidden_layers, dropout, activation)\n", "\t\tself.model = MLPAttentionNet(input_size, 32, hidden_size, output_size, 4, dropout)\n", "\t\t# self.model = nn.DataParallel(self.model) ## multi-gpu support with data parallelism\n", "\n", "\t\toptim_map = {'adam': torch.optim.Adam, 'sgd': torch.optim.SGD}\n", "\t\tparams, lr = self.model.parameters(), self.learning_rate\n", "\t\tself.optimizer = optim_map[self.optimizer](params, lr=lr)\n", "\n", "\t\tloss_map = {'mse': nn.<PERSON><PERSON><PERSON>, 'mae': nn.<PERSON>1<PERSON><PERSON>, 'huber': nn.<PERSON><PERSON><PERSON><PERSON>}\n", "\t\tif self.loss in loss_map.keys():\n", "\t\t\tself.calc_loss = loss_map[self.loss]()\n", "\t\telse:\n", "\t\t\tself.calc_loss = Loss(loss_type=self.loss)\n", "\n", "\tdef set_random_state(self, random_state: int):\n", "\t\ttorch.manual_seed(random_state)\n", "\t\tnp.random.seed(random_state)\n", "\t\tif torch.cuda.is_available():\n", "\t\t\ttorch.cuda.manual_seed(random_state)\n", "\t\t\ttorch.cuda.manual_seed_all(random_state)\n", "\t\t\ttorch.backends.cudnn.deterministic = True\n", "\t\t\ttorch.backends.cudnn.benchmark = False\n", "\n", "\tdef shuffle_tensor(self, tensor: torch.Tensor):\n", "\t\tidxs = torch.randperm(tensor.shape[0])\n", "\t\treturn tensor[idxs]\n", "\n", "\tdef make_dataset(self, dataset: np.ndarray):\n", "\t\tdataset = torch.from_numpy(dataset).float()\n", "\t\tdataset = self.shuffle_tensor(dataset)\n", "\t\treturn BatchDataset(dataset, self.batch_size)\n", "\n", "\tdef fit_1epoch(self, epoch: int, trainset: BatchDataset, desc: str=''):\n", "\t\tself.calc_loss = self.calc_loss.to(self.device)\n", "\t\tself.model = self.model.to(self.device)\n", "\n", "\t\tNUM_LABEL = self.output_size\n", "\t\ttrain_loader = DataLoader(trainset, batch_size=1, pin_memory=True)\n", "\n", "\t\tself.model.train()  ## set to train mode\n", "\t\tlosses = []\n", "\t\tfor i, value in enumerate(train_loader):\n", "\t\t\tfeature = value[0, :, :-NUM_LABEL].to(self.device, non_blocking=True)\n", "\t\t\tlabel = value[0, :, -NUM_LABEL:].to(self.device, non_blocking=True)\n", "\t\t\tif len(feature) <= 5:\n", "\t\t\t\tcontinue\n", "\t\t\tout = self.model(feature)  ## forward process\n", "\t\t\tloss = self.calc_loss(out, label)\n", "\t\t\tlosses.append(loss.item())\n", "\n", "\t\t\tloss.backward()\n", "\t\t\tself.optimizer.step()\n", "\t\t\tself.optimizer.zero_grad()\n", "\t\t\tif self.verbose > 0:\n", "\t\t\t\tif (i + 1) % self.verbose == 0:\n", "\t\t\t\t\tprint(\n", "\t\t\t\t\t\tf'[({self.model_name}) epoch {epoch} {desc}] progress: {i + 1}/{len(train_loader)}, train_loss: {np.mean(losses[-10:]):.4f}',\n", "\t\t\t\t\t\tend='\\r',\n", "\t\t\t\t\t)\n", "\n", "\t\tgc.collect()\n", "\t\ttorch.cuda.empty_cache()\n", "\n", "\tdef fit(self, trainset: torch.Tensor, desc: str=''):\n", "\t\tfor epoch in range(self.num_epochs):\n", "\t\t\tself.fit_1epoch(epoch, trainset, desc)\n", "\n", "\tdef predict(self, x: np.n<PERSON>ray):\n", "\t\tx = torch.from_numpy(x).to(self.device, non_blocking=True)\n", "\t\tself.model.eval()  ## set to eval mode\n", "\t\twith torch.inference_mode():\n", "\t\t\treturn self.model(x).cpu().numpy()\n", "\n", "\tdef xrpredict(self, xrf: xr.<PERSON>):\n", "\t\tsyms = xrf['SYMBOL'].to_numpy()\n", "\t\tdatetimes = xrf['DATETIME'].to_numpy()\n", "\t\tkeys = [f'pred_{i}' for i in range(self.output_size)]\n", "\t\tcoords = (('SYMBOL', syms), ('DATETIME', datetimes), ('KEY', keys))\n", "\n", "\t\tpreds = []\n", "\t\tfor sym in syms:\n", "\t\t\tf = xrf.sel({'SYMBOL': sym}).to_numpy()\n", "\t\t\tf[np.isnan(f)] = 0.0\n", "\t\t\typred = self.predict(f)\n", "\t\t\tpreds.append(ypred)\n", "\t\tpreds = xr.<PERSON>y(np.stack(preds), coords=coords)\n", "\t\treturn preds.transpose('<PERSON><PERSON><PERSON><PERSON><PERSON>', 'KEY', 'SYMB<PERSON>')\n", "\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[(MLP) epoch 4 MLPA-round_1] progress: 1280/1366, train_loss: 114869.2695\r"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>8</th>\n", "      <th>9</th>\n", "      <th>10</th>\n", "      <th>11</th>\n", "      <th>12</th>\n", "      <th>13</th>\n", "      <th>14</th>\n", "      <th>15</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>MLPA-base</th>\n", "      <td>8.4081</td>\n", "      <td>6.4362</td>\n", "      <td>5.0063</td>\n", "      <td>3.5970</td>\n", "      <td>1.8507</td>\n", "      <td>1.5314</td>\n", "      <td>1.1593</td>\n", "      <td>0.8819</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              8      9      10     11     12     13     14     15\n", "MLPA-base 8.4081 6.4362 5.0063 3.5970 1.8507 1.5314 1.1593 0.8819"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["mlp_cfg = {\n", "\t'learning_rate': 1.0e-3,\n", "\t'weight_decay': 1.0e-3,\n", "\t'num_epochs': 5,\n", "\t'loss': 'mse',\n", "\t'optimizer': 'adam',\n", "\t'device': 'cuda:0',\n", "\t'activation': 'selu',\n", "\t'hidden_size': 50,\n", "\t'hidden_layers': 0,\n", "\t'num_threads': 20,\n", "\t'verbose': 128,\n", "\t'dropout': 0.1,\n", "\t'num_label': ytrain.shape[1],\n", "\t'random_state': None,\n", "\t'num_rounds': 1,\n", "}\n", "\n", "\n", "def mlp_check2(\n", "\ttrainset: pd.DataFrame, validset: pd.DataFrame, mlp_cfg: dict, tag: str = ''\n", "):\n", "\tnum_label = mlp_cfg['num_label']\n", "\n", "\ttrainset = trainset.copy().fillna(0.0).to_numpy()\n", "\tvalidset = validset.copy().fillna(0.0).to_numpy()\n", "\n", "\tmlp_cfg['input_size'] = trainset.shape[-1] - num_label\n", "\tmlp_cfg['output_size'] = num_label\n", "\tmlp_cfg['batch_size'] = 1024 * 10\n", "\n", "\txvalid = validset[:, :-num_label]\n", "\tyvalid = validset[:, -num_label:]\n", "\n", "\tics = []\n", "\tfor i in range(mlp_cfg['num_rounds']):\n", "\t\tmdl = MLPRegressor(**mlp_cfg)\n", "\t\tmlptrainset = mdl.make_dataset(trainset)\n", "\t\tmdl.fit(mlptrainset, desc=f'MLPA-round_{i + 1}')\n", "\t\tmgd = pd.DataFrame(np.hstack([mdl.predict(xvalid), yvalid]))\n", "\t\tic = mgd.DIY.corr(k=-num_label, device='cuda:0').loc['r2'] * 100.0\n", "\t\tic = pd.Series(np.diag(ic), index=ic.columns)\n", "\t\tics.append(ic)\n", "\t\ttorch.cuda.empty_cache()\n", "\n", "\treturn pd.concat(ics, axis=1).mean(axis=1).to_frame(tag).T\n", "\n", "\n", "ic2 = mlp_check2(trainset, validset, mlp_cfg, 'MLPA-base')\n", "ic2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### fbase"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from yrqtlib.utils import data\n", "bar = data.BarDataManager()\n", "\n", "base_root = '/mnt/sda/NAS/ShareFolder/lishuanglin/crypto/features/rawX/'\n", "\n", "def load_base(base_root, dates, desc=''):\n", "    results = {}\n", "    for date in tqdm(dates, desc=desc):\n", "        xrf = bar.load(base_root, '3second', 'base', date, ['BTCUSDT.BNF'])\n", "        df = xrf.sel(SYMBOL='BTCUSDT.BNF').to_pandas()\n", "        results[date] = df.astype(np.float32)\n", "    \n", "    return pd.concat(results, axis=0)\n", "\n", "xtrain_base = load_base(base_root, train_dates, 'train')\n", "xvalid_base = load_base(base_root, valid_dates, 'valid')"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["trainset_base = pd.concat([xtrain_base, ytrain], axis=1)\n", "validset_base = pd.concat([xvalid_base, yvalid], axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ic = trainset_base.DIY.corr(k=-8).loc['r2']\n", "# ic.DIY.heatmap()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# trainset_base = trainset_base[~np.isnan(trainset_base.iloc[:,:-8]).all(axis=1)].fillna(0.0)\n", "# validset_base = validset_base[~np.isnan(validset_base.iloc[:,:-8]).all(axis=1)].fillna(0.0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ic0 = mlp_check(trainset_base, validset_base, mlp_cfg, 'shipan_base')\n", "ic0"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# rs = ReduceSifter(**mlp_cfg)\n", "# rs.fit(trainset_base)\n", "# rs.reduce_sift()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# x0 = trainset_base.iloc[:, rs.sifted_idx]\n", "# x1 = validset_base.iloc[:, rs.sifted_idx]\n", "\n", "# xy0 = pd.concat([x0, trainset_base.iloc[:,-8:]], axis=1)\n", "# xy1 = pd.concat([x1, validset_base.iloc[:,-8:]], axis=1)\n", "\n", "# mlp_check(xy0, xy1, mlp_cfg, 'shipan_base')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### ga-base"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[(MLP) epoch 4 round_5] progress: 1280/1366, train_loss: 115921.1281\r"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>8</th>\n", "      <th>9</th>\n", "      <th>10</th>\n", "      <th>11</th>\n", "      <th>12</th>\n", "      <th>13</th>\n", "      <th>14</th>\n", "      <th>15</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>ga-base</th>\n", "      <td>8.5117</td>\n", "      <td>6.5196</td>\n", "      <td>5.0045</td>\n", "      <td>3.3914</td>\n", "      <td>1.8409</td>\n", "      <td>1.5640</td>\n", "      <td>1.1917</td>\n", "      <td>1.1925</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            8      9      10     11     12     13     14     15\n", "ga-base 8.5117 6.5196 5.0045 3.3914 1.8409 1.5640 1.1917 1.1925"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["import torch\n", "from torch import nn\n", "from yrqtlib.crypto import fitting\n", "\n", "ic1 = mlp_check(trainset, validset, mlp_cfg, 'ga-base')\n", "ic1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_time, end_time = '08:00:00', '20:00:00'\n", "trainset1 = trainset.droplevel(0).between_time(start_time,end_time) ## asia investors active\n", "trainset2 = trainset.loc[~(trainset.droplevel(0).index.isin(trainset1.index))] ## america investors active\n", "\n", "validset1 = validset.droplevel(0).between_time(start_time,end_time)\n", "validset2 = validset.loc[~(validset.droplevel(0).index.isin(validset1.index))]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from yrqtlib.crypto import fitting\n", "\n", "ic1 = mlp_check(trainset1, validset1, mlp_cfg, 'ga-base')\n", "ic1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from yrqtlib.crypto import fitting\n", "\n", "ic1 = mlp_check(trainset2, validset1, mlp_cfg, 'ga-base')\n", "ic1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rs = ReduceSifter(**mlp_cfg)\n", "rs.fit(trainset)\n", "rs.reduce_sift()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(rs.sifted_idx)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x0 = trainset.iloc[:, rs.sifted_idx]\n", "x1 = validset.iloc[:, rs.sifted_idx]\n", "\n", "xy0 = pd.concat([x0, trainset.iloc[:,-8:]], axis=1)\n", "xy1 = pd.concat([x1, validset.iloc[:,-8:]], axis=1)\n", "\n", "mlp_cfg['num_epochs'] = 1\n", "\n", "mlp_check(xy0, xy1, mlp_cfg, 'ga_base-sifted')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["corr = xtrain.DIY.corr().loc['r2']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["corr = corr.DIY.mask_diag()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sifted = tools.corr_sift(corr, threshold=0.8).index.tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(sifted)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x0 = trainset.loc[:, sifted]\n", "x1 = validset.loc[:, sifted]\n", "\n", "xy0 = pd.concat([x0, trainset.iloc[:,-8:]], axis=1)\n", "xy1 = pd.concat([x1, validset.iloc[:,-8:]], axis=1)\n", "\n", "mlp_cfg['num_epochs'] = 5\n", "\n", "mlp_check(xy0, xy1, mlp_cfg, 'ga_base-sifted2')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### ga-fit"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def execute(programs: list, x: pd.DataFrame):\n", "\tdef pexec(idx, x, gaxs):\n", "\t\tgaxs[:, idx] = programs[idx].execute(x).values\n", "\n", "\tgaxs = np.empty((x.shape[0], len(programs)), dtype=np.float32)\n", "\ttasks = [(i, x, gaxs) for i in range(len(programs))]\n", "\t_ = often.parallel(pexec, tasks, 20, 'threading', False)\n", "\n", "\tcols = [f'gax_{i}' for i in range(gaxs.shape[1])]\n", "\treturn pd.DataFrame(gaxs, index=x.index, columns=cols)\n", "\n", "\n", "def get_ga_trainset(sifted: list, merge_base=True, shipan_base=False):\n", "\tga_xtrain = execute(sifted, xtrain)\n", "\tga_xvalid = execute(sifted, xvalid)\n", "\tif (ga_xtrain is None) | (ga_xvalid is None):\n", "\t\treturn None, None\n", "\n", "\tif merge_base:\n", "\t\tif not shipan_base:\n", "\t\t\tga_xtrain = pd.concat([xtrain, ga_xtrain], axis=1)\n", "\t\t\tga_xvalid = pd.concat([xvalid, ga_xvalid], axis=1)\n", "\t\telse:\n", "\t\t\tga_xtrain = pd.concat([xtrain_base, ga_xtrain], axis=1)\n", "\t\t\tga_xvalid = pd.concat([xvalid_base, ga_xvalid], axis=1)\n", "\n", "\tga_trainset = pd.concat([ga_xtrain, ytrain], axis=1)\n", "\tga_validset = pd.concat([ga_xvalid, yvalid], axis=1)\n", "\treturn ga_trainset, ga_validset\n", "\n", "\n", "def check_1block(idx: int, num_step: int, tmp_programs: list, merge_base=True, shipan_base=False):\n", "\tsifted = [tmp_programs[i][1] for i in range(idx, min(idx+num_step, len(tmp_programs)))]\n", "\tga_trainset, ga_validset = get_ga_trainset(sifted, merge_base=merge_base, shipan_base=shipan_base)\n", "\tif (ga_trainset is None) | (ga_validset is None):\n", "\t\treturn None\n", "\t# Logger.info(f'{ga_trainset.shape}, {ga_trainset.shape}', end='\\n')\n", "\tmlp_cfg['device'] = f'cuda:{(idx//num_step)%2}'\n", "\tic = mlp_check(ga_trainset, ga_validset, mlp_cfg, f'{idx}')\n", "\tprint(idx, np.round(ic.values,3)[0], end='\\n') ## output 3 digit\n", "\treturn ic\n", "\n", "\t# rs = ReduceSifter(**mlp_cfg)\n", "\t# rs.fit(ga_trainset)\n", "\t# rs.reduce_sift()\n", "\t# return rs.sifted_idx\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mlp_cfg"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ga_trainset, ga_validset = get_ga_trainset(sifted, merge_base=False, shipan_base=False)\n", "mlp_check(ga_trainset, ga_validset, mlp_cfg, 'corr_0.5')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["num_step = 100\n", "tmp_programs = good_programs\n", "tasks = [(i, num_step, tmp_programs, True, False) for i in range(0, len(tmp_programs), num_step)]\n", "ics = often.parallel(check_1block, tasks[:], 6, 'loky', True, 'top2bot')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gain = pd.concat(ics).sub(ic1.values,axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sifted_idx = gain.loc[gain[10]>=0.01].sort_index(key=np.int64).index.tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sifted_programs = [tmp_programs[int(i):int(i)+num_step] for i in sifted_idx]\n", "sifted_programs = tools.unfold_sublists(sifted_programs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(sifted_programs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["num_step = 20\n", "tmp_programs = sifted_programs\n", "tasks = [(i, num_step, tmp_programs, True, False) for i in range(0, len(tmp_programs), num_step)]\n", "ics2 = often.parallel(check_1block, tasks[:], 6, 'loky', True, 'top2bot')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gain = pd.concat(ics2).sub(ic1.values,axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gain.sort_values(10)[::-1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sifted_idx = gain.loc[gain[10]>=0.01].sort_index(key=np.int64).index.tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(sifted_idx)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sifted_programs2 = [sifted_programs[int(i):int(i)+num_step] for i in sifted_idx]\n", "sifted_programs2 = tools.unfold_sublists(sifted_programs2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sifted_programs2_ = [s[1] for s in sifted_programs2]\n", "sifted_programs2_"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ga_trainset, ga_validset = get_ga_trainset(sifted_programs2_, merge_base=True, shipan_base=False)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mlp_cfg"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mlp_cfg['num_epochs'] = 1\n", "mlp_cfg['hidden_size'] = 50\n", "# ic2 = mlp_check(ga_trainset, ga_validset, mlp_cfg, 'ic2-ga_gabase')\n", "# ic2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["num_step = 30\n", "tmp_programs = sifted_programs2\n", "tasks = [(i, num_step, tmp_programs, True, False) for i in range(0, len(tmp_programs), num_step)]\n", "ics3 = often.parallel(check_1block, tasks[:], 6, 'loky', True, 'top2bot')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gain = pd.concat(ics3).sub(ic1.values,axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gain.sort_values(10)[::-1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sifted_idx = gain.loc[gain[10]>=0.01].sort_index(key=np.int64).index.tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sifted_idx"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(sifted_idx)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sifted_programs3 = [sifted_programs[int(i):int(i)+num_step] for i in sifted_idx]\n", "sifted_programs3 = tools.unfold_sublists(sifted_programs3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sifted_programs3_ = [s[1] for s in sifted_programs3]\n", "sifted_programs3_"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ga_trainset, ga_validset = get_ga_trainset(sifted_programs3_, merge_base=True, shipan_base=False)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mlp_cfg['num_epochs'] = 5\n", "mlp_cfg['hidden_size'] = 50\n", "mlp_check(ga_trainset, ga_validset, mlp_cfg, 'ic2-ga_gabase')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# rs = ReduceSifter(**mlp_cfg)\n", "# rs.fit(ga_trainset)\n", "# rs.reduce_sift()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# x0 = ga_trainset.iloc[:, rs.sifted_idx]\n", "# x1 = ga_validset.iloc[:, rs.sifted_idx]\n", "\n", "# xy0 = pd.concat([x0, ga_trainset.iloc[:,-8:]], axis=1)\n", "# xy1 = pd.concat([x1, ga_validset.iloc[:,-8:]], axis=1)\n", "\n", "# mlp_check(xy0, xy1, mlp_cfg, 'ic2-ga_base_sifted')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ic1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "torch.cuda.empty_cache()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gain = pd.concat(ics_top).sub(ic1.values, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gain.sort_values(10)[::-1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.concat(ics_top).sort_values(8)[::-1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gain.mean(axis=1).sort_values()[::-1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["good_programs[128000][1].__str__()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xtrain.iloc[:,[8]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "tmp_programs = good_programs[::-1]\n", "tasks = [(i, tmp_programs, trainset, validset, True) for i in range(0, 3000, 20)]\n", "ics_bot = often.parallel(check_1block, tasks, njobs=1, backend='loky', progress_bar=True, desc='bot')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tmp_programs[-1][1].__str__(), tmp_programs[-1][1].fitness_"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x, y = xtrain.copy(), ytrain.copy()\n", "\n", "x = x.sub(x.mean()).div(x.std())\n", "y = y.sub(y.mean()).div(y.std())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xtx = x.T @ x\n", "xty = x.T @ y\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "y_demean = y.sub(y.mean()).div(y.std())\n", "\n", "sigma = (y_demean.T @ y_demean) / (len(y) - 1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.DataFrame(np.linalg.solve(xtx, xty))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["(pd.DataFrame(np.linalg.solve(xtx, xty) @ np.linalg.pinv(sigma)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "torch.cuda.empty_cache()"]}], "metadata": {"kernelspec": {"display_name": "uvbase", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}