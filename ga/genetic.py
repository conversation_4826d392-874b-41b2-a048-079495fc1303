"""Genetic Programming in Python, with a scikit-learn inspired API

The :mod:`gplearn.genetic` module implements Genetic Programming. These
are supervised learning methods based on applying evolutionary operations on
computer programs.
"""

# Author: <PERSON> <trevorstephens.com>
#
# License: BSD 3 clause

import os
import torch
import joblib
import warnings
import itertools
import numpy as np
import pandas as pd

from time import time
from tqdm.auto import tqdm
from abc import ABCMeta, abstractmethod
from sklearn.base import BaseEstimator
from sklearn.base import TransformerMixin
from sklearn.exceptions import NotFittedError

from ..utils import tools
from ..utils.algo import Algo
from ..crypto import often
from ..utils.logger import Logger

from ._program import _Program
from .fitness import _fitness_map, _Fitness
from .functions import _function_map, _Function
from .utils import _partition_estimators, check_random_state

__all__ = ['BaseSymbolic', 'SymbolicTransformer']
MAX_INT = np.iinfo(np.int32).max


def _parallel_evolve(
	n_programs,
	parents,
	X,
	y,
	sample_weight,
	seeds,
	params,
	gen,
	num_threads,
	all_programs,
	good_programs,
):
	n_samples, n_features = X.shape
	function_set = params['function_set']
	arities = params['arities']
	init_depth = params['init_depth']
	init_method = params['init_method']
	const_range = params['const_range']
	metric = params['_metric']
	transformer = params['_transformer']
	parsimony_coefficient = params['parsimony_coefficient']
	method_probs = params['method_probs']
	p_point_replace = params['p_point_replace']
	max_samples = params['max_samples']
	feature_names = params['feature_names']

	max_samples = int(max_samples * n_samples)
	num_cuda = torch.cuda.device_count()

	if parents is not None:
		# best_parents = [p for p in parents if np.abs(p.fitness_) >= params['threshold']]
		best_parents = good_programs
		# print('num_good_parents', len(best_parents))

	def _tournament(
		random_state,
	):  # get best parent by fitness in n randomly selected parents
		parent_index = random_state.randint(0, len(best_parents), 1)[
			0
		]  # random n numbers
		return best_parents[parent_index], parent_index

	def check_program(tmp_program, random_state):
		if isinstance(tmp_program, _Program):
			x = tmp_program.program
		else:
			x = tmp_program
		terminals = [0]
		fs = []
		fnames = []
		final = []
		wds, wfs = 0, []
		for i, node in enumerate(x):
			final.append(node)
			if isinstance(node, _Function):
				terminals.append(node.arity)
				fs.append(node)
				if node.wd_range is not None:
					wds += node.arity
					wfs.append(node)
			elif isinstance(node, int):
				if len(fs) == 0:
					break
				terminals[-1] -= 1
				if fs[-1].wd_range is not None:
					wds -= 1
				while terminals[-1] == 0:
					terminals.pop()
					terminals[-1] -= 1
					# if len(wfs)>0: print('ssss', fs[-1] is wfs[-1])
					# if (len(wfs)>0) and (fs[-1].wd_range is not None): ## calculated one function
					#     wds -= 1
					# wds
					# wfs.pop()
			# print(i,wds,terminals,
			#       node.name if isinstance(node, _Function) else node,
			#       [s.name for s in wfs],
			#       [s.name for s in fs],
			#       [s.name if isinstance(s, _Function) else s for s in final],
			#       )
			if (wds % 2 == 1 and isinstance(node, _Function)) or (
				wds % 2 == 0 and len(terminals) <= len(fs) and len(wfs) > 0
			):
				# print('##############')
				final[-1] = random_state.randint(
					wfs[-1].wd_range[0], wfs[-1].wd_range[1] + 1
				)
				# print('final', [s.name if isinstance(s, _Function) else s for s in final])
				continue

		return final
		# if isinstance(tmp_program, _Program):
		#     tmp_program.program = final
		# else: tmp_program = final

	def get_real_depth(output):
		x = []
		x.append('')
		for item in output:
			if item not in {'(', ',', ')'}:
				x[-1] += item
			else:
				x.append('')
		x = [s for s in x if len(s) > 0]
		return len(x)

	def str_(program):
		"""Overloads `print` output of the object to resemble a LISP tree."""
		if isinstance(program, _Program):
			program = program.program

		terminals = [0]
		output = ''
		fs = []
		for i, node in enumerate(program):
			if isinstance(node, _Function):
				terminals.append(node.arity)
				output += node.name + '('
				fs.append(node)
			else:
				if isinstance(node, int):
					if len(fs) == 0:
						output += 'X%s' % node
					elif (fs[-1].wd_range is not None) and (terminals[-1] == 1):
						output += 'wd%s' % node
					else:
						output += 'X%s' % node
				else:
					output += '%.3f' % node
				terminals[-1] -= 1
				while terminals[-1] == 0:
					terminals.pop()
					terminals[-1] -= 1
					output += ')'
				if i != len(program) - 1:
					output += ','
		semi = 1
		try:
			start = output.find('(')
		except:
			return output
		for i in range(start + 1, len(output)):
			if output[i] == '(':
				semi += 1
			elif output[i] == ')':
				semi -= 1
			if semi == 0:
				break
		return output[: i + 1]

	def build_one_program(idx):
		nonlocal all_programs
		warnings.filterwarnings('ignore')
		random_state = check_random_state(seeds[idx])
		if parents is None:
			program, genome = None, None
		else:
			parent, parent_index = _tournament(random_state)
			# print('father',parent, parent_index)
			method = random_state.uniform()
			if method < method_probs[0]:
				donor, donor_index = _tournament(random_state)
				# print('mother', donor, donor_index)
				program, removed, remains = parent.crossover(
					donor.program, random_state
				)
				# print('son', [s.name if isinstance(s, _Function) else s for s in program])
				genome = {
					'method': 'Crossover',
					'parent_idx': parent_index,
					'parent_nodes': removed,
					'donor_idx': donor_index,
					'donor_nodes': remains,
				}
			else:
				program = parent.reproduce()
				genome = {
					'method': 'Reproduction',
					'parent_idx': parent_index,
					'parent_nodes': [],
				}

			# print('before', str_(program), gen)
			program = check_program(program, random_state)
			program = program[: get_real_depth(str_(program))]
			# if gen>1:
			#     print('after', str_(program))

			# while True:
			#     program = program[:get_real_depth(str_(program))]
			#     program1 = program[:get_real_depth(str_(program))]
			#     if len(program) == len(program1):
			#         break
			# print('after', str_(program), program)

		def validate_program_(program):
			"""Rough check that the embedded program in the object is valid."""
			terminals = [0]
			if isinstance(program, _Program):
				x = program.program
			else:
				x = program
			# print('valid', len(x), str_(x))
			for node in x:
				# print(node)
				if isinstance(node, _Function):
					terminals.append(node.arity)
				else:
					terminals[-1] -= 1
					while terminals[-1] == 0:
						terminals.pop()
						terminals[-1] -= 1
			return terminals == [-1]

		tmp_program = _Program(
			function_set=function_set,
			arities=arities,
			init_depth=init_depth,
			init_method=init_method,
			n_features=n_features,
			metric=metric,
			transformer=transformer,
			const_range=const_range,
			p_point_replace=p_point_replace,
			parsimony_coefficient=parsimony_coefficient,
			feature_names=feature_names,
			random_state=random_state,
			program=program,
		)
		# except Exception as err:
		#     print(err, str_(program), get_real_depth(str_(program)))
		#     validate_program_(program)

		if gen > 1:  # noqa: PLR1702
			tmp_program.program = check_program(tmp_program, random_state)
			# print('before', tmp_program)
			tmp_program.program = tmp_program.program[
				: get_real_depth(tmp_program.__str__())
			]
			# print('after', tmp_program)

		if tmp_program not in all_programs:
			all_programs.add(tmp_program)
			program = tmp_program
		else:
			return None

		program.parents = genome
		# indices, not_indices = program.get_all_indices(n_samples, max_samples, random_state)

		# program.raw_fitness_ = program.raw_fitness(X, y, device=f'cuda:{idx % num_cuda}')

		# if np.isnan(program.raw_fitness_) | np.isinf(program.raw_fitness_):
		# 	program.raw_fitness_ = 0.0
		# if (program.raw_fitness_ >= params['threshold']) & (params['verbose'] > 1):
		# 	# print(' ' * 100, end='\r')  ## clear printed text and move to head of line
		# 	Logger.info(f'[{idx:6d}]: {program.raw_fitness_: .4f} {program.depth_: 4d} {program.__str__():20}')

		return program

	# Build programs
	# all_programs = []
	tasks = [(i,) for i in range(n_programs)]
	programs = often.parallel(
		build_one_program,
		tasks[:],
		njobs=num_threads,
		backend='loky' if len(all_programs) == 0 else 'threading',
		progress_bar=params['progress_bar'],
		desc=f'[gen {gen} build_programs]',
	)

	programs = [s for s in programs if s is not None]

	def calc_raw_fitness(i, programs):
		programs[i].raw_fitness_ = programs[i].raw_fitness(
			X, y, device=f'cuda:{i % num_cuda}'
		)
		if np.isnan(programs[i].raw_fitness_) | np.isinf(programs[i].raw_fitness_):
			programs[i].raw_fitness_ = 0.0
		if (programs[i].raw_fitness_ >= params['threshold']) & (params['verbose'] > 1):
			# print(' ' * 100, end='\r')  ## clear printed text and move to head of line
			Logger.info(
				f'[{i:6d}]: {programs[i].raw_fitness_: .4f} {programs[i].depth_: 4d} {programs[i].__str__():20}'
			)

	tasks = [(i, programs) for i in range(len(programs))]
	_ = often.parallel(
		calc_raw_fitness,
		tasks[:],
		num_threads,
		'threading',
		params['progress_bar'],
		f'[gen {gen} calc_raw_fitness]',
	)

	# print(f'gen {gen} unique num_program: {len(programs)}')
	return programs


class BaseSymbolic(BaseEstimator, metaclass=ABCMeta):
	"""Base class for symbolic regression / classification estimators.

	Warning: This class should not be used directly.
	Use derived classes instead.

	"""

	@abstractmethod
	def __init__(self, cfg: dict):
		self.__dict__.update(cfg)

	def _verbose_reporter(self, run_details=None):
		if run_details is None:
			print('    |{:^25}|{:^42}|'.format('Population Average', 'Best Individual'))
			print('-' * 4 + ' ' + '-' * 25 + ' ' + '-' * 42 + ' ' + '-' * 10)
			line_format = '{:>4} {:>8} {:>16} {:>8} {:>16} {:>16} {:>10}'
			print(
				line_format.format(
					'Gen',
					'max_fitness',
					'min_fitness',
					'top_program',
					'bot_program',
					'OOB Fitness',
					'Time Left',
				)
			)

		else:
			# Estimate remaining time for run
			gen = run_details['generation'][-1]
			generation_time = run_details['generation_time'][-1]
			remaining_time = (self.generations - gen - 1) * generation_time
			if remaining_time > 60:
				remaining_time = '{0:.2f}m'.format(remaining_time / 60.0)
			else:
				remaining_time = '{0:.2f}s'.format(remaining_time)

			line_format = '{:4d} {:8.4f} {:6d} {:25s} {:>10}'
			contents = line_format.format(
				run_details['generation'][-1],
				run_details['max_fitness'][-1],
				run_details['depth'][-1],
				run_details['top_program'][-1],
				remaining_time,
			)

			Logger.info(contents)

	def fit(self, X, y, sample_weight=None):
		random_state = check_random_state(self.random_state)

		print('num_function_set: ', len(self.function_set), flush=True)
		self._function_set = []
		for function in self.function_set:
			if isinstance(function, str):
				self._function_set.append(_function_map[function])
			elif isinstance(function, _Function):
				self._function_set.append(function)

		# For point-mutation to find a compatible replacement node
		# check arity for each function in function_set
		self._arities = {}
		for function in self._function_set:
			arity = function.arity
			self._arities[arity] = self._arities.get(arity, [])
			self._arities[arity].append(function)

		# check metric
		if isinstance(self.metric, _Fitness):  # self-defined metric
			self._metric = self.metric
		elif isinstance(self, TransformerMixin):  # pre-defined metric
			self._metric = _fitness_map[self.metric]

		self._method_probs = np.array(
			[
				self.p_crossover,
				self.p_subtree_mutation,
				self.p_hoist_mutation,
				self.p_point_mutation,
			]
		)
		self._method_probs = np.cumsum(self._method_probs)

		if self._method_probs[-1] > 1:
			raise ValueError(
				'The sum of p_crossover, p_subtree_mutation, '
				'p_hoist_mutation and p_point_mutation should '
				'total to 1.0 or less.'
			)

		if self.init_method not in {'half and half', 'grow', 'full'}:
			raise ValueError(
				'Valid program initializations methods include '
				'"grow", "full" and "half and half". Given %s.' % self.init_method
			)

		# params = self.get_params()
		params = self.__dict__
		for key in ['threshold', 'corr_threshold']:
			if isinstance(params[key], tuple):
				params[key] = params[key][0]

		params['_metric'] = self._metric
		if hasattr(self, '_transformer'):
			params['_transformer'] = self._transformer
		else:
			params['_transformer'] = None
		params['function_set'] = self._function_set
		params['arities'] = self._arities
		params['method_probs'] = self._method_probs

		self.run_details_ = {
			'generation': [],
			'max_fitness': [],
			'depth': [],
			'top_program': [],
			'generation_time': [],
		}

		if self.warm_start:
			Logger.info('loading pre-fitted programs')
			cached_all = f'{self.cache_root}/{self.savetag}.all.npy'
			if not os.path.exists(cached_all):
				Logger.info('cached programmes not found')
				self._programs = []
				self.all_programs, self.good_programs = set(), []
			else:
				self._programs = np.load(cached_all, allow_pickle=True).tolist()
				all_programs_ = tools.unfold_sublists(self._programs)
				self.all_programs = set([p for p in all_programs_ if p is not None])
				self.good_programs = [
					p for p in all_programs_ if np.abs(p.fitness_) >= self.threshold
				]
		else:
			self._programs = []
			self.all_programs, self.good_programs = set(), []

		prior_generations = len(self._programs)
		for gen in range(prior_generations, self.generations):
			self.gen = gen
			start_time = time()
			if gen == 0:
				parents = None
			else:
				parents = self._programs[gen - 1]

			# Parallel loop
			n_jobs, n_programs, starts = _partition_estimators(self.population_size, 1)
			seeds = random_state.randint(
				MAX_INT, size=self.population_size
			)  # random number seeds

			population = _parallel_evolve(
				n_programs[0],
				parents,
				X,
				y,
				sample_weight,
				seeds[starts[0] : starts[1]],
				params,
				gen,
				self.num_threads,
				self.all_programs,
				self.good_programs,
			)

			# Reduce, maintaining order across different n_jobs
			population = list(itertools.chain.from_iterable([population]))

			self.fitness = [program.raw_fitness_ for program in population]
			# print(f'fitness: {sorted(fitness)}')
			length = [program.length_ for program in population]

			parsimony_coefficient = None
			if self.parsimony_coefficient == 'auto':
				parsimony_coefficient = np.cov(length, self.fitness)[1, 0] / np.var(
					length
				)

			for program in population:
				program.fitness_ = program.fitness(parsimony_coefficient)

			self._programs.append(population)

			self.good_programs = [
				s
				for s in self._programs[-1]
				if np.abs(s.raw_fitness_) >= params['threshold']
			]  # best individual

			# Record run details
			# if self._metric.greater_is_better:
			# 	best_program = population[np.argmax(self.fitness)]
			# else:
			# 	best_program = population[np.argmin(self.fitness)]

			# print(f'num_nan in fitness: {np.isnan(fitness).sum()}')
			self.run_details_['generation'].append(gen)
			self.run_details_['max_fitness'].append(np.nanmax(self.fitness))
			self.run_details_['depth'].append(
				population[np.argmax(self.fitness)].depth_
			)
			self.run_details_['top_program'].append(
				str(population[np.argmax(self.fitness)])
			)
			generation_time = time() - start_time
			self.run_details_['generation_time'].append(generation_time)

			if self.verbose > 0:
				self._verbose_reporter(self.run_details_)

			# tobe_cached_programmes = {
			# 	'all_programs': self._programs,
			# 	'good_programs': self.good_programs,
			# }
			# joblib.dump(tobe_cached_programmes, f'{self.cache_root}/{self.savetag}.ga')
			
			tobe_cached_all = np.array(self._programs)
			os.makedirs(f'{self.cache_root}', exist_ok=True)
			np.save(f'{self.cache_root}/{self.savetag}.all.npy', tobe_cached_all, allow_pickle=True)
			
			torch.cuda.empty_cache()

			if self.sift:
				self.good_programs = self.ga_corr_sift(
					X, corr_threshold=self.corr_threshold
				)
				joblib.dump(
					self.good_programs,
					f'{self.cache_root}/{self.savetag}_gen{gen}_{self.corr_threshold}.sifted',
				)

		# self.corr_sift(X, self.corr_threshold)
		# self.best_programs = self.ga_corr_sift(X, corr_threshold=self.corr_threshold)

	def corr_sift(self, X, corr_threshold: float = 0.5):
		if isinstance(self, TransformerMixin):
			self.best_programs = self.good_programs

			for k in range(100):
				num_best = len(self.best_programs)
				Logger.info(f'[{k}] num_best_programs: {num_best}')
				# sift good_programs
				best_programs = []
				for i in range(0, len(self.best_programs), 500):
					preds = pd.DataFrame(
						np.vstack(
							[p.execute(X) for p in self.best_programs[i : i + 500]]
						)
					).T
					self.corrs = (
						Algo.mat_corr(preds).dropna(how='all').dropna(how='all', axis=1)
					)
					self.sifted = tools.corr_sift(self.corrs, threshold=corr_threshold)
					best_programs_ = [
						self.best_programs[j] for j in self.sifted.index.tolist()
					]
					best_programs.extend(best_programs_)
					# Logger.info(
					# 	f'sifting good_programs: {min(i + 500, num_best)}/{num_best}'
					# )
				self.best_programs = best_programs
				if len(self.best_programs) == num_best:
					break

			# Logger.info(f'num_good_programs: {len(self.good_programs)}')
			# preds = pd.DataFrame(np.vstack([p.execute(X) for p in self.good_programs])).T
			# self.corrs = Algo.mat_corr(preds).dropna(how='all').dropna(how='all', axis=1)
			# self.sifted = tools.corr_sift(self.corrs, threshold=corr_threshold)
			# Logger.info(f'sifted_good_programs: {self.sifted.shape[0]}')
			# self.best_programs = [self.good_programs[i] for i in self.sifted.index.tolist()]

		else:  # noqa: PLR5501
			# Find the best individual in the final generation
			if self._metric.greater_is_better:
				self._program = self._programs[-1][np.argmax(self.fitness)]
			else:
				self._program = self._programs[-1][np.argmin(self.fitness)]

		# return self

	def get_corrs_cuda(self, df1: pd.Series, df2: pd.Series, device: str = 'cuda:0'):
		x = torch.from_numpy(df1.to_numpy()).pin_memory().to(device, non_blocking=True)
		y = torch.from_numpy(df2.to_numpy()).pin_memory().to(device, non_blocking=True)
		x[torch.isnan(x)] = 0.0
		y[torch.isnan(y)] = 0.0
		cov = x.mul(y).mean() - x.mean() * y.mean()
		std = x.std() * y.std()
		r2 = torch.true_divide(cov, std)
		return r2.cpu().numpy()

	def ga_corr_sift(self, xvalid: pd.DataFrame, corr_threshold: float = 0.8):
		import gc, torch, threading, time
		lock = threading.Lock()
		stop_event = threading.Event()
		
		num_cuda = torch.cuda.device_count()
		torch.set_num_threads(self.num_threads)

		good_fitness = [
			(i, program.fitness_) for i, program in enumerate(self.good_programs[:])
		]
		good_fitness.sort(key=lambda x: x[1], reverse=True)
		print(good_fitness[:5])

		def execute(program):
			return program.execute(xvalid)

		def prepare(good_fitness: list[tuple], good_preds: list):
			while not stop_event.is_set():
				if self.progress_bar:
					good_fitness = tqdm(good_fitness, desc='data preparation')
				for idx, _ in good_fitness:
					with lock:
						not_None = [value is not None for value in good_preds.values()]

						while np.sum(not_None) >= 20:
							time.sleep(0.1)
							not_None = [value is not None for value in good_preds.values()]

						try:
							good_preds[idx] = execute(self.good_programs[idx])
						except Exception as e:
							good_preds[idx] = None

		first_idx = good_fitness[0][0]
		good_preds = {first_idx: execute(self.good_programs[first_idx])}
		data_thread = threading.Thread(
			target=prepare, args=(good_fitness, good_preds), daemon=True
		)
		data_thread.start()

		selected = [self.good_programs[good_fitness[0][0]]]
		base_pred = [good_preds[first_idx].copy()]

		def udpate_maxcorr(
			idx: int,
			item: pd.DataFrame,
			max_corr: list,
			pred: pd.DataFrame,
			lock2: threading.Lock,
		):
			if max_corr[0] > corr_threshold:
				return None
			corr_ = self.get_corrs_cuda(pred, item, device=f'cuda:{idx % num_cuda}')
			if np.abs(corr_) > max_corr[0]:
				with lock2:
					max_corr[0] = np.abs(corr_)

		if self.progress_bar:
			good_fitness = tqdm(good_fitness[1:], desc='corr_sifting')
		else:
			Logger.info('corr_sifting ...')
		for idx, _ in good_fitness:
			program = self.good_programs[idx]
			while idx not in good_preds.keys():
				time.sleep(0.1)
			max_corr = [0.0]
			lock2 = threading.Lock()

			if good_preds[idx] is None:
				continue
			tasks = [
				(i, item, max_corr, good_preds[idx], lock2)
				for i, item in enumerate(base_pred[::-1])
			]
			_ = often.parallel(
				udpate_maxcorr,
				tasks,
				njobs=num_cuda,
				backend='threading',
				progress_bar=False,
			)

			if max_corr[0] > corr_threshold:
				good_preds[idx] = None  ## in case of memory overflow
				continue
			else:
				selected.append(program)
				base_pred.append(good_preds[idx].copy())
				good_preds[idx] = None  ## in case of memory overflow

				contents = f'selected: {program.fitness_:5.4f}, {idx: 5d}, {program.depth_: 5d}, {len(selected): 5d},  {program.__str__():25}'
				Logger.info(contents)

		stop_event.set()
		data_thread.join()

		del good_preds
		gc.collect()
		torch.cuda.empty_cache()
		return selected


class SymbolicTransformer(BaseSymbolic, TransformerMixin):
	def __init__(
		self,
		cfg: dict,
	):
		super(SymbolicTransformer, self).__init__(cfg)
		self.__dict__.update(cfg)

	def transform(self, X):
		if not hasattr(self, '_best_programs'):
			raise NotFittedError('SymbolicTransformer not fitted.')
		X_new = np.array([gp.execute(X) for gp in self._best_programs]).T
		return X_new

	def fit_transform(self, X, y, sample_weight=None):
		return self.fit(X, y, sample_weight).transform(X)
